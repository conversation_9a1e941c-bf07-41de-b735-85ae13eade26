import ReactDOM from 'react-dom';
import { I18nextProvider } from 'react-i18next';
import { Provider } from 'react-redux';
import { BrowserRouter, MemoryRouter } from 'react-router-dom';

import { SystemService } from 'genesis-web-service/lib/system/system.service';
import { UserService } from 'genesis-web-service/lib/user/user.service';
import { security } from 'genesis-web-shared';
import { ProductLineI18n } from 'genesis-web-shared/lib/i18n/product-line-i18n';
import { l10nInit } from 'genesis-web-shared/lib/l10n';

import Layout from './App';
import i18n from './i18n.ts';
import {
  fillClientConfig,
  fillLocale,
  fillMixedUserInfo,
  fillModulePermissions,
  getBizDictsPromise,
  getEnvConfig,
  getTenantDictsPromise,
} from './redux/action';
import store from './redux/store';
import { routesConfig } from './router';

// 处理主子应用跳转时 异步加载样式丢失问题
// 设定两个全局变量存储bootstrap阶段获取到的样式文件，并且在每次mount跟router change的情况下累算样式文件个数，不一致则删除所有样式文件并重新添加
window.styleProductCount = 0;
window.styleProductList = [];

window.fixStyleBugProduct = () => {
  const qiankunStyles = document.querySelectorAll('div[data-name="genesis-web-market"] link');
  const len = qiankunStyles.length;
  if (window.styleProductCount < len) {
    window.styleProductList = Array.from(qiankunStyles);
    window.styleProductCount = len;
  } else if (window.styleProductCount > len) {
    if (document.querySelectorAll('div[data-name="genesis-web-market"]')) {
      const container = document.querySelectorAll('div[data-name="genesis-web-market"]')[0];
      qiankunStyles.forEach(style => {
        container.removeChild(style);
      });
      window.styleProductList.forEach(style => {
        // 重新添加完整<style>
        container.appendChild(style);
      });
    }
  }
};

const init = async omsConfig => {
  try {
    if (process.env.NODE_ENV === 'development') {
      const data = await SystemService.generateSecurityContext();
      if (data?.csrfToken) {
        security.csrf(data?.csrfToken);
      }
    }
    ProductLineI18n.getInstance();
    l10nInit();

    store.dispatch(getEnvConfig(omsConfig));
    store.dispatch(getBizDictsPromise());
    store.dispatch(getTenantDictsPromise());

    const { lang } = await SystemService.getLocale();
    store.dispatch(fillLocale({ payload: lang }));

    const config = await SystemService.getConfiguration();
    store.dispatch(fillClientConfig({ payload: config }));

    if (config.security) {
      const permissions = await UserService.getPermissions();
      store.dispatch(fillModulePermissions({ payload: permissions }));
    }

    const user = await UserService.getOauthUserInfo();
    store.dispatch(fillMixedUserInfo(user));
    window.tenantRegion = user.region.toUpperCase();
  } catch (e) {
    // Do nothing, just hold error
  }
};

const renderApp = async ({ Layout: App, omsConfig, selector, RouterCompo, theme, messageDuration }) => {
  ReactDOM.render(
    <Provider store={store}>
      <I18nextProvider i18n={i18n}>
        <RouterCompo>
          <App config={omsConfig} theme={theme} messageDuration={messageDuration} />
        </RouterCompo>
      </I18nextProvider>
    </Provider>,
    selector
  );
};

const initRenderApp = params => {
  (async props => {
    let RouterCompo = BrowserRouter;
    const { container, loadPage, setGlobalState, theme, messageDuration } = props || {};
    // BI - 将路由传给主应用，获取sidebar权限
    if (setGlobalState) {
      setGlobalState({ routesInfo: routesConfig });
    }
    const omsConfig = await SystemService.getConfiguration();
    // 存储zeUS关联的tenant-configuration页面tenant信息
    // 判断主应用是否使用loadMicroApp加载此微应用，并且是否传递注定加载page
    if (loadPage) {
      // 1. loadMicroApp
      // 2. 有指定加载page
      // 需要使用MemoryRouter，否则会影响顶层路由
      RouterCompo = MemoryRouter;
    }
    // 初始化数据
    await init(omsConfig);
    // 渲染
    const selector = container ? container.querySelector('#micro') : document.querySelector('#micro');
    await renderApp({
      Layout,
      omsConfig,
      selector,
      RouterCompo,
      theme,
      messageDuration,
    });
  })(params);
};

// 独立运行时，直接挂载应用
// eslint-disable-next-line no-underscore-dangle
if (!window.__POWERED_BY_QIANKUN__) {
  initRenderApp();
}

/**
 * bootstrap 只会在微应用初始化的时候调用一次，下次微应用重新进入时会直接调用 mount 钩子，不会再重复触发 bootstrap。
 * 通常我们可以在这里做一些全局变量的初始化，比如不会在 unmount 阶段被销毁的应用级别的缓存等。
 */
export async function bootstrap() {
  // console.log('ReactMicroApp bootstraped');
}

/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount(props) {
  window.fixStyleBugProduct();
  initRenderApp(props);
}

/**
 * 应用每次 切出/卸载 会调用的方法，通常在这里我们会卸载微应用的应用实例
 */
export async function unmount(props) {
  ReactDOM.unmountComponentAtNode(props.container.querySelector('#micro'));
}

if (module.hot) {
  module.hot.accept('./App', () => renderApp({ Layout }));
}
