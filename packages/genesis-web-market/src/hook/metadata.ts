import { useCallback, useEffect, useState } from 'react';

import { MetadataService, ObjectCategoryComponent } from 'genesis-web-service';

export const useObjectCategoryTree = () => {
  const [objectCategoryComponents, setObjectCategoryComponents] = useState<ObjectCategoryComponent[]>([]);

  const [objectTypeI18nMap, setObjectTypeI18nMap] = useState<Record<string, string>>({});
  const [objectSubCategoryI18nMap, setObjectSubCategoryI18nMap] = useState<Record<string, string>>({});

  useEffect(() => {
    MetadataService.queryObjectComponent().then(res => {
      const tempMap: Record<string, string> = {};
      const tempSubCategoryMap: Record<string, string> = {};

      res.forEach(item => {
        item?.components.map(subItem => {
          tempSubCategoryMap[`${subItem.objectCategory}_${subItem.objectSubCategory}`] = subItem.name;
          tempMap[`${subItem.objectCategory}_${subItem.objectSubCategory}`] = item.name;
        });
      });

      setObjectTypeI18nMap(tempMap);
      setObjectSubCategoryI18nMap(tempSubCategoryMap);
      setObjectCategoryComponents(res);
    });
  }, []);

  return {
    objectCategoryComponents,
    objectTypeI18nMap,
    objectSubCategoryI18nMap,
  };
};
