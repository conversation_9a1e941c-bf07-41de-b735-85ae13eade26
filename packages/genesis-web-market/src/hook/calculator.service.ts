import { useEffect, useState } from 'react';

import { MatrixTableBasicInfo } from 'genesis-web-service';
import { CalculatorService } from 'genesis-web-service/lib/calculator/calculator.service';

export const useMatrixList = (categoryList: number[] | string[]) => {
  const [matrixList, setMatrixList] = useState<MatrixTableBasicInfo[]>([]);

  useEffect(() => {
    CalculatorService.queryMatrixTableList(categoryList).then(res => {
      setMatrixList(res);
    });
  }, [categoryList]);

  return matrixList;
};
