import { useEffect, useState } from 'react';

import { useRequest } from 'ahooks';

import {
  FormulaListRelationResponse,
  SchemaFormulaRelationRequest,
  enums_ProductTypeEnum,
} from 'genesis-web-service/service-types/product-types/package/index';

import { NewProductService } from '@market/services/product/product.service.new';

export const useRiderProductList = () => {
  const { data, loading } = useRequest(() =>
    NewProductService.ProductStructureService.queryProductList({
      type: enums_ProductTypeEnum.RIDER,
    }).then(res => res || [])
  );

  return {
    data: data || [],
    loading,
  };
};

export const useFormulaList = (condition: SchemaFormulaRelationRequest) => {
  const [formulaList, setFormulaList] = useState<FormulaListRelationResponse[]>([]);

  useEffect(() => {
    NewProductService.ProductSchemaFormulaMgmtService.list(condition).then(res => {
      setFormulaList(res);
    });
  }, []);

  return formulaList;
};
