import { useEffect, useState } from 'react';

import {
  BankType,
  BrokerCompanyType,
  ChannelDetail,
  QueryAgencyResult,
  QueryChannelRelationResult,
  QueryChannelResult,
  QueryServiceCompanyResult,
  TenantOrgType,
} from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service/lib/channel/channel.service';
import { MarketService } from 'genesis-web-service/lib/market/market.service';

import { FEChannel } from '@market/common/interface';
import { queryChannelList } from '@market/marketService/channel.service';
import { NewChannelService } from '@market/services/channel/channel.service.new';
import { ChannelResponse } from 'genesis-web-service/service-types/channel-types/package';

export const useChannelList = () => {
  const [channelList, setChannelList] = useState<ChannelResponse[]>([]);

  useEffect(() => {
    NewChannelService.ChannelService.pageQueryChannel({
      queryParams: {
        pageIndex: 0,
        pageSize: 10000,
        type: 'SALE_CHANNEL',
      }
    }).then(res => {
      if (res.data) {
        setChannelList(res.data || []);
      }
    });
  }, []);

  return channelList;
};

/**
 * 获取渠道列表，列表中包含sub channel
 */
export const useChannelAndSubChannelList = () => {
  const [channelList, setChannelList] = useState<FEChannel[]>([]);

  useEffect(() => {
    queryChannelList().then(totalChannelList => {
      setChannelList(totalChannelList);
    });
  }, []);

  return channelList;
};

export const useServiceCompanyList = () => {
  const [serviceCompanyList, setServiceCompanyList] = useState<QueryServiceCompanyResult[]>([]);

  useEffect(() => {
    MarketService.queryServiceCompany().then(res => {
      if (res.success) {
        setServiceCompanyList(res.value.results);
      }
    });
  }, []);

  return serviceCompanyList;
};

export const useAgencyList = () => {
  const [agencyList, setAgencyList] = useState<QueryAgencyResult[]>([]);

  useEffect(() => {
    MarketService.queryAgency().then(res => {
      if (res.success) {
        setAgencyList(res.value.results);
      }
    });
  }, []);

  return agencyList;
};

export const useRelationList = () => {
  const [channelRelationList, setChannelRelationList] = useState<QueryChannelRelationResult[]>([]);

  useEffect(() => {
    MarketService.queryChannelRelation().then(res => {
      if (res.success) {
        setChannelRelationList(res.value.results);
      }
    });
  }, []);

  return channelRelationList;
};

export const useTiedAgentChannelList = () => {
  const [tiedAgentChannelList, setTiedAgentChannelList] = useState<QueryChannelResult[]>([]);

  useEffect(() => {
    MarketService.queryTiedAgentChannel().then(res => {
      if (res.success) {
        setTiedAgentChannelList(res.value.results);
      }
    });
  }, []);

  return tiedAgentChannelList;
};

export const useLeasingChannelList = () => {
  const [channelList, setChannelList] = useState<ChannelDetail[]>([]);

  useEffect(() => {
    ChannelService.getChannelList({
      pageIndex: 0,
      pageSize: 10000,
      type: BankType.LeaseChannel,
    }).then(res => {
      setChannelList(res.data || []);
    });
  }, []);

  return channelList;
};

export const useBankList = () => {
  const [bankList, setBankList] = useState<ChannelDetail[]>([]);

  useEffect(() => {
    ChannelService.getChannelList({
      pageIndex: 0,
      pageSize: 10000,
      type: BankType.Bank,
    }).then(res => {
      setBankList(res.data || []);
    });
  }, []);

  return bankList;
};

export const useBrokerCompanyList = () => {
  const [brokerCompanyList, setBrokerCompanyList] = useState<ChannelDetail[]>([]);

  useEffect(() => {
    ChannelService.getChannelList({
      pageIndex: 0,
      pageSize: 10000,
      type: BrokerCompanyType.BrokerCompany,
    }).then(res => {
      setBrokerCompanyList(res.data || []);
    });
  }, []);

  return brokerCompanyList;
};

export const useFrontLineChannelList = () => {
  const [frontLineChannelList, setFrontLineChannelList] = useState<ChannelDetail[]>([]);

  useEffect(() => {
    ChannelService.getChannelList({
      pageIndex: 0,
      pageSize: 10000,
      type: TenantOrgType.FRONT_LINE_CHANNEL,
    }).then(res => {
      setFrontLineChannelList(res.data || []);
    });
  }, []);

  return frontLineChannelList;
};

export const useKeyAccountChannelList = () => {
  const [keyAccountChannelList, setKeyAccountChannelList] = useState<ChannelDetail[]>([]);

  useEffect(() => {
    ChannelService.getChannelList({
      pageIndex: 0,
      pageSize: 10000,
      type: TenantOrgType.KEY_ACCOUNT_CHANNEL,
    }).then(res => {
      setKeyAccountChannelList(res.data || []);
    });
  }, []);

  return keyAccountChannelList;
};
