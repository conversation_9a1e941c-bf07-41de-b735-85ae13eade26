import { useRequest } from 'ahooks';

import {
  CreateClaimStackTemplateRelationRequest,
  SaveStackTemplateRequest,
  UpdateClaimStackTemplateRelationRequest,
} from 'genesis-web-service/service-types/market-types/package';

import { NewMarketService } from '@market/services/market/market.service.new';

// create template relation
export const useCreateTemplateRelation = () => {
  const { runAsync: createTemplateRelation, loading } = useRequest(
    (param: CreateClaimStackTemplateRelationRequest) =>
      NewMarketService.PackageProductInsuranceMgmtService.createClaimStackTemplateRelation(param),
    {
      manual: true,
    }
  );

  return {
    createTemplateRelation,
    loading,
  };
};

// update template relation
export const useUpdateTemplateRelation = () => {
  const { runAsync: updateTemplateRelation, loading } = useRequest(
    (relationId: number, param: SaveStackTemplateRequest) =>
      NewMarketService.PackageProductInsuranceMgmtService.updateClaimStackTemplateRelation(relationId, param),
    {
      manual: true,
    }
  );

  return {
    updateTemplateRelation,
    loading,
  };
};

// delete new template relation
export const useDeleteTemplateRelation = () => {
  const { runAsync: deleteTemplateRelation, loading } = useRequest(
    (relationId: number) => {
      return NewMarketService.PackageProductInsuranceMgmtService.deleteClaimStackInstanceRelation(relationId);
    },

    {
      manual: true,
    }
  );

  return {
    deleteTemplateRelation,
    loading,
  };
};

// update stack instance relation
export const useUpdateInstanceRelation = () => {
  const { runAsync: updateInstanceRelation, loading } = useRequest(
    (param: UpdateClaimStackTemplateRelationRequest) =>
      NewMarketService.PackageProductInsuranceMgmtService.updateClaimStack(param),
    {
      manual: true,
    }
  );

  return {
    updateInstanceRelation,
    loading,
  };
};
