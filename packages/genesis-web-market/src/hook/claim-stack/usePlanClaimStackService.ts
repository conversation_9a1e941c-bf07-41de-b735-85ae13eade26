import { useRequest } from 'ahooks';

import { NewMarketService } from '@market/services/market/market.service.new';

export const usePlanClaimStackService = (goodsPlanId: number, packageId: number) => {
  const {
    runAsync: queryPlanClaimStack,
    loading,
    data: planClaimStack,
  } = useRequest(() => NewMarketService.GoodsPlanMgmtService.queryPlanClaimStack(goodsPlanId, packageId), {
    refreshDeps: [goodsPlanId],
  });

  return {
    queryPlanClaimStack,
    loading,
    planClaimStack,
  };
};
