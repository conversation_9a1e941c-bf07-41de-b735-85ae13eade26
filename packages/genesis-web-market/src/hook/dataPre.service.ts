/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { useEffect, useMemo, useState } from 'react';

import { useRequest } from 'ahooks';
import { keyBy } from 'lodash-es';

import { DataPreConfigurationService, GV, PartnerType } from 'genesis-web-service';

import { useChannelList } from './channel.serivce';
import { useGoodsSalesAttributes } from './goods.service';

export const useDatePreGoodsInfo = (goodsId: string | null) => {
  const [goodsInfo, setGoodsInfo] = useState<GV.DataPreGoodsInfo>();

  useEffect(() => {
    if (goodsId) {
      DataPreConfigurationService.queryGoodsDetail(goodsId).then(res => {
        setGoodsInfo(res);
      });
    }
  }, []);

  return goodsInfo;
};

export const usePolicySchema = (
  goodsInfo: GV.DataPreGoodsInfo | undefined,
  selectedPlanIndex: number,
  selectedProductAndLiabilityIds?: {
    productId: number;
    liabilityIds: number[];
  }[],
  plannedPremiumCalMethod?: any,
  calculationMethod?: any
) => {
  const channelList = useChannelList();
  const salesAttributes = useGoodsSalesAttributes(goodsInfo?.id);

  const goodsChannelList = useMemo(() => {
    const channelMap = keyBy(channelList, 'code');
    return salesAttributes?.salesPartnerList
      ?.filter(salesPartner => salesPartner.partnerType === PartnerType.salesChannel)
      .map(salesPartner => ({
        code: salesPartner.partnerCode,
        name: channelMap[salesPartner.partnerCode]?.name,
      }));
  }, [channelList, salesAttributes]);

  const {
    loading,
    error,
    data: planSchema,
  } = useRequest(
    () => {
      if (!goodsInfo || !goodsChannelList || !selectedProductAndLiabilityIds) {
        return Promise.reject();
      }

      return DataPreConfigurationService.extractSubSchema({
        goodsId: goodsInfo.id,
        phase: 'POLICY',
        json: JSON.stringify({
          goodsId: goodsInfo.id,
          plannedPremiumCalMethod,
          calculationMethod,
          planId: goodsInfo.planList[selectedPlanIndex].id,
          productLiability: selectedProductAndLiabilityIds,
        }),
      }).then(res => {
        const promotionCodeIndex = res?.properties?.promotionCode['x-index'];
        res.properties.promotionCode['x-tag'] = ['QUOTATION'];
        res.properties.channelCode = {
          readOnly: false,
          enum: true,
          'x-enum-display': goodsChannelList || [],
          'x-data-type': GV.DataPreSchemaFieldDataType.STRING,
          key: 'channelCode',
          'x-code': 'channelCode',
          title: 'Sales Channel',
          // sales channel显示在promotionCode的前面
          'x-index': promotionCodeIndex ? promotionCodeIndex - 0.5 : 100,
          'x-level': 'POLICY',
          'x-required': true,
          'x-tag': ['QUOTATION'],
        };
        return res;
      });
    },
    {
      refreshDeps: [
        selectedProductAndLiabilityIds,
        selectedPlanIndex,
        goodsInfo,
        goodsChannelList,
        plannedPremiumCalMethod,
        calculationMethod,
      ],
      loadingDelay: 500,
    }
  );

  return {
    loading,
    error,
    planSchema,
  };
};
