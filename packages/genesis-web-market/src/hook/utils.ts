import { useCallback, useRef, useState } from 'react';

/**
 * 多个状态中只能激活其中一个的互斥状态
 */
export const useActiveStatus = <T>(init?: T) => {
  const [current, setCurrent] = useState<T | undefined>(init);

  /**
   * 判断传入元素是否处于激活的状态
   */
  const isActive = useCallback((item: T) => item === current, [current]);

  /**
   * 是否存在激活的元素
   */
  const hasActive = useCallback(() => !!current, [current]);

  /**
   * 判断传入元素是否处于禁用状态
   */
  const isDisable = useCallback(
    (item: T) => {
      if (current && !isActive(item)) {
        return true;
      }
      return false;
    },
    [current, isActive]
  );

  return {
    isDisable,
    setCurrent,
    isActive,
    hasActive,
  };
};

export const useFileLoadingInfo = () => {
  const [loadingInfo, setLoadingInfo] = useState({
    visible: false,
    percent: 0,
    name: '',
  });
  const timer = useRef<number>();

  const setupLoading = useCallback((fileName: string, percent = 5) => {
    let step = 10;
    const initPercent = 20;
    setLoadingInfo({
      visible: true,
      percent: initPercent,
      name: fileName,
    });
    let currentPercent = initPercent + step;
    timer.current = window.setInterval(() => {
      if (percent > 50) {
        step = 5;
      } else if (percent > 70) {
        step = 4;
      } else if (percent > 90) {
        step = 3;
      }
      const nextPercent = currentPercent + step;

      setLoadingInfo({
        visible: true,
        percent: nextPercent > 99 ? 99 : nextPercent,
        name: fileName,
      });
      currentPercent = nextPercent;
    }, 1000);
  }, []);

  const stopLoading = useCallback(() => {
    clearInterval(timer.current);
  }, []);

  const cancelLoading = useCallback(() => {
    stopLoading();
    setLoadingInfo({
      visible: false,
      percent: 0,
      name: '',
    });
  }, [stopLoading]);

  const completeLoading = useCallback(
    (fileName: string) =>
      new Promise((resolve, reject) => {
        stopLoading();
        setLoadingInfo({
          visible: true,
          percent: 100,
          name: fileName,
        });

        setTimeout(() => {
          cancelLoading();

          resolve(true);
        }, 1000);
      }),
    [stopLoading, cancelLoading]
  );

  return {
    loadingInfo,
    setupLoading,
    stopLoading,
    completeLoading,
    cancelLoading,
  };
};
