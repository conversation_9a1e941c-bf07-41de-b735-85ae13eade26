import { useState } from 'react';

import { getFileFromDownloadResp } from '@market/util';

const useDownloadFile = (downloadFileFun: () => Promise<any>) => {
  const [uploading, setUploading] = useState(false);
  const downloadFun = () => {
    setUploading(true);
    downloadFileFun()
      .then(response => getFileFromDownloadResp(response))
      .finally(() => setUploading(false));
  };

  return [uploading, downloadFun];
};

export default useDownloadFile;
