import { useMemo, useState } from 'react';

import { useRequest } from 'ahooks';

import { NewMetadataService } from '@market/services/metadata/metadata.service.new';

export interface ObjectCategoryNode {
  code: string;
  name?: string;
  children: ObjectCategoryNode[];
  objectTypes: string[];
}

export interface EnhanceObjectCategoryNode extends ObjectCategoryNode {
  disableCheckbox?: boolean;
}

interface ObjectCategoryTenantConfig {
  categories: ObjectCategoryNode[];
}

/**
 * 展示的时候，过滤掉Address，看不到且选不到。
 * 提交的时候，检查标的同层级是否存在Address，存在就带着一起提交。
 * 过滤address，同时收集包含address的category。
 */
export const hideAddressAndCollectCategories = (
  categories: ObjectCategoryNode[],
  collectHasAddressCategory: (codes: string[]) => void,
  parentCategoryCodes: string[] = []
) => {
  const resultCategories: ObjectCategoryNode[] = [];
  categories.forEach(category => {
    const objectTypes = category?.objectTypes || [];
    const onlyHasAddress = objectTypes.length === 1 && objectTypes[0] === 'ADDRESS';

    if (objectTypes.includes('ADDRESS')) {
      category.objectTypes = objectTypes.filter((type: string) => type !== 'ADDRESS');

      collectHasAddressCategory([...parentCategoryCodes, category.code]);
    }

    const hasChildren = Array.isArray(category.children) && category.children.length > 0;
    if (hasChildren) {
      category.children = hideAddressAndCollectCategories(category.children, collectHasAddressCategory, [
        ...parentCategoryCodes,
        category.code,
      ]);
    }

    if (onlyHasAddress && !hasChildren) {
      // 如果有且只有一个ADDRESS，并且没有children，则隐藏不显示
    } else {
      resultCategories.push(category);
    }
  });

  return resultCategories;
};

/**
 * 将租户数据处理成cascader选项
 * 只有objectTypes可以选中，其余无法选中
 */
export const dealObjectCategories = (categories: EnhanceObjectCategoryNode[]) => {
  categories.forEach(category => {
    category.disableCheckbox = true;
    if (Array.isArray(category.children)) {
      dealObjectCategories(category.children);
    } else {
      category.children = [];
    }

    if (Array.isArray(category.objectTypes)) {
      category.children.push(
        ...category.objectTypes.map(
          type =>
            ({
              name: type,
              code: type,
            }) as ObjectCategoryNode
        )
      );
    }
  });

  return categories;
};

export const useObjectCategory = () => {
  const [hasAddressCategoryArr, setHasAddressCategoryArr] = useState<string[][]>([]);

  const { data: objectCategoryTenantConfig, loading } = useRequest(
    () => NewMetadataService.TenantConfigMgmtService.tenantConfig('object-category'),
    {
      cacheKey: 'tenant-config-object-category',
    }
  );

  const categories = useMemo(() => {
    const tempArr: string[][] = [];
    const categoriesWithoutAddress = hideAddressAndCollectCategories(
      (objectCategoryTenantConfig as ObjectCategoryTenantConfig)?.categories || [],
      codes => {
        tempArr.push(codes);
      }
    );
    setHasAddressCategoryArr(tempArr);
    return categoriesWithoutAddress;
  }, [objectCategoryTenantConfig]);

  const objectCategoryOptions = useMemo(() => dealObjectCategories(categories), [categories]);

  return {
    categories: categories || [],
    objectCategoryOptions,
    hasAddressCategoryArr,
    loading,
  };
};
