/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { useSelector } from 'react-redux';

import { BizDict, SelectOption } from '@market/common/interface';
import { selectEnums } from '@market/redux/selector';

export interface TransBizDictOption {
  labelKey: keyof BizDict;
  valueKey: keyof BizDict;
}

const defaultTransBizDictOption: TransBizDictOption = {
  labelKey: 'dictValueName',
  valueKey: 'dictValue',
};

export const useBizDict = (dictKey: string): BizDict[] => {
  const enums: Record<string, Array<BizDict>> = useSelector(selectEnums);

  if (!Array.isArray(enums[dictKey])) {
    return [];
  }

  return enums[dictKey];
};

export const useBizDictAsOptions = (dictKey: string, transBizDictOption = defaultTransBizDictOption): SelectOption[] =>
  useBizDict(dictKey).map(bizdict => ({
    label: bizdict[transBizDictOption.labelKey],
    value: bizdict[transBizDictOption.valueKey],
  }));

export const useBizDictDisplayName = (
  dictKey: string,
  value: string,
  transBizDictOption = defaultTransBizDictOption
): string => {
  const bizDict = useBizDict(dictKey).find(bizdict => bizdict[transBizDictOption.valueKey] === value);
  if (bizDict) {
    return bizDict[transBizDictOption.labelKey];
  }
  return '';
};
