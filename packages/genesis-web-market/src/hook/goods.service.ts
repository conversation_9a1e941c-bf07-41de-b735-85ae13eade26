import { useCallback, useEffect, useMemo, useState } from 'react';

import { FormulaProps, GoodsInfo, GoodsStatusType } from 'genesis-web-service';
import {
  GoodsSalesAttributesResponse,
  QueryGoodsSalesPartnerResponse,
} from 'genesis-web-service/service-types/market-types/package';

import { BizTopic, CommissionSubCategory } from '@market/common/enums';
import { DetailPageMode } from '@market/common/interface';
import { NewMarketService } from '@market/services/market/market.service.new';
import { mapFormulaToOldBizdict } from '@market/utils/enum';

export const useGoodsSalesAttributes = (goodsId?: string | number) => {
  const [salesAttributes, setSalesAttributes] = useState<GoodsSalesAttributesResponse>();

  useEffect(() => {
    if (goodsId) {
      NewMarketService.MarketStructureMgmtService.queryGoodsRelating({
        goodsId: +goodsId,
        queryBasic: true,
        querySalesAttributes: true,
      }).then(res => {
        setSalesAttributes(res.salesAttributes);
      });
    }
  }, [goodsId]);

  return salesAttributes;
};

/* Sales Attributes start */
export const useSalesChannelInfo = (goodsId: string, mode: DetailPageMode, partnerType?: number) => {
  const [salesChannelInfo, setSalesChannelInfo] = useState<QueryGoodsSalesPartnerResponse>();

  useCallback(() => {
    if (partnerType && (mode === DetailPageMode.edit || mode === DetailPageMode.view)) {
      NewMarketService.GoodsSalesPartnerMgmtService.queryGoodsSalesPartner(+goodsId).then(res => {
        setSalesChannelInfo(res);
      });
    }
  }, [goodsId, mode, partnerType]);

  return salesChannelInfo;
};

export const useCommissionFormulaOptions = (
  formulaList: FormulaProps[],
  formulaCategory: BizTopic,
  formulaSubCategory?: string
) => {
  const commissionFormulaOptions = useMemo(
    () =>
      formulaList
        .filter(formula => {
          // 历史数据没有subcateogry，没有subcategory视为GrossCommission
          if (formulaSubCategory === CommissionSubCategory.GrossCommission) {
            return (
              `${formula.formulaTableTypeId}` === formulaCategory &&
              (formula.formulaSubCategoryCode === formulaSubCategory || !formula.formulaSubCategoryCode)
            );
          }
          return (
            `${formula.formulaTableTypeId}` === formulaCategory && formula.formulaSubCategoryCode === formulaSubCategory
          );
        })
        .map(mapFormulaToOldBizdict),
    [formulaSubCategory, formulaList, formulaCategory]
  );

  return commissionFormulaOptions;
};

/* Sales Attributes end */

export const useGoodsList = ({
  filterStatus,
}: {
  filterStatus?: GoodsStatusType;
} = {}) => {
  const [goodsList, setGoodsList] = useState<GoodsInfo[]>([]);

  useEffect(() => {
    NewMarketService.MarketStructureMgmtService.queryGoodsRelatingList({}).then(res => {
      let result = res;

      if (filterStatus) {
        result = res.filter(goods => (goods.goodsBasicInfo?.goodsStatus as GoodsStatusType) === filterStatus);
      }

      setGoodsList(result);
    });
  }, []);

  return goodsList;
};
