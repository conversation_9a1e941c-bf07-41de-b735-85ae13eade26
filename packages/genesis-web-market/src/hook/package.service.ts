import { useEffect, useState } from 'react';

import { ApplicationElementsPackBase, PackageQueryResponseDTO } from 'market-types';

import { NewMarketService } from '@market/services/market/market.service.new';
import { uniqBy } from 'lodash-es';

export const useApplicationElementsGroupList = () => {
  const [list, setList] = useState<ApplicationElementsPackBase[]>();

  useEffect(() => {
    NewMarketService.ApplicationElementsPackService.list().then(res => {
      setList(uniqBy(res, 'applicationElementsPackCode'));
    });
  }, []);

  return list;
};

export const usePackageList = () => {
  const [list, setList] = useState<PackageQueryResponseDTO[]>();

  useEffect(() => {
    NewMarketService.PackageQueryMgmtService.query$POST$mgmt_package_queryByCondition({}).then(res => {
      setList(res.value?.packageList || []);
    });
  }, []);

  return list;
};
