import { Dispatch, SetStateAction, useState } from 'react';

import { DetailPageMode } from '@market/common/interface';

export interface DrawerState<T> {
  visible: boolean;
  mode?: DetailPageMode;
  record?: T;
}

export const useDrawerState = <T>(): [DrawerState<T>, Dispatch<SetStateAction<DrawerState<T>>>] => {
  const [drawerState, setDrawerState] = useState<DrawerState<T>>({
    visible: false,
  });

  return [drawerState, setDrawerState];
};
