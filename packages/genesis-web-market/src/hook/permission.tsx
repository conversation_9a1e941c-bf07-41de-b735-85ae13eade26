/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';

import { Permission } from 'genesis-web-shared/lib/constant';

import { selectGrantedPermissionMap, selectPermissionCheckMap } from '../redux/selector';
import { canActivateHelper } from '../util';

export interface PermissionControlLike {
  oneOf?: string[];
  allOf?: string[];
}

/**
 * Get a test function to verify if user has required permissions.
 * Notice that the priority of allOf is higher than anyOf,
 * which means, if allOf is set, anyOf will be ignored.
 * It will return true if meets empty list or undefined.
 */
export const useCanActivateFn = () => {
  const permissionMap = useSelector(selectPermissionCheckMap);
  return useCallback(
    (requiredPermissions: PermissionControlLike) => canActivateHelper(permissionMap, requiredPermissions),
    [permissionMap]
  );
};

export const useCanActivate = (requiredPermissions: PermissionControlLike) => {
  const canActivate = useCanActivateFn();
  return useMemo(() => canActivate(requiredPermissions), [canActivate, requiredPermissions]);
};

export const usePermission = (id: string): Permission | undefined => {
  const permissionMap: Record<string, Permission> = useSelector(selectGrantedPermissionMap);
  return useMemo(() => permissionMap[id], [id, permissionMap]);
};
