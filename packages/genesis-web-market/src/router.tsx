import { lazy, useEffect, useMemo } from 'react';
import { Location, NavigateFunction, matchRoutes, useLocation } from 'react-router-dom';

import { isEmpty } from 'lodash-es';

import { useCanActivateFn } from 'genesis-web-shared/lib/util/permissions';

const PackageManagementContainerComponent = lazy(() => import('./pages/PackageManagement'));
const GoodsManagementContainerComponent = lazy(() => import('./pages/GoodsManagement'));
const PackageBasicConfigComponent = lazy(() => import('./pages/PackageBasicConfig'));
const BenefitConfigurationContainerComponent = lazy(() => import('./pages/BenefitConfiguration/Container.js'));
const PackageAgreementContainerComponent = lazy(() => import('./pages/PackageAgreement/Container.js'));
const PackageApplicationElementsComponentNew = lazy(() => import('./pages/PackageApplicationElementsNew'));
const DeclarationSettingComponent = lazy(() => import('./pages/DeclarationSetting'));
const GoodsBasicConfigComponent = lazy(() => import('./pages/GoodsBasicConfig'));
const DocDisplayComponent = lazy(() => import('./pages/DocDisplay/DocDisplay'));
const SalesAttributesComponent = lazy(() => import('./pages/SalesAttributes'));
const CoveragePlanComponent = lazy(() => import('./pages/CoveragePlan'));
const DeclarationLibraryManagementComponent = lazy(() => import('./pages/DeclarationLibraryManagement'));
const QuotationManagementComponent = lazy(() => import('./pages/QuickQuotation/QuotationManagement'));
const QuotationDetailComponent = lazy(() => import('./pages/QuickQuotation/index'));
const NoticeConfigurationComponent = lazy(() => import('./pages/NoticeConfig/Container.js'));
const VoucherManagement = lazy(() => import('./pages/CustomerLoyalty/VoucherManagement/VoucherManagement'));
const VoucherDetail = lazy(() => import('./pages/CustomerLoyalty/VoucherDetail/VoucherDetail'));
const CustomerVoucher = lazy(() => import('./pages/CustomerVoucher/CustomerVoucherCenter/CustomerVoucherCenter'));
const ApplicationElementsGroupManagement = lazy(
  () => import('./pages/ApplicationElementsGroup/Management/ApplicationElementsGroup')
);
const ApplicationElementsGroupDetail = lazy(
  () => import('./pages/ApplicationElementsGroup/Detail/ApplicationElementsGroupDetail')
);
const PolicyChange = lazy(() => import('./pages/PolicyChange'));

interface AppRoute {
  id: string;
  path: string;
  component: React.FC<any>;
  oneOfPermissions?: string[];
  allOfPermissions?: string[];
}
export const useCanActivateRoutes = (routers: AppRoute[], permissions: Record<string, boolean>, security: boolean) => {
  const canActivate = useCanActivateFn(permissions);

  return useMemo<AppRoute[]>(
    () =>
      routers.filter((route: AppRoute) => {
        if (!security || isEmpty(permissions)) {
          return true;
        }

        const { oneOfPermissions, allOfPermissions } = route;
        return canActivate({
          oneOf: oneOfPermissions,
          allOf: allOfPermissions,
        });
      }),
    [canActivate, routers, security, permissions]
  );
};

export const routesConfig: AppRoute[] = [
  {
    id: 'market-01',
    path: '/market/package/search',
    component: PackageManagementContainerComponent,
    oneOfPermissions: ['market.view'],
  },
  {
    id: 'market-02',
    path: '/market/package/basic',
    component: PackageBasicConfigComponent,
    oneOfPermissions: ['market.view'],
  },
  {
    id: 'market-03',
    path: '/market/package/benefit-configuration',
    component: BenefitConfigurationContainerComponent,
    oneOfPermissions: ['market.view'],
  },
  {
    id: 'market-04',
    path: '/market/package/agreement',
    component: PackageAgreementContainerComponent,
    oneOfPermissions: ['market.view'],
  },
  {
    id: 'market-05',
    path: '/market/goods/search',
    component: GoodsManagementContainerComponent,
    oneOfPermissions: ['market.goods.view'],
  },
  {
    id: 'market-06',
    path: '/market/package/declaration',
    component: DeclarationSettingComponent,
    oneOfPermissions: ['market.view'],
  },
  {
    id: 'market-07',
    path: '/market/package/application-elements',
    component: PackageApplicationElementsComponentNew,
    oneOfPermissions: ['market.view'],
  },
  {
    id: 'market-09',
    path: '/market/goods/basic',
    component: GoodsBasicConfigComponent,
    oneOfPermissions: ['market.goods.view'],
  },
  {
    id: 'market-10',
    path: '/market/goods/doc-display',
    component: DocDisplayComponent,
    oneOfPermissions: ['market.goods.view'],
  },
  {
    id: 'market-11',
    path: '/market/goods/salesAttributes',
    component: SalesAttributesComponent,
    oneOfPermissions: ['market.goods.view'],
  },
  {
    id: 'market-12',
    path: '/market/goods/coverage-plan',
    component: CoveragePlanComponent,
    oneOfPermissions: ['market.goods.view'],
  },
  {
    id: 'market-13',
    path: '/market/declaration-library-management',
    component: DeclarationLibraryManagementComponent,
    oneOfPermissions: ['market.declaration.view'],
  },
  {
    id: 'market-17',
    path: '/market/quotation/management',
    component: QuotationManagementComponent,
    oneOfPermissions: ['quick.quotation.view'],
  },
  {
    id: 'market-18',
    path: '/market/quotation/detail',
    component: QuotationDetailComponent,
    oneOfPermissions: ['quick.quotation.view'],
  },
  {
    id: 'market-19',
    path: '/market/goods/notification-config',
    component: NoticeConfigurationComponent,
    oneOfPermissions: ['market.goods.view'],
  },
  {
    id: 'customer-loyalty-1',
    path: '/market/customer-loyalty/voucher-management',
    component: VoucherManagement,
    oneOfPermissions: ['market.view'],
  },
  {
    id: 'customer-loyalty-2',
    path: '/market/customer-loyalty/voucher-detail',
    component: VoucherDetail,
    oneOfPermissions: ['market.view'],
  },
  {
    id: 'customer-loyalty-3',
    path: '/market/customer-loyalty/customer-voucher',
    component: CustomerVoucher,
    oneOfPermissions: ['market.view'],
  },
  {
    id: 'market-33',
    path: '/market/application-elements-group-center',
    component: ApplicationElementsGroupManagement,
    oneOfPermissions: ['market.view'],
  },
  {
    id: 'market-34',
    path: '/market/application-elements-group-detail',
    component: ApplicationElementsGroupDetail,
    oneOfPermissions: ['market.view'],
  },
  {
    id: 'market-35',
    path: '/market/package/policy-change',
    component: PolicyChange,
    oneOfPermissions: ['market.view'],
  },
];

interface IProps {
  permissions: Record<string, boolean>;
  security: boolean;
}

export interface RouteComponentProps {
  clearSpin: () => void;
  loadSpin: () => void;
  tenant: string;
  enums: any;
  navigate: NavigateFunction;
  location: Location;
  [prop: string]: any;
}

export const useActivateRoutes = (props: IProps) => {
  const { permissions, security } = props;
  const { pathname } = useLocation();
  const activatedRoutes = useCanActivateRoutes(routesConfig, permissions, security);

  useEffect(() => {
    const matchRouters = matchRoutes(routesConfig ?? [], pathname) || [];
    matchRouters.forEach(selectedRoute => {
      const { oneOfPermissions } = (selectedRoute.route as { oneOfPermissions?: string[] }) || {};

      if (
        !selectedRoute ||
        (permissions && oneOfPermissions?.length && oneOfPermissions.every(permission => !permissions?.[permission]))
      ) {
        window.history.pushState(null, '', '/404');
      }
    });
  }, [pathname, permissions]);

  return activatedRoutes;
};
