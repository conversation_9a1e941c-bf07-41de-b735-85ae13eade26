/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

const alphanumeric = /[^0-9]/g;
const character = /[^A-Za-z]/g;
const emailPattern = /[^A-Za-z0-9@.]/g;
const charAndSpace = /[^A-Za-z\s]/g;
const alpAndChar = /[^A-Za-z0-9]/g;
const accountPattern = /[^+0-9\s]/g;
const specialCharacter = /[`!~@#$%^&*+=<>?"{}()|,./;'\\[\]·~@#￥%……&*——+={}|《》？！“”【】、；‘’，。、]/im;
// &符号不算特殊符号
const specialCharacter2 = /[`~@#$%^*+=<>?"{}|,./;'\\[\]·~@#￥%……*——+={}|《》？“”【】、；‘’，。、]/im;
const codeRule = /^[A-Za-z0-9_]+$/;

export default {
  specialCharacter,
  specialCharacter2,
  alphanumeric,
  character,
  emailPattern,
  charAndSpace,
  alpAndChar,
  accountPattern,
  codeRule,
};
