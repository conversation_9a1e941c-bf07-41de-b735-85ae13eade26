/**
 * 双向链表
 */

/**
 * 节点类型
 */
export class LinkedNode<N> {
  /** 元素对象本身的值 */
  element: N;

  /** 指向前一项 */
  previous?: LinkedNode<N>;

  /** 指向下一项 */
  next?: LinkedNode<N>;

  constructor(element: N) {
    this.element = element;
  }
}

export class DoublyLinkedList<T> {
  /** 头节点 */
  head?: LinkedNode<T>;

  /** 链表的长度 */
  length = 0;

  constructor(element: T) {
    this.head = new LinkedNode(element);
    this.head.next = this.head;
    this.head.previous = this.head;
    this.length = 1;
  }

  /**
   * @description 用于寻找符合条件的节点，回调函数返回为 true 时，返回对应节点
   * @param cb 用于寻找的回调函数
   */
  find(cb: (element?: LinkedNode<T>) => boolean) {
    let i;
    let currentNode: LinkedNode<T> | undefined = this.head;
    for (i = 0; i < this.length; i += 1) {
      if (cb.call(this, currentNode)) break;
      currentNode = currentNode?.next;
    }
    return i !== this.length ? currentNode : null;
  }

  /**
   * @description 在指定节点插入元素
   * @param newElement 插入元素
   * @param node 被插入节点
   */
  insert(newElement: T, node: LinkedNode<T>): LinkedNode<T> | undefined {
    const newNode = new LinkedNode(newElement);
    const previousNode = this.find(element => element === node);
    if (previousNode) {
      const rawNextNode = previousNode.next;
      previousNode.next = newNode;
      newNode.previous = previousNode;
      newNode.next = rawNextNode;
      if (rawNextNode) {
        rawNextNode.previous = newNode;
      }
      this.length += 1;

      return newNode;
    }
  }

  /**
   * 删除指定的节点
   * @param node 要删除的节点
   */
  remove(node: LinkedNode<T>) {
    const deletedNode = this.find(element => element === node);
    if (deletedNode) {
      if (this.head === deletedNode) {
        this.head = deletedNode.next;
      }
      if (deletedNode.previous) {
        deletedNode.previous.next = deletedNode.next;
      }
      if (deletedNode.next) {
        deletedNode.next.previous = deletedNode.previous;
      }
      this.length -= 1;
    }
  }
}
