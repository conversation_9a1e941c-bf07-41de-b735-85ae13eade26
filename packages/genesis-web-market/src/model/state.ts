import { ModulePermission } from '@market/model/common';

export interface UserInfo {
  username: string;
  id: number;
  fullName: string;
  countryCode: string;
  mobile: string;
  email: string;
}

export interface UserState extends Partial<UserInfo> {
  loginName?: string;
  userId: number;
  userRealName?: string;
  countyCode?: string;
  mobile?: string;
  tenant?: string;
  region?: string;
  email?: string;
  offsetTime?: string;
  modulePermissions?: ModulePermission[];
}

export interface ClientConfig {
  security: boolean;
}

export interface MarketState {
  userInfo: UserState;
  clientConfig: ClientConfig;
}
