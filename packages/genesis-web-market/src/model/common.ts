import { ReactNode } from 'react';

import type { ColumnProps } from 'antd/es/table';

export interface AppRoute {
  [key: string]: any;
  path: string;
  pageCode?: string;
  oneOfPermissions?: string[];
  allOfPermissions?: string[];
}

export interface Permission {
  attributes: {
    id: number;
    name: string;
    attributes: Record<string, any>;
  };
  attributesRules: Record<string, any>;
  attributesSchema: Record<string, any>;
  category: string;
  exclusions: any[];
  id: string;
  module: string;
  subModule: string;
}

export interface SubModulePermissions {
  module: string;
  permissions: Permission[];
}

export type ModulePermission =
  | SubModulePermissions
  | {
      module: string;
      subModules: SubModulePermissions[];
    };

export enum SubAppPrefix {
  Product = 'product',
  Template = 'template',
  UW = 'uw',
}

export interface EnumType {
  itemName: string;
  itemExtend1: string;
}

export interface Pagination {
  current: number;
  pageSize: number;
  total: number;
}

export type ColumnEx<T> = ColumnProps<T> & {
  editable?: boolean;
  editChildren?: ReactNode;
};
