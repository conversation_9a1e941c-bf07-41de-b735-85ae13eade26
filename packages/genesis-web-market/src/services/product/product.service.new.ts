import { message } from 'antd';

import ApiClient from 'genesis-web-service/ApiClient';
import { XZaShiftOneObject } from 'genesis-web-service/lib/constants';
import {
  ClaimRelatingMgmtControllerClient,
  ClaimStackServiceMgmtControllerClient,
  FormulaReferenceMgmtControllerClient,
  FormulaSchemaRelationMgmtControllerClient,
  HttpClient,
  InsurableInterestMgmtControllerClient,
  InterestLevelAgreementMgmtControllerClient,
  LiabilityCollocationServiceMgmtControllerClient,
  LiabilityMgmtControllerClient,
  ProductAgreementControllerClient,
  ProductAgreementServiceMgmtControllerClient,
  ProductBenefitIllustrationControllerClient,
  ProductBenefitOptionCodeControllerClient,
  ProductBenefitOptionControllerClient,
  ProductCollocationMgmtControllerClient,
  ProductConfigDynamicServiceMgmtControllerClient,
  ProductConfigMgmtControllerClient,
  ProductConfigServiceMgmtControllerClient,
  ProductConfigTenantMgmtControllerClient,
  ProductFileServiceMgmtControllerClient,
  ProductHistoryMgmtControllerClient,
  ProductLabelMgmtControllerClient,
  ProductLiabilityMgmtControllerClient,
  ProductLibraryProductModelCollectionMgmtControllerClient,
  ProductMgmtControllerClient,
  ProductRateTableMgmtControllerClient,
  ProductSchemaFormulaMgmtControllerClient,
  ProductSchemaMgmtControllerClient,
  ProductStructureServiceMgmtControllerClient,
  RelationPolicyTemplateMgmtControllerClient,
  StackTemplateMgmtControllerClient,
} from 'genesis-web-service/service-types/product-types/package/index';

const baseUrl = '/api/product';

const wafHeaderConfig = XZaShiftOneObject;

// 请求体参数的key如果包含limit、condition这两个单词，就会被waf拦截，所以需要在请求头中添加wafHeaderConfig
const needWafHeaderUrl = [
  `${baseUrl}/product/rateTable/rateTableInfoByPage`,
  `${baseUrl}/product/claimStack/pageLike`,
  `${baseUrl}/product/claimStack/byStackCodes`,
  `${baseUrl}/productConfig`,
  `${baseUrl}/product/relation-policy-template/list-page`,
  `${baseUrl}/productConfig/productInfo/page`,
];

const client = new ApiClient({
  errorToastFn: msg => {
    message.error(msg);
  },
});

client.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    message.error(error.message);
    return Promise.reject(error);
  }
);

client.interceptors.request.use(config => {
  let cloneConfig = { ...config };
  cloneConfig.url = config.url?.replace('mgmt', baseUrl);

  if (needWafHeaderUrl.includes(cloneConfig.url!)) {
    cloneConfig.headers = {
      ...cloneConfig.headers,
      ...wafHeaderConfig,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  return { ...cloneConfig, ...config.options, params: config.queryParams };
});

export const NewProductService = {
  LiabilityCollocationService: new LiabilityCollocationServiceMgmtControllerClient(client as HttpClient),

  ProductConfigService: new ProductConfigServiceMgmtControllerClient(client as HttpClient),

  ProductConfigDynamicService: new ProductConfigDynamicServiceMgmtControllerClient(client as HttpClient),

  ProductAgreementService: new ProductAgreementServiceMgmtControllerClient(client as HttpClient),

  ProductFileService: new ProductFileServiceMgmtControllerClient(client as HttpClient),

  ProductCollocationService: new ProductCollocationMgmtControllerClient(client as HttpClient),

  ClaimStackService: new ClaimStackServiceMgmtControllerClient(client as HttpClient),

  ProductStructureService: new ProductStructureServiceMgmtControllerClient(client as HttpClient),

  ProductLibraryProductModelCollectionService: new ProductLibraryProductModelCollectionMgmtControllerClient(
    client as HttpClient
  ),

  ProductLiability: new ProductLiabilityMgmtControllerClient(client as HttpClient),

  ProductAgreementV3Service: new ProductAgreementControllerClient(client as HttpClient),

  ProductLabelMgmtService: new ProductLabelMgmtControllerClient(client as HttpClient),

  ProductRateTableMgmtService: new ProductRateTableMgmtControllerClient(client as HttpClient),

  ProductMgmtService: new ProductMgmtControllerClient(client as HttpClient),

  FormulaSchemaRelationMgmtService: new FormulaSchemaRelationMgmtControllerClient(client as HttpClient),

  ProductConfigMgmtService: new ProductConfigMgmtControllerClient(client as HttpClient),

  ProductBenefitIllustrationService: new ProductBenefitIllustrationControllerClient(client as HttpClient),

  ProductHistoryMgmtService: new ProductHistoryMgmtControllerClient(client as HttpClient),

  InsurableInterestMgmtService: new InsurableInterestMgmtControllerClient(client as HttpClient),

  ProductConfigTenantMgmtService: new ProductConfigTenantMgmtControllerClient(client as HttpClient),

  InterestLevelAgreementMgmtService: new InterestLevelAgreementMgmtControllerClient(client as HttpClient),

  ClaimRelatingMgmtService: new ClaimRelatingMgmtControllerClient(client as HttpClient),

  ProductSchemaMgmtService: new ProductSchemaMgmtControllerClient(client as HttpClient),

  LiabilityMgmtService: new LiabilityMgmtControllerClient(client as HttpClient),

  ProductSchemaFormulaMgmtService: new ProductSchemaFormulaMgmtControllerClient(client as HttpClient),

  ProductBenefitOptionService: new ProductBenefitOptionControllerClient(client as HttpClient),

  ProductBenefitOptionCodeService: new ProductBenefitOptionCodeControllerClient(client as HttpClient),

  FormulaReferenceMgmtService: new FormulaReferenceMgmtControllerClient(client as HttpClient),

  RelationPolicyTemplateMgmtService: new RelationPolicyTemplateMgmtControllerClient(client as HttpClient),

  StackTemplateService: new StackTemplateMgmtControllerClient(client as HttpClient),
};
