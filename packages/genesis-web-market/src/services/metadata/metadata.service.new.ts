import { message } from 'antd';

import ApiClient from 'genesis-web-service/ApiClient';
import { XZaShiftOneObject } from 'genesis-web-service/lib/constants';
import {
  BizDictGroupMgmtControllerClient,
  BizDictGroupV2MgmtControllerClient,
  BizDictMgmtControllerClient,
  BizDictOfZeusMgmtV2ControllerClient,
  DynamicParamGroupMgmtControllerClient,
  ExchangeRateControllerClient,
  FileShareMgmtControllerClient,
  HttpClient,
  IDataTimeControllerClient,
  IllustrationTriggerControllerClient,
  MaskingRulesMgmtControllerClient,
  NoClaimDiscountMgmtControllerClient,
  PrecisionMgmtControllerClient,
  SchemaDefFieldMatrixMgmtControllerClient,
  SchemaDefMgmtControllerClient,
  SchemaDefMgmtV2ControllerClient,
  SchemaDefUniqueKeyMgmtControllerClient,
  SensitiveDataMgmtControllerClient,
  TenantBizDictConfigShareControllerClient,
  TenantConfigMgmtControllerClient,
  TenantNonBizDictMgmtControllerClient,
  TransportationMgmtControllerClient,
  ValueNameItem,
} from 'genesis-web-service/service-types/metadata-types/package/index';

const baseUrl = '/api/metadata';

const wafHeaderConfig = XZaShiftOneObject;

// 请求体参数的key如果包含limit、condition这两个单词，就会被waf拦截，所以需要在请求头中添加wafHeaderConfig
const needWafHeaderUrl = [`${baseUrl}/tenant/dict/config/queryBizDictPage`];

const client = new ApiClient({
  errorToastFn: msg => {
    message.error(msg);
  },
});

client.interceptors.response.use(
  response => response,
  (error: { message: string }) => {
    message.error(error.message);
    return Promise.reject(error);
  }
);

client.interceptors.request.use(config => {
  const cloneConfig = { ...config };
  cloneConfig.url = config.url?.replace('mgmt', baseUrl);

  if (needWafHeaderUrl.includes(cloneConfig.url!)) {
    cloneConfig.headers = {
      ...cloneConfig.headers,
      ...wafHeaderConfig,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  return { ...cloneConfig, ...config.options, params: config.queryParams };
});

export const NewMetadataService = {
  BizDictMgmtService: new BizDictMgmtControllerClient(client as HttpClient),

  BizDictGroupV2MgmtService: new BizDictGroupV2MgmtControllerClient(client as HttpClient),

  SensitiveDataMgmtService: new SensitiveDataMgmtControllerClient(client as HttpClient),

  BizDictGroupMgmtService: new BizDictGroupMgmtControllerClient(client as HttpClient),

  PrecisionMgmtService: new PrecisionMgmtControllerClient(client as HttpClient),

  MaskingRulesMgmtService: new MaskingRulesMgmtControllerClient(client as HttpClient),

  IllustrationTriggerService: new IllustrationTriggerControllerClient(client as HttpClient),

  ExchangeRateService: new ExchangeRateControllerClient(client as HttpClient),

  SchemaDefMgmtService: new SchemaDefMgmtControllerClient(client as HttpClient),

  SchemaDefMgmtV2Service: new SchemaDefMgmtV2ControllerClient(client as HttpClient),

  DynamicParamGroupMgmtService: new DynamicParamGroupMgmtControllerClient(client as HttpClient),

  IDataTimeService: new IDataTimeControllerClient(client as HttpClient),

  FileShareMgmtService: new FileShareMgmtControllerClient(client as HttpClient),

  SchemaDefUniqueKeyMgmtService: new SchemaDefUniqueKeyMgmtControllerClient(client as HttpClient),

  NoClaimDiscountMgmtService: new NoClaimDiscountMgmtControllerClient(client as HttpClient),

  BizDictOfZeusMgmtV2Service: new BizDictOfZeusMgmtV2ControllerClient(client as HttpClient),

  TransportationMgmtService: new TransportationMgmtControllerClient(client as HttpClient),

  TenantBizDictConfigShareService: new TenantBizDictConfigShareControllerClient(client as HttpClient),

  TenantNonBizDictMgmtService: new TenantNonBizDictMgmtControllerClient(client as HttpClient),

  TenantConfigMgmtService: new TenantConfigMgmtControllerClient(client as HttpClient),

  SchemaDefFieldMatrixMgmtService: new SchemaDefFieldMatrixMgmtControllerClient(client as HttpClient),
  // 自动生成的接口上传方法不可用，需单独定义
  UploadService: {
    SchemaDefFieldMatrixParseExcel: (formData: FormData): Promise<ValueNameItem[]> =>
      client.post(`mgmt/v1/schema/def/field/matrix/parse`, formData, {
        headers: {
          'content-type': 'multipart/form-data',
        },
      }),
  },
};
