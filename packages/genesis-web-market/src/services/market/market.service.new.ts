import { message } from 'antd';

import ApiClient from 'genesis-web-service/ApiClient/ApiClient';
import { XZaShiftOneObject } from 'genesis-web-service/lib/constants';
import {
  AdminFileShareMgmtControllerClient,
  ApplicationElementsPackControllerClient,
  ChannelShareMgmtControllerClient,
  CompanyShareMgmtControllerClient,
  FormulaReferenceMgmtControllerClient,
  GoodSearchMgmtControllerClient,
  GoodsFileDisplayMgmtControllerClient,
  GoodsLabelMgmtControllerClient,
  GoodsMgmtControllerClient,
  GoodsNotificationConfigMgmtControllerClient,
  GoodsPackageMgmtControllerClient,
  GoodsPackageValidateControllerClient,
  GoodsPlanMgmtControllerClient,
  GoodsPremiumCompositionMgmtControllerClient,
  GoodsSalesPartnerMgmtControllerClient,
  GoodsVoucherMgmtControllerClient,
  HttpClient,
  IPackageApplicationElementCustomizedListShareMgmtControllerClient,
  InformQuestionMgmtControllerClient,
  MarketBenefitMgmtControllerClient,
  MarketStructureMgmtControllerClient,
  PackageAgreementConfigMgmtControllerClient,
  PackageAgreementInvestmentMgmtControllerClient,
  PackageAgreementParticipatingMgmtControllerClient,
  PackageApplicationElementShareMgmtControllerClient,
  PackageBasicInfoSaveMgmtControllerClient,
  PackageBenefitMatchControllerClient,
  PackageGoodsStepInfoMgmtControllerClient,
  PackageProductInsuranceMgmtControllerClient,
  PackageProductMgmtControllerClient,
  PackageProductMgmtV2ControllerClient,
  PackageQueryMgmtControllerClient,
  PackageUwQuestionnaireControllerClient,
  PremiumAdjustmentMgmtControllerClient,
  PropertyPackageInformMgmtControllerClient,
  QuoteConfiguredControllerClient,
} from 'genesis-web-service/service-types/market-types/package/index';

const baseUrl = '/api/market';

const wafHeaderConfig = XZaShiftOneObject;

// 请求体参数的key如果包含limit、condition这两个单词，就会被waf拦截，所以需要在请求头中添加wafHeaderConfig
const needWafHeaderUrl = [`${baseUrl}/goodSearch/queryGoods`];

const client = new ApiClient({
  errorToastFn: msg => {
    message.error(msg);
  },
});

client.interceptors.response.use(
  response => response,
  error => {
    message.error(error.message);
    return Promise.reject(error);
  }
);

client.interceptors.request.use(config => {
  const cloneConfig = { ...config };
  cloneConfig.url = config.url?.replace('mgmt', baseUrl);

  if (needWafHeaderUrl.includes(cloneConfig.url!)) {
    cloneConfig.headers = {
      ...cloneConfig.headers,
      ...wafHeaderConfig,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  return { ...cloneConfig, ...config.options, params: config.queryParams };
});

export const NewMarketService = {
  PackageProductInsuranceMgmtService: new PackageProductInsuranceMgmtControllerClient(client as HttpClient),

  GoodsPlanMgmtService: new GoodsPlanMgmtControllerClient(client as HttpClient),

  GoodsSalesPartnerMgmtService: new GoodsSalesPartnerMgmtControllerClient(client as HttpClient),

  PackageUwQuestionnaireService: new PackageUwQuestionnaireControllerClient(client as HttpClient),

  PackageQueryMgmtService: new PackageQueryMgmtControllerClient(client as HttpClient),

  PropertyPackageInformMgmtService: new PropertyPackageInformMgmtControllerClient(client as HttpClient),

  PremiumAdjustmentMgmtService: new PremiumAdjustmentMgmtControllerClient(client as HttpClient),

  MarketBenefitMgmtService: new MarketBenefitMgmtControllerClient(client as HttpClient),

  GoodsFileDisplayMgmtService: new GoodsFileDisplayMgmtControllerClient(client as HttpClient),

  GoodMgmtService: new GoodsMgmtControllerClient(client as HttpClient),

  GoodsVoucherMgmtService: new GoodsVoucherMgmtControllerClient(client as HttpClient),

  GoodsNotificationConfigMgmtService: new GoodsNotificationConfigMgmtControllerClient(client as HttpClient),

  PackageBenefitMatchService: new PackageBenefitMatchControllerClient(client as HttpClient),

  PackageProductMgmtService: new PackageProductMgmtControllerClient(client as HttpClient),

  ApplicationElementsPackService: new ApplicationElementsPackControllerClient(client as HttpClient),

  GoodSearchMgmtService: new GoodSearchMgmtControllerClient(client as HttpClient),

  IPackageApplicationElementCustomizedListShareMgmtService:
    new IPackageApplicationElementCustomizedListShareMgmtControllerClient(client as HttpClient),

  FormulaReferenceMgmtService: new FormulaReferenceMgmtControllerClient(client as HttpClient),

  PackageAgreementInvestmentMgmtService: new PackageAgreementInvestmentMgmtControllerClient(client as HttpClient),

  ChannelShareMgmtService: new ChannelShareMgmtControllerClient(client as HttpClient),

  GoodsPackageValidateService: new GoodsPackageValidateControllerClient(client as HttpClient),

  PackageBasicInfoSaveMgmtService: new PackageBasicInfoSaveMgmtControllerClient(client as HttpClient),

  MarketStructureMgmtService: new MarketStructureMgmtControllerClient(client as HttpClient),

  InformQuestionMgmtService: new InformQuestionMgmtControllerClient(client as HttpClient),

  PackageApplicationElementShareMgmtService: new PackageApplicationElementShareMgmtControllerClient(
    client as HttpClient
  ),

  GoodsLabelMgmtService: new GoodsLabelMgmtControllerClient(client as HttpClient),

  CompanyShareMgmtService: new CompanyShareMgmtControllerClient(client as HttpClient),

  PackageAgreementParticipatingMgmtService: new PackageAgreementParticipatingMgmtControllerClient(client as HttpClient),

  AdminFileShareMgmtService: new AdminFileShareMgmtControllerClient(client as HttpClient),

  PackageAgreementConfigMgmtService: new PackageAgreementConfigMgmtControllerClient(client as HttpClient),

  PackageProductMgmtV2Service: new PackageProductMgmtV2ControllerClient(client as HttpClient),

  PackageGoodsStepInfoMgmtService: new PackageGoodsStepInfoMgmtControllerClient(client as HttpClient),

  QuoteConfiguredControllerService: new QuoteConfiguredControllerClient(client as HttpClient),

  GoodsPremiumCompositionMgmtControllerService: new GoodsPremiumCompositionMgmtControllerClient(client as HttpClient),

  GoodsMgmtControllerService: new GoodsMgmtControllerClient(client as HttpClient),

  GoodsPackageMgmtControllerService: new GoodsPackageMgmtControllerClient(client as HttpClient),
};
