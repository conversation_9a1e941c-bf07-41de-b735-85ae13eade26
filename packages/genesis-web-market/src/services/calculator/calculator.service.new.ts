import { message } from 'antd';

import {
  BonusRateDeclarationControllerClient,
  FormulaControllerClient,
  HttpClient,
  MatrixTableControllerClient,
  MatrixTableV2ControllerClient,
  RateTableControllerClient,
  RateTableReferenceControllerClient,
  SchemaFormulaCalculateControllerClient,
} from 'calculator-types';

import ApiClient from 'genesis-web-service/ApiClient';
import { XZaShiftOneObject } from 'genesis-web-service/lib/constants';

const baseUrl = '/api/calculator';

const wafHeaderConfig = XZaShiftOneObject;

// 请求体参数的key如果包含limit、condition这两个单词，就会被waf拦截，所以需要在请求头中添加wafHeaderConfig
const needWafHeaderUrl = [`${baseUrl}/formula/formulaTableByPage`, `${baseUrl}/v2/query/formulas`];

const client = new ApiClient({
  errorToastFn: msg => {
    message.error(msg);
  },
});

client.interceptors.response.use(
  response => response,
  (error: { message?: string }) => {
    message.error(error.message);
    return Promise.reject(error);
  }
);

client.interceptors.request.use(config => {
  const cloneConfig = { ...config };
  cloneConfig.url = config.url?.replace('mgmt', baseUrl);

  if (needWafHeaderUrl.includes(cloneConfig.url!)) {
    cloneConfig.headers = {
      ...cloneConfig.headers,
      ...wafHeaderConfig,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  return { ...cloneConfig, ...config.options, params: config.queryParams };
});

export const NewCalculatorService = {
  BonusRateDeclarationService: new BonusRateDeclarationControllerClient(client as HttpClient),

  MatrixTableService: new MatrixTableControllerClient(client as HttpClient),

  RateTableReferenceService: new RateTableReferenceControllerClient(client as HttpClient),

  FormulaService: new FormulaControllerClient(client as HttpClient),

  RateTableService: new RateTableControllerClient(client as HttpClient),

  MatrixTableV2Service: new MatrixTableV2ControllerClient(client as HttpClient),

  SchemaFormulaCalculateService: new SchemaFormulaCalculateControllerClient(client as HttpClient),
};
