import { message } from 'antd';

import ApiClient from 'genesis-web-service/ApiClient/ApiClient';
import { XZaShiftOneObject } from 'genesis-web-service/lib/constants';
import { ChannelControllerClient, InstituteControllerClient } from 'genesis-web-service/service-types/channel-types/package';


const baseUrl = '/api/channel';

const wafHeaderConfig = XZaShiftOneObject;

// 请求体参数的key如果包含limit、condition这两个单词，就会被waf拦截，所以需要在请求头中添加wafHeaderConfig
const needWafHeaderUrl: string[] = [];

const client = new ApiClient({
  errorToastFn: msg => {
    message.error(msg);
  },
});

client.interceptors.response.use(
  response => response,
  error => {
    message.error(error.message);
    return Promise.reject(error);
  }
);

client.interceptors.request.use(config => {
  const cloneConfig = { ...config };
  cloneConfig.url = config.url?.replace('mgmt', baseUrl);

  if (needWafHeaderUrl.includes(cloneConfig.url!)) {
    cloneConfig.headers = {
      ...cloneConfig.headers,
      ...wafHeaderConfig,
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  return {
    ...cloneConfig,
    ...config.options,
    /**
     * config.options.queryParams 这边特殊处理一下，后端生成的类型格式不对，get接口拿不到入参
     * 例如：/mgmt/v2/channel/、 /mgmt/v2/institutes/
     */
    params: config.queryParams || config.options.queryParams
  };
});

export const NewChannelService = {
  ChannelService: new ChannelControllerClient(client),

  InstituteService: new InstituteControllerClient(client),
};
