import React from 'react';
import { connect } from 'react-redux';

import { Button, Input, Popconfirm, Tooltip, message } from 'antd';

import { cloneDeep, debounce, isEmpty, isEqual } from 'lodash-es';
import PropTypes from 'prop-types';

import { Icon, Table } from '@zhongan/nagrand-ui';

import { GoodsCategoryItemExtend1 } from '@market/common/enums';
import GeneralSelect from '@market/components/GeneralSelect';
import { I18nLanguageDrawer } from '@market/components/I18nLanguageDrawer';
import I18nInstance, { t } from '@market/i18n';
import { NewMarketService } from '@market/services/market/market.service.new';
import rules from '@market/shared/rules';

import { urlQuery } from '../../util';

const InputGroup = Input.Group;

class EditableCell extends React.Component {
  getInput = () => {
    const {
      record,
      inputType,
      categoryList,
      packageCodesByCategory,
      allPackageCodes,
      dataIndex,
      onChangePlan,
      goodsCategory,
      setI18nLanguageDrawer,
    } = this.props;
    let options = [];
    let valueKey = '';
    let labelKey = '';
    let mode = '';
    if (dataIndex === 'packageCategory') {
      valueKey = 'id';
      labelKey = 'categoryName';
      options =
        goodsCategory && goodsCategory === GoodsCategoryItemExtend1.GroupEmployeeBenefit
          ? categoryList.filter(item => item.id === GoodsCategoryItemExtend1.GroupEmployeeBenefit)
          : categoryList;
    }

    if (dataIndex === 'packageIds') {
      valueKey = 'packageId';
      labelKey = 'packageCode';
      options = !record.packageCategory ? allPackageCodes : packageCodesByCategory;
      if (goodsCategory === GoodsCategoryItemExtend1.ProductBundle) {
        mode = 'multiple';
      }
    }

    if (dataIndex === 'planCode' && record.key !== 'add') {
      /* 已经提交过的 Plan Code不允许编辑 */
      return this.props.children;
    }
    if (dataIndex === 'planCode') {
      return (
        <Input
          placeholder={t('Please input')}
          maxLength={64}
          defaultValue={record[dataIndex]}
          onChange={e => {
            e.persist();
            const value = e.target.value;
            if (value && !rules.codeRule.test(value)) {
              message.warning(
                I18nInstance.t('The plan code could only contain letters, numbers, and underscores (_).', {
                  ns: ['market', 'common'],
                })
              );
              return;
            }
            onChangePlan(e, record, dataIndex, inputType);
          }}
        />
      );
    }
    if (inputType === 'input') {
      return (
        <Input
          placeholder={t('Please input')}
          maxLength={64}
          defaultValue={record[dataIndex]}
          onChange={e => {
            e.persist();
            onChangePlan(e, record, dataIndex, inputType);
          }}
        />
      );
    }
    if (inputType === 'inputPlanName') {
      return (
        <InputGroup compact>
          <Input
            maxLength={64}
            defaultValue={record[dataIndex]}
            onChange={e => {
              e.persist();
              onChangePlan(e, record, dataIndex, 'input');
            }}
            style={{ width: '240px' }}
            placeholder={I18nInstance.t('Please input')}
          />
          <Tooltip placement="top" title={I18nInstance.t('Multilingual')}>
            <Icon
              type="more"
              onClick={() => setI18nLanguageDrawer(true)}
              style={{
                width: '32px',
                height: '32px',
                lineHeight: '32px',
                cursor: 'pointer',
                fontSize: '22px',
                padding: '3px',
              }}
            />
          </Tooltip>
        </InputGroup>
      );
    }
    if (inputType === 'select') {
      let value = record?.[dataIndex];
      if (dataIndex === 'packageIds' && Array.isArray(value) && mode !== 'multiple') {
        value = value[0];
      }
      return (
        <GeneralSelect
          style={{ width: '280px' }}
          defaultValue={value}
          allowClear={false}
          placeholder={t('Please select')}
          onChange={this.onChangePlan}
          option={
            Array.isArray(options)
              ? options.map(item => {
                  const label = dataIndex === 'packageIds' ? `${item[labelKey]}-${item.packageName}` : item[labelKey];
                  return { key: item[valueKey], value: item[valueKey], label };
                })
              : []
          }
          getPopupContainer={() => document.body}
          mode={mode}
          maxTagCount={10}
          maxCount={10}
        />
      );
    }
    return <Input placeholder={t('Please input')} />;
  };

  onChangePlan = e => {
    const { record, dataIndex, inputType } = this.props;
    if (dataIndex === 'packageCategory') {
      delete record.packageIds;
    }
    this.props.onChangePlan(e, record, dataIndex, inputType);
  };

  render() {
    const { editing, dataIndex, title, inputType, record, index, children, ...restProps } = this.props;

    if (dataIndex === 'action-operation' || dataIndex === 'index') {
      return (
        <td {...restProps} className={editing ? `edit-td-highLight ${restProps.className}` : restProps.className}>
          {children}
        </td>
      );
    }

    return (
      <td {...restProps} className={editing ? 'edit-td-highLight' : restProps.className}>
        {editing ? this.getInput() : children}
      </td>
    );
  }
}

class EditableTable extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      tableLoading: false,
      editingKey: '',
      disabledAdd: false,
      currentRecord: undefined,
      i18nLanguageDrawerVisible: false,
      tableDataSource: [],
      columns: [
        {
          editable: true,
          width: 70,
          className: 'order',
          inputType: 'input',
          title: t('number'),
          key: 'index',
          dataIndex: 'index',
        },
        {
          editable: true,
          inputType: 'select',
          width: 280,
          title: t('Package Category'),
          key: 'packageCategory',
          dataIndex: 'packageCategory',
          render: text => {
            if (!text) {
              return t('- -');
            }
            return this.getLabelAndValue(props.enums.productCategory || [], text, 'itemExtend1', 'dictValueName');
          },
        },
        {
          editable: true,
          inputType: 'select',
          width: 280,
          title: t('Package Code'),
          key: 'packageIds',
          className: 'required',
          dataIndex: 'packageIds',
          render: (text, record) => record.planPackages?.map(item => item.packageCode)?.join(' , '),
        },
        {
          editable: true,
          width: 280,
          className: 'required',
          inputType: 'input',
          title: t('Plan Code'),
          key: 'planCode',
          dataIndex: 'planCode',
        },
        {
          editable: true,
          width: 320,
          className: 'required',
          inputType: 'inputPlanName',
          title: t('Plan Name'),
          key: 'goodsPlanName',
          dataIndex: 'goodsPlanName',
          render: (text, record) => {
            return (
              <div>
                <span>{text}</span>
                {!!record.i18nPlanName?.length && (
                  <Tooltip
                    className="inline-block align-middle"
                    title={
                      <div>
                        {record.i18nPlanName
                          ?.filter(({ i18nContent }) => !!i18nContent)
                          ?.map(({ lang, i18nContent }) => (
                            <div key={lang}>
                              {
                                (this.props.enums.language || []).find(enumItem => enumItem.itemExtend1 === lang)
                                  ?.dictValueName
                              }
                              : {i18nContent}
                            </div>
                          ))}
                      </div>
                    }
                  >
                    <Icon type="info-circle" className="ml-2 text-[16px] text-[#9fb3c8]" />
                  </Tooltip>
                )}
              </div>
            );
          },
        },
        {
          editable: true,
          title: t('Actions'),
          align: 'right',
          width: 96,
          fixed: 'right',
          className: 'action',
          dataIndex: 'action-operation',
          render: (text, record) => {
            const editable = this.isEditing(record);
            const { disabled } = this.props;
            return editable ? (
              <span>
                {this.state.currentRecord && !isEqual(record?.packageIds, this.state.currentRecord?.packageIds) ? (
                  <Popconfirm
                    title={t(
                      'Switching the package will clear all definitions under the plan. Please confirm to proceed with the change.'
                    )}
                    onConfirm={debounce(() => this.save(record), 500)}
                    okText={t('yes')}
                    cancelText={t('no')}
                  >
                    <Icon type="check" />
                  </Popconfirm>
                ) : (
                  <Icon type="check" onClick={debounce(() => this.save(record), 500)} />
                )}
                <Icon type="close" style={{ marginLeft: 16 }} onClick={() => this.cancel(record.key)} />
              </span>
            ) : (
              <span>
                {disabled && null}
                {!disabled && (
                  <span>
                    <Icon
                      type="edit"
                      onClick={() => {
                        const { editingKey } = this.state;
                        if (editingKey === '' || editingKey === record.key) {
                          this.edit(record);
                        }
                      }}
                    />
                    <Popconfirm
                      title={t('Are you sure to delete this record?')}
                      onConfirm={() => {
                        this.delete(record);
                      }}
                      placement="topLeft"
                      okText={t('yes')}
                      cancelText={t('no')}
                    >
                      <Icon type="delete" style={{ marginLeft: 16 }} />
                    </Popconfirm>
                  </span>
                )}
              </span>
            );
          },
        },
      ],
    };
  }

  componentDidMount() {
    const { dataSource = [] } = this.props;
    this.setState({ tableDataSource: dataSource });
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.dataSource.length !== this.props.dataSource.length ||
      !isEqual(prevProps.dataSource, this.props.dataSource)
    ) {
      this.setState({ tableDataSource: this.props.dataSource });
    }
  }

  getLabelAndValue = (list, value, valueKey, labelKey) => {
    if (!Array.isArray(list) || list.length === 0 || !value || !valueKey || !labelKey) return '';
    const arr = list.find(item => item[valueKey] === Number(value));
    if (!arr) {
      return '';
    }
    return arr[labelKey];
  };

  isEditing = record => record.key === this.state.editingKey;

  setI18nLanguageDrawer = i18nLanguageDrawerVisible => {
    const { currentRecord } = this.state;
    const { dataSource } = this.props;
    if (i18nLanguageDrawerVisible && !currentRecord) {
      this.setState({
        currentRecord: dataSource.find(item => item.key === 'add'),
      });
    }
    this.setState({ i18nLanguageDrawerVisible });
  };

  saveLanguageInfo = arr => {
    const { currentRecord } = this.state;
    this.props.onChangePlan(arr, currentRecord, 'i18nPlanName', 'select');
  };

  cancel = () => {
    const { goodsId } = this.props;
    const queryModel = urlQuery('queryModel');
    this.setState({
      editingKey: '',
      disabledAdd: false,
      currentRecord: undefined,
    });
    this.props.queryGoodsBasic(goodsId, queryModel);
  };

  edit(record) {
    this.setState({
      disabledAdd: true,
      editingKey: record.key,
      currentRecord: record,
    });
    this.props.queryPackageCodes(record.packageCategory);
  }

  delete = async record => {
    const { goodsPlanId } = record;
    /* 此处只是删除未提交的计划行 */
    if (!goodsPlanId) {
      this.setState({
        disabledAdd: false,
        editingKey: '',
        currentRecord: undefined,
      });
      return this.props.onDelete(record);
    }
    /* 下面是远程删除计划行 */
    const { goodsId } = this.props;
    const queryModel = urlQuery('queryModel');

    this.setState({ tableLoading: true });
    const res = await NewMarketService.GoodsPlanMgmtService.delete({
      goodsId,
      plans: [{ goodsPlanId }],
    });
    this.setState({ tableLoading: false });

    if (res.success) {
      this.props.queryGoodsBasic(goodsId, queryModel);
      this.setState({ disabledAdd: false, editingKey: '' });
    }
  };

  save = async record => {
    let { dataSource, goodsId } = this.props;
    const queryModel = urlQuery('queryModel');
    const { planCode, goodsPlanName, packageIds } = record;

    if (!planCode) {
      return message.error(t('Plan code is required'));
    }
    if (!goodsPlanName) {
      return message.error(t('Plan name is required'));
    }
    if (!packageIds || (Array.isArray(packageIds) && !packageIds.length)) {
      return message.error(t('Package code is required'));
    }

    this.setState({ tableLoading: true, currentRecord: undefined });

    dataSource = cloneDeep(dataSource).map(item => {
      for (const i in item) {
        if (i === 'packageIds') {
          if (!Array.isArray(item[i])) {
            item[i] = [item[i]];
          }
        } else if (i === 'packageId') {
          delete item[i];
        } else if (typeof item[i] === 'object' && i !== 'i18nPlanName') {
          item[i] = item[i].key;
        }
      }
      delete item.showEndTime;
      delete item.showStartTime;
      return item;
    });

    await this.savePlans(dataSource, () => {
      this.setState({ editingKey: '', disabledAdd: false });
    });
  };

  savePlans = async (plans, onSuccess) => {
    let { goodsId } = this.props;
    const queryModel = urlQuery('queryModel');
    const res = await NewMarketService.GoodsPlanMgmtService.save({
      goodsId,
      plans,
    });
    this.setState({ tableLoading: false });
    if (res.success) {
      onSuccess?.();
      this.props.queryGoodsBasic(goodsId, queryModel);
    }
  };

  onAddNewPlan = async () => {
    this.setState({ disabledAdd: true, editingKey: 'add' });
    this.props.onAddNewPlan(result => {
      if (result === false) {
        this.setState({ disabledAdd: false });
      }
    });
  };

  handleSortChange = async list => {
    this.setState({
      tableDataSource: list.map((item, idx) => {
        item.index = '0' + (idx + 1);
        item.orderNo = idx + 1;
        return item;
      }),
    });

    await this.savePlans(list);
  };

  render() {
    const {
      dataSource = [],
      packageCategoryList: categoryList,
      disabled,
      packageCodesByCategory,
      allPackageCodes,
      loading,
      onDelete,
      onChangePlan,
      goodsCategory,
      isViewMode,
    } = this.props;
    let { disabledAdd = false, columns = [], i18nLanguageDrawerVisible, tableDataSource } = this.state;

    const _dataSource = isEmpty(tableDataSource) ? dataSource : tableDataSource;

    _dataSource?.map(record => {
      if (record.packageCategory) {
        if (record.planPackages) {
          record.planPackages[0].packageCategory = record.packageCategory;
        }
        return;
      }

      const first = record.planPackages?.[0]?.packageCategory;

      if (record.planPackages?.every(item => item.packageCategory === first)) {
        record.packageCategory = first;
      }
    });

    const components = {
      body: {
        cell: EditableCell,
      },
    };

    columns = columns.map(col => {
      if (!col.editable) {
        return col;
      }
      return {
        ...col,
        onCell: record => ({
          record,
          goodsCategory,
          onChangePlan,
          onDelete,
          categoryList,
          packageCodesByCategory,
          allPackageCodes,
          inputType: col.inputType,
          dataIndex: col.dataIndex,
          title: col.title,
          i18nLanguageDrawerVisible,
          setI18nLanguageDrawer: this.setI18nLanguageDrawer,
          editing: this.isEditing(record),
        }),
      };
    });
    return (
      <div className="coverage-plan-table">
        <Button
          disabled={disabledAdd || disabled}
          onClick={this.onAddNewPlan}
          className="long-add-btn btn-group-above-table"
        >
          <Icon type="add" /> {t('Add')}
        </Button>
        <Table
          draggable={!isViewMode}
          loading={loading}
          components={components}
          dataSource={_dataSource}
          columns={columns}
          rowKey="planCode"
          rowClassName="editable-row"
          pagination={false}
          scroll={{ x: 'max-content' }}
          setDataSource={this.handleSortChange}
        />
        {i18nLanguageDrawerVisible ? (
          <I18nLanguageDrawer
            options={(this.props.enums.language || []).map(enumItem => ({
              value: enumItem.itemExtend1,
              label: enumItem.dictValueName,
            }))}
            visible={i18nLanguageDrawerVisible}
            setVisible={this.setI18nLanguageDrawer}
            saveLanguageInfo={this.saveLanguageInfo}
            initData={_dataSource.find(item => item.key === this.state.editingKey).i18nPlanName || []}
          />
        ) : null}
      </div>
    );
  }
}

const EditableFormTable = EditableTable;

EditableFormTable.propTypes = {
  onAddNewPlan: PropTypes.func.isRequired,
  dataSource: PropTypes.array.isRequired,
  loading: PropTypes.bool.isRequired,
  disabled: PropTypes.bool.isRequired,
};

export default connect(state => {
  return {
    enums: state.enums,
  };
})(EditableFormTable);
