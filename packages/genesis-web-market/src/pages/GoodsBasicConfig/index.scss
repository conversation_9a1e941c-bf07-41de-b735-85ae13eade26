.goods-basic-config {
  height: 100%;
  .market-content .basic-config .market-ant4-radio-group {
    width: 240px;
  }

  .add-new-btn {
    cursor: pointer;
    height: 32px;
    line-height: 32px;
    text-align: center;
    width: 100%;
    font-size: 14px;
    display: block;
    border-radius: 4px;
    border: 1px dashed var(--border-default);
    color: var(--text-color);

    .plus-icon {
      margin-right: 8px;
      font-size: 20px;
    }
  }

  .add-new-btn[disabled] {
    cursor: not-allowed;
  }

  .base-section {
    .plan-table {
      .action {
        min-width: 100px;
        cursor: pointer;
      }

      .anticon-edit:hover,
      .anticon-delete:hover,
      .anticon-check:hover,
      .anticon-close:hover {
        color: var(--primary-color);
      }

      .disabled-wrap {
        color: var(--disabled-bg);
        cursor: not-allowed;
        opacity: 1;
      }

      .disabled-wrap .anticon {
        color: var(--disabled-bg);
      }
    }

    button[disabled] span {
      color: var(--text-color-tertiary);
    }
  }

  .edit-td-highLight {
    background-color: var(--disabled-bg);
  }
}
