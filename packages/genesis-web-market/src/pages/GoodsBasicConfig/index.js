import { Component, createRef } from 'react';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';

import { Button, Col, Form, Input, Layout, Radio, Row, Skeleton, message } from 'antd';

import { cloneDeep, isEmpty, isNil } from 'lodash-es';

import { MoreConfigurationContainer } from 'genesis-web-component/lib/components/MoreConfigurationContainer';
import { MetadataService } from 'genesis-web-service/lib/metadata/metadata.service';

import { DetailPageMode } from '@market/common/interface';
import { CardWithTitle } from '@market/components/CardWithTitle';
import GeneralSelect from '@market/components/GeneralSelect';
import PremiumComposition from '@market/pages/GoodsBasicConfig/PremiumComposition';
import { selectPermissionCheckMap } from '@market/redux/selector';
import { NewMarketService } from '@market/services/market/market.service.new';
import { mapBizDictToOptions } from '@market/utils/enum';

import { GoodsCategoryItemExtend1 } from '../../common/enums';
import { FMarketHeader } from '../../components/F-Market-Header';
import FMarketMenu from '../../components/F-Market-Menu';
import { urlQuery } from '../../util';
import EditComponent from './EditComponent';
import { CoveragePlanBundle } from './components/CoveragePlanBundle';
import { handleTechProductId } from './components/CoveragePlanBundle/Card/handle';
import GoodsLabelConfiguration from './components/GoodsLabelConfiguration';
import { TechProduct } from './components/TechProduct';
import './index.scss';
import { coveragePlanId, handleCoveragePlanBundle, handleCoveragePlanBundleFieldsErr } from './utils';
import { GoodsCodeValidator } from './validateConfig';

const DEFAULT_CHANNEL_CAMPAIGN_ID = '1';

const { Sider, Content } = Layout;

class GoodsBasicConfig extends Component {
  constructor(props) {
    super(props);
    this.state = {
      goodsId: '',
      goodsCategory: undefined,
      mode: 'add',
      loading: false,
      skeletonVisible: true,
      dataSource: [],
      isShowEditButton: undefined,
      // 根据 packageCategory 查询出来对应的 packageCodes
      packageCodesByCategory: [],
      // packageCodes 全集, 无关 packageCategory, 但是与 Tech product 有联动关系
      allPackageCodes: [],
      // 页面初始化时, 需要查询出 packageCodes 全集, 无关 packageCategory 和 Tech product
      initAllPackageCodes: [],
      packageCategoryList: [],
      techProducts: [],
      goodsDetail: {},
      productCategoryList: [],
    };
    this.formRef = createRef();
    this.premiumCompositionRef = createRef();
    this.coveragePlanBundleRef = createRef();
  }

  async componentDidMount() {
    const goodsId = urlQuery('goodsId');
    const { state } = this.props.location;
    const layoutId = urlQuery('layoutId');
    let queryModel = 1;

    layoutId && this.setState({ layoutId });
    goodsId && this.setState({ goodsId });

    if (state) {
      if (state.queryModel) {
        queryModel = state.queryModel;
        this.setState({ queryModel: Number(state.queryModel) });
      }
      if (state.mode) {
        this.setState({ mode: state.mode });
      }
    }

    /* 组合产品类型 */
    await this.queryCategory();
    await this.fetchPackageCodes();

    /* 等待枚举加载好，基本信息下拉框依赖两个枚举显示Label */
    if (goodsId) {
      /* 处理编辑状态的逻辑 */
      // 查询模式 1-编辑 2-复制 3-新增版本 默认为1
      this.queryGoodsBasic(goodsId, queryModel);
      this.queryTechProducts(goodsId);
    } else {
      this.setState({ skeletonVisible: false });
    }
  }

  queryGoodsBasic = async (goodsId, queryModel, from = '') => {
    const { hasEditAuth, isSuperUser, userInfo } = this.props;
    /* 查询商品的基本信息 */
    this.setState({ loading: true });
    const res = await NewMarketService.GoodMgmtService.find({
      goodsId,
      queryModel,
    });
    this.setState({ loading: false });

    /* 查询模式，影响基本信息的编辑和显示 */
    if (res && res.value) {
      if (Object.keys(res.value.basicProp).length === 0) return;
      // 新增字段待待添加
      const {
        code,
        goodsName,
        goodsCategory,
        goodsDesc,
        orgCode,
        version,
        salesType,
        goodsType,
        creator,
        isMasterPolicy,
        usageBased,
        ncdConfiguredCode,
        scope,
      } = res.value.basicProp;
      if (scope === 'LIBRARY') {
        message.warning('This Goods is read only.');
        this.setState({
          mode: 'view',
        });
      }

      this.setState({
        isShowEditButton: scope !== 'LIBRARY' && ((creator === `${userInfo.userId}` && hasEditAuth) || isSuperUser),
      });
      let { plans = [] } = res.value;

      plans = plans.map((item, index) => {
        item.key = item.goodsPlanId;
        index += 1;
        if (index < 10) {
          index = `0${index}`;
        }
        item.index = index;
        return item;
      });
      this.setState({
        goodsFormInitialValues: {
          code,
          goodsName,
          goodsCategory,
          goodsDesc,
          orgCode,
          version,
          salesType,
          goodsType,
          creator,
          isMasterPolicy,
          usageBased,
          ncdConfiguredCode,
        },
      });
      if (from !== 'add') {
        this.setState({ dataSource: plans });
      }
    }
    this.setState({
      skeletonVisible: false,
    });
  };

  fetchPackageCodes = async () => {
    const res = await NewMarketService.PackageQueryMgmtService.query$POST$mgmt_package_queryByCondition({
      packageStatus: 3,
    });
    if (res.success && res.value.packageList) {
      const list = res.value.packageList;
      this.setState({ initAllPackageCodes: list, allPackageCodes: list });
    }
  };

  /**
   * 初始化时拿到已有的 tech product 数据, 处理 plan 区块内 packageCategory 和 packageIds 两个字段的数据源
   */
  async queryTechProducts(goodsId) {
    const techProductRes = await NewMarketService.GoodsPackageMgmtControllerService.query(goodsId);
    this.onTechProductBlur(techProductRes);
  }

  /**
   * TechProduct 组件内用, Cascader失焦时触发
   */
  onTechProductBlur = selectedTechProducts => {
    const { productCategoryList, packageCodesByCategory, initAllPackageCodes } = this.state;
    const isSetTechProduct = !!selectedTechProducts?.length;

    let _packageCategoryList = productCategoryList;
    let _allPackageCodes = initAllPackageCodes;

    if (isSetTechProduct) {
      const categoriesInTechProducts = selectedTechProducts.map(item => item.packageCategory);
      const packageCategoryList = productCategoryList.filter(item => categoriesInTechProducts.includes(item.id));

      const packageIdsInTechProducts = selectedTechProducts.map(item => item.packageId);
      const _packageCodes = initAllPackageCodes.filter(item => packageIdsInTechProducts.includes(item.packageId));

      _packageCategoryList = packageCategoryList;
      _allPackageCodes = _packageCodes;
    }

    this.setState({
      packageCategoryList: _packageCategoryList,
      allPackageCodes: _allPackageCodes,
      techProducts: selectedTechProducts,
    });
  };

  /**
   * TechProduct 组件内用, 点击Cascader时触发
   */
  onTechProductClick = async () => {
    const { goodsId } = this.state;
    if (goodsId) return goodsId;
    // 如果没有goodsId，则需要通过保存基本信息之后获得
    const res = await this.onSaveBasic();
    if (res.success) {
      return res.value.goodsId;
    }
  };

  async queryCategory() {
    const { goodsCategory, productCategory } = this.props;
    const ncdEnums = await MetadataService.fetchNcdManagement();

    const goodsCategoryList = goodsCategory?.map(item => ({
      id: item.itemExtend1,
      categoryName: item.itemName ?? [],
    }));
    const productCategoryList = productCategory?.map(item => ({
      id: item.itemExtend1,
      categoryName: item.itemName ?? [],
    }));

    this.setState({
      goodsCategoryList,
      productCategoryList,
      packageCategoryList: productCategoryList,
      ncdEnums,
    });
  }

  onSaveBasic = async () => {
    /**
     * @desc: 点击表格顶部Add, 保存基本信息，成功之后打开抽屉操作产品和责任添加
     */
    const { getFieldsValue } = this.formRef.current;
    const { mode, goodsId } = this.state;

    const values = getFieldsValue(); // 子组件组合的基本信息
    if (!values) return;
    /* 编辑时候，传入goodsId */
    if (goodsId) {
      values.goodsId = goodsId;
    }
    delete values.companyCode;

    const data = {
      basicProp: values,
    };
    const urlId = urlQuery('goodsId');
    /* 新增 */
    if (mode === 'add' && !urlId) {
      /* 新增 */
      const response = await NewMarketService.GoodMgmtService.add(data);
      if (response.success) {
        const { layoutId } = response.value;
        /* 新增成功后，mode更新为edit，提供给next按钮update */
        this.setState({
          layoutId,
          goodsId: response.value.goodsId,
          mode: 'edit',
          goodsDetail: values,
        });

        const param = {
          goodsId: response.value.goodsId,
          layoutId,
        };

        this.props.navigate(`?${new URLSearchParams(param).toString()}`, {
          state: {
            mode: 'edit',
            queryModel: this.state.queryModel,
          },
        });
      }
      return response;
    }

    /* 编辑或者复制 */
    const res = await NewMarketService.GoodMgmtService.update(data);
    if (mode === 'copy') {
      // 如果初始copy状态,把状态置为edit
      this.setState({ mode: 'edit' });
    }
    return res;
  };

  onAddNewPlan = async cb => {
    const { goodsId, techProducts } = this.state;
    if (!goodsId) {
      // 如果没有goodsId，则需要通过保存基本信息之后获得
      const res = await this.onSaveBasic();

      if (!res || !res.success) {
        /* 当基本信息没有填写，新增计划不成功，重置掉add btn */
        const result = false;
        return cb(result);
      }
      this.queryGoodsBasic(res.value.goodsId, this.state.queryModel, 'add');
      this.setState({ goodsId: res.value.goodsId });
    }

    let { dataSource = [] } = this.state;
    let newData = {
      key: 'add',
      number: dataSource.length + 1,
      orderNo: dataSource.length + 1,
      planCode: '',
      goodsPlanName: '',
      packageId: '',
      channelCampaignId: DEFAULT_CHANNEL_CAMPAIGN_ID,
    };
    if (Array.isArray(techProducts) && techProducts.length === 1) {
      const { packageId, packageCategory } = techProducts?.[0] || {};
      this.queryPackageCodes(packageCategory);
      newData = {
        ...newData,
        packageIds: [packageId],
        packageCategory,
      };
    }

    dataSource = dataSource.concat([newData]);
    this.setState({ dataSource });
  };

  onGoodsLabelConfiguration = async cb => {
    const { goodsId } = this.state;
    if (!goodsId) {
      // 如果没有goodsId，则需要通过保存基本信息之后获得
      const res = await this.onSaveBasic();

      if (!res || !res.success) {
        /* 当基本信息没有填写，新增计划不成功，重置掉add btn */
        const result = false;
        return cb(result);
      }
      this.queryGoodsBasic(res.value.goodsId, this.state.queryModel, 'add');
      this.setState({ goodsId: res.value.goodsId });
    }
    return cb(true);
  };

  gotoNextPage = async () => {
    const { goodsId, layoutId, queryModel, mode } = this.state;

    const isProhibitNextStep = await this.checkIsBothEmpty(goodsId);
    if (isProhibitNextStep) return;

    this.props.navigate(`/market/goods/salesAttributes?goodsId=${goodsId}&layoutId=${layoutId}`, {
      state: {
        mode: mode === 'add' || mode === 'copy' ? 'edit' : mode,
        queryModel,
      },
    });
  };

  renderEditSaveBtn() {
    const { isShowEditButton, mode } = this.state;
    if (!isShowEditButton || this.props.envConfig.env === 'prd') {
      return null;
    }

    if (mode === 'view') {
      return (
        <Button size="large" onClick={() => this.onEdit()}>
          {this.props.t('Edit')}
        </Button>
      );
    }

    return (
      <Button size="large" onClick={() => this.onNextToSales(true)}>
        {this.props.t('Save')}
      </Button>
    );
  }

  /**
   * Next/Save Button
   */
  onNextToSales = async isSave => {
    const { goodsId, layoutId, queryModel, mode, isShowEditButton, allPackageCodes } = this.state;
    /* 点击下一步需要对基本信息进行保存 */
    let readOnly = mode === 'view' || !isShowEditButton || this.props.envConfig.env === 'prd';

    if (isShowEditButton === undefined && mode !== 'view') {
      // 第一次新增的时候没有锁状态,第一次保存失败之后mode会改成edit
      readOnly = false;
    }
    if (readOnly) {
      /* 跳转至销售属性配置页面 */
      return this.props.navigate(`/market/goods/salesAttributes?goodsId=${goodsId}&layoutId=${layoutId}`, {
        state: {
          mode,
          queryModel,
        },
      });
    }

    // 校验 bundle coverage plan 数据是否合法
    const bundleFormValues = await this.coveragePlanBundleRef?.current?.submit();
    const originBundleFormVal = cloneDeep(bundleFormValues?.items);

    const convertBundleFormVal = originBundleFormVal?.map(item => {
      const techProductPlans = cloneDeep(item.techProductPlans)?.map(plan => {
        const { techProductId } = plan;

        let _techProductId = undefined;
        if (!!techProductId) {
          _techProductId = handleTechProductId(techProductId);
        }

        return {
          ...plan,
          techProductId: _techProductId,
        };
      });

      return { ...item, techProductPlans };
    });

    if (!isEmpty(bundleFormValues?.items)) {
      // 检测是否有没有填写的字段, 有则标红显示
      const isBundleDataInvalid = handleCoveragePlanBundleFieldsErr(convertBundleFormVal);
      if (isBundleDataInvalid) return;
    }

    const res = await this.onSaveBasic();
    if (!res || !res.success) return;

    const _goodsId = goodsId || res.value.goodsId;

    // 提交 bundle coverage plan 数据
    // 注意: 这里不能用 isEmpty, 否则下列场景更新有问题: 之前有数据, 这次全部删除, 就会变成空数组, 如果用 isEmpty 就不会走更新逻辑
    if (!isNil(bundleFormValues?.items)) {
      await handleCoveragePlanBundle({
        formValues: convertBundleFormVal,
        goodsId: _goodsId,
        allPackageCodes,
      });
    }

    const isNeedSetCoveragePlan = await this._checkIsNeedSetCoveragePlan(_goodsId);
    if (isNeedSetCoveragePlan) return;

    if (res.success) {
      const premiumSubmitRes = await this.premiumCompositionRef.current.onPremiumCompositionSubmit();
      if (premiumSubmitRes === 'fail') {
        return;
      }

      message.success(this.props.t('Save successfully'));
      if (!isSave) {
        this.props.navigate(`/market/goods/salesAttributes?goodsId=${_goodsId}&layoutId=${layoutId}`, {
          state: {
            mode: mode === 'add' || mode === 'copy' ? 'edit' : mode,
            queryModel,
          },
        });
      }
    }
  };

  /**
   * 校验是否需要配置 Coverage Plan
   */
  _checkIsNeedSetCoveragePlan = async goodsId => {
    const techProductRes = await NewMarketService.GoodsPackageMgmtControllerService.query(goodsId);
    const isHaveTechProduct = techProductRes?.length;
    const requiredMsg = this.props.t('Please select a package or add a coverage plan.');
    const goodsInfoRes = await NewMarketService.GoodMgmtService.find({
      goodsId,
      queryModel: this.state.queryModel,
    });

    const isResException = !goodsInfoRes || !goodsInfoRes.success || !goodsInfoRes.value;
    if (isHaveTechProduct && isResException) {
      message.error(requiredMsg);
      return true;
    }

    let dataSource = [];
    if (goodsInfoRes.value.plans) {
      dataSource = goodsInfoRes.value.plans;
    }

    const isHavePlans = !!dataSource.length;

    // 如果用户配置了 Tech Product, 则 coverage Plan 可以不用配置.
    // 但是对于历史场景, 如果配置的有 coverage Plan 数据, 则需要检测 plan 内 package 字段值是否跟已有的 Tech Product 数据源匹配, 不匹配需要报错且不允许跳下一步
    if (isHaveTechProduct) {
      if (!isHavePlans) return false;

      const packageIdsInTechProducts = techProductRes.map(item => item.packageId);
      const isMatch = dataSource.every(item => item.packageIds.every(id => packageIdsInTechProducts.includes(id)));
      if (!isMatch) {
        message.error(
          this.props.t('The packages associated with the coverage plan must be within the selected packages.')
        );
        document
          .querySelector(`#${coveragePlanId}`)
          .scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
        return true;
      }
    }

    // 用户没有配置 Tech Product, 但是配置的有 Coverage Plan, 可以跳下一步
    if (isHavePlans) return false;

    // 用户没有配置 Tech Product, 也没有配任何的 Coverage Plan, 不允许跳下一步
    message.error(requiredMsg);
    return true;
  };

  /**
   * 校验 Coverage Plan 和 Tech Product 是否都为空
   */
  checkIsBothEmpty = async goodsId => {
    const techProductRes = await NewMarketService.GoodsPackageMgmtControllerService.query(goodsId);
    const isHaveTechProduct = techProductRes?.length;
    const requiredMsg = this.props.t('Please select a package or add a plan.');
    const goodsInfoRes = await NewMarketService.GoodMgmtService.find({
      goodsId,
      queryModel: this.state.queryModel,
    });

    const isResException = !goodsInfoRes || !goodsInfoRes.success || !goodsInfoRes.value;
    if (isHaveTechProduct && isResException) {
      message.error(requiredMsg);
      return true;
    }

    const isCoveragePlansEmpty = !goodsInfoRes.value.plans.length;

    if (isCoveragePlansEmpty && !isHaveTechProduct) {
      message.error(requiredMsg);
      return true;
    }
  };

  /**
   * @param packageCategory optional
   * 场景一. Tech Product 没有配置任何数据:
   *  如果 packageCategory 不存在, 接口返回 packageCodes 全集
   *  如果存在, 则返回对应 packageCategory 下的 packageCodes 数据
   *
   * 场景二. Tech Product 有数据:
   *  如果 packageCategory 不存在, 接口返回 Tech Product 数据内的 packageCodes 全集
   *  如果存在, 则返回 Tech Product 数据内对应 packageCategory 下的 packageCodes 数据
   */
  queryPackageCodes = async packageCategory => {
    const { techProducts, initAllPackageCodes } = this.state;

    // 场景一. Tech Product 没有配置任何数据:
    if (!techProducts.length) {
      const data = { packageCategory, packageStatus: 3 };
      const res = await NewMarketService.PackageQueryMgmtService.query$POST$mgmt_package_queryByCondition(data);
      if (res.success && res.value.packageList) {
        const list = res.value.packageList;

        this.setState({ [packageCategory ? 'packageCodesByCategory' : 'allPackageCodes']: list });
      }
      return;
    }

    // 场景二. Tech Product 有数据:
    const _handleFn = list => {
      const _packageIds = list.map(item => item.packageId);
      return initAllPackageCodes
        .filter(item => _packageIds.includes(item.packageId))
        .map(({ packageCode, packageId, packageName }) => ({
          packageCode,
          packageId,
          packageName,
          packageStatus: 3,
        }));
    };

    const filterTargetCategories = techProducts.filter(item => item.packageCategory === packageCategory);
    const codeListFilterByCategory = _handleFn(filterTargetCategories);
    const fullCodeList = _handleFn(techProducts);
    const list = packageCategory ? codeListFilterByCategory : fullCodeList;

    this.setState({ [packageCategory ? 'packageCodesByCategory' : 'allPackageCodes']: list });
  };

  onChangePlan = (e, record, dataIndex, inputType) => {
    /* packageCode的下拉框，依赖packageCategory和基本信息的insuranceCompany选择 */
    if (dataIndex === 'packageCategory') {
      this.queryPackageCodes(e);
    }
    record[dataIndex] = inputType === 'input' ? e.target.value : e;
  };

  onDelete = record => {
    const { dataSource = [] } = this.state;
    const arr = dataSource.filter(item => !(item.key === record.key));
    this.setState({ dataSource: arr });
  };

  onEdit = () => {
    if (this.state.mode !== 'view') {
      return;
    }
    this.setState({ mode: 'edit' });
  };

  render() {
    const {
      dataSource = [],
      goodsCategoryList,
      goodsId,
      packageCodesByCategory,
      allPackageCodes,
      loading,
      layoutId,
      mode,
      goodsCategory,
      isShowEditButton,
      goodsFormInitialValues,
      packageCategoryList,
      productCategoryList,
      initAllPackageCodes,
    } = this.state;
    let readOnly = mode === 'view' || (!isShowEditButton && mode !== 'add');

    if (isShowEditButton === undefined && mode !== 'view') {
      readOnly = false;
    }

    const { salesType, goodsType, goodsDesc } = goodsFormInitialValues ?? {};
    const moreConfigurationExpand = !!salesType || !!goodsType || !!goodsDesc;
    const goodsCategoryOptions = mapBizDictToOptions(goodsCategoryList, {
      optionName: 'categoryName',
      optionValue: 'id',
    });
    const productCategoryOptions = mapBizDictToOptions(productCategoryList, {
      optionName: 'categoryName',
      optionValue: 'id',
    });

    return (
      <Layout className="goods-basic-config market-layout">
        <FMarketHeader backPath="/market/goods/search" subMenu="Marketing_Goods" />
        <div className="flex" style={{ height: 'calc(100% - 50px)' }}>
          <Sider width={208} className="market-sider">
            <FMarketMenu
              page="GOODS"
              type={mode}
              queryModel={this.state.queryModel}
              layoutId={layoutId}
              goodsId={goodsId}
              defaultSelectedKeys={['1']}
              condition3={this.state.condition3}
              condition4={this.state.condition4}
              category={2}
              navigate={this.props.navigate}
              goodsDetail={this.state.goodsDetail}
              checkIsBothProductEmptyOnGoodsPage={this.checkIsBothEmpty}
            />
          </Sider>
          <Content className="market-content !bg-layoutBg">
            <Skeleton active loading={this.state.skeletonVisible}>
              <Form layout="vertical" ref={this.formRef} disabled={mode === 'view'}>
                <div className="right-content">
                  <CardWithTitle title={this.props.t('Basic Configuration')} className="mt-2">
                    <div className="grid grid-cols-3">
                      <Form.Item
                        label={this.props.t('Goods Code')}
                        name="code"
                        initialValue={this.state.goodsFormInitialValues?.code}
                        rules={GoodsCodeValidator}
                      >
                        <Input
                          placeholder={this.props.t('Please input')}
                          disabled={!!this.state.goodsFormInitialValues?.code}
                        />
                      </Form.Item>
                      {/* UI hidden, only collect value */}
                      <Form.Item
                        name="version"
                        initialValue={this.state.goodsFormInitialValues?.version || 'V01'}
                        hidden
                      >
                        <Input />
                      </Form.Item>
                      <Form.Item
                        label={this.props.t('insurance_goods_name')}
                        name="goodsName"
                        initialValue={this.state.goodsFormInitialValues?.goodsName}
                        rules={[
                          {
                            required: true,
                            message: this.props.t('Please input'),
                          },
                        ]}
                      >
                        <Input placeholder={this.props.t('Please input')} />
                      </Form.Item>
                      <Form.Item
                        label={this.props.t('Goods Category')}
                        name="goodsCategory"
                        rules={[
                          {
                            required: true,
                            message: this.props.t('Please select'),
                          },
                        ]}
                        initialValue={this.state.goodsFormInitialValues?.goodsCategory}
                      >
                        <GeneralSelect
                          options={goodsCategoryOptions}
                          onChange={() =>
                            this.setState({
                              dataSource: [],
                            })
                          }
                          disabled={mode !== DetailPageMode.add}
                        />
                      </Form.Item>
                      <Form.Item
                        label={this.props.t('Usage-based Goods')}
                        name="usageBased"
                        initialValue={this.state.goodsFormInitialValues?.usageBased ?? '2'}
                      >
                        <Radio.Group
                          optionValue="dictValue"
                          optionLabel="dictValueName"
                          options={mapBizDictToOptions(this.props.enums.yesNo)}
                        />
                      </Form.Item>
                      <Form.Item
                        label={this.props.t('Allow Master Policy Issuance')}
                        name="isMasterPolicy"
                        initialValue={this.state.goodsFormInitialValues?.isMasterPolicy ?? '2'}
                      >
                        <Radio.Group
                          optionValue="dictValue"
                          optionLabel="dictValueName"
                          options={mapBizDictToOptions(this.props.enums.yesNo)}
                        />
                      </Form.Item>
                      <Form.Item shouldUpdate={(pre, curr) => pre.goodsCategory !== curr.goodsCategory}>
                        {({ getFieldValue }) =>
                          getFieldValue('goodsCategory') === 22 ? (
                            <Form.Item
                              label={this.props.t('NCD')}
                              name="ncdConfiguredCode"
                              initialValue={
                                this.state.goodsFormInitialValues?.ncdConfiguredCode ??
                                mapBizDictToOptions(this.state.ncdEnums, {
                                  optionValue: 'groupCode',
                                  optionName: 'groupCode',
                                })?.[0]?.groupCode ??
                                'NCD001'
                              }
                            >
                              <GeneralSelect
                                option={mapBizDictToOptions(this.state.ncdEnums, {
                                  optionValue: 'groupCode',
                                  optionName: 'groupCode',
                                })}
                              />
                            </Form.Item>
                          ) : null
                        }
                      </Form.Item>
                      <Form.Item label={this.props.t('Package')} className="col-span-2 mr-4">
                        <TechProduct
                          readOnly={readOnly}
                          goodsId={goodsId ? +goodsId : undefined}
                          onTechProductClick={this.onTechProductClick}
                          onTechProductBlur={this.onTechProductBlur}
                          options={initAllPackageCodes}
                        />
                      </Form.Item>
                    </div>

                    <MoreConfigurationContainer visible={moreConfigurationExpand}>
                      <Row>
                        <Col span={8}>
                          <Form.Item label={this.props.t('salesType')} name="salesType" initialValue={salesType}>
                            <GeneralSelect options={mapBizDictToOptions(this.props.salesTypeEnums)} allowClear />
                          </Form.Item>
                        </Col>
                        <Col span={8}>
                          <Form.Item label={this.props.t('Goods Type')} name="goodsType" initialValue={goodsType}>
                            <GeneralSelect
                              options={mapBizDictToOptions(this.props.goodsTypeEnums)}
                              optionValue="dictValue"
                              optionLabel="dictValueName"
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Form.Item
                        label={this.props.t('Goods Introduction')}
                        name="goodsDesc"
                        initialValue={goodsDesc}
                        className="col-span-2"
                      >
                        <Input.TextArea className="!w-[560px]" placeholder={this.props.t('Please input')} />
                      </Form.Item>
                    </MoreConfigurationContainer>
                  </CardWithTitle>
                </div>
                <GoodsLabelConfiguration
                  onGoodsLabelConfiguration={this.onGoodsLabelConfiguration}
                  disabled={readOnly}
                  goodsId={goodsId}
                />
                <Form.Item shouldUpdate style={{ marginBottom: 0 }}>
                  {({ getFieldValue }) => {
                    const goodsCategory = getFieldValue('goodsCategory');
                    const isBundle = goodsCategory === GoodsCategoryItemExtend1.ProductBundle;

                    if (isBundle) {
                      return (
                        <CardWithTitle title={this.props.t('Plan')} id={coveragePlanId} titleClassName="!mb-4">
                          <CoveragePlanBundle
                            goodsId={goodsId}
                            readOnly={readOnly}
                            ref={this.coveragePlanBundleRef}
                            allPackageCodes={allPackageCodes?.map(item => ({
                              label: `${item.packageCode}-${item.packageName}`,
                              value: item.packageId,
                            }))}
                          />
                        </CardWithTitle>
                      );
                    }

                    return (
                      <CardWithTitle title={this.props.t('Plan')} id={coveragePlanId}>
                        <EditComponent
                          disabled={readOnly}
                          loading={loading}
                          isViewMode={mode === DetailPageMode.view}
                          queryGoodsBasic={this.queryGoodsBasic}
                          goodsId={goodsId}
                          onAddNewPlan={this.onAddNewPlan}
                          onChangePlan={this.onChangePlan}
                          onDelete={this.onDelete}
                          queryPackageCodes={this.queryPackageCodes}
                          packageCodesByCategory={packageCodesByCategory}
                          allPackageCodes={allPackageCodes}
                          packageCategoryList={packageCategoryList}
                          dataSource={dataSource
                            .sort((a, b) => a.orderNo - b.orderNo)
                            .map((item, idx) => {
                              item.index = `0${idx + 1}`;
                              return item;
                            })}
                          goodsCategory={goodsCategory}
                        />
                      </CardWithTitle>
                    );
                  }}
                </Form.Item>

                <PremiumComposition disabled={readOnly} goodsId={goodsId} ref={this.premiumCompositionRef} />
              </Form>
            </Skeleton>
          </Content>

          <div className="bottom-action-bar">
            {readOnly && (
              <Button size="large" onClick={this.gotoNextPage} type="primary">
                {this.props.t('Next')}
              </Button>
            )}
            {this.renderEditSaveBtn()}
            {!readOnly && (
              <Button size="large" onClick={() => this.onNextToSales()} type="default">
                {this.props.t('Next')}
              </Button>
            )}
          </div>
        </div>
      </Layout>
    );
  }
}

const app = withTranslation(['market', 'common'])(
  connect(state => {
    const enums = state.enums;
    const permissionMap = selectPermissionCheckMap(state);
    return {
      goodsCategory: enums.goodsCategory || [],
      productCategory: enums.productCategory || [],
      salesTypeEnums: enums.salesType || [],
      goodsTypeEnums: enums.goodsType || [],
      usageBasedType: enums.usageBasedType || [],
      envConfig: state.envConfig,
      hasEditAuth: !!permissionMap['market.edit'],
      isSuperUser: !!permissionMap['market.edit-all'],
      userInfo: state.userInfo,
    };
  })(GoodsBasicConfig)
);

export default app;
