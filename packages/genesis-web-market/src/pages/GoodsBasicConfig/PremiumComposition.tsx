import React, { useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, InputNumber, Space, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';

import { v4 as uuid } from 'uuid';

import { CommonForm, DeleteAction, EditAction, FieldType, Icon, Select, Table, message } from '@zhongan/nagrand-ui';

import { CalculatorService } from 'genesis-web-service/lib/calculator';

import { CardWithTitle } from '@market/components/CardWithTitle';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { NewMarketService } from '@market/services/market/market.service.new';
import { floatDivide, floatMultiplication, safeAdd } from '@market/utils';

import { urlQuery } from '../../util';

interface Props {
  disabled: boolean;
  goodsId: string;
}

interface Option {
  value: string;
  label: string;
}

interface DataSourceItem {
  uniqKey: string;
  id?: number;
  premiumType?: string;
  premiumAllocation?: number;
  matrixTableCode?: string;
}

interface EditableCellProps {
  editing: boolean;
  dataIndex: string;
  premiumOptions: Option[];
  matrixTableOptions: Option[];
  dataSource: DataSourceItem[];
  children: React.ReactNode;
}

enum ValueAssignment {
  DirectInput = '1',
  Ratetable = '2',
}

const EditableCell: React.FC<EditableCellProps> = ({
  editing,
  dataIndex,
  premiumOptions,
  matrixTableOptions,
  dataSource,
  children,
  ...restProps
}) => (
  <td {...restProps}>
    {editing ? (
      <Form.Item name={dataIndex} noStyle rules={[{ required: true, message: '' }]}>
        {dataIndex === 'premiumType' && (
          <Select
            options={premiumOptions.filter(option => dataSource.findIndex(row => row.premiumType === option.value) < 0)}
            style={{ minWidth: 280 }}
          />
        )}
        {dataIndex === 'matrixTableCode' && <Select options={matrixTableOptions} style={{ minWidth: 280 }} />}
        {dataIndex === 'premiumAllocation' && (
          <InputNumber min={0} max={100} style={{ minWidth: 280 }} placeholder="Please input" addonAfter="%" />
        )}
      </Form.Item>
    ) : (
      children
    )}
  </td>
);

const PremiumComposition = React.forwardRef(({ disabled, goodsId }: Props, ref) => {
  const [form] = Form.useForm();
  const [t] = useTranslation(['market', 'common']);
  const [innerTableForm] = Form.useForm<DataSourceItem>();
  const valueAssignmentWatchedValue = Form.useWatch<number>('valueAssignment', form);
  const methodOptions = useBizDictAsOptions('splittingRatioInputMethod').map(item => ({
    ...item,
    value: Number(item.value),
  }));
  const premiumOptions = useBizDictAsOptions('premiumBreakdownItem');
  const [matrixTableOptions, setMatrixTableOptions] = useState<Option[]>([]);

  const [dataSource, setDataSource] = useState<DataSourceItem[]>([]);
  const [editingKey, setEditingKey] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  const isEditing = (record: DataSourceItem) => record.uniqKey === editingKey;

  const queryPremium = () => {
    if (goodsId) {
      NewMarketService.GoodsPremiumCompositionMgmtControllerService.query(goodsId).then(res => {
        if (res.valueAssignment) {
          form.setFieldValue('valueAssignment', res.valueAssignment);
        }
        setDataSource(
          res?.items?.length
            ? res.items.map(item => ({
                ...item,
                uniqKey: uuid(),
                premiumAllocation: floatMultiplication(Number(item.premiumAllocation), 100),
              }))
            : []
        );
      });
    }
  };

  const queryMatrixTableList = () => {
    CalculatorService.queryMatrixTableList([56]).then(res => {
      setMatrixTableOptions(
        res?.length
          ? res.map(item => ({
              value: item.matrixTableCode,
              label: item.matrixTableName,
            }))
          : []
      );
    });
  };

  useEffect(() => {
    queryPremium();
    queryMatrixTableList();
  }, []);

  const onAdd = () => {
    setIsAdding(true);
    const uniqUUID = uuid();
    setEditingKey(uniqUUID);
    innerTableForm.setFieldValue(['uniqKey'], uniqUUID);
    setDataSource(data => [...data, { uniqKey: uniqUUID }]);
  };

  const onEdit = (record: DataSourceItem) => {
    innerTableForm.setFieldsValue(record);
    setEditingKey(record.uniqKey);
  };

  const onCancel = (actionType: 'cancel' | 'save') => {
    setEditingKey('');
    innerTableForm.resetFields();
    if (isAdding && actionType === 'cancel') {
      const data = [...dataSource];
      const result = data.slice(0, -1);
      setDataSource(result);
    }
    setIsAdding(false);
  };

  const onOk = () => {
    innerTableForm.validateFields().then(values => {
      const data = [...dataSource];
      const index = data.findIndex(item => item.uniqKey === values.uniqKey);
      if (index > -1) {
        data.splice(index, 1, values);
      } else {
        data.push(values);
      }
      setDataSource(data);
      onCancel('save');
    });
  };

  useImperativeHandle(ref, () => ({
    onPremiumCompositionSubmit: () =>
      new Promise(resolve => {
        if (valueAssignmentWatchedValue === +ValueAssignment.DirectInput && dataSource.length > 0) {
          const totalAllocation = dataSource.reduce(
            (accumulator, item) => safeAdd(accumulator, item.premiumAllocation!),
            0
          );
          if (totalAllocation !== 100) {
            message.warning(t('Premium Allocation must equal to 100%.'));
            resolve('fail');
            return;
          }
        }

        const params = {
          goodsId: Number(goodsId),
          valueAssignment: valueAssignmentWatchedValue,
        };

        const createPayload = {
          ...params,
          items: dataSource.map(item => ({
            premiumType: item.premiumType,
            matrixTableCode: item.matrixTableCode,
            premiumAllocation: item.premiumAllocation ? String(floatDivide(item.premiumAllocation, 100)) : undefined,
          })),
        };

        NewMarketService.GoodsPremiumCompositionMgmtControllerService.query(goodsId).then(res => {
          if (res?.items?.length) {
            const deletePayload = {
              ...params,
              items: res.items.map(item => ({ id: item.id })),
            };
            NewMarketService.GoodsPremiumCompositionMgmtControllerService.delete(deletePayload).then(() => {
              NewMarketService.GoodsPremiumCompositionMgmtControllerService.save(createPayload).then(() => {
                queryPremium();
                resolve('success');
              });
            });
          } else {
            NewMarketService.GoodsPremiumCompositionMgmtControllerService.save(createPayload).then(() => {
              queryPremium();
              resolve('success');
            });
          }
        });
      }),
  }));

  const columns: ColumnsType<DataSourceItem> = [
    {
      title: 'Premium Type',
      dataIndex: 'premiumType',
      width: '45%',
      render: (text: string) => premiumOptions?.find(item => item.value === text)?.label,
    },
    {
      title: valueAssignmentWatchedValue === +ValueAssignment.DirectInput ? 'Premium Allocation (%)' : 'Ratetable (%)',
      dataIndex: valueAssignmentWatchedValue === +ValueAssignment.DirectInput ? 'premiumAllocation' : 'matrixTableCode',
      render: (text: string, record: DataSourceItem) => {
        if (isEditing(record)) {
          return text;
        }
        return valueAssignmentWatchedValue === +ValueAssignment.DirectInput
          ? `${text}%`
          : matrixTableOptions.find(item => item.value === text)?.label;
      },
    },
    {
      title: t('Actions'),
      dataIndex: 'action',
      width: '80px',
      render: (_: string, record: DataSourceItem) => {
        const editable = isEditing(record);
        return (
          <Space>
            {editable ? (
              <React.Fragment>
                <EditAction
                  onClick={() => onOk()}
                  icon={<Icon type="check" />}
                  tooltipTitle={false as unknown as string}
                />
                <EditAction
                  onClick={() => onCancel('cancel')}
                  icon={<Icon type="close" />}
                  tooltipTitle={false as unknown as string}
                />
              </React.Fragment>
            ) : (
              <React.Fragment>
                <EditAction
                  onClick={() => onEdit(record)}
                  disabled={editingKey !== '' || disabled}
                  tooltipTitle={false as unknown as string}
                />
                <DeleteAction
                  doubleConfirmType="popconfirm"
                  deleteConfirmContent={t('Are you sure to delete this record?')}
                  disabled={editingKey !== '' || disabled}
                  onClick={() => {
                    const result = dataSource.filter(item => item.uniqKey !== record.uniqKey);
                    setDataSource(result);
                  }}
                />
              </React.Fragment>
            )}
          </Space>
        );
      },
    },
  ].map(col => ({
    ...col,
    align: col.dataIndex === 'action' ? 'right' : 'left',
    onCell:
      col.dataIndex === 'action'
        ? undefined
        : record => ({
            record,
            title: col.title,
            dataIndex: col.dataIndex,
            editing: isEditing(record),
            premiumOptions,
            matrixTableOptions,
            dataSource,
          }),
  }));

  return (
    <CardWithTitle title={t('Premium Composition')}>
      <CommonForm
        formProps={{
          form,
          initialValues: {
            valueAssignment: undefined,
          },
        }}
        fields={[
          {
            col: 24,
            key: 'valueAssignment',
            label:
              valueAssignmentWatchedValue === +ValueAssignment.Ratetable ? (
                <span>
                  {t('Value Assignment')}
                  <Tooltip
                    title={t(
                      'Please define numerical values in the table without including the percentage sign. For example, if you wish to define 10%, simply enter 10 in the table.'
                    )}
                  >
                    <Icon type="info-circle" style={{ marginLeft: 4 }} />
                  </Tooltip>
                </span>
              ) : (
                t('Value Assignment')
              ),
            type: FieldType.RadioGroup,
            extraProps: {
              disabled,
              options: methodOptions,
              onChange: () => {
                setDataSource([]);
                setIsAdding(false);
                setEditingKey('');
                innerTableForm.resetFields();
              },
            },
          },
        ]}
      />
      <Form form={innerTableForm} component={false}>
        <Form.Item noStyle name="uniqKey" />
        <Button onClick={onAdd} icon={<Icon type="add" />} type="dashed" block disabled={editingKey !== '' || disabled}>
          {t('Add')}
        </Button>
        <Table
          size="middle"
          columns={columns}
          pagination={false}
          dataSource={dataSource}
          style={{ margin: '8px 0 48px 0' }}
          components={{
            body: {
              cell: EditableCell,
            },
          }}
        />
      </Form>
    </CardWithTitle>
  );
});

export default PremiumComposition;
