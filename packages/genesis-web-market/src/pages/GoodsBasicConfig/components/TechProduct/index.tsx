import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Cascader } from 'antd';
import { CascaderProps } from 'antd/lib/cascader';

import { useRequest } from 'ahooks';
import { cloneDeep } from 'lodash-es';

import { PackageQueryResponseDTO } from 'genesis-web-service/service-types/market-types/package/index';

import { NewMarketService } from '@market/services/market/market.service.new';

interface Option {
  value: string;
  label: string;
  disabled?: boolean;
  children?: Option[];
  isLeaf?: boolean;
}

type GoodsId = string;

interface SelectedTechProduct {
  goodsId: number;
  packageId: number;
  packageCategory?: number;
}

interface IProps {
  readOnly: boolean;
  goodsId?: number;
  options: PackageQueryResponseDTO[];
  onTechProductClick: () => Promise<GoodsId>;
  onTechProductBlur: (selectedTechProducts: SelectedTechProduct[]) => void;
}

type SelectedValues = number[][];

export const TechProduct = ({
  readOnly,
  goodsId: goodsIdFromProps,
  options,
  onTechProductClick,
  onTechProductBlur,
}: IProps) => {
  const [t] = useTranslation(['market', 'common']);
  const [goodsId, setGoodsId] = useState<number | undefined>(goodsIdFromProps);
  const [selectedValues, setSelectedValues] = useState<SelectedValues>();
  const cachedSelectedValues = useRef<SelectedValues>();

  /**
   * 查询/回显之前保存的数据
   */
  const { loading: queryLoading } = useRequest(
    () => NewMarketService.GoodsPackageMgmtControllerService.query(goodsId!),
    {
      ready: !!goodsId,
      refreshDeps: [goodsId],
      onSuccess: techProductRes => {
        const values = techProductRes.map(item => [item.packageId!]);
        setSelectedValues(values);
        cachedSelectedValues.current = values;
      },
    }
  );

  /**
   * 点击tag上面的x不会触发onBlur，改成onMouseLeave触发保存
   */
  const onMouseLeave: CascaderProps<Option>['onMouseLeave'] = async () => {
    if (readOnly) {
      return;
    }
    // case 1: 将之前已保存的数据全部删除后, 鼠标移出应该调用接口
    // case 2: market goods 新增模式下, 直接点击并鼠标移出, 不应该调用接口
    if (!goodsId && !selectedValues) return;

    const payload = cloneDeep(selectedValues)?.map(([packageId]) => ({
      goodsId: +goodsId!,
      packageId: +packageId,
      packageCategory: options?.find(opt => opt.packageId === packageId)?.packageCategory, // Note: 该字段勿删
    }));

    await NewMarketService.GoodsPackageMgmtControllerService.save(goodsId!, payload || []);
    cachedSelectedValues.current = selectedValues;

    payload && onTechProductBlur(payload);
  };

  const dataSource = options?.map(item => ({
    label: `${item.packageCode!}-${item.packageName!}`,
    value: item.packageId,
    isLeaf: true,
  }));

  return (
    <Cascader
      multiple
      showSearch
      disabled={readOnly}
      options={goodsId ? dataSource : []}
      value={selectedValues}
      onMouseLeave={onMouseLeave}
      placeholder={t('Please select')}
      style={{ width: '100%' }}
      showCheckedStrategy={Cascader.SHOW_CHILD}
      loading={queryLoading}
      onChange={values => {
        setSelectedValues(values as SelectedValues);
      }}
      onClick={async () => {
        const _goodsId = await onTechProductClick();
        if (!_goodsId) return;
        setGoodsId(+_goodsId);
      }}
    />
  );
};
