import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, message } from 'antd';
import type { ColumnProps } from 'antd/es/table';

import classNames from 'classnames/bind';
import { cloneDeep, intersectionBy } from 'lodash-es';

import { DeleteAction, Icon, Table } from '@zhongan/nagrand-ui';

import { GoodsLabels } from 'genesis-web-service/lib/market';

import { CardWithTitle } from '@market/components/CardWithTitle';
import { useBizDict } from '@market/hook/bizDict';
import { renderBizdictName, renderChildBizDictName } from '@market/utils/enum';
import { NewMarketService } from '@market/services/market/market.service.new';

import GoodsLabelConfigurationDrawer from '../GoodsLabelConfigurationDrawer';
import styles from './GoodsLabelConfiguration.module.scss';

const cx = classNames.bind(styles);

interface Props {
  disabled: boolean;
  goodsId: string;
  onGoodsLabelConfiguration: (fun: (result: boolean) => void) => Promise<any>;
}

export const GoodsLabelConfiguration = ({
  disabled,
  goodsId,
  onGoodsLabelConfiguration,
}: Props): JSX.Element | null => {
  const [t] = useTranslation(['market', 'common']);
  /* ============== 枚举使用start ============== */
  const goodsLabelOptions = useBizDict('goodsLabel');
  /* ============== 枚举使用end ============== */
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [goodsLabelList, setGoodsLabelList] = useState<GoodsLabels[]>([]);

  const queryGoodsLabel = useCallback(() => {
    NewMarketService.GoodsLabelMgmtService.query(+goodsId).then(res => {
      const tempGoodsLabelList: GoodsLabels[] = [];
      if (res) {
        res.goodsLabelList.forEach(goodsLabelItem => {
          const goodsLabelBizDictInfo = goodsLabelOptions.find(
            item => item.dictValue === goodsLabelItem.goodsLabelCode
          );
          if (goodsLabelBizDictInfo && goodsLabelBizDictInfo.childList && goodsLabelBizDictInfo.childList.length > 0) {
            const goodsSubLabelIntersectionList = intersectionBy(
              goodsLabelItem.goodsSubLabelCodes.map(code => ({
                dictValue: code,
              })),
              goodsLabelBizDictInfo.childList,
              'dictValue'
            );

            if (goodsSubLabelIntersectionList.length > 0) {
              tempGoodsLabelList.push(goodsLabelItem);
            }
          }
        });
        setGoodsLabelList(tempGoodsLabelList);
      }
    });
  }, [goodsId, goodsLabelOptions]);

  useEffect(() => {
    if (goodsId) {
      queryGoodsLabel();
    }
  }, [goodsId]);

  const resetState = useCallback(() => {
    setDrawerVisible(false);
  }, []);

  const onDrawerSubmit = useCallback(
    (list: GoodsLabels[]) => {
      if (list.length === 0) {
        resetState();
        return;
      }
      const param = {
        goodsId,
        goodsLabelList: list,
      };
      NewMarketService.GoodsLabelMgmtService.saveOrUpdate(param).then(res => {
        message.success(t('Save successfully'));
        queryGoodsLabel();
      });
    },
    [goodsId, queryGoodsLabel, resetState, t]
  );

  const onDelete = useCallback(
    (record: GoodsLabels) => {
      const param = {
        goodsId,
        goodsLabelCode: record.goodsLabelCode,
      };
      NewMarketService.GoodsLabelMgmtService.delete(param).then(res => {
        message.success(t('Delete Successfully'));
        queryGoodsLabel();
      });
    },
    [goodsId, queryGoodsLabel, t]
  );

  const columns: ColumnProps<GoodsLabels>[] = useMemo(() => {
    const tempColumns: ColumnProps<GoodsLabels>[] = [
      {
        title: t('Goods Label'),
        dataIndex: 'goodsLabelCode',
        width: 200,
        render: (text: string) => <span>{renderBizdictName(text, goodsLabelOptions) || t('- -')}</span>,
      },
      {
        title: t('Matched Label Content'),
        dataIndex: 'goodsSubLabelCodes',
        render: (textList: string[], record: GoodsLabels) =>
          textList.map(textItem => (
            <div className={cx('label-item')}>
              {renderChildBizDictName(record.goodsLabelCode, textItem, goodsLabelOptions)}
            </div>
          )),
      },
    ];

    if (!disabled) {
      tempColumns.push({
        title: t('Actions'),
        dataIndex: 'key',
        width: 100,
        fixed: 'right',
        align: 'right',
        className: 'agreement-action-column',
        render: (text: string, record: GoodsLabels) => (
          <DeleteAction
            disabled={disabled}
            doubleConfirmType="popconfirm"
            onClick={() => onDelete(record)}
            deleteConfirmContent={t('Are you sure to delete this record?')}
          />
        ),
      });
    }

    return tempColumns;
  }, [disabled, goodsLabelOptions, onDelete, t]);

  return (
    <CardWithTitle title={t('Goods Label Configuration')}>
      {disabled ? null : (
        <div style={{ marginBottom: 8 }}>
          <Button
            onClick={() =>
              onGoodsLabelConfiguration((result: boolean) => {
                if (result) {
                  setDrawerVisible(true);
                }
              })
            }
            icon={<Icon type="edit" />}
          >
            {t('Edit')}
          </Button>
        </div>
      )}
      <Table
        columns={columns}
        dataSource={goodsLabelList}
        rowKey="goodsLabelCode"
        scroll={{ x: 'max-content' }}
        pagination={false}
        emptyType="text"
      />
      {drawerVisible ? (
        <GoodsLabelConfigurationDrawer
          goodsId={goodsId}
          visible={drawerVisible}
          goodsLabelList={goodsLabelList}
          onDrawerSubmit={onDrawerSubmit}
          closeDrawer={() => resetState()}
          queryGoodsLabel={queryGoodsLabel}
        />
      ) : null}
    </CardWithTitle>
  );
};

export default GoodsLabelConfiguration;
