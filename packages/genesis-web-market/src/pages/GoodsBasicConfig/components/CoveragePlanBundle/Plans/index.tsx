import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { FormLayout } from '@formily/antd-v5';
import { Form, createForm, onFormReact } from '@formily/core';
import { FormProvider } from '@formily/react';

import { Modal } from '@zhongan/nagrand-ui';

import { PlanInfoBase } from 'genesis-web-service/service-types/market-types/package';

import { REMOVE_BTN, plans } from '../constants';
import { EditableSelect, type PlanInfo } from './EditableSelect';
import { Preview } from './Preview';
import { SchemaField } from './SchemaField';
import { schema } from './schema';
import styles from './style.module.scss';

interface IProps {
  onChange: (values: PlanInfo[]) => void;
  value: PlanInfoBase[];
  disabled: boolean;
}

interface IForm {
  plans: PlanInfo[];
}

export const Plans = (props: IProps) => {
  const [t] = useTranslation(['market', 'common']);
  const [open, setOpen] = useState(false);
  const { value = [], disabled, onChange } = props;

  /**
   * form 初始化
   */
  const form = useMemo(
    () =>
      createForm<IForm>({
        validateFirst: true,
        disabled, // 只读模式下禁用
        initialValues: { plans: [{} as PlanInfo] },
        effects() {
          onFormReact((newForm: Form<{ plans: PlanInfo[] }>) => {
            const plansLen = newForm.values.plans.length;
            // 当只有一条数据时, 禁用删除 icon
            newForm.setFieldState(`${plans}.*.${REMOVE_BTN}`, state => {
              state.disabled = plansLen <= 1;
            });
          });
        },
      }),
    [disabled]
  );

  /**
   * 数据回显
   */
  useEffect(() => {
    if (open) {
      form.reset();
      form && form.setInitialValues({ plans: value ?? [{}] });
    }
  }, [form, value, open]);

  /**
   * Confirm
   */
  const onClose = () => {
    setOpen(false);
  };

  const onConfirm = async () => {
    const values = await form.submit<IForm>();
    onChange(values.plans);
    onClose();
  };

  return (
    <div className="flex-1">
      {/* 只读模式下显示 */}
      {disabled && <Preview list={value} />}

      {/* 编辑模式下显示 */}
      {!disabled && <EditableSelect list={value} disabled={disabled} setOpen={setOpen} onChange={onChange} />}

      <Modal title={t('Edit')} open={open} wrapClassName={styles.plansModal} onCancel={onClose} onOk={onConfirm}>
        <div className="mb-4 font-medium leading-[22px]">{t('Plan (Plan Code + Plan Name)')}</div>
        <FormProvider form={form}>
          <FormLayout layout="vertical">
            <SchemaField schema={schema} scope={{ t }} />
          </FormLayout>
        </FormProvider>
      </Modal>
    </div>
  );
};
