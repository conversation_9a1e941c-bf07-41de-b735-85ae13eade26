import { Tag, Tooltip } from 'antd';

import cls from 'classnames';

import { Icon } from '@zhongan/nagrand-ui';

import { PlanInfoBase } from 'genesis-web-service/service-types/market-types/package';

import { useBizDictAsOptions } from '@market/hook/bizDict';

export const Preview = ({ list }: { list: PlanInfoBase[] }) => {
  const languages = useBizDictAsOptions('language', {
    labelKey: 'dictValueName',
    valueKey: 'itemExtend1',
  });

  const isDisplayMultilingual = list.some(item => !!item.i18nPlanName?.length);

  const Multilingual = () => (
    <Tooltip
      className="inline-block align-middle"
      title={
        <div className="px-1 py-[2px]">
          {list
            .filter(({ i18nPlanName }) => !!i18nPlanName)
            .map((plan, idx) => (
              <div className={cls({ 'mb-4': idx !== list.length - 1 })}>
                <span className="font-bold">{plan.planCode}</span>
                {plan.i18nPlanName
                  ?.filter(({ i18nContent }) => !!i18nContent)
                  ?.map(({ lang, i18nContent }) => (
                    <div key={lang} className="mb-1">
                      {languages.find(item => item.value === lang)?.label}: {i18nContent};
                    </div>
                  ))}
              </div>
            ))}
        </div>
      }
    >
      <Icon type="info-circle" className="ml-2 text-[16px] text-[#9fb3c8]" />
    </Tooltip>
  );

  return (
    <div>
      {list?.map(({ planCode, planName }, index) => {
        return (
          <Tooltip
            title={
              <div>
                {planCode} - {planName}{' '}
              </div>
            }
          >
            <Tag
              style={{
                paddingInlineEnd: 'var(--gap-xs)',
                backgroundColor: 'var(--info-color-bg)',
                border: 'none',
                borderRadius: 'var(--checkbox-border-radius)',
                color: 'var(--info-color-text-dark)',
                cursor: 'pointer',
              }}
              key={index + (planCode ?? '')}
            >
              {planCode}
            </Tag>
          </Tooltip>
        );
      })}

      {isDisplayMultilingual && <Multilingual />}
    </div>
  );
};
