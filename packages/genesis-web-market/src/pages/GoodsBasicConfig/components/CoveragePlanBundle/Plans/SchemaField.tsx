import { ArrayBase, ArrayItems, FormItem, Space } from '@formily/antd-v5';
import { createSchemaField } from '@formily/react';

import { Icon, Input } from '@zhongan/nagrand-ui';

import { Multilingual } from './Multilingual';

// 重写 ArrayItems 的下列组件
ArrayItems.Addition = props => (
  <ArrayBase.Addition {...props} icon={<Icon type="add" />} type="default" block={false} className="!outline-none" />
);

ArrayItems.Remove = props => (
  <ArrayBase.Remove {...props} icon={<Icon type="delete" />} type="text" block={false} className="!outline-none" />
);

export const SchemaField = createSchemaField({
  components: {
    FormItem,
    ArrayItems,
    Input,
    Space,
    Multilingual,
  },
});
