import { ISchema } from '@formily/react';

import { REMOVE_BTN, plans } from '../constants';

export const schema: ISchema = {
  type: 'object',
  properties: {
    [plans]: {
      type: 'array',
      'x-decorator': 'FormItem',
      'x-component': 'ArrayItems',
      // Add New 按钮
      properties: {
        add: {
          type: 'void',
          title: "{{ t('Add New') }}",
          'x-component': 'ArrayItems.Addition',
        },
      },
      items: {
        type: 'object',
        properties: {
          CONTAINER: {
            type: 'void',
            'x-component': 'Space',
            'x-component-props': {
              align: 'baseline',
            },
            properties: {
              // Plan Code
              planCode: {
                type: 'string',
                'x-decorator': 'FormItem',
                'x-component': 'Input',
                'x-component-props': {
                  placeholder: "{{ t('Plan Code') }}",
                },
                'x-validator': [
                  {
                    required: true,
                    message: "{{ t('Please input') }}",
                  },
                ],
              },
              // Plan Name
              planName: {
                type: 'string',
                'x-decorator': 'FormItem',
                'x-component': 'Input',
                'x-component-props': {
                  placeholder: "{{ t('Plan Name') }}",
                },
                'x-validator': [
                  {
                    required: true,
                    message: "{{ t('Please input') }}",
                  },
                ],
              },
              // 图标 - 用于配置多语言
              i18nPlanName: {
                type: 'array',
                'x-decorator': 'FormItem',
                'x-component': 'Multilingual',
              },
              // Remove icon
              [REMOVE_BTN]: {
                type: 'void',
                'x-component': 'ArrayItems.Remove',
              },
            },
          },
        },
      },
    },
  },
};
