import { useMemo } from 'react';

import { SelectProps, Tag, Tooltip } from 'antd';

import { cloneDeep } from 'lodash-es';

import { Icon, Select } from '@zhongan/nagrand-ui';

import { PlanInfoBase } from 'genesis-web-service/service-types/market-types/package';

import styles from './style.module.scss';

export interface PlanInfo {
  planCode: string;
  planName: string;
}

interface IProps {
  list: PlanInfoBase[];
  disabled: boolean;
  setOpen: (isOpen: boolean) => void;
  onChange: (values: PlanInfo[]) => void;
}

export const EditableSelect = ({ list, disabled, setOpen, onChange }: IProps) => {
  const tagRender: SelectProps['tagRender'] = ({ label, onClose }) => {
    const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
      event.preventDefault();
      event.stopPropagation();
    };

    return (
      <Tag
        onMouseDown={onPreventMouseDown}
        // 1. 当只有一个 coverage plan 时, 隐藏 tag上的删除icon, 不允许删除,
        closable={list?.length > 1}
        onClose={onClose}
        style={{
          paddingInlineEnd: 'var(--gap-xs)',
          backgroundColor: 'var(--info-color-bg)',
          border: 'none',
          borderRadius: 'var(--checkbox-border-radius)',
          color: 'var(--info-color-text-dark)',
        }}
      >
        {label}
      </Tag>
    );
  };

  const title = useMemo(() => {
    // 还没有配置任何 plan 时, 不显示 tooltip
    if (!list?.length) return;
    return (
      <div>
        {list?.map(({ planCode, planName }, index) => {
          return (
            <div key={index + (planCode ?? '')}>
              {planCode} - {planName}
            </div>
          );
        })}
      </div>
    );
  }, [list]);

  return (
    <Tooltip title={title}>
      <Select
        mode="multiple"
        className="!w-full"
        popupClassName={styles.selectPopup}
        suffixIcon={<Icon type="edit" className="text-[16px]" />}
        value={list?.filter?.(item => !!item.planCode)?.map(item => item.planCode)}
        options={list?.map(item => ({ label: item.planCode, value: item.planCode }))}
        // 点击组件时, 不显示下拉面板
        dropdownRender={() => <div />}
        tagRender={tagRender}
        onClick={() => {
          if (disabled) return;
          setOpen(true);
        }}
        // 点击 tag 上的删除图标后触发
        onDeselect={async deselectVal => {
          const plans = cloneDeep(list);
          const newPlans = plans.filter(plan => plan.planCode !== deselectVal);
          onChange(newPlans as PlanInfo[]);
        }}
      />
    </Tooltip>
  );
};
