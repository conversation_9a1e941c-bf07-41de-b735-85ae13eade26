import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Tooltip, message } from 'antd';

import { cloneDeep } from 'lodash-es';

import { Input, Modal } from '@zhongan/nagrand-ui';

import { hasDuplicates } from 'genesis-web-component/lib/util/array';
import { I18nLanguageInfo } from 'genesis-web-service';

import Icon from '@market/asset/svg/multilingual.svg';
import GeneralSelect from '@market/components/GeneralSelect/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';

import styles from './style.module.scss';

interface IProps {
  value: I18nLanguageInfo[];
  onChange: (value: I18nLanguageInfo[]) => void;
}

export const Multilingual = ({ onChange, value }: IProps) => {
  const [open, setOpen] = useState(false);
  const [i18nLanguageList, setI18nLanguageList] = useState<I18nLanguageInfo[]>([]);
  const [t] = useTranslation(['market', 'common']);
  const languages = useBizDictAsOptions('language', {
    labelKey: 'dictValueName',
    valueKey: 'itemExtend1',
  });

  useEffect(() => {
    const list = languages.map(item => ({
      lang: item.value?.toString(),
      i18nContent:
        value && Array.isArray(value) ? value.find(inner => inner.lang === item.value)?.i18nContent || '' : '',
    }));
    setI18nLanguageList(list || []);
  }, [value]);

  /**
   * Confirm
   */
  const onConfirmSubmit = () => {
    if (hasDuplicates(i18nLanguageList, 'lang')) {
      return message.error(t('Same language should only have one remark'));
    }
    const _list = i18nLanguageList.filter(item => !!item.i18nContent);
    onChange(_list);
    setOpen(false);
  };

  const changeLanguageItem = (value: string, index: number) => {
    const tempI18nLanguageList = cloneDeep(i18nLanguageList);
    tempI18nLanguageList[index]['i18nContent'] = value;
    setI18nLanguageList(tempI18nLanguageList);
  };

  return (
    <div className="inline-block align-middle">
      <Tooltip title={t('Multilingual')}>
        <Icon onClick={() => setOpen(true)} className="cursor-pointer ml-4 mr-[-8px]" />
      </Tooltip>
      <Modal
        open={open}
        title={t('Multilingual')}
        onOk={onConfirmSubmit}
        onCancel={() => setOpen(false)}
        okText={t('Confirm')}
        cancelText={t('Cancel')}
        wrapClassName={styles.modal}
        closable={false}
        width={560}
      >
        <div>{t('Language')}</div>
        {i18nLanguageList.map((item, index) => (
          <Input.Group className={styles['inputGroup-item']} compact>
            <GeneralSelect
              style={{ width: 200 }}
              value={item.lang}
              placeholder={t('Please select')}
              option={languages}
              suffixIcon={false}
              disabled
            />
            <Input
              style={{ width: 280 }}
              value={item.i18nContent}
              placeholder={t('Please input')}
              onChange={event => changeLanguageItem(event.target.value, index)}
            />
          </Input.Group>
        ))}
      </Modal>
    </div>
  );
};
