import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Divider, Form, FormInstance } from 'antd';

import { Input } from '@zhongan/nagrand-ui';

interface IProps {
  fieldName: number;
  form: FormInstance;
  isPopoverOpen: boolean;
  onPopoverOpen: (isOpen: boolean) => void;
}

export const EditGroupName = ({ fieldName, isPopoverOpen, onPopoverOpen, form }: IProps) => {
  const [t] = useTranslation(['market', 'common']);
  const [name, setName] = useState<string>();
  const planGroupNamePath = ['items', fieldName, 'planGroupName'];
  const planGroupCodePath = ['items', fieldName, 'planGroupCode'];

  useEffect(() => {
    const planGroupCode = form.getFieldValue(planGroupCodePath);
    form.setFields([{ name: planGroupNamePath, value: planGroupCode, errors: [] }]);
  }, [isPopoverOpen]);

  const onCancel = () => {
    onPopoverOpen(false);
  };

  const onConfirm = async () => {
    if (!name) return;
    form.setFieldValue(planGroupCodePath, name);
    onCancel();
  };

  return (
    <div>
      <Divider style={{ margin: '16px 0', borderColor: 'var(--divider-color)' }} />
      <Form.Item
        label={t('Plan Group Name')}
        name={[fieldName, 'planGroupName']}
        rules={[{ required: true, message: t('Please input') }]}
      >
        <Input
          onChange={event => {
            setName(event.target.value);
          }}
        />
      </Form.Item>
      <div className="flex justify-end gap-x-4">
        <Button onClick={onCancel}>{t('Cancel')}</Button>
        <Button type="primary" onClick={onConfirm}>
          {t('Confirm')}
        </Button>
      </div>
    </div>
  );
};
