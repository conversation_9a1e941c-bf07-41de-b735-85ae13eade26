import { forwardRef, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, message } from 'antd';

import { useRequest } from 'ahooks';

import { Icon, Input, Modal } from '@zhongan/nagrand-ui';

import { LabelAndValue } from 'genesis-web-component/lib/components/MultipleSelectAll/MultipleSelectAll';
import { PlanGroupResponse } from 'genesis-web-service/service-types/market-types/package/index';

import { removeAllRedBorder } from '@market/pages/GoodsBasicConfig/utils/handleErr';
import { NewMarketService } from '@market/services/market/market.service.new';

import { Card } from './Card';

interface IProps {
  goodsId: number;
  readOnly: boolean;
  allPackageCodes: LabelAndValue[];
}

export interface CoveragePlanBundleRefProps {
  submit: () => Promise<PlanGroupResponse>;
}

export const CoveragePlanBundle = forwardRef<CoveragePlanBundleRefProps, IProps>(
  ({ goodsId, readOnly, allPackageCodes }, ref) => {
    const [t] = useTranslation(['market', 'common']);
    const [form] = Form.useForm();

    const { data } = useRequest(() => NewMarketService.GoodsPlanMgmtService.queryPlanGroup(goodsId), {
      ready: !!goodsId,
      onSuccess: res => {
        form.setFieldsValue({
          items: res.map(item => ({ ...item, planGroupName: item.planGroupCode })),
        });
      },
    });

    useImperativeHandle(ref, () => ({
      submit: async () => {
        return await form.validateFields();
      },
    }));

    /**
     * Add new
     */
    const onAddNew = ({ index, add }) => {
      let name = '';
      const className = 'planGroupName';

      Modal.confirm({
        content: (
          <Form.Item label={t('Plan Group Name')} layout="vertical" required>
            <Input
              onChange={event => {
                name = event.target.value;
              }}
              className={className}
            />
          </Form.Item>
        ),
        title: t('Add'),
        icon: null,
        width: 320,
        centered: true,
        onOk: async () => {
          const list = form.getFieldsValue().items as PlanGroupResponse[];
          const duplicateName = list?.find?.(item => item.planGroupCode === name);
          const invalidName = !name || !!duplicateName;

          if (!!duplicateName) {
            message.error(t('plan group code must be unique'));
          }

          if (invalidName) {
            return Promise.reject();
          }
          add({ planGroupCode: name }, index);
        },
      });
    };

    return (
      <Form layout="vertical" requiredMark form={form} name="coverage_plan_bundle" autoComplete="off">
        <Form.List name="items">
          {(fields, { add, remove }) => (
            <div>
              {fields.map(field => (
                <Card
                  index={field.name}
                  key={field.key}
                  form={form}
                  readOnly={readOnly}
                  initialData={data?.[field.name]?.techProductPlans}
                  allPackageCodes={allPackageCodes}
                  onRemove={() => {
                    // 将要删除卡片内的数据都删除, 否则 formily form 依然会渲染卡片
                    data?.splice?.(field.name, 1);
                    remove(field.name);
                  }}
                />
              ))}

              {!readOnly && (
                <Button
                  icon={<Icon type="add" />}
                  onClick={() => {
                    removeAllRedBorder();
                    onAddNew({
                      add,
                      index: fields.length,
                    });
                  }}
                >
                  {t('Add New')}
                </Button>
              )}
            </div>
          )}
        </Form.List>
      </Form>
    );
  }
);
