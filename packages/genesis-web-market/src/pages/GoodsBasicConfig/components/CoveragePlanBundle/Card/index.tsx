import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Divider, Form, FormInstance } from 'antd';

import { createForm, onFormReact, onFormValuesChange } from '@formily/core';
import { FormProvider } from '@formily/react';

import cls from 'classnames';

import { Icon, Popconfirm, PreviewText } from '@zhongan/nagrand-ui';

import { LabelAndValue } from 'genesis-web-component/lib/components/MultipleSelectAll/MultipleSelectAll';
import {
  PlanGroupResponse,
  TechProductPlanResponse,
} from 'genesis-web-service/service-types/market-types/package/index';

import ArrowUpIcon from '@market/asset/svg/arrow-up.svg';
import {
  ITechProductId,
  handleTechProductId,
} from '@market/pages/GoodsBasicConfig/components/CoveragePlanBundle/Card/handle';

import { EditGroupName } from '../EditGroupName/index';
import { REMOVE_BTN, REMOVE_COLUMN, calTableCardCls, techProductIdField, techProductPlans } from '../constants';
import { SchemaField } from './SchemaField';
import { schema } from './schema';
import styles from './style.module.scss';
import { SelectedItems } from './type';

interface IProps {
  form: FormInstance;
  index: number;
  readOnly: boolean;
  initialData: PlanGroupResponse['techProductPlans'];
  allPackageCodes: LabelAndValue<number>[];
  onRemove: () => void;
}

interface IForm {
  techProductPlans: PlanGroupResponse['techProductPlans'];
}

export const Card = ({ index, readOnly, allPackageCodes, initialData, form: antdForm, onRemove }: IProps) => {
  const [t] = useTranslation(['market', 'common']);
  const [visible, setVisible] = useState(true);
  const [isGroupNamePopoverOpen, setIsGroupNamePopoverOpen] = useState(false);
  const [selectedItems, setSelectedItems] = useState<SelectedItems[]>([]);
  const techProductPlansPath = ['items', index, 'techProductPlans'];

  /**
   * formily schema form 初始化及 effects 方法
   */
  const schemaForm = useMemo(() => {
    return createForm<IForm>({
      validateFirst: true,
      disabled: readOnly,
      initialValues: { techProductPlans: [{}] },
      effects() {
        // 实时监听 schema 字段变化, 同步赋值给 antd form 的 techProductPlans 字段
        onFormValuesChange(form => {
          antdForm?.setFieldValue?.(techProductPlansPath, form.values.techProductPlans);
        });
        onFormReact(form => {
          const plansLen = form.values.techProductPlans.length;

          // 当只有一条数据时, 禁用删除 icon
          form.setFieldState(`${techProductPlans}.*.${REMOVE_COLUMN}`, state => {
            state.disabled = plansLen <= 1;
          });
        });
      },
    });
  }, [antdForm, readOnly]);

  /**
   * 回显数据
   */
  useEffect(() => {
    // isMandatory 需要从 YES/NO 转换为 boolean
    const uiData = initialData?.map(item => ({ ...item, isMandatory: item.isMandatory === 'YES' }));
    schemaForm && schemaForm.setInitialValues({ techProductPlans: uiData });
  }, [schemaForm, initialData]);

  /**
   * 初始化时处理 Tech Product 字段的下拉选项, 需要在下拉列表中隐藏接口已选择过的选项
   */
  useEffect(() => {
    // 只读模式下, 不需要处理
    if (readOnly) return;
    initialData?.forEach(({ techProductId }) => {
      const targetPackage = allPackageCodes.find(pkg => pkg.value === techProductId);
      setSelectedItems(prev => [
        ...prev,
        {
          disabled: true,
          key: techProductId,
          label: targetPackage?.label,
          title: undefined,
          value: techProductId,
        } as SelectedItems,
      ]);
    });
  }, [initialData, allPackageCodes]);

  /**
   * 给每一个 table row 的删除按钮绑定点击事件, 将要删除的 techProductId 从 setSelectedItems 中释放出来,以便其他 row 能够选择
   */
  useEffect(() => {
    // 只读模式下, 不需要处理
    if (readOnly) return;
    if (!schemaForm) return;
    schemaForm.setFieldState(`${techProductPlans}.*.${REMOVE_BTN}`, state => {
      if (state.componentProps) {
        const { techProductId } = state.record as TechProductPlanResponse;
        state.componentProps.onClick = () => {
          const _techProductId = handleTechProductId(techProductId as ITechProductId);
          // 将要删除的 techProductId 从 setSelectedItems中释放出来
          const newSelectedItems = selectedItems.filter(item => item.value !== _techProductId);
          setSelectedItems(newSelectedItems);
        };
      }
    });
  }, [schemaForm, selectedItems]);

  /**
   * 动态配置 Tech Product 字段组件属性
   */
  useEffect(() => {
    if (!schemaForm) return;

    // 需求: 要求隐藏下拉列表中已选择的选项
    // 实现方案: 在全量的 options 内将已选择过的选项 disabled: true, 然后在 css 内将 disabled 的选项隐藏.
    // 解释: 不能使用真正的过滤, 否则在 [刷新数据回显] 的场景下, 之前已选择过的选项无法展示正确的 label
    const filteredOptions = allPackageCodes.map(pkg => {
      const hasSelected = selectedItems.find(item => item.value === pkg.value);
      return !!hasSelected ? { ...pkg, disabled: true } : pkg;
    });

    schemaForm.setFieldState(`${techProductPlans}.*.${techProductIdField}`, state => {
      if (state.componentProps) {
        // 在只读模式下应使用 PreviewText
        if (readOnly) {
          state.componentType = PreviewText.Select;
          state.componentProps = {
            ...state.componentProps,
            options: allPackageCodes,
          };
          return;
        }

        const { techProductId: prevTechProductId } = state.record;

        state.componentProps = {
          ...state.componentProps,
          popupClassName: styles.selectPopup,
          options: filteredOptions,
          labelInValue: true,
          showSearch: true,
          optionFilterProp: 'label',
          onChange: (selectedPkgItem: SelectedItems) => {
            // 如果之前已经选择过值了, 再次更换别的 option, 需要将之前的旧值释放放回可选择的数据源内
            const _prevTechProductId = handleTechProductId(prevTechProductId as ITechProductId);
            const newSelectedItems = selectedItems.filter(item => item.value !== _prevTechProductId);
            setSelectedItems([...newSelectedItems, selectedPkgItem]);
          },
        };
      }
    });
  }, [schemaForm, selectedItems, allPackageCodes]);

  const onEditGroupName = () => {
    setIsGroupNamePopoverOpen(true);
  };

  return (
    <div
      className={cls(calTableCardCls(index), 'rounded-xl p-4 mt-4 mb-2 border border-solid font-medium', {
        'mt-2': index !== 0,
      })}
      style={{
        backgroundColor: visible ? 'var(--input-addon-bg)' : '#fff',
        borderColor: visible ? 'var(--input-addon-bg)' : 'var(--border-color-base)',
      }}
    >
      <div className="flex items-center gap-2 justify-between">
        <Form.Item shouldUpdate noStyle>
          {() => {
            return <span className="font-bold">{antdForm.getFieldValue(['items', index, 'planGroupCode'])}</span>;
          }}
        </Form.Item>

        <div className="flex items-center gap-4">
          {!readOnly && (
            <>
              <Popconfirm
                placement="topLeft"
                overlayInnerStyle={{ padding: '8px 16px 0', width: 320 }}
                overlayClassName={styles.editPopconfirm}
                title={t('Edit')}
                icon={null}
                open={isGroupNamePopoverOpen}
                showCancel={false}
                // 点击任意地方关闭 Popconfirm
                onOpenChange={setIsGroupNamePopoverOpen}
                description={
                  <EditGroupName
                    onPopoverOpen={setIsGroupNamePopoverOpen}
                    isPopoverOpen={isGroupNamePopoverOpen}
                    fieldName={index}
                    form={antdForm}
                  />
                }
              >
                <Icon type="edit" className="cursor-pointer" onClick={onEditGroupName} />
              </Popconfirm>
              <Popconfirm
                title={t('Are you sure to delete this record ?')}
                placement="topLeft"
                width={320}
                onConfirm={onRemove}
              >
                <Icon type="delete" className="cursor-pointer" />
              </Popconfirm>
              <Divider type="vertical" className="!m-0" />
            </>
          )}
          <ArrowUpIcon
            className={cls('cursor-pointer', { 'rotate-180': visible })}
            onClick={() => {
              setVisible(!visible);
            }}
          />
        </div>
      </div>
      <div className={cls(styles.formilyTable, 'relative ', visible ? 'visible mt-6' : 'hidden')}>
        <div className="mb-[13px]">{t('Package Configuration')}</div>
        <FormProvider form={schemaForm}>
          <SchemaField schema={schema} scope={{ t }} />
        </FormProvider>
      </div>
    </div>
  );
};
