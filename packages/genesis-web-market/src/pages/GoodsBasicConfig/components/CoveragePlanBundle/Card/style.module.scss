.formilyTable {
  td {
    border-color: var(--border-color-base) !important;
    border-inline-end: none !important;
  }
  // 重置表头颜色, 去掉表头内的分割线
  th {
    background-color: var(--default-color-bg) !important;
    border-inline-end: none !important;
  }
  table {
    border: 1px solid var(--border-light) !important;
    border-bottom: none !important;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  tbody tr:last-of-type {
    td:first-child {
      border-bottom-left-radius: 8px;
    }

    td:last-child {
      border-bottom-right-radius: 8px;
    }
  }

  :global {
    .#{$ant-prefix}-formily-item-error-help {
      display: none;
    }

    .#{$ant-prefix}-table-tbody > tr.#{$ant-prefix}-table-row:last-child > td {
      border-bottom: 1px solid var(--border-color-base) !important;
    }
  }
}

.selectPopup {
  :global {
    // 勿删
    .market-ant4-select-item-option-disabled {
      display: none !important;
    }
  }
}

.editPopconfirm {
  :global {
    .#{$ant-prefix}-popconfirm-buttons {
      display: none;
    }
  }
}
