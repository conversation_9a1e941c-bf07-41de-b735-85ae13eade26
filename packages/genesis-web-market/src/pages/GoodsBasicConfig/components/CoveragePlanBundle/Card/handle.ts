import { isObject } from 'lodash-es';

import { SelectedItems } from './type';

export type ITechProductId = SelectedItems | number;

/**
 * 将 techProductId 处理成 number 类型
 * case 1: 接口需要的是 number 类型, 所以初始化场景, 该字段拿到的值是 number 类型
 * case 2: 页面上添加了新的数据还没有提交接口存储时, 此时 techProductId 是个对象, 因为 Select 组件配置了 labelInValue: true 属性所以得到的是个对象, 这里要取对象内的 value 重新赋值
 * @returns number
 */
export function handleTechProductId(techProductId: ITechProductId) {
  return isObject(techProductId) ? techProductId.value : techProductId;
}
