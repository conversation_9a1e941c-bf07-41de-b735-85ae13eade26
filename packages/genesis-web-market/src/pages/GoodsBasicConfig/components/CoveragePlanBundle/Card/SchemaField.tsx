import { ArrayBase, ArrayTable, Checkbox, FormItem, Select } from '@formily/antd-v5';
import { createSchemaField, useField } from '@formily/react';

import { Icon, PreviewText } from '@zhongan/nagrand-ui';

import { removeAllRedBorder } from '@market/pages/GoodsBasicConfig/utils/handleErr';

import { Plans } from '../Plans';

// 重写 ArrayTable 的下列组件
ArrayTable.Index = props => {
  const index = ArrayBase.useIndex();

  return <span {...props}>{index + 1}</span>;
};

ArrayTable.Addition = props => {
  const filed = useField();

  // 只读模式下隐藏新增按钮
  if (filed.disabled) return;

  return (
    <ArrayBase.Addition
      {...props}
      icon={<Icon type="add" />}
      type="default"
      block={false}
      className="absolute right-0 top-[-38px]"
      onClick={() => {
        removeAllRedBorder();
      }}
    />
  );
};

export const SchemaField = createSchemaField({
  components: {
    FormItem,
    ArrayTable,
    Checkbox,
    Select,
    Plans,
    PreviewText,
  },
});
