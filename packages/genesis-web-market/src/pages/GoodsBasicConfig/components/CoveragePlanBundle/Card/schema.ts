import { ISchema } from '@formily/react';

import {
  REMOVE_BTN,
  REMOVE_COLUMN,
  plansCls,
  techProductIdCls,
  techProductIdField,
  techProductPlans,
} from '../constants';

export const schema: ISchema = {
  type: 'object',
  properties: {
    [techProductPlans]: {
      type: 'array',
      'x-decorator': 'FormItem',
      'x-component': 'ArrayTable',
      'x-component-props': {
        scroll: { x: 'max-content' },
      },
      // Add New 按钮
      properties: {
        add: {
          type: 'void',
          title: "{{ t('Add New') }}",
          'x-component': 'ArrayTable.Addition',
          'x-component-props': {
            method: 'unshift',
            // 新增数据的默认值
            defaultValue: {
              isMandatory: false,
              techProductId: null,
              plans: null,
            },
          },
        },
      },
      items: {
        type: 'object',
        properties: {
          // Index
          INDEX: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            'x-component-props': { width: 80, title: 'No.', align: 'center' },
            properties: {
              index: {
                type: 'void',
                'x-component': 'ArrayTable.Index',
              },
            },
          },
          // Mandatory
          CHECKBOX: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            'x-component-props': {
              title: "{{ t('Mandatory') }}",
              width: 120,
            },
            properties: {
              isMandatory: {
                type: 'boolean',
                default: false,
                'x-decorator': 'FormItem',
                'x-component': 'Checkbox',
              },
            },
          },
          // Tech Product
          SELECT: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            'x-component-props': {
              title: "{{ t('Package') }}",
              width: 240,
            },
            properties: {
              [techProductIdField]: {
                type: 'string',
                'x-decorator': 'FormItem',
                'x-decorator-props': {
                  className: techProductIdCls,
                },
                'x-component': 'Select',
                'x-component-props': {
                  placeholder: `{{ t('Please select') }}`,
                },
                'x-validator': [
                  {
                    required: true,
                    message: ' ',
                  },
                ],
              },
            },
          },
          // Coverage Plan
          Coverage_Plan: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            'x-component-props': {
              title: "{{ t('Plan') }}",
            },
            properties: {
              plans: {
                type: 'array',
                'x-decorator': 'FormItem',
                'x-decorator-props': {
                  className: plansCls,
                },
                'x-component': 'Plans',
                'x-validator': [
                  {
                    required: true,
                    message: ' ',
                  },
                ],
              },
            },
          },
          // Actions
          ACTIONS: {
            type: 'void',
            'x-component': 'ArrayTable.Column',
            'x-component-props': {
              title: "{{ t('Actions') }}",
              dataIndex: 'operations',
              width: 80,
            },
            // 只读模式下, 隐藏该列
            'x-reactions': {
              fulfill: {
                state: {
                  visible: '{{ !$self.disabled }}',
                },
              },
            },
            properties: {
              [REMOVE_COLUMN]: {
                type: 'void',
                'x-component': 'FormItem',
                properties: {
                  [REMOVE_BTN]: {
                    type: 'void',
                    'x-component': 'ArrayTable.Remove',
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};
