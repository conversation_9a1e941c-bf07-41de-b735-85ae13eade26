import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import Icon from '@ant-design/icons';
import { Button } from 'antd';

import classNames from 'classnames/bind';
import { cloneDeep, isEmpty, keyBy } from 'lodash-es';

import { Drawer } from '@zhongan/nagrand-ui';

import { BizDictItem } from 'genesis-web-service/lib/foundation/foundation.interface';
import { GoodsLabels } from 'genesis-web-service/lib/market';

import tick from '@market/asset/svg/tick.svg';
import { useBizDict } from '@market/hook/bizDict';
import { NewMarketService } from '@market/services/market/market.service.new';

import styles from './GoodsLabelConfigurationDrawer.module.scss';

const cx = classNames.bind(styles);

interface Props {
  goodsId: string;
  visible: boolean;
  closeDrawer: () => void;
  queryGoodsLabel: () => void;
  goodsLabelList: GoodsLabels[];
  onDrawerSubmit: (list: GoodsLabels[]) => void;
}

export const GoodsLabelConfigurationDrawer = ({
  goodsId,
  visible,
  closeDrawer,
  queryGoodsLabel,
  goodsLabelList,
  onDrawerSubmit,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  /* ============== 枚举使用start ============== */
  const goodsLabelBizDict = useBizDict('goodsLabel');
  /* ============== 枚举使用end ============== */
  const [goodsLabels, setGoodsLabels] = useState<GoodsLabels[]>([]);

  const goodLabelsMap = useMemo(() => keyBy(goodsLabels, 'goodsLabelCode'), [goodsLabels]);

  useEffect(() => {
    setGoodsLabels(goodsLabelList);
  }, [goodsLabelList]);

  const checkLabel = useCallback(
    (childItem: BizDictItem) => {
      const tempGoodsLabels = cloneDeep(goodsLabels);
      if (tempGoodsLabels.find(groupItem => groupItem.goodsLabelCode === childItem.parentValue)) {
        tempGoodsLabels.forEach(item => {
          if (item.goodsLabelCode === (childItem.parentValue as string)) {
            if (item.goodsSubLabelCodes.includes(childItem.dictValue as string)) {
              item.goodsSubLabelCodes = item.goodsSubLabelCodes.filter(code => code !== childItem.dictValue);
            } else {
              item.goodsSubLabelCodes.push(childItem.dictValue as string);
            }
          }
        });
      } else {
        tempGoodsLabels.push({
          goodsLabelCode: childItem.parentValue as string,
          goodsSubLabelCodes: [childItem.dictValue as string],
        });
      }
      setGoodsLabels(tempGoodsLabels);
    },
    [goodsLabels]
  );

  const renderBizDictchildList = useCallback(
    (childList: BizDictItem[]) =>
      childList.map(itemInfo => (
        <div
          className={cx('label-item', {
            'selected-label-item': goodLabelsMap?.[itemInfo.parentValue as string]?.goodsSubLabelCodes?.includes(
              itemInfo.dictValue as string
            ),
          })}
          onClick={() => checkLabel(itemInfo)}
        >
          {itemInfo.dictValueName}
          <Icon className={cx('selected-icon')} component={tick} />
        </div>
      )),
    [checkLabel, goodLabelsMap]
  );

  const close = useCallback(() => {
    closeDrawer();
    setGoodsLabels([]);
  }, [closeDrawer]);

  const onSubmit = async () => {
    if (isEmpty(goodsLabels)) {
      await NewMarketService.GoodsLabelMgmtService.delete({ goodsId: +goodsId });
      setGoodsLabels([]);
      queryGoodsLabel();
    }

    onDrawerSubmit(goodsLabels);
    close();
  };

  return (
    <Drawer
      open={visible}
      title={t('Goods Label')}
      onClose={close}
      footer={
        <div className="text-right">
          <Button onClick={close} size="large" className="mr-md">
            {t('Cancel')}
          </Button>
          <Button
            onClick={() => {
              setGoodsLabels([]);
            }}
            size="large"
            className="mr-md"
          >
            {t('Clear All')}
          </Button>
          <Button onClick={onSubmit} type="primary" size="large">
            {t('Submit')}
          </Button>
        </div>
      }
    >
      <div>
        {(goodsLabelBizDict || [])
          .filter(item => (item?.childList as BizDictItem[])?.length > 0)
          .map(bizDictItem => (
            <div>
              <span>{bizDictItem.dictValueName}</span>
              <div className={cx('label-box')}>{renderBizDictchildList(bizDictItem?.childList || [])}</div>
            </div>
          ))}
      </div>
    </Drawer>
  );
};

export default GoodsLabelConfigurationDrawer;
