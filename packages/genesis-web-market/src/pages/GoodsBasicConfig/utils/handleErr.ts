import { isEmpty, isNil } from 'lodash-es';

import { PlanGroupResponse } from 'genesis-web-service/service-types/market-types/package/index';

import { calTableCardCls, plansCls, techProductIdCls } from '../components/CoveragePlanBundle/constants';

export const coveragePlanId = 'coverage-plan';
export type PlanGroup = PlanGroupResponse & { planGroupName?: string };

const _errorCls = 'market-ant4-formily-item-error';

/**
 * 校验 coverage plan 内是否有没有填写的字段, 有则标红显示并禁止直行后续逻辑
 * @returns  true 表示该区块数据不合法, 禁止进行后续逻辑的执行
 */
export function handleCoveragePlanBundleFieldsErr(formValues: PlanGroup[]) {
  // Reset all invalid fields red border
  removeAllRedBorder();

  // 目标表格内哪一行内的 tech product 字段是非法的
  let whichTableRowTechProductIdIsInvalid = -1;
  // 哪个表格内的 tech product 字段是非法的
  const techProductIdInvalidIdx = formValues?.findIndex(record => {
    whichTableRowTechProductIdIsInvalid = record.techProductPlans!.findIndex(plan => !plan.techProductId);
    return record.techProductPlans!.some(plan => !plan.techProductId);
  });

  // 目标表格内哪一行内的 coverage plan 字段是非法的
  let whichTableRowPlansIsInvalid = -1;
  // 哪个表格内的 coverage plan 字段是非法的
  const plansInvalidIdx = formValues?.findIndex(record => {
    whichTableRowPlansIsInvalid = record.techProductPlans!.findIndex(plan => isEmpty(plan.plans));
    return record.techProductPlans!.some(plan => isEmpty(plan.plans));
  });

  // 表格内 tech product 字段所在非法行的 class name
  const TableRowTechProductIdCls = `market-ant4-formily-array-table-row-${whichTableRowTechProductIdIsInvalid + 1}`;
  // 非法 tech product 字段所在的表格的 class name
  const whichTechProductTableCard = calTableCardCls(techProductIdInvalidIdx);
  // 获取非法的 tech product 字段的 dom
  const techProductIdNode = document.querySelector<HTMLElement>(
    `.${whichTechProductTableCard} .${TableRowTechProductIdCls} .${techProductIdCls}`
  );

  // 表格内 coverage plan 字段所在非法行的 class name
  const TableRowPlansCls = `market-ant4-formily-array-table-row-${whichTableRowPlansIsInvalid + 1}`;
  // 非法 tech product 字段所在的表格的 class name
  const whichPlansTableCard = calTableCardCls(plansInvalidIdx);
  // 获取非法的 coverage plan 字段的 dom
  const plansNode = document.querySelector<HTMLElement>(`.${whichPlansTableCard} .${TableRowPlansCls} .${plansCls}`);

  // 给非法的 tech product 字段添加红色边框
  if (techProductIdInvalidIdx !== -1 && !isNil(techProductIdInvalidIdx)) {
    techProductIdNode?.classList?.add?.(_errorCls);
  } else {
    removeAllTechProductIdRedBorder();
  }

  // 给非法的 coverage plan 字段添加红色边框
  if (plansInvalidIdx !== -1 && !isNil(plansInvalidIdx)) {
    plansNode?.classList?.add?.(_errorCls);
  } else {
    removeAllPlansRedBorder();
  }

  // 将 coverage plan 卡片滚动到视图内并禁止后续逻辑执行
  if (techProductIdInvalidIdx !== -1 || plansInvalidIdx !== -1) {
    document
      .querySelector(`#${coveragePlanId}`)
      ?.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
    return true;
  }
}

export function removeAllTechProductIdRedBorder() {
  const nodeList = document.querySelectorAll(`.${techProductIdCls}`);
  const nodeArray = Array.from(nodeList);
  nodeArray.forEach(node => {
    node.classList.remove(_errorCls);
  });
}

export function removeAllPlansRedBorder() {
  const nodeList = document.querySelectorAll(`.${plansCls}`);
  const nodeArray = Array.from(nodeList);
  nodeArray.forEach(node => {
    node.classList.remove(_errorCls);
  });
}

export function removeAllRedBorder() {
  removeAllTechProductIdRedBorder();
  removeAllPlansRedBorder();
}
