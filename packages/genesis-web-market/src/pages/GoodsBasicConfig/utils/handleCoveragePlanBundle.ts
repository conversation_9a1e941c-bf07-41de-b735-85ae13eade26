import { cloneDeep } from 'lodash-es';

import {
  PackageQueryResponseDTO,
  PlanGroupRequest,
} from 'genesis-web-service/service-types/market-types/package/index';

import { NewMarketService } from '@market/services/market/market.service.new';

import { type PlanGroup } from './handleErr';

interface IParams {
  allPackageCodes: PackageQueryResponseDTO[];
  formValues: PlanGroup[];
  goodsId: number;
}

/**
 * 提交 bundle coverage plan 数据
 */
export async function handleCoveragePlanBundle({ formValues, allPackageCodes, goodsId }: IParams) {
  // 组装后端需要的数据结构
  const backendData = formValues?.map(item => {
    const { planGroupName, ...rest } = item;
    const techProductPlans = cloneDeep(item.techProductPlans)?.map(plan => {
      const { isMandatory, techProductId } = plan;
      const targetPackage = allPackageCodes.find(pkg => pkg.packageId === techProductId);

      return {
        ...plan,
        isMandatory: isMandatory ? 'YES' : 'NO',
        packageCode: targetPackage?.packageCode,
        packageName: targetPackage?.packageName,
      };
    });

    return { ...rest, techProductPlans };
  });

  await NewMarketService.GoodsPlanMgmtService.savePlanGroup(goodsId, backendData as PlanGroupRequest[]);
}
