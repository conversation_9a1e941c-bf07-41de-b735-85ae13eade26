import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Location, useLocation, useNavigate } from 'react-router-dom';

import { Button, Form, Input, Layout, Space } from 'antd';

import { uniqBy } from 'lodash-es';

import { Select, message } from '@zhongan/nagrand-ui';

import { CalculatorService } from 'genesis-web-service';

import { DetailPageMode } from '@market/common/interface';
import { FMarketHeader } from '@market/components/F-Market-Header';
import { useGoodsList } from '@market/hook/goods.service';
import CoveragePlanSection from '@market/pages/QuickQuotation/CoveragePlanSection';
import DefaultValueSection from '@market/pages/QuickQuotation/DefaultValueSection';
import { NewMarketService } from '@market/services/market/market.service.new';

import styles from './styles.module.scss';

const { Content, Footer } = Layout;

export interface FileInfo {
  name: string;
  uploadTime?: string;
  user?: string;
}

export interface QuotationFormType {
  id: number;
  quoteCode: string;
  goodsCode: string;
  goodsVersion: string;
  matrixTableCode: string;
  defaultValueMatrixTableCode: string;
  parametersResult: string[];
  parametersResult2: string[];
  fileInfo: FileInfo;
  fileInfo2: FileInfo;
}

const QuickQuotation = () => {
  const navigate = useNavigate();
  const goodsList = useGoodsList();
  const location = useLocation() as unknown as Location<{ mode: DetailPageMode }>;
  const [form] = Form.useForm<QuotationFormType>();
  const [t] = useTranslation(['market', 'common']);
  const [mode, setMode] = useState<DetailPageMode>();

  const goodsOptions = useMemo(() => {
    const goodsBasicInfoList = goodsList.map(({ goodsBasicInfo }) => goodsBasicInfo);
    return uniqBy(goodsBasicInfoList, 'goodsCode').map(goods => ({
      label: goods.goodsCode,
      value: goods.goodsCode,
    }));
  }, [goodsList]);

  useEffect(() => {
    setMode(location?.state?.mode ?? DetailPageMode.view);

    const id = location?.search?.split('=')?.[1];
    if (id) {
      NewMarketService.QuoteConfiguredControllerService.query(Number(id)).then(res => {
        const matrixTableCode = res.quoteRecommendationPlanMatrixTableCode;
        const defaultValueMatrixTableCode = res.quoteDefaultValueMatrixTableCode;
        form.setFieldsValue({
          ...res,
          matrixTableCode,
          defaultValueMatrixTableCode,
        });

        if (matrixTableCode) {
          CalculatorService.queryMatrixTable(matrixTableCode).then(matrixRes => {
            if (matrixRes) {
              if (matrixRes.scope === 'LIBRARY') {
                setMode('view');
              }
              form.setFieldValue('parametersResult', matrixRes.resultCodeList);
              form.setFieldValue('fileInfo', {
                name: matrixRes.fileName,
              });
            }
          });
        }

        if (defaultValueMatrixTableCode) {
          CalculatorService.queryMatrixTable(defaultValueMatrixTableCode).then(defaultMatrixRes => {
            if (defaultMatrixRes) {
              if (defaultMatrixRes.scope === 'LIBRARY') {
                setMode('view');
              }
              form.setFieldValue(
                'parametersResult2',
                defaultMatrixRes.resultSchemaFactorList?.map(item => item.jsonPath)
              );
              form.setFieldValue('fileInfo2', {
                name: defaultMatrixRes.fileName,
              });
            }
          });
        }
      });
    }
  }, [form, location]);

  const saveQuoteConfig = useCallback(() => {
    form.validateFields().then(values => {
      NewMarketService.QuoteConfiguredControllerService.save(values).then(res => {
        if (res.quoteRecommendationPlanMatrixTableCode) {
          form.setFieldsValue({
            ...res,
            matrixTableCode: res.quoteRecommendationPlanMatrixTableCode,
            defaultValueMatrixTableCode: res.quoteDefaultValueMatrixTableCode,
          });
        }
        message.success(t('Save Successful.'));
      });
    });
  }, [form, t]);

  return (
    <Layout className="h-full">
      <FMarketHeader backPath="/market/quotation/management" subMenu="Quick_Quotation" />
      <Content className={styles.content}>
        <Form form={form} name="quotationForm" autoComplete="off" layout="vertical">
          <div className="grid grid-cols-3 mb-6">
            <Form.Item noStyle name="id" />
            <Form.Item noStyle name="matrixTableCode" />
            <Form.Item noStyle name="defaultValueMatrixTableCode" />
            <Form.Item noStyle name={['fileInfo', 'name']} />
            <Form.Item noStyle name={['fileInfo', 'uploadTime']} />
            <Form.Item noStyle name={['fileInfo', 'user']} />
            <Form.Item noStyle name={['fileInfo2', 'name']} />
            <Form.Item noStyle name={['fileInfo2', 'uploadTime']} />
            <Form.Item noStyle name={['fileInfo2', 'user']} />

            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) => (
                <Form.Item name="quoteCode" label="Quotation Code" rules={[{ required: true, message: '' }]}>
                  <Input
                    disabled={getFieldValue('matrixTableCode')}
                    placeholder={t('Please input')}
                    style={{ width: 280 }}
                  />
                </Form.Item>
              )}
            </Form.Item>

            <Form.Item name="goodsCode" label="Goods Code" rules={[{ required: true, message: '' }]}>
              <Select
                showSearch
                style={{ width: 280 }}
                options={goodsOptions}
                disabled={mode === 'view'}
                onChange={() => {
                  form.resetFields(['goodsVersion']);
                }}
              />
            </Form.Item>

            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) => (
                <Form.Item name="goodsVersion" label="Goods Version" rules={[{ required: true, message: '' }]}>
                  <Select
                    style={{ width: 280 }}
                    disabled={mode === 'view'}
                    placeholder={t('Please select goods first')}
                    options={goodsList
                      .filter(({ goodsBasicInfo }) => goodsBasicInfo.goodsCode === getFieldValue('goodsCode'))
                      .map(({ goodsBasicInfo }) => ({
                        label: goodsBasicInfo.goodsVersion,
                        value: goodsBasicInfo.goodsVersion,
                      }))}
                  />
                </Form.Item>
              )}
            </Form.Item>
          </div>

          <Form.Item noStyle shouldUpdate>
            {({ getFieldValue }) =>
              (getFieldValue('matrixTableCode') as string) && (
                <Space direction="vertical" size="large">
                  <CoveragePlanSection quotationForm={form} mode={mode} />

                  <DefaultValueSection quotationForm={form} mode={mode} />
                </Space>
              )
            }
          </Form.Item>
        </Form>
      </Content>

      <Footer className={styles.footer}>
        {mode !== 'view' && (
          <Fragment>
            <Button onClick={saveQuoteConfig} type="primary">
              {t('Save')}
            </Button>
            <Button
              onClick={() => {
                navigate('/market/quotation/management');
              }}
            >
              {t('Cancel')}
            </Button>
          </Fragment>
        )}
      </Footer>
    </Layout>
  );
};

export default QuickQuotation;
