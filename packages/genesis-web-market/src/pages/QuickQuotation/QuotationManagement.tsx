import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { Form, message } from 'antd';

import { useRequest } from 'ahooks';
import { uniqBy } from 'lodash-es';
import { QuoteGoodsConfigurationDTO } from 'market-types';

import {
  AddNewButton,
  DeleteAction,
  EditAction,
  FieldType,
  OperationContainer,
  QueryForm,
  QueryResultContainer,
  Table,
  TableActionsContainer,
  ViewAction,
} from '@zhongan/nagrand-ui';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import { dateFormatInstance } from 'genesis-web-shared/lib/l10n';
import { selectTimeZone } from 'genesis-web-shared/lib/redux';

import { useGoodsList } from '@market/hook/goods.service';
import { usePermission } from '@market/hook/permission';
import { NewMarketService } from '@market/services/market/market.service.new';

interface QueryQuotationFormType {
  goodsCodeAndVersion: string;
  quoteCode: string;
}

const QuotationManagement = () => {
  const navigate = useNavigate();
  const goodsList = useGoodsList();
  const [t] = useTranslation(['market', 'common']);
  const editable = !!usePermission('quick.quotation.edit');

  const [form] = Form.useForm();
  const { defaultZoneInfo } = useSelector(selectTimeZone);
  const [searchQuery, setSearchQuery] = useRouterState<{
    [key: string]: any;
    current: number;
    pageSize: number;
  }>({
    current: 0,
    pageSize: 10,
  });

  const goodsOptions = useMemo(() => {
    const goodsBasicInfoList = goodsList.map(({ goodsBasicInfo }) => goodsBasicInfo);
    return uniqBy(goodsBasicInfoList, 'goodsCode').map(goods => ({
      value: `${goods.goodsCode}-${goods.goodsVersion}`,
      label: `${goods.goodsCode}-${goods.goodsVersion}`,
    }));
  }, [goodsList]);

  const {
    data,
    run: searchResultRun,
    loading,
  } = useRequest(
    () => {
      const { current, pageSize, ...condition } = searchQuery;
      return NewMarketService.QuoteConfiguredControllerService.search(condition, {
        page: current,
        size: pageSize,
      }).then(res => ({
        dataSource: res.content!,
        total: res.totalElements!,
      }));
    },
    {
      refreshDeps: [searchQuery],
      debounceWait: 300,
      onError: error => {
        message.error(error.message);
      },
    }
  );

  useEffect(() => {
    searchResultRun();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery]);

  const jumpPage = useCallback(
    (mode: string, id?: number) => {
      navigate(id ? `/market/quotation/detail?id=${id}` : '/market/quotation/detail', {
        state: { mode },
      });
    },
    [navigate]
  );

  useEffect(() => {
    form.setFieldsValue(searchQuery);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div>
      <QueryForm
        needSearchAfterClear
        title={t('Quick Quotation') as string}
        loading={loading}
        queryFields={[
          {
            col: 8,
            type: FieldType.Select,
            label: t('Goods Code') as string,
            key: 'goodsCodeAndVersion',
            extraProps: {
              options: goodsOptions,
            },
          },
          {
            col: 8,
            type: FieldType.Input,
            key: 'quoteCode',
            label: t('Quotation Code') as string,
          },
        ]}
        onSearch={values => {
          const { goodsCodeAndVersion, quoteCode } = values as unknown as QueryQuotationFormType;
          if (goodsCodeAndVersion) {
            const [goodsCode, goodsVersion] = goodsCodeAndVersion.split('-');
            setSearchQuery({
              goodsCodeAndVersion,
              goodsCode,
              goodsVersion,
              quoteCode,
              current: 0,
              pageSize: 10,
            });
          } else {
            setSearchQuery({
              quoteCode,
              current: 0,
              pageSize: 10,
            });
          }
        }}
        formProps={{
          form,
        }}
      />

      <QueryResultContainer>
        <OperationContainer>
          <OperationContainer.Left>
            {editable ? (
              <AddNewButton type="primary" ghost onClick={() => jumpPage('add')}>
                {t('Add New')}
              </AddNewButton>
            ) : null}
          </OperationContainer.Left>
        </OperationContainer>

        <Table<QuoteGoodsConfigurationDTO>
          size="middle"
          dataSource={data?.dataSource}
          columns={[
            {
              title: t('Quotation Code') as string,
              dataIndex: 'quoteCode',
            },
            {
              title: t('Goods Code') as string,
              dataIndex: 'goodsCode',
            },
            {
              title: t('Goods Version') as string,
              dataIndex: 'goodsVersion',
            },
            {
              title: t('Create Time') as string,
              dataIndex: 'gmtCreated',
              render: (text: string) => {
                if (defaultZoneInfo && text) {
                  // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                  return `${dateFormatInstance.getDateTimeString(text)} (${Object.values(defaultZoneInfo)[0]})`;
                }
                return text;
              },
            },
            {
              title: t('Update Time') as string,
              dataIndex: 'gmtModified',
              render: (text: string) => {
                if (defaultZoneInfo && text) {
                  // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                  return `${dateFormatInstance.getDateTimeString(text)} (${Object.values(defaultZoneInfo)[0]})`;
                }
                return text;
              },
            },
            {
              title: t('Actions') as string,
              key: 'actions',
              align: 'right',
              fixed: 'right',
              render: (_, record) => (
                <TableActionsContainer>
                  <ViewAction onClick={() => jumpPage('view', record.id)} />
                  {editable && <EditAction onClick={() => jumpPage('edit', record.id)} />}
                  {editable && (
                    <DeleteAction
                      doubleConfirmPlacement="left"
                      doubleConfirmType="popconfirm"
                      deleteConfirmContent={t('Are you sure to clear this section?') as string}
                      onClick={() => {
                        NewMarketService.QuoteConfiguredControllerService.delete(record.id).then(() => {
                          searchResultRun();
                        });
                      }}
                    />
                  )}
                </TableActionsContainer>
              ),
            },
          ]}
          loading={loading}
          emptyType="icon"
          pagination={{
            total: data?.total,
            pageSize: searchQuery.pageSize,
            current: searchQuery.current + 1,
            onChange: (page: number, pageSize: number) => {
              setSearchQuery({
                ...searchQuery,
                current: page - 1,
                pageSize,
              });
            },
          }}
        />
      </QueryResultContainer>
    </div>
  );
};

export default QuotationManagement;
