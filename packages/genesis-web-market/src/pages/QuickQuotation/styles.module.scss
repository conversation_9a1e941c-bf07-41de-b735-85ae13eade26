.content {
  background: $white;
  padding: 16px 24px;

  button {
    font-weight: 500;
  }

  :global {
    .#{$market-prefix}-space {
      display: flex;
    }

    .#{$market-prefix}-form-item-label {
      font-weight: 500;
    }

    .#{$market-prefix}-form-item {
      margin-bottom: 0;
    }
  }
}

.footer {
  width: 100%;
  position: absolute;
  bottom: 0;
  height: 72px;
  padding: 0 24px;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  gap: 16px;
  background: $white;
  border-top: 1px solid $default-color-bg;
  border-left: 1px solid $default-color-bg;

  button {
    height: 40px;
    font-weight: 500;
  }
}

.hide-form-item {
  display: none !important;
}
