import { FC, Fragment, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, FormInstance, Space, Upload, UploadProps } from 'antd';

import moment from 'moment';
import { v4 as uuid } from 'uuid';

import { Icon, Modal, Select, UploadFileItem, message } from '@zhongan/nagrand-ui';

import { CalculatorService } from 'genesis-web-service/lib/calculator/calculator.service';

import { BizTopic } from '@market/common/enums';
import { PolicySchema, SchemaField } from '@market/common/schema.interface';
import { FileInfo, QuotationFormType } from '@market/pages/QuickQuotation/index';
import { NewProductService } from '@market/services/product/product.service.new';
import { downloadFile } from '@market/util';
import { transformSchema } from '@market/utils/schemaUtils';

interface Props {
  quotationForm: FormInstance<QuotationFormType>;
  mode: string;
}

interface ParameterFormType {
  parameters: string[];
  results: string[];
}

const SAVE_MATRIX_TABLE_PARAMS = {
  description: '',
  matrixTableType: BizTopic.QuickQuotationMatrix,
  productCategoryCode: '',
};

const DefaultValueSection: FC<Props> = ({ quotationForm, mode }) => {
  const [open, setOpen] = useState(false);
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm<ParameterFormType>();
  const [options, setOptions] = useState<SchemaField[]>([]);

  useEffect(() => {
    if (!options.length) {
      NewProductService.ProductSchemaMgmtService.schemaFieldsWithCalculationFlag({
        productSubCategoryId: 100,
        applicableFormulaCategory: +BizTopic.QuickQuotationMatrix,
      }).then(res => {
        const factors = transformSchema(res as PolicySchema);
        if (factors.parameterList?.length) {
          setOptions(factors.parameterList);
        }
      });
    }
  }, [options.length]);

  const uploadProps: UploadProps = {
    method: 'POST',
    name: 'multipartFile',
    accept: '.xls, .xlsx',
    showUploadList: false,
    action: '/api/calculator/matrixTable/upload',
    data: {
      matrixTableCode: quotationForm.getFieldValue('defaultValueMatrixTableCode'),
    },
    onChange(info) {
      if (info.file.status === 'done') {
        quotationForm.setFieldValue('fileInfo2', {
          name: info.file.name,
          uploadTime: info.file.lastModifiedDate ? moment(info.file.lastModifiedDate).format('YYYY-MM-DD') : '',
        });
        message.success(t('Upload Successful.'));
      }
      if (info.file.status === 'error') {
        message.error(t('Upload Fail!'));
      }
    },
  };

  const closeModal = useCallback(() => {
    form.resetFields();
    setOpen(false);
  }, [form]);

  const downloadMatrixTableFile = useCallback(() => {
    const { defaultValueMatrixTableCode } = quotationForm.getFieldsValue();
    CalculatorService.queryMatrixTableRevision(defaultValueMatrixTableCode).then(res => {
      const index = res.length ? res.length - 1 : 0;
      downloadFile(
        `/api/calculator/v2/matrix-table/download`,
        {
          matrixTableCode: defaultValueMatrixTableCode,
          matrixTableRevisionId: res?.[index]?.id ?? -1,
        },
        'post'
      );
    });
  }, [quotationForm]);

  const downloadTemplate = useCallback(
    (results?: string[]) => {
      const { defaultValueMatrixTableCode } = quotationForm.getFieldsValue();
      downloadFile(
        `/api/calculator/matrixTable/generateTemplate/${defaultValueMatrixTableCode}`,
        {},
        'get',
        (res: string) => {
          if (res === 'success') {
            if (results) {
              quotationForm.setFieldValue('parametersResult2', results);
            }
          }
        }
      );
    },
    [quotationForm]
  );

  const createMatrixTable = useCallback(() => {
    form.validateFields().then(values => {
      const { defaultValueMatrixTableCode } = quotationForm.getFieldsValue();
      const schemaFactorsList = values.parameters.map(item => {
        const match = options.find(opt => opt.key === item);
        return {
          ...match,
          factorSourceType: 1,
          factorUuid: uuid(),
          factorCode: match?.code,
          jsonPath: match?.showText,
        };
      });
      const resultSchemaFactorList = values.results.map(item => {
        const match = options.find(opt => opt.key === item);
        return {
          ...match,
          factorSourceType: 1,
          factorUuid: uuid(),
          factorCode: match?.code,
          jsonPath: match?.showText,
        };
      });
      CalculatorService.saveMatrixTable({
        ...SAVE_MATRIX_TABLE_PARAMS,
        matrixTableCode: defaultValueMatrixTableCode,
        schemaFactorsList,
        resultSchemaFactorList,
        matrixTableName: defaultValueMatrixTableCode,
        factorCodeList: schemaFactorsList.map(factor => factor.factorCode ?? ''),
        resultCodeList: resultSchemaFactorList.map(factor => factor.factorCode ?? ''),
      })
        .then(() => {
          downloadTemplate(values.results);
        })
        .then(() => {
          closeModal();
        });
    });
  }, [closeModal, downloadTemplate, form, options, quotationForm]);

  return (
    <Space direction="vertical">
      <div className="font-[700]">{t('Default Value-Matrix Table')}</div>

      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) =>
          (getFieldValue('parametersResult2') as string[])?.length > 0 && (
            <Form.Item name="parametersResult2" label={t('Parameters(Result)')} rules={[{ required: true }]}>
              <Select
                disabled
                mode="multiple"
                options={options.map(item => ({
                  value: item.key,
                  label: item.showText,
                }))}
                style={{ width: 600 }}
              />
            </Form.Item>
          )
        }
      </Form.Item>

      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) =>
          (getFieldValue('parametersResult2') as string[])?.length > 0 ? (
            <a onClick={() => downloadTemplate()} className="!underline font-[500]">
              {t('Download Template')}
            </a>
          ) : (
            <div>
              <span className="text-@divider-color font-[500] mr-1">{t('Please')}</span>
              <a onClick={() => setOpen(true)} className="!underline font-[500]">
                {t('Download Template')}
              </a>
              <span className="text-@divider-color font-[500] ml-1">{t('First')}</span>
            </div>
          )
        }
      </Form.Item>

      <Form.Item noStyle shouldUpdate={() => true}>
        {({ getFieldValue }) => {
          const fileInfo2 = getFieldValue('fileInfo2') as FileInfo;
          const parametersResult = getFieldValue('parametersResult2') as string[];
          // 必须先download template成功，才能upload
          const canUpload = parametersResult && parametersResult.length > 0;

          return fileInfo2?.name ? (
            <UploadFileItem
              style={{ width: 600 }}
              fileName={fileInfo2.name}
              needPreview={false}
              hoverInfoList={
                mode === 'view'
                  ? [
                      {
                        icon: <Icon type="download" />,
                        onClick: downloadMatrixTableFile,
                      },
                    ]
                  : [
                      {
                        icon: (
                          <Upload {...uploadProps}>
                            <Icon type="reload" />
                          </Upload>
                        ),
                      },
                      {
                        icon: <Icon type="download" />,
                        onClick: downloadMatrixTableFile,
                      },
                    ]
              }
            />
          ) : (
            <Upload {...uploadProps} disabled={!canUpload}>
              <Button disabled={!canUpload} icon={<Icon type="upload" />}>
                {t('Upload')}
              </Button>
            </Upload>
          );
        }}
      </Form.Item>

      <Modal
        closable={false}
        width={724}
        open={open}
        okText="Download"
        onCancel={closeModal}
        onOk={createMatrixTable}
        title={t('Download Template')}
        okButtonProps={{
          icon: <Icon type="download" />,
        }}
      >
        <Form form={form} layout="vertical" autoComplete="off" name="parameterForm">
          <Form.Item name="parameters" label={t('Parameters')} rules={[{ required: true, message: '' }]}>
            <Select
              showSearch
              allowClear
              mode="multiple"
              selectAll={false}
              options={options.map(item => ({
                value: item.key,
                label: item.showText,
              }))}
              style={{ width: 660 }}
            />
          </Form.Item>

          <Form.Item name="results" label={t('Parameters(Result)')} rules={[{ required: true, message: '' }]}>
            <Select
              showSearch
              allowClear
              mode="multiple"
              selectAll={false}
              options={options.map(item => ({
                value: item.key,
                label: item.showText,
              }))}
              style={{ width: 660 }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  );
};

export default DefaultValueSection;
