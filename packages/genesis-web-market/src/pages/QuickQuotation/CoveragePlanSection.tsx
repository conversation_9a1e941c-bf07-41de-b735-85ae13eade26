import { FC, useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, FormInstance, Space, Upload, UploadProps } from 'antd';

import moment from 'moment';
import { v4 as uuid } from 'uuid';

import { Icon, Modal, Select, UploadFileItem, message } from '@zhongan/nagrand-ui';

import { CalculatorService } from 'genesis-web-service/lib/calculator/calculator.service';

import { BizTopic } from '@market/common/enums';
import { PolicySchema, SchemaField } from '@market/common/schema.interface';
import { FileInfo, QuotationFormType } from '@market/pages/QuickQuotation/index';
import { NewProductService } from '@market/services/product/product.service.new';
import { downloadFile } from '@market/util';
import { transformSchema } from '@market/utils/schemaUtils';

interface Props {
  quotationForm: FormInstance<QuotationFormType>;
  mode: string;
}

interface ParameterFormType {
  parameters: string[];
  results: string[];
}

const SAVE_MATRIX_TABLE_PARAMS = {
  description: '',
  matrixTableType: BizTopic.QuickQuotationMatrix,
  productCategoryCode: '',
};

const RESULT_OPTIONS = [
  { value: 'planCode', label: 'planCode' },
  { value: 'productCode', label: 'productCode' },
  { value: 'liabilityId', label: 'liabilityId' },
  { value: 'sumInsured', label: 'sumInsured' },
  { value: 'premium', label: 'Premium' },
];

const CoveragePlanSection: FC<Props> = ({ quotationForm, mode }) => {
  const [open, setOpen] = useState(false);
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm<ParameterFormType>();
  const [options, setOptions] = useState<SchemaField[]>([]);

  useEffect(() => {
    if (!options.length) {
      NewProductService.ProductSchemaMgmtService.schemaFieldsWithCalculationFlag({
        productSubCategoryId: 100,
        applicableFormulaCategory: +BizTopic.QuickQuotationMatrix,
      }).then(res => {
        const factors = transformSchema(res as PolicySchema);
        if (factors.parameterList?.length) {
          setOptions(factors.parameterList);
        }
      });
    }
  }, [options.length]);

  const uploadProps: UploadProps = {
    method: 'POST',
    name: 'multipartFile',
    accept: '.xls, .xlsx',
    showUploadList: false,
    action: '/api/calculator/matrixTable/upload',
    data: {
      matrixTableCode: quotationForm.getFieldValue('matrixTableCode'),
    },
    onChange(info) {
      if (info.file.status === 'done') {
        quotationForm.setFieldValue('fileInfo', {
          name: info.file.name,
          uploadTime: info.file.lastModifiedDate ? moment(info.file.lastModifiedDate).format('YYYY-MM-DD') : '',
        });
        message.success(t('Upload Successful.'));
      }
      if (info.file.status === 'error') {
        message.error(t('Upload Fail!'));
      }
    },
  };

  const closeModal = useCallback(() => {
    form.resetFields();
    setOpen(false);
  }, [form]);

  const downloadMatrixTableFile = useCallback(() => {
    const { matrixTableCode } = quotationForm.getFieldsValue();
    CalculatorService.queryMatrixTableRevision(matrixTableCode).then(res => {
      const index = res.length ? res.length - 1 : 0;
      downloadFile(
        `/api/calculator/v2/matrix-table/download`,
        {
          matrixTableCode,
          matrixTableRevisionId: res?.[index]?.id ?? -1,
        },
        'post'
      );
    });
  }, [quotationForm]);

  const downloadTemplate = useCallback(
    (results?: string[]) => {
      const { matrixTableCode } = quotationForm.getFieldsValue();
      downloadFile(`/api/calculator/matrixTable/generateTemplate/${matrixTableCode}`, {}, 'get', (res: string) => {
        if (res === 'success') {
          if (results) {
            quotationForm.setFieldValue('parametersResult', results);
          }
        }
      });
    },
    [quotationForm]
  );

  const createMatrixTable = useCallback(() => {
    form.validateFields().then(values => {
      const { matrixTableCode } = quotationForm.getFieldsValue();
      const schemaFactorsList = values.parameters.map(item => {
        const match = options.find(opt => opt.key === item);
        return {
          ...match,
          factorSourceType: 1,
          factorUuid: uuid(),
          factorCode: match?.code,
          jsonPath: match?.showText,
        };
      });
      CalculatorService.saveMatrixTable({
        ...SAVE_MATRIX_TABLE_PARAMS,
        matrixTableCode,
        schemaFactorsList,
        matrixTableName: matrixTableCode,
        factorCodeList: schemaFactorsList.map(factor => factor.factorCode ?? ''),
        resultCodeList: values.results,
      })
        .then(() => {
          downloadTemplate(values.results);
        })
        .then(() => {
          closeModal();
        });
    });
  }, [closeModal, downloadTemplate, form, options, quotationForm]);

  return (
    <Space direction="vertical">
      <div className="font-[700]">{t('Plan Recommendation-Matrix Table')}</div>

      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) =>
          (getFieldValue('parametersResult') as string[])?.length > 0 && (
            <Form.Item name="parametersResult" label={t('Parameters(Result)')} rules={[{ required: true }]}>
              <Select disabled mode="multiple" options={[]} style={{ width: 600 }} />
            </Form.Item>
          )
        }
      </Form.Item>

      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) =>
          (getFieldValue('parametersResult') as string[])?.length > 0 ? (
            <a onClick={() => downloadTemplate()} className="!underline font-[500]">
              {t('Download Template')}
            </a>
          ) : (
            <div>
              <span className="text-@divider-color font-[500] mr-1">{t('Please')}</span>
              <a onClick={() => setOpen(true)} className="!underline font-[500]">
                {t('Download Template')}
              </a>
              <span className="text-@divider-color font-[500] ml-1">{t('First')}</span>
            </div>
          )
        }
      </Form.Item>

      <Form.Item noStyle shouldUpdate={() => true}>
        {({ getFieldValue }) => {
          const fileInfo = getFieldValue('fileInfo') as FileInfo;
          const parametersResult = getFieldValue('parametersResult') as string[];
          // 必须先download template成功，才能upload
          const canUpload = parametersResult && parametersResult.length > 0;

          return fileInfo?.name ? (
            <UploadFileItem
              style={{ width: 600 }}
              fileName={fileInfo.name}
              needPreview={false}
              hoverInfoList={
                mode === 'view'
                  ? [
                      {
                        icon: <Icon type="download" />,
                        onClick: downloadMatrixTableFile,
                      },
                    ]
                  : [
                      {
                        icon: (
                          <Upload {...uploadProps}>
                            <Icon type="reload" />
                          </Upload>
                        ),
                      },
                      {
                        icon: <Icon type="download" />,
                        onClick: downloadMatrixTableFile,
                      },
                    ]
              }
            />
          ) : (
            <Upload {...uploadProps} disabled={!canUpload}>
              <Button disabled={!canUpload} icon={<Icon type="upload" />}>
                {t('Upload')}
              </Button>
            </Upload>
          );
        }}
      </Form.Item>

      <Modal
        width={724}
        open={open}
        closable={false}
        okText="Download"
        onCancel={closeModal}
        onOk={createMatrixTable}
        title={t('Download Template')}
        okButtonProps={{
          icon: <Icon type="download" />,
        }}
      >
        <Form form={form} layout="vertical" autoComplete="off" name="parameterForm">
          <Form.Item name="parameters" label={t('Parameters')} rules={[{ required: true, message: '' }]}>
            <Select
              showSearch
              allowClear
              mode="multiple"
              selectAll={false}
              options={options.map(item => ({
                value: item.key,
                label: item.showText,
              }))}
              style={{ width: 660 }}
            />
          </Form.Item>

          <Form.Item name="results" label={t('Parameters(Result)')} rules={[{ required: true, message: '' }]}>
            <Select
              showSearch
              allowClear
              mode="multiple"
              selectAll={false}
              options={RESULT_OPTIONS}
              style={{ width: 660 }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  );
};

export default CoveragePlanSection;
