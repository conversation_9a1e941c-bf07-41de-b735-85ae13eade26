import { cloneDeep, orderBy } from 'lodash-es';

import { AgentFormulaInfo } from 'genesis-web-service/lib/market';

import { FEChannel } from '../../common/interface';
import { t } from '../../i18n';

/**
 * channel 与 sub channel 回显逻辑
 */
export const renderTableSalesChannel = (
  totalChannelList: FEChannel[],
  partnerCode: string,
  subPartnerCode?: string
) => {
  const channeName = totalChannelList.find(item => item.code === partnerCode)?.name || t('- -');
  const subChanneName = totalChannelList.find(item => item.code === subPartnerCode)?.name || '';
  if (subChanneName) {
    return `${channeName} - ${subChanneName}`;
  }
  return `${channeName}`;
};

// 对接口返回的表格数据进行合并单元格处理
export const partitionAgentFormulaList = (
  tableData: AgentFormulaInfo[],
  setTableList: (arr: AgentFormulaInfo[]) => void
) => {
  const temptTableData = cloneDeep(tableData).map((agentItem: AgentFormulaInfo, index: number) => ({
    ...agentItem,
    index,
    feeType: `${agentItem.feeType}`,
    formulaCategory: `${agentItem.formulaCategory}`,
    formulaSubCategory: `${agentItem.formulaSubCategory}`,
    agentCategory: `${agentItem.agentCategory}`,
    formulaOrder: agentItem?.formulaOrder,
  }));

  temptTableData.forEach(item => {
    if (item.formulaOrder === 1) {
      item.rowSpan = temptTableData.filter(agentItem => agentItem.agentCategory === item.agentCategory).length;
    } else {
      item.rowSpan = 0;
    }
  });

  setTableList(orderBy(temptTableData, ['agentCategory', 'formulaOrder'], ['asc', 'asc']));
};
