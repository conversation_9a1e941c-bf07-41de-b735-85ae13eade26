import React, { Component, Fragment, createRef } from 'react';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';

import { QuestionCircleOutlined } from '@ant-design/icons';
import { Button, Checkbox, Col, Form, InputNumber, Radio, Row, TimePicker, Tooltip } from 'antd';

import { cloneDeep, isEmpty, pick } from 'lodash-es';
import moment from 'moment';

import { lessVars } from '@zhongan/nagrand-ui';

import { TimeFormatEnum, YesNoType } from 'genesis-web-service';
import { ChannelService } from 'genesis-web-service/lib/channel/channel.service';
import { ruleService } from 'genesis-web-service/lib/rule-engine/rule.service';
import { withL10n } from 'genesis-web-shared/lib/l10n';

import { GoodsCategoryItemExtend1 } from '@market/common/enums';
import { SalesDistrictType } from '@market/common/salesAttributes.interface';
import FTimezonePicker from '@market/components/F-Timezone-Picker';
import GeneralSelect from '@market/components/GeneralSelect';
import Section from '@market/components/Section/section';
import { NewMarketService } from '@market/services/market/market.service.new';
import { formErrorHandler } from '@market/utils/formUtils';

import Input from '../../../components/Input';
import TwoItemSort from '../../../components/TwoItemSort/twoItemSort';
import { mapBizDictToOptions } from '../../../utils/enum';
import PolicyEffectiveWithoutCollectionNB from '../components/PolicyEffectiveWithoutCollectionNB';
import PolicyEffectiveWithoutCollectionRenewal from '../components/PolicyEffectiveWithoutCollectionRenewal';

const monthList = [
  {
    itemExtend1: '1',
    itemName: '1st',
  },
  {
    itemExtend1: '2',
    itemName: '2nd',
  },
  {
    itemExtend1: '3',
    itemName: '3rd',
  },
  {
    itemExtend1: '4',
    itemName: '4th',
  },
  {
    itemExtend1: '5',
    itemName: '5th',
  },
  {
    itemExtend1: '6',
    itemName: '6th',
  },
  {
    itemExtend1: '7',
    itemName: '7th',
  },
  {
    itemExtend1: '8',
    itemName: '8th',
  },
  {
    itemExtend1: '9',
    itemName: '9th',
  },
  {
    itemExtend1: '10',
    itemName: '10th',
  },
  {
    itemExtend1: '11',
    itemName: '11th',
  },
  {
    itemExtend1: '12',
    itemName: '12th',
  },
  {
    itemExtend1: '13',
    itemName: '13th',
  },
  {
    itemExtend1: '14',
    itemName: '14th',
  },
  {
    itemExtend1: '15',
    itemName: '15th',
  },
  {
    itemExtend1: '16',
    itemName: '16th',
  },
  {
    itemExtend1: '17',
    itemName: '17th',
  },
  {
    itemExtend1: '18',
    itemName: '18th',
  },
  {
    itemExtend1: '19',
    itemName: '19th',
  },
  {
    itemExtend1: '20',
    itemName: '20th',
  },
  {
    itemExtend1: '21',
    itemName: '21th',
  },
  {
    itemExtend1: '22',
    itemName: '22th',
  },
  {
    itemExtend1: '23',
    itemName: '23th',
  },
  {
    itemExtend1: '24',
    itemName: '24th',
  },
  {
    itemExtend1: '25',
    itemName: '25th',
  },
  {
    itemExtend1: '26',
    itemName: '26th',
  },
  {
    itemExtend1: '27',
    itemName: '27th',
  },
  {
    itemExtend1: '28',
    itemName: '28th',
  },
  {
    itemExtend1: '29',
    itemName: '29th',
  },
  {
    itemExtend1: '30',
    itemName: '30th',
  },
  {
    itemExtend1: '31',
    itemName: '31th',
  },
];

class FormBox extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: {},
      channelList: [
        {
          channelCode: undefined,
          relationshipCode: undefined,
        },
      ],
      tenantInfo: {},
      ruleList: [],
      ruleCategoryOptions: [],
      goodsInfo: {},
    };

    this.renderNewBusinessSection = this.renderNewBusinessSection.bind(this);
    this.renderInsuringProcessSection = this.renderInsuringProcessSection.bind(this);
    this.formRef = createRef();
  }

  componentDidMount() {
    if (typeof this.props.myRef === 'function') {
      this.props.myRef(this);
    }

    if (this.props.tenant) {
      ChannelService.queryTenantConfig(this.props.tenant).then(res => {
        this.setState({
          tenantInfo: res,
        });
      });
    }

    ruleService.queryRulesUnderCategory(['POLICY_EFF_WO_COLL'], true).then(res => {
      if (res) {
        this.setState({
          ruleList: res.map(ruleItem => ({
            label: ruleItem.name,
            value: ruleItem.code,
          })),
        });
      }
    });
    ruleService.searchRuleEngineEnums().then(ruleCategoryRes => {
      if (ruleCategoryRes) {
        this.setState({
          ruleCategoryOptions: ruleCategoryRes?.ruleCategories.map(option => ({
            label: option.name,
            value: option.code,
          })),
        });
      }
    });

    if (this.props.goodsId) {
      NewMarketService.GoodsMgmtControllerService.find({
        goodsId: Number(this.props.goodsId),
      }).then(res => {
        this.setState({
          goodsInfo: res?.value?.basicProp,
        });
      });
    }

    const {
      beginShelvesDate,
      endUnderShelvesDate,
      isSales,
      beginSalesDate,
      endSalesDate,
      isCalculationCycle,
      cutOffDate,
      cutOffTime,
      publicDisplayPeriod,
      maxSalesCount,
      suitCrowd,
      zoneId,
      agencyCode,
      salesChannelResponseDTOS,
      enableWholesaleWarning,
      policyEffectiveWithoutPay,
      needStuInitialPremiumCollection,
      enableUnderwriting,
      unifiedUnderwritingConclusionFlag,
      underwritingType,
      underwritingOrder,
      policyIssuanceOrder,
      salesDistrict,
      enablePayment,
      paymentType,
      paymentMethod,
      triggerPoint,
      commissionFormulaCode,
      ruleCode,
      policyEffectiveWithoutCollectionRenewal,
      ruleCodeRenewal,
    } = this.props.data;

    let finalMaxSalesCount;
    let finalMaxSalesCount_number;

    if (maxSalesCount) {
      if (maxSalesCount === 2147483647) {
        finalMaxSalesCount = 'unlimited';
      } else {
        finalMaxSalesCount = 'limited';
      }
    }

    if (maxSalesCount && maxSalesCount !== 2147483647) {
      finalMaxSalesCount_number = maxSalesCount;
    }

    const data = {
      beginShelvesDate: this.transformDate(beginShelvesDate), // 上架开始时间
      endUnderShelvesDate: this.transformDate(endUnderShelvesDate), // 上架结束时间
      isSales, // 是否可销售
      beginSalesDate: this.transformDate(beginSalesDate), // 销售时间-开始
      endSalesDate: this.transformDate(endSalesDate), // 销售时间-结束
      isCalculationCycle,
      cutOffDate,
      cutOffTime,
      publicDisplayPeriod,
      maxSalesCount: finalMaxSalesCount, // 销售数量是否限制
      maxSalesCount_number: finalMaxSalesCount_number, // 销售数量
      suitCrowd, // 适用人群
      zoneId,
      agencyCode,
      salesChannelResponseDTOS,
      enableWholesaleWarning,
      policyEffectiveWithoutPay,
      needStuInitialPremiumCollection,
      isEnableWholesaleWarning: enableWholesaleWarning ? 1 : 2,
      enableUnderwriting: enableUnderwriting ? enableUnderwriting?.toString() : YesNoType.Yes,
      unifiedUnderwritingConclusionFlag: unifiedUnderwritingConclusionFlag
        ? unifiedUnderwritingConclusionFlag?.toString()
        : YesNoType.Yes,
      underwritingType: underwritingType ? underwritingType.split(',') : ['1'],
      underwritingOrder: underwritingOrder ? underwritingOrder.split(',') : ['1', '2'],
      policyIssuanceOrder: policyIssuanceOrder ? policyIssuanceOrder.split(',') : ['2', '1'],
      salesDistrict: salesDistrict ? salesDistrict.split(',') : [],
      enablePayment,
      paymentType,
      paymentMethod,
      triggerPoint,
      commissionFormulaCode,
      ruleCode,
      policyEffectiveWithoutCollectionRenewal,
      ruleCodeRenewal,
    };
    this.initValue(data, this.props);
  }

  // 把时间字符串转化为时间戳
  transformDate = time => {
    if (time) {
      return new Date(time).getTime();
    }
    return undefined;
  };

  // 保存前 校验
  saveBefore = async () => {
    const form = this.formRef.current;
    const isValid = await form.validateFields().catch(formErrorHandler(form));
    return isValid;
  };

  // 提取值
  getValue = async () => {
    let value = {};
    const form = this.formRef.current;

    await form
      .validateFields()
      .then(val => {
        const channelList = cloneDeep(this.state.channelList);
        if (val.launchTime && val.launchTime.length > 1) {
          val.beginShelvesDate = val.launchTime[0];
          val.endUnderShelvesDate = val.launchTime[1];
          delete val.launchTime;
        }
        if (val.salesTime && val.salesTime.length > 1) {
          val.beginSalesDate = val.salesTime[0];
          val.endSalesDate = val.salesTime[1];
          delete val.salesTime;
        }
        Object.entries(
          pick(val, [
            'triggerPoint',
            'paymentMethod',
            'salesDistrict',
            'underwritingOrder',
            'policyIssuanceOrder',
            'underwritingType',
          ])
        ).forEach(([key, value]) => {
          val[key] = value?.join(',') ?? value;
        });

        Object.keys(val).forEach(key => {
          if (key.indexOf('salesChannel') > -1) {
            const channelCode = val[key];
            const index = Number(key.split('-')[1]);
            channelList[index].channelCode = channelCode;
            delete val[key];
          }
          if (key.indexOf('relation') > -1) {
            const relationshipCode = val[key];
            const index = Number(key.split('-')[1]);
            channelList[index].relationshipCode = relationshipCode;
            delete val[key];
          }
        });
        value = val;
        value.salesChannelRequestDTOS = channelList;
      })
      .catch(formErrorHandler(form));

    return value;
  };

  // 赋值
  initValue = (data, props) => {
    const obj = cloneDeep(data);
    const { timeZone, l10n } = props;
    let { channelList } = this.state;
    if (!obj.zoneId) {
      obj.zoneId = timeZone.defaultZoneOptionList && timeZone.defaultZoneOptionList[0].key;
    }
    Object.keys(obj).forEach(key => {
      if (obj[key] == null) {
        if (key === 'maxSalesCount') {
          obj[key] = 'unlimited';
        }

        if (key === 'isSales') {
          obj[key] = 1;
        }
      }

      if (
        (key === 'beginShelvesDate' ||
          key === 'endUnderShelvesDate' ||
          key === 'beginSalesDate' ||
          key === 'endSalesDate') &&
        obj[key]
      ) {
        obj[key] = l10n.dateFormat.l10nMoment(obj[key], obj.zoneId);
      }
    });
    if (obj.salesChannelResponseDTOS && obj.salesChannelResponseDTOS.length > 0) {
      channelList = cloneDeep(obj.salesChannelResponseDTOS);
    }
    this.setState(
      {
        data: obj,
        channelList,
      },
      () => {
        this.changeTimeZone(obj.zoneId);
      }
    );
  };

  // 重置有通知的渠道
  resetNoticeChannel = channel => {
    this.setState({
      channelList: channel,
    });
  };

  // 处理出售与否的变化
  handleSoldOrNotChange = e => {
    if (e.target.value === 2) {
      this.formRef.current.setFieldsValue({
        salesTime: undefined,
      });
    }
  };

  // 获取商品最大销售数量的值
  getMaximum = () => {
    return this.formRef.current.getFieldsValue(['maxSalesCount', 'maxSalesCount_number']);
  };

  // 获取商品最大销售数量的值
  getLaunchDate = () => {
    return this.formRef.current.getFieldValue('launchTime');
  };

  changeTimeZone = value => {
    this.setState(
      {
        currentTimeZone: value,
      },
      () => {
        this.formRef.current.setFieldsValue({
          launchTimeZoneId: value,
          salesTimeZoneId: value,
        });
        this.props.setTimeZoneId(value);
      }
    );
  };

  changeSalesDistrict = value => {
    // 判断是否选全国，如果选择了 不可选择其他
    if (value?.includes(SalesDistrictType.Nationwide)) {
      const salesDistrict = [SalesDistrictType.Nationwide];
      this.setState(
        preState => ({
          data: { ...preState.data, salesDistrict },
        }),
        () => {
          this.formRef.current.setFieldsValue({
            salesDistrict,
          });
        }
      );
    }
  };

  getPayMethodOption = () => {
    const { enums } = this.props;
    let arr = [];
    if (enums.payMethod) {
      arr = enums.payMethod.map(item => {
        return {
          label: item.dictValueName,
          value: item.dictValue,
          key: item.dictValue,
        };
      });
    }
    return arr;
  };

  renderNewBusinessSection() {
    const { t, enums, readOnly } = this.props;
    const { data, tenantInfo = {} } = this.state;

    if (['SALE_CHANNEL', 'AGENCY'].includes(tenantInfo.orgType)) {
      return (
        <Section style={{ border: `1px solid ${lessVars['@border-default']}` }} title={t('New Business')}>
          <Form.Item noStyle shouldUpdate>
            {({ getFieldValue }) => (
              <React.Fragment>
                <Form.Item
                  label={t('Enable Underwriting')}
                  required
                  name="enableUnderwriting"
                  initialValue={data.enableUnderwriting}
                  rules={[
                    {
                      required: true,
                      message: t('Please select'),
                    },
                  ]}
                >
                  <Radio.Group
                    disabled={readOnly}
                    optionValue="dictValue"
                    optionLabel="dictValueName"
                    options={mapBizDictToOptions(this.props.enums.yesNo)}
                  />
                </Form.Item>
                {getFieldValue('enableUnderwriting') === 1 ? (
                  <Form.Item
                    label={t('Underwriting Type')}
                    required
                    name="underwritingType"
                    initialValue={data.underwritingType}
                    rules={[
                      {
                        required: true,
                        message: t('Please select'),
                      },
                    ]}
                  >
                    <Checkbox.Group
                      options={(enums.uwIssuePlatform || []).map(platform => ({
                        label: platform.dictValueName,
                        value: platform.dictValue,
                      }))}
                      disabled={readOnly}
                    />
                  </Form.Item>
                ) : null}
                {/* 只有当underwritingType两个都选中的时候才会出现 */}
                {getFieldValue('underwritingType') && getFieldValue('underwritingType').length === 2 ? (
                  <Form.Item
                    label={t('Underwriting Order')}
                    required
                    name="underwritingOrder"
                    initialValue={data.underwritingOrder}
                  >
                    <TwoItemSort
                      options={(enums.uwIssuePlatform || []).map(platform => ({
                        label: platform.dictValueName,
                        value: platform.dictValue,
                      }))}
                    />
                  </Form.Item>
                ) : null}
                <Form.Item
                  label={t('Policy Issuance Order')}
                  required
                  name="policyIssuanceOrder"
                  initialValue={data.policyIssuanceOrder}
                >
                  <TwoItemSort
                    options={(enums.uwIssuePlatform || []).map(platform => ({
                      label: platform.dictValueName,
                      value: platform.dictValue,
                    }))}
                  />
                </Form.Item>
                <Form.Item
                  label={t('Enable Payment')}
                  required
                  name="enablePayment"
                  initialValue={data.enablePayment || 1}
                  rules={[
                    {
                      required: true,
                      message: t('Please select'),
                    },
                  ]}
                >
                  <Radio.Group disabled={readOnly}>
                    <Radio value={1}>{t('yes')}</Radio>
                    <Radio value={2}>{t('no')}</Radio>
                  </Radio.Group>
                </Form.Item>
                {getFieldValue('enablePayment') === 1 ? (
                  <React.Fragment>
                    <Form.Item
                      required
                      label={t('payment_type')}
                      name="paymentType"
                      initialValue={data.paymentType && data.paymentType.toString()}
                      rules={[
                        {
                          required: true,
                          message: t('Please select'),
                        },
                      ]}
                    >
                      <GeneralSelect
                        placeholder={t('Please select')}
                        allowClear={false}
                        disabled={readOnly}
                        option={
                          Array.isArray(enums.paymentType)
                            ? enums.paymentType.map((item, index) => ({
                                key: index,
                                value: item.dictValue,
                                label: item.dictValueName,
                              }))
                            : []
                        }
                      />
                    </Form.Item>
                    <Form.Item
                      required
                      label={t('pay_method')}
                      name="paymentMethod"
                      initialValue={data.paymentMethod}
                      rules={[
                        {
                          required: true,
                          message: t('Please select'),
                        },
                      ]}
                    >
                      <Checkbox.Group
                        className="payment-checkbox"
                        options={this.getPayMethodOption()}
                        disabled={readOnly}
                      />
                    </Form.Item>
                  </React.Fragment>
                ) : null}
              </React.Fragment>
            )}
          </Form.Item>
        </Section>
      );
    }

    // insurance company 返回这个
    if (tenantInfo.orgType === 'INSURANCE') {
      return (
        <Section style={{ border: `1px solid ${lessVars['@border-default']}` }} title={t('New Business')}>
          <div>
            <Form.Item
              label={t('Enable Underwriting')}
              required
              name="enableUnderwriting"
              initialValue={data.enableUnderwriting}
            >
              <Radio.Group
                disabled={readOnly}
                optionValue="dictValue"
                optionLabel="dictValueName"
                options={mapBizDictToOptions(this.props.enums.yesNo)}
              />
            </Form.Item>
            {this.state.goodsInfo?.goodsCategory === GoodsCategoryItemExtend1.ProductBundle ? (
              <Form.Item
                label={t('Unified Underwriting Conclusion Flag')}
                required
                name="unifiedUnderwritingConclusionFlag"
                initialValue={data.unifiedUnderwritingConclusionFlag}
              >
                <Radio.Group
                  disabled={readOnly}
                  optionValue="dictValue"
                  optionLabel="dictValueName"
                  options={mapBizDictToOptions(this.props.enums.yesNo)}
                />
              </Form.Item>
            ) : null}
          </div>
        </Section>
      );
    }

    return null;
  }

  renderInsuringProcessSection() {
    const { t } = this.props;
    const { tenantInfo = {} } = this.state;
    if (!['AGENCY', 'INSURANCE', 'SALE_CHANNEL'].includes(tenantInfo.orgType)) {
      return null;
    }
    return (
      <Fragment>
        <div
          style={{
            paddingTop: 24,
            paddingBottom: 16,
            borderTop: `1px dashed ${lessVars['@border-default']}`,
          }}
          id="insuringProcessConfiguration"
        >
          <span className="mb-6 font-bold text-base">{t('Insuring Process Configuration')}</span>
        </div>
        <div className="insuringProcessConfiguration">{this.renderNewBusinessSection()}</div>
      </Fragment>
    );
  }

  timezonePickerValidator(timeKey, zoneIdKey, required = true) {
    const { t } = this.props;
    if (!this.formRef.current) {
      return;
    }
    const { getFieldValue } = this.formRef.current;
    const isValid = required ? getFieldValue(timeKey) && getFieldValue(zoneIdKey) : true;

    return {
      help: !isValid ? t('Please select') : '',
      validateStatus: !isValid ? 'error' : 'success',
    };
  }

  render() {
    const { timeZone, t, enums, readOnly, formExtendLimit, l10n } = this.props;

    const { currentTimeZone, data, ruleList, ruleCategoryOptions } = this.state;

    return !isEmpty(data) ? (
      <Form colon={false} layout="vertical" ref={this.formRef}>
        <div id="salesConfiguration" className="mb-6 font-bold text-base">
          {t('Sales Configuration')}
        </div>
        {formExtendLimit.triggerShow && (
          <Form.Item label={t('Trigger Point by Event Policy')} name="triggerPoint" initialValue={data.triggerPoint}>
            <GeneralSelect
              mode="tags"
              allowClear={false}
              showArrow
              className="!w-[580px]"
              placeholder={t('Please select')}
              disabled={readOnly}
              option={
                Array.isArray(enums.triggerPointType)
                  ? enums.triggerPointType.map((item, index) => ({
                      key: index,
                      value: item.dictValue,
                      label: item.dictValueName,
                    }))
                  : []
              }
            />
          </Form.Item>
        )}
        <Form.Item
          label={t('Time Zone')}
          name="zoneId"
          initialValue={data.zoneId}
          rules={[{ required: true, message: t('Please select') }]}
        >
          <GeneralSelect
            className="!w-[580px]"
            allowClear={false}
            placeholder={t('Please select')}
            disabled={readOnly}
            onChange={this.changeTimeZone}
            option={
              Array.isArray(timeZone.zoneOptionList)
                ? timeZone.zoneOptionList.map((item, index) => ({
                    key: index,
                    value: item.value,
                    label: item.name,
                  }))
                : []
            }
          />
        </Form.Item>

        <Form.Item noStyle shouldUpdate>
          {({ getFieldValue, setFieldsValue, validateFields }) => (
            <React.Fragment>
              <Form.Item
                label={t('Launch Date')}
                required
                {...this.timezonePickerValidator('launchTime', 'launchTimeZoneId')}
              >
                <FTimezonePicker
                  form={this.formRef.current}
                  selectItem={{
                    key: 'launchTimeZoneId',
                    initialValue: currentTimeZone,
                  }}
                  pickerItem={{
                    key: 'launchTime',
                    initialValue: data.beginShelvesDate &&
                      data.endUnderShelvesDate && [data.beginShelvesDate, data.endUnderShelvesDate],
                  }}
                  pickerType="range"
                  showTime
                  format={
                    l10n.dateFormat?.timeFormatByZeus === TimeFormatEnum.NoSecond
                      ? l10n?.dateFormat?.dateTimeFormatNoSecond
                      : l10n?.dateFormat?.dateTimeFormat
                  }
                  selectObj={{
                    list: timeZone.zoneOptionList,
                    placeholder: 'Please select',
                    dropdownMatchSelectWidth: false,
                    allowClear: false,
                    disabled: true,
                  }}
                  customizeSize={[200, 380]}
                  placeholder={['Start Time', 'End Time']}
                  disabled={readOnly}
                />
              </Form.Item>
              <Form.Item label={t('Sales District')} name="salesDistrict" initialValue={data.salesDistrict}>
                <GeneralSelect
                  className="!w-[580px]"
                  mode="multiple"
                  disabled={readOnly}
                  placeholder={t('Please select')}
                  options={enums.salesDistrict
                    ?.sort((a, b) => a.dictValue - b.dictValue)
                    ?.map((item, index) => ({
                      key: index,
                      value: item.dictValue,
                      label: item.dictValueName,
                      disabled:
                        getFieldValue('salesDistrict')?.includes(SalesDistrictType.Nationwide) &&
                        item.dictValue !== SalesDistrictType.Nationwide,
                    }))}
                  onChange={this.changeSalesDistrict}
                />
              </Form.Item>
              <div className="soldOrNot">
                <Form.Item
                  label={t('On Sale')}
                  name="isSales"
                  initialValue={data.isSales || 1}
                  rules={[{ required: true, message: t('Please select') }]}
                >
                  <Radio.Group onChange={this.handleSoldOrNotChange} disabled={readOnly}>
                    <Radio value={1}>{t('yes')}</Radio>
                    <Radio value={2}>{t('no')}</Radio>
                  </Radio.Group>
                </Form.Item>
              </div>
              <div className="flex gap-4 items-center">
                <Form.Item
                  label={t('Sales Time')}
                  required={getFieldValue('isSales') === 1}
                  {...this.timezonePickerValidator('salesTime', 'salesTimeZoneId', getFieldValue('isSales') === 1)}
                >
                  <FTimezonePicker
                    form={this.formRef.current}
                    selectItem={{
                      key: 'salesTimeZoneId',
                      initialValue: currentTimeZone,
                    }}
                    pickerItem={{
                      key: 'salesTime',
                      initialValue:
                        getFieldValue('isSales') === 0
                          ? undefined
                          : data.beginSalesDate && data.endSalesDate && [data.beginSalesDate, data.endSalesDate],
                    }}
                    pickerType="range"
                    showTime
                    format={
                      l10n.dateFormat?.timeFormatByZeus === TimeFormatEnum.NoSecond
                        ? l10n?.dateFormat?.dateTimeFormatNoSecond
                        : l10n?.dateFormat?.dateTimeFormat
                    }
                    selectObj={{
                      list: timeZone.zoneOptionList,
                      placeholder: 'Please select',
                      dropdownMatchSelectWidth: false,
                      allowClear: false,
                      disabled: true,
                    }}
                    customizeSize={[200, 380]}
                    disabled={readOnly || getFieldValue('isSales') === 0}
                    placeholder={['Start Time', 'End Time']}
                  />
                </Form.Item>
                <Button
                  onClick={() => {
                    setFieldsValue({
                      salesTimeZoneId: getFieldValue('launchTimeZoneId'),
                      salesTime: getFieldValue('launchTime'),
                    });
                  }}
                  disabled={readOnly}
                >
                  {t('Same as Launch Period')}
                </Button>
              </div>
              <div className="soldOrNot">
                <Form.Item
                  label={t('Calculation Cycle')}
                  name="isCalculationCycle"
                  initialValue={data.isCalculationCycle || 2}
                >
                  <Radio.Group onChange={() => {}} disabled={readOnly}>
                    <Radio value={1}>{t('yes')}</Radio>
                    <Radio value={2}>{t('no')}</Radio>
                  </Radio.Group>
                </Form.Item>
              </div>
              {`${getFieldValue('isCalculationCycle')}` === '1' && (
                <React.Fragment>
                  <Row>
                    <Col span={6}>
                      <Form.Item
                        label={t('Cut off Date')}
                        name="cutOffDate"
                        initialValue={data.cutOffDate && data.cutOffDate.toString()}
                        rules={[
                          {
                            required: !!getFieldValue('cutOffTime'),
                            message: t('Please select'),
                          },
                        ]}
                      >
                        <GeneralSelect
                          disabled={readOnly}
                          onChange={() => {
                            setTimeout(() => {
                              validateFields(['cutOffDate'], { force: true });
                              validateFields(['cutOffTime'], { force: true });
                            }, 50);
                          }}
                          placeholder={t('Please select')}
                          option={monthList.map(i => ({
                            value: i.itemExtend1,
                            label: i.itemName,
                          }))}
                        />
                      </Form.Item>
                    </Col>
                    <Col className="ml-[50px]" span={6}>
                      <Form.Item
                        label={t('Cut off Time')}
                        name="cutOffTime"
                        initialValue={data.cutOffTime ? moment(`2020-08-20 ${data.cutOffTime}`) : undefined}
                        rules={[
                          {
                            required: !!getFieldValue('cutOffDate'),
                            message: t('Please select'),
                          },
                        ]}
                      >
                        <TimePicker
                          disabled={readOnly}
                          getPopupContainer={trigger => trigger.parentElement}
                          className="w-[240px]"
                          onChange={() => {
                            setTimeout(() => {
                              validateFields(['cutOffDate'], { force: true });
                              validateFields(['cutOffTime'], { force: true });
                            }, 50);
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={8}>
                      <Form.Item
                        label={
                          <span>
                            {t('Public Display Period')}
                            <Tooltip title={t('Maximum 30 days')}>
                              <QuestionCircleOutlined className="ml-[2px]" />
                            </Tooltip>
                          </span>
                        }
                        name="publicDisplayPeriod"
                        initialValue={data.publicDisplayPeriod}
                        rules={[
                          {
                            trigger: 'onChange',
                            validator: (rule, value, callback) => {
                              if (value && (!/^(0|[1-9][0-9]*)$/.test(value) || parseInt(value) > 30)) {
                                const message2 = t('Please input right format');
                                callback(message2);
                              }
                              callback();
                            },
                          },
                        ]}
                      >
                        <Input disabled={readOnly} className="w-[240px]" addonAfter={<span>{t('Days')}</span>} />
                      </Form.Item>
                    </Col>
                  </Row>
                </React.Fragment>
              )}
              <Form.Item
                label={t('Wholesale Volume')}
                name="maxSalesCount"
                initialValue={data.maxSalesCount || 'unlimited'}
                rules={[{ required: true, message: t('Please select') }]}
              >
                <Radio.Group
                  disabled={readOnly}
                  onChange={e => {
                    if (e.target.value === 'unlimited') {
                      setFieldsValue({
                        maxSalesCount_number: undefined,
                      });
                    }
                  }}
                  options={[
                    { label: t('Unlimited'), value: 'unlimited' },
                    {
                      label: (
                        <React.Fragment>
                          {t('Limited')}
                          {getFieldValue('maxSalesCount') === 'limited' && (
                            <Form.Item
                              name="maxSalesCount_number"
                              initialValue={data.maxSalesCount_number}
                              rules={[
                                {
                                  required: true,
                                  message: t('Please input'),
                                },
                              ]}
                              noStyle
                            >
                              <InputNumber
                                min={1}
                                max={2147483646}
                                maxLength={10}
                                precision={0}
                                className="w-[110px] ml-3"
                                placeholder={t('Please input')}
                                disabled={readOnly}
                              />
                            </Form.Item>
                          )}
                        </React.Fragment>
                      ),
                      value: 'limited',
                    },
                  ]}
                  className="[&_.market-ant4-radio-wrapper]:block"
                />
              </Form.Item>

              <Form.Item
                label={t('Wholesale Volume Warning')}
                name="isEnableWholesaleWarning"
                initialValue={data.isEnableWholesaleWarning && data.isEnableWholesaleWarning.toString()}
                rules={[{ required: true, message: t('Please select') }]}
              >
                <Radio.Group
                  disabled={readOnly}
                  className="[&_.market-ant4-radio-wrapper]:block"
                  options={[
                    {
                      label: t('no'),
                      value: '2',
                    },
                    {
                      label: (
                        <React.Fragment>
                          {t('yes')}
                          {getFieldValue('isEnableWholesaleWarning') === '1' && (
                            <Form.Item
                              className="!inline-block"
                              name="enableWholesaleWarning"
                              initialValue={data.enableWholesaleWarning}
                              rules={[
                                {
                                  required: true,
                                  message: t('Please input'),
                                },
                              ]}
                              noStyle
                            >
                              <InputNumber
                                min={1}
                                max={9999999999}
                                maxLength={10}
                                precision={0}
                                className="w-[158px] ml-3"
                                placeholder={t('Warning Threshold')}
                                disabled={readOnly}
                              />
                            </Form.Item>
                          )}
                        </React.Fragment>
                      ),
                      value: '1',
                    },
                  ]}
                />
              </Form.Item>
            </React.Fragment>
          )}
        </Form.Item>

        <PolicyEffectiveWithoutCollectionNB
          data={data}
          disabled={readOnly}
          form={this.formRef.current}
          ruleList={ruleList}
          ruleCategoryOptions={ruleCategoryOptions}
        />
        <PolicyEffectiveWithoutCollectionRenewal
          data={data}
          disabled={readOnly}
          form={this.formRef.current}
          ruleList={ruleList}
          ruleCategoryOptions={ruleCategoryOptions}
        />
        <Form.Item
          label={t('Targeted Customer')}
          name="suitCrowd"
          initialValue={data.suitCrowd}
          rules={[
            {
              message: t('Please don’t enter special characters, such as~`!@#$%^&*()+.'),
              pattern: /^((?![~`@#$%^&*+]).)*$/,
            },
          ]}
        >
          <Input placeholder={t('Please input')} disabled={readOnly} />
        </Form.Item>
        {this.renderInsuringProcessSection()}
      </Form>
    ) : null;
  }
}

export default withL10n()(
  connect(state => {
    return {
      enums: state.enums,
      timeZone: state.timeZone,
    };
  })(withTranslation(['market', 'common'])(FormBox))
);
