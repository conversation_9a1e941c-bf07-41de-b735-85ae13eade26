import { Ref, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';

import { uniqBy } from 'lodash-es';

import {
  CustomerLoyaltyService,
  NcbVoucherInfo,
  PartnerType,
  PosRefundVoucherInfo,
  SalesPartner,
  VoucherSearchResult,
  VoucherStatus,
} from 'genesis-web-service';
import { NewMarketService } from '@market/services/market/market.service.new';

import { SalesAttributesNS } from '@market/common/salesAttributes.interface';
import { attachRandomKeyForObj } from '@market/utils';

import ForPOSPremiumRefundTable from '../ForPOSPremiumRefundTable';

interface Props {
  goodsId: string | number;
  disabled: boolean;
  refInstance: Ref<unknown>;
  initData?: SalesPartner[];
}

export const VoucherCorrelation = ({ goodsId, disabled, initData, refInstance }: Props): JSX.Element => {
  const containerEl = useRef<HTMLDivElement>(null);
  const [t] = useTranslation(['market', 'common']);
  const [goodsPosRefundVoucherList, setGoodsPosRefundVoucherList] = useState<PosRefundVoucherInfo[]>([]);
  const [channePartnerlList, setChannePartnerlList] = useState<SalesAttributesNS.FESalesPartnerChannel[]>([]);
  const [voucherList, setVoucherList] = useState<VoucherSearchResult[]>([]);
  const [form] = Form.useForm();

  useEffect(() => {
    CustomerLoyaltyService.searchVoucher({ status: VoucherStatus.Effective }, 0, 1000).then(res => {
      setVoucherList(res.content || []);
    });
  }, []);

  useEffect(() => {
    if (initData) {
      const tempChannePartner: SalesAttributesNS.FESalesPartnerChannel[] = [];
      // 去掉互斥逻辑之前，Agency Company中也会配置salesChannel的数据
      // 所以不能过滤partnerConfig上面的partnerType
      initData.forEach(partnerConfig => {
        tempChannePartner.push(
          ...(partnerConfig?.goodsSalesPartnerList
            .filter(item => item.partnerType === PartnerType.salesChannel)
            .map(attachRandomKeyForObj) as SalesAttributesNS.FESalesPartnerChannel[])
        );
      });
      setChannePartnerlList(uniqBy(tempChannePartner, item => item.partnerCode));
    }
  }, [initData]);

  useEffect(() => {
    NewMarketService.GoodsVoucherMgmtService.queryGoodsVouchers(+goodsId).then(res => {
      const tempGoodsPosRefundVoucherList = (res.goodsPosRefundVoucherList || []).map(
        posRefundVoucherItem => ({
          goodsId,
          key: Math.random(),
          voucherDefId: posRefundVoucherItem?.voucherDefId ? `${posRefundVoucherItem?.voucherDefId}` : undefined,
          targetUserOfVoucher: posRefundVoucherItem?.targetUserOfVoucher
            ? `${posRefundVoucherItem?.targetUserOfVoucher}`
            : undefined,
          effectivePeriodExtension: posRefundVoucherItem?.effectivePeriodExtension
            ? `${posRefundVoucherItem?.effectivePeriodExtension}`
            : undefined,
          posPremiumRefundType: `${posRefundVoucherItem?.posPremiumRefundType}`,
          salesChannelCodesList: posRefundVoucherItem?.salesChannelCodesList,
        })
      );
      setGoodsPosRefundVoucherList(tempGoodsPosRefundVoucherList);
      if (res.goodsNcbVoucher) {
        form.setFieldsValue({
          ncbIssueType: res.goodsNcbVoucher?.ncbIssueType ? `${res.goodsNcbVoucher?.ncbIssueType}` : undefined,
          voucherDefId: res.goodsNcbVoucher?.voucherDefId ? `${res.goodsNcbVoucher?.voucherDefId}` : undefined,
          targetUserOfVoucher: res.goodsNcbVoucher?.targetUserOfVoucher
            ? `${res.goodsNcbVoucher?.targetUserOfVoucher}`
            : undefined,
          effectivePeriodExtension: res.goodsNcbVoucher?.effectivePeriodExtension
            ? `${res.goodsNcbVoucher?.effectivePeriodExtension}`
            : undefined,
        });
      }
    });
  }, [goodsId]);

  useImperativeHandle(refInstance, () => ({
    getFormChangedData: () =>
      new Promise((resolve, reject) => {
        form
          .validateFields()
          .then((values: NcbVoucherInfo) => {
            const voucherInfoValues: NcbVoucherInfo = {
              goodsId,
              ncbIssueType: values.ncbIssueType,
              voucherDefId: values.voucherDefId,
              targetUserOfVoucher: values.targetUserOfVoucher,
              effectivePeriodExtension: values.effectivePeriodExtension,
            };
            resolve(voucherInfoValues);
          })
          .catch(e => {
            reject(e);
          });
      }),
    getTableChangedData: (): PosRefundVoucherInfo[] =>
      goodsPosRefundVoucherList.map(posRefundVoucherItem => ({
        goodsId,
        voucherDefId: posRefundVoucherItem.voucherDefId,
        targetUserOfVoucher: posRefundVoucherItem.targetUserOfVoucher,
        effectivePeriodExtension: posRefundVoucherItem.effectivePeriodExtension,
        posPremiumRefundType: posRefundVoucherItem.posPremiumRefundType,
        salesChannelCodesList: posRefundVoucherItem.salesChannelCodesList,
      })),
  }));

  return (
    <div
      style={{
        marginBottom: 24,
      }}
      id="voucherCorrelation"
      ref={containerEl}
    >
      <div style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>{t('Voucher Correlation')}</div>
      <ForPOSPremiumRefundTable
        goodsId={goodsId}
        disabled={disabled}
        voucherList={voucherList}
        channePartnerlList={channePartnerlList}
        goodsPosRefundVoucherList={goodsPosRefundVoucherList}
        setGoodsPosRefundVoucherList={setGoodsPosRefundVoucherList}
      />
    </div>
  );
};

export default VoucherCorrelation;
