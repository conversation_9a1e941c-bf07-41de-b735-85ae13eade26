import { Ref, forwardRef, useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { message } from 'antd';

import {
  FormulaProps,
  MktYesNo,
  PartnerFeeType,
  PartnerType,
  SalesPartner,
  SalesPartnerFormula,
  SettlementRule,
} from 'genesis-web-service';

import { BizTopic } from '@market/common/enums';
import { SalesAttributesNS } from '@market/common/salesAttributes.interface';
import EditTable from '@market/components/F-EditableTable';
import { useBizDict } from '@market/hook/bizDict';
import { useKeyAccountChannelList } from '@market/hook/channel.serivce';
import { mapBizdictToOldBizdict, mapChannelToOldBizdict, renderOldBizdictName } from '@market/utils/enum';

import ClawbackCommissionFormula from '../ClawbackCommissionFormula';
import CommissionFormulaTable from '../CommissionFormulaTable';

interface Props {
  goodsId: number | string;
  disabled: boolean;
  totalFormulaList: FormulaProps[];
  salesPartner?: SalesPartner;
  onEditStateChange: (isEditing: boolean) => void;
  submitService: (salesPartner: SalesPartner) => Promise<unknown>;
  reload: () => void;
}

export const KeyAccountChannel = ({
  goodsId,
  disabled,
  salesPartner,
  totalFormulaList,
  onEditStateChange,
  submitService,
  reload,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const settlementRuleBizDicts = useBizDict('settlementRule');
  const yesNoOptions = useBizDict('yesNo');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef<HTMLDivElement>(null);
  const [t] = useTranslation(['market', 'common']);
  const partnerType = PartnerType.keyAccountChannel;

  const keyAccountChannelList = useKeyAccountChannelList();

  const agencyPartnerList: SalesAttributesNS.FESalesPartnerAgency[] = useMemo(
    () =>
      salesPartner?.goodsSalesPartnerList?.map(item => ({
        key: Math.random(),
        ...item,
      })) || [],
    [salesPartner]
  );

  const handleEditBefore = useCallback(
    (record: SalesAttributesNS.FESalesPartnerAgency) => {
      const tempTableData = [...agencyPartnerList];
      const editingRow = tempTableData.find((row: SalesAttributesNS.FESalesPartnerAgency) => row.key === record.key);
      if (editingRow) {
        editingRow.editing = true;
        editingRow.accumulatedToPremium = `${editingRow.accumulatedToPremium!}`;
        editingRow.settlementRule = `${editingRow.settlementRule!}`;
      }
    },
    [agencyPartnerList]
  );

  const keyAccountChannelOptions = useMemo(
    () => keyAccountChannelList.map(mapChannelToOldBizdict),
    [keyAccountChannelList]
  );
  const settlementRuleOptions = useMemo(
    () => settlementRuleBizDicts.map(mapBizdictToOldBizdict),
    [settlementRuleBizDicts]
  );

  const columns = useMemo(
    () => [
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Key Account Channel')}</span>,
        dataIndex: 'partnerCodes',
        editable: true,
        inputType: 'select',
        width: '240px',
        selectOptions: keyAccountChannelOptions,
        render: (partnerCodes: string[]): string =>
          partnerCodes?.map(partnerCode => renderOldBizdictName(partnerCode, keyAccountChannelOptions)).join(', '),
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          mode: 'multiple',
          getPopupContainer: () => containerEl.current || document.body,
        },
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Settlement Rule')}</span>,
        editable: true,
        dataIndex: 'settlementRule',
        inputType: 'select',
        initialValue: SettlementRule.basedOnPremiumOffset,
        selectOptions: settlementRuleOptions,
        render: (settlementRule: string): string => renderOldBizdictName(settlementRule, settlementRuleOptions),
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
        },
        width: '240px',
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Accumulated to Premium')}</span>,
        editable: true,
        dataIndex: 'accumulatedToPremium',
        inputType: 'select',
        initialValue: MktYesNo.No,
        selectOptions: yesNoOptions,
        render: (accumulatedToPremium: string): string =>
          accumulatedToPremium ? renderOldBizdictName(accumulatedToPremium, yesNoOptions) : t('- -'),
        width: '240px',
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
        },
      },
    ],
    [settlementRuleOptions, t, yesNoOptions, keyAccountChannelOptions]
  );

  const saveSalesPartnerNew = useCallback(
    (list: SalesAttributesNS.FESalesPartnerAgency[] | null, formulalist: SalesPartnerFormula[] | null) => {
      // 如果没传取原始数据
      let goodsSalesPartnerList = salesPartner?.goodsSalesPartnerList;
      if (list) {
        goodsSalesPartnerList = list;
      }
      const goodsSalesPartnerFormulaList = formulalist
        ? formulalist.map(row => {
            row.goodsId = goodsId;
            row.partnerType = partnerType;

            return row;
          })
        : salesPartner?.goodsSalesPartnerFormulaList;

      submitService({
        goodsId,
        partnerType,
        goodsSalesPartnerList: goodsSalesPartnerList || [],
        goodsSalesPartnerFormulaList: goodsSalesPartnerFormulaList || [],
      }).then(() => {
        message.success(t('Submit Successfully'));
        reload();
      });
    },
    [salesPartner, goodsId, partnerType, reload, submitService, t]
  );

  const saveFormulaList = useCallback(
    (list: SalesPartnerFormula[]) => {
      saveSalesPartnerNew(null, list);
    },
    [saveSalesPartnerNew]
  );

  const onTableSubmit = useCallback(
    (
      rowData: SalesAttributesNS.FESalesPartnerAgency & {
        key: number;
        editing: boolean;
      },
      key,
      index: number
    ) => {
      const tempData = [...agencyPartnerList];
      if (tempData.find(row => !row.editing && row.partnerCode === rowData.partnerCode)) {
        message.error(t('Duplicate Key Account Channel. Please change.'));
        return Promise.reject();
      }

      rowData.goodsId = goodsId;
      rowData.partnerType = partnerType;
      if (key === 'add') {
        rowData.key = Math.random();
        tempData.push(rowData);
      } else {
        tempData[index] = rowData;
      }

      // accumulatedToPremium 所有数据保持一致
      tempData.forEach(existRow => {
        existRow.accumulatedToPremium = rowData.accumulatedToPremium;
      });

      saveSalesPartnerNew([...tempData], null);
      return Promise.resolve();
    },
    [agencyPartnerList, goodsId, partnerType, saveSalesPartnerNew, t]
  );

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-return
  const commissionFormulaList = useMemo(
    () =>
      ((salesPartner?.goodsSalesPartnerFormulaList as SalesPartnerFormula[]) || [])?.filter(
        item =>
          `${item.feeType}` === PartnerFeeType.commission &&
          `${item.formulaCategory}` === BizTopic.Commission &&
          !item?.agentCategory // 可能是老字段
      ) || [],
    [salesPartner?.goodsSalesPartnerFormulaList]
  );

  const clawbackcommissionFormulaList = useMemo(
    () =>
      ((salesPartner?.goodsSalesPartnerFormulaList as SalesPartnerFormula[]) || [])?.filter(
        item =>
          `${item.feeType}` === PartnerFeeType.commission &&
          `${item.formulaCategory}` === BizTopic.CommissionClawback &&
          !item?.agentCategory // 可能是老字段
      ) || [],
    [salesPartner?.goodsSalesPartnerFormulaList]
  );

  return (
    <div
      style={{
        marginBottom: 24,
      }}
      ref={containerEl}
    >
      <EditTable
        disabled={disabled}
        columns={columns}
        data={agencyPartnerList}
        scroll={{ x: 'max-content' }}
        onSubmit={onTableSubmit}
        onDelete={(key: string, index: number) => {
          agencyPartnerList?.splice(index, 1);

          saveSalesPartnerNew([...agencyPartnerList], null);
        }}
        onEditBefore={handleEditBefore}
        onCancel={() => {
          agencyPartnerList.forEach(row => {
            row.editing = false;
          });
        }}
        onStateChange={(state: string) => {
          onEditStateChange(state === 'Editing');
        }}
        pagination={
          agencyPartnerList?.length > 10
            ? {
                defaultPageSize: 10,
              }
            : undefined
        }
      />
      <CommissionFormulaTable
        goodsId={goodsId}
        disabled={disabled}
        totalFormulaList={totalFormulaList}
        initData={commissionFormulaList}
        onEditStateChange={() => {}}
        saveFormulaList={formulaList => {
          saveFormulaList([...formulaList, ...clawbackcommissionFormulaList]);
        }}
      />
      <ClawbackCommissionFormula
        goodsId={goodsId}
        disabled={disabled}
        totalFormulaList={totalFormulaList}
        initData={clawbackcommissionFormulaList}
        onEditStateChange={() => {}}
        saveFormulaList={formulaList => {
          saveFormulaList([...commissionFormulaList, ...formulaList]);
        }}
      />
    </div>
  );
};

export default forwardRef(KeyAccountChannel);
