import React from 'react';
import { useTranslation } from 'react-i18next';

import { Form, FormInstance, Radio, Tooltip } from 'antd';

import { Icon } from '@zhongan/nagrand-ui';

import { PolicyEffectiveWithoutCollection } from '@market/common/enums';
import { RuleCategory, SelectOption } from '@market/common/interface';
import { RuleOrRuleSet } from '@market/components/RuleOrRuleSet/RuleOrRuleSet';
import { useBizDictAsOptions } from '@market/hook/bizDict';

interface Props {
  disabled: boolean;
  data: {
    policyEffectiveWithoutPay: number;
    needStuInitialPremiumCollection: string;
    ruleCategory: string;
    ruleCode: string;
  };
  ruleCategoryOptions: SelectOption[];
  ruleList: SelectOption[];
  form: FormInstance;
}

export const PolicyEffectiveWithoutCollectionNB = ({
  form,
  disabled,
  data,
  ruleCategoryOptions,
  ruleList,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const policyEffectiveWithoutCollectionOptions = useBizDictAsOptions('policyEffectiveWithoutCollection');
  const yesNoOptions = useBizDictAsOptions('yesNo', {
    labelKey: 'dictValueName',
    valueKey: 'enumItemName',
  });
  /* ============== 枚举使用end ============== */
  const [t] = useTranslation(['market', 'common']);

  return (
    <React.Fragment>
      <Form.Item
        label={t('Policy Effective Without Collection (NB)')}
        name="policyEffectiveWithoutPay"
        // https://jira.zaouter.com/browse/GIS-46962
        // 增加 Not Relevant 来清空选项
        initialValue={data.policyEffectiveWithoutPay || null}
      >
        <Radio.Group disabled={disabled}>
          {policyEffectiveWithoutCollectionOptions.map(item => (
            <Radio value={+item.value}>{item.label}</Radio>
          ))}
          <Radio value={null}>{t('Not Relevant')}</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) => (
          <React.Fragment>
            {[2, 3].includes(getFieldValue('policyEffectiveWithoutPay')) && (
              <Form.Item
                label={
                  <React.Fragment>
                    {t('RSTU／STU must be collected for initial premium')}
                    <Tooltip
                      overlayInnerStyle={{ minWidth: 680 }}
                      title={
                        <React.Fragment>
                          <div>
                            {t(
                              'Yes-Recurring Single Top Up / Single Top Up must be collected before policy is effective.'
                            )}
                          </div>
                          <div>
                            {t(
                              "No-Recurring Single Top Up / Single Top Up's collection status does not impact policy effectiveness."
                            )}
                          </div>
                        </React.Fragment>
                      }
                    >
                      <Icon type="info-circle" style={{ marginTop: 3, marginLeft: 3 }} />
                    </Tooltip>
                  </React.Fragment>
                }
                name="needStuInitialPremiumCollection"
                initialValue={data.needStuInitialPremiumCollection || null}
              >
                <Radio.Group disabled={disabled} options={yesNoOptions} />
              </Form.Item>
            )}

            {getFieldValue('policyEffectiveWithoutPay') === PolicyEffectiveWithoutCollection.ByRule ? (
              <RuleOrRuleSet
                form={form}
                disabled={disabled}
                ruleCategoryFieldName="ruleCategory"
                ruleCodeFieldName="ruleCode"
                ruleCategoryInitialValue={RuleCategory.PolicyEffectiveWithoutCollection}
                ruleCodeInitialValue={data.ruleCode}
                ruleCategoryOptions={ruleCategoryOptions}
                ruleList={ruleList}
              />
            ) : null}
          </React.Fragment>
        )}
      </Form.Item>
    </React.Fragment>
  );
};

export default PolicyEffectiveWithoutCollectionNB;
