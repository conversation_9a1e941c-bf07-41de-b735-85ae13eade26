import { forwardRef, useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Tooltip, message } from 'antd';

import { intersection } from 'lodash-es';

import { PosRefundVoucherInfo, VoucherSearchResult } from 'genesis-web-service';

import { PosPremiumRefundType, SalesAttributesNS } from '@market/common/salesAttributes.interface';
import EditTable from '@market/components/F-EditableTable';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { useChannelList } from '@market/hook/channel.serivce';
import { mapChannelToOldBizdict, renderOldBizdictName, renderOptionName } from '@market/utils/enum';

interface Props {
  goodsId: string | number;
  disabled: boolean;
  voucherList: VoucherSearchResult[];
  channePartnerlList: SalesAttributesNS.FESalesPartnerChannel[];
  goodsPosRefundVoucherList: PosRefundVoucherInfo[];
  setGoodsPosRefundVoucherList: (arr: PosRefundVoucherInfo[]) => void;
}

export const ForPOSPremiumRefundTable = ({
  goodsId,
  disabled,
  voucherList,
  channePartnerlList,
  goodsPosRefundVoucherList,
  setGoodsPosRefundVoucherList,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const targetUserOfVoucherEnumOptions = useBizDictAsOptions('targetUserOfVoucher');
  const posPremiumRefundTypeEnumOptions = useBizDictAsOptions('posPremiumRefundType');
  const yesNoOptions = useBizDictAsOptions('yesNo');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef<HTMLDivElement>(null);
  const [t] = useTranslation(['market', 'common']);
  const totalChannelList = useChannelList();
  const [isShowValueType, setIsShowValueType] = useState(true);
  const [tableIsRequired, setTableIsRequired] = useState(false);

  const totalChannelOptions = useMemo(() => totalChannelList.map(mapChannelToOldBizdict), [totalChannelList]);

  const voucherOptions = useMemo(
    () =>
      (voucherList || []).map(voucherItem => ({
        value: `${voucherItem.voucherDefId}`,
        label: voucherItem.voucherName,
      })),
    [voucherList]
  );

  const tempChannePartnerlList = useMemo(() => {
    channePartnerlList.forEach(item => {
      const totalChannelItemInfo = totalChannelList.find(
        totalChannelItem => totalChannelItem.code === item.partnerCode
      );
      if (totalChannelItemInfo) {
        item.name = totalChannelItemInfo.name;
        item.code = totalChannelItemInfo.code;
      }
    });
    return channePartnerlList;
  }, [channePartnerlList, totalChannelList]);

  const handleEditBefore = useCallback(
    (record: PosRefundVoucherInfo) => {
      const tempTableData = [...goodsPosRefundVoucherList];
      const editingRow = tempTableData.find((row: PosRefundVoucherInfo) => row.key === record.key);
      if (editingRow) {
        editingRow.editing = true;
        editingRow.posPremiumRefundType = `${editingRow.posPremiumRefundType}`;
        editingRow.voucherDefId = editingRow?.voucherDefId ? `${editingRow.voucherDefId}` : undefined;
        editingRow.targetUserOfVoucher = editingRow?.targetUserOfVoucher
          ? `${editingRow.targetUserOfVoucher}`
          : undefined;
        editingRow.effectivePeriodExtension = editingRow?.effectivePeriodExtension
          ? `${editingRow.effectivePeriodExtension}`
          : undefined;
      }
    },
    [goodsPosRefundVoucherList]
  );

  const columns = useMemo(
    () => [
      {
        title: t('Sales Channel'),
        dataIndex: 'salesChannelCodesList',
        editable: true,
        inputType: 'select',
        width: '240px',
        selectOptions: tempChannePartnerlList.map(enumItem => ({
          itemExtend1: enumItem.partnerCode,
          itemName: enumItem.name,
        })),
        render: (relatedChannelCodes: string[]): JSX.Element => {
          const codeText = relatedChannelCodes?.map(code => renderOldBizdictName(code, totalChannelOptions)).join(',');
          return (
            <Tooltip title={codeText}>
              <span
                style={{
                  display: 'inline-block',
                  maxWidth: 240,
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {codeText || t('- -')}
              </span>
            </Tooltip>
          );
        },
        controllerprops: {
          required: tableIsRequired,
          allowClear: false,
          style: { width: 240 },
          mode: 'multiple',
          getPopupContainer: () => containerEl.current || document.body,
          onChange: () => setTableIsRequired(true),
        },
      },
      {
        title: <span className={tableIsRequired ? 'ant-form-item-required' : ''}>{t('POS Premium Refund Type')}</span>,
        editable: true,
        dataIndex: 'posPremiumRefundType',
        inputType: 'select',
        selectOptions: posPremiumRefundTypeEnumOptions.map(enumItem => ({
          itemExtend1: enumItem.value,
          itemName: enumItem.label,
        })),
        render: (settlementRule: string) => renderOptionName(settlementRule, posPremiumRefundTypeEnumOptions),
        controllerprops: {
          required: tableIsRequired,
          allowClear: false,
          style: { width: 240 },
          onChange: (posPremiumRefundType: PosPremiumRefundType) => {
            setTableIsRequired(true);
            if (posPremiumRefundType === PosPremiumRefundType.InCash) {
              setIsShowValueType(false);
            } else {
              setIsShowValueType(true);
            }
          },
          getPopupContainer: () => containerEl.current || document.body,
        },
        width: '240px',
      },
      {
        title: (
          <span className={tableIsRequired && isShowValueType ? 'ant-form-item-required' : ''}>
            {t('Voucher Name')}
          </span>
        ),
        editable: isShowValueType,
        dataIndex: 'voucherDefId',
        inputType: 'select',
        selectOptions: voucherOptions.map(enumItem => ({
          itemExtend1: enumItem.value,
          itemName: enumItem.label,
        })),
        controllerprops: {
          required: tableIsRequired && isShowValueType,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
          onChange: () => setTableIsRequired(true),
        },
        width: '240px',
        render: (text: string, record: PosRefundVoucherInfo) => {
          if (PosPremiumRefundType.InCash === record.posPremiumRefundType || !record.posPremiumRefundType) {
            return t('- -');
          }
          return renderOptionName(text, voucherOptions) || t('- -');
        },
      },
      {
        title: (
          <span className={tableIsRequired && isShowValueType ? 'ant-form-item-required' : ''}>
            {t('Target User of Voucher')}
          </span>
        ),
        editable: isShowValueType,
        dataIndex: 'targetUserOfVoucher',
        inputType: 'select',
        selectOptions: targetUserOfVoucherEnumOptions.map(enumItem => ({
          itemExtend1: enumItem.value,
          itemName: enumItem.label,
        })),
        render: (text: string, record: PosRefundVoucherInfo) => {
          if (PosPremiumRefundType.InCash === record.posPremiumRefundType || !record.posPremiumRefundType) {
            return t('- -');
          }
          return renderOptionName(text, targetUserOfVoucherEnumOptions) || t('- -');
        },
        width: '240px',
        controllerprops: {
          required: tableIsRequired && isShowValueType,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
          onChange: () => setTableIsRequired(true),
        },
      },
      {
        title: (
          <span className={tableIsRequired && isShowValueType ? 'ant-form-item-required' : ''}>
            {t('Effective Period Extension for Reversed Voucher')}
          </span>
        ),
        editable: isShowValueType,
        dataIndex: 'effectivePeriodExtension',
        inputType: 'select',
        selectOptions: yesNoOptions.map(enumItem => ({
          itemExtend1: enumItem.value,
          itemName: enumItem.label,
        })),
        render: (text: string, record: PosRefundVoucherInfo) => {
          if (PosPremiumRefundType.InCash === record.posPremiumRefundType || !record.posPremiumRefundType) {
            return t('- -');
          }
          return renderOptionName(text, yesNoOptions) || t('- -');
        },
        width: '400px',
        controllerprops: {
          required: tableIsRequired && isShowValueType,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
          onChange: () => setTableIsRequired(true),
        },
      },
    ],
    [
      t,
      tableIsRequired,
      tempChannePartnerlList,
      posPremiumRefundTypeEnumOptions,
      isShowValueType,
      targetUserOfVoucherEnumOptions,
      yesNoOptions,
      totalChannelOptions,
    ]
  );

  const onTableSubmit = useCallback(
    (
      rowData: PosRefundVoucherInfo & {
        key: number;
        editing: boolean;
      },
      key,
      index: number
    ) => {
      const tempData = [...goodsPosRefundVoucherList];
      if (
        tempData.find(
          row => !row.editing && intersection(row.salesChannelCodesList, rowData.salesChannelCodesList).length > 0
        )
      ) {
        message.error(t('Selected sales channel has been configured with conflicted POS premium refund rule.'));
        return Promise.reject();
      }
      if (key === 'add') {
        rowData.key = Math.random();
        rowData.goodsId = goodsId;
        tempData.push(rowData);
      } else {
        tempData[index] = rowData;
      }
      setGoodsPosRefundVoucherList([...tempData]);
      return Promise.resolve();
    },
    [goodsId, goodsPosRefundVoucherList, setGoodsPosRefundVoucherList, t]
  );

  return (
    <div
      style={{
        marginBottom: 24,
      }}
      ref={containerEl}
    >
      <div style={{ marginBottom: 6, fontSize: 14, fontWeight: 'bold' }}>{t('For POS Premium Refund')}</div>
      <div>{t('Please configure POS premium refund rule for each sales channel.')}</div>
      <EditTable
        disabled={disabled}
        columns={columns}
        data={goodsPosRefundVoucherList}
        scroll={{ x: 'max-content' }}
        onSubmit={onTableSubmit}
        onDelete={(key: string, index: number) => {
          goodsPosRefundVoucherList?.splice(index, 1);

          setGoodsPosRefundVoucherList([...goodsPosRefundVoucherList]);
        }}
        onEditBefore={handleEditBefore}
        onCancel={() => {
          goodsPosRefundVoucherList.forEach(row => {
            row.editing = false;
          });
        }}
        onStateChange={(state: string) => {}}
        pagination={
          goodsPosRefundVoucherList?.length > 10
            ? {
                defaultPageSize: 10,
              }
            : undefined
        }
      />
    </div>
  );
};

export default forwardRef(ForPOSPremiumRefundTable);
