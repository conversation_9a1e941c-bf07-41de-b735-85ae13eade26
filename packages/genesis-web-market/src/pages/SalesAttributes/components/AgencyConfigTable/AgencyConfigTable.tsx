import { forwardRef, useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { Tooltip, message } from 'antd';

import { useRequest } from 'ahooks';

import {
  FormulaProps,
  GoodsSalesPartner,
  MktYesNo,
  PartnerFeeType,
  PartnerType,
  SalesPartner,
  SalesPartnerFormula,
  SettlementRule,
} from 'genesis-web-service';

import { BizTopic } from '@market/common/enums';
import { SalesAttributesNS } from '@market/common/salesAttributes.interface';
import EditTable from '@market/components/F-EditableTable';
import { useBizDict } from '@market/hook/bizDict';
import { useAgencyList } from '@market/hook/channel.serivce';
import { queryTotalChannelList } from '@market/marketService/channel.service';
import { mapBizdictToOldBizdict, mapChannelToOldBizdict, renderOldBizdictName } from '@market/utils/enum';

import ClawbackCommissionFormula from '../ClawbackCommissionFormula';
import CommissionFormulaTable from '../CommissionFormulaTable';

interface Props {
  goodsId: number | string;
  disabled: boolean;
  totalFormulaList: FormulaProps[];
  agencySalesPartner?: SalesPartner;
  onEditStateChange: (isEditing: boolean) => void;
  submitService: (salesPartner: SalesPartner) => Promise<unknown>;
  reload: () => void;
}

export const AgencyConfigTable = ({
  goodsId,
  disabled,
  agencySalesPartner,
  totalFormulaList,
  onEditStateChange,
  submitService,
  reload,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const settlementRuleBizDicts = useBizDict('settlementRule');
  const yesNoOptions = useBizDict('yesNo');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef<HTMLDivElement>(null);
  const [t] = useTranslation(['market', 'common']);
  const totalAgencyList = useAgencyList();
  const partnerType = PartnerType.agencyCompany;

  const agencyPartnerList: SalesAttributesNS.FESalesPartnerAgency[] = useMemo(
    () =>
      agencySalesPartner?.goodsSalesPartnerList
        .filter(item => item.partnerType === PartnerType.agencyCompany)
        .map(item => ({
          key: Math.random(),
          ...item,
        })) || [],
    [agencySalesPartner]
  );

  const handleEditBefore = useCallback(
    (record: SalesAttributesNS.FESalesPartnerAgency) => {
      const tempTableData = [...agencyPartnerList];
      const editingRow = tempTableData.find((row: SalesAttributesNS.FESalesPartnerAgency) => row.key === record.key);
      if (editingRow) {
        editingRow.editing = true;
        editingRow.accumulatedToPremium = `${editingRow.accumulatedToPremium!}`;
        editingRow.settlementRule = `${editingRow.settlementRule!}`;
      }
    },
    [agencyPartnerList]
  );

  const { data: totalChannelOptions = [] } = useRequest(() =>
    queryTotalChannelList().then(res => res.map(mapChannelToOldBizdict))
  );

  const totalAgencyOptions = useMemo(() => totalAgencyList.map(mapChannelToOldBizdict), [totalAgencyList]);
  const settlementRuleOptions = useMemo(
    () => settlementRuleBizDicts.map(mapBizdictToOldBizdict),
    [settlementRuleBizDicts]
  );

  const columns = useMemo(
    () => [
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Company Name')}</span>,
        dataIndex: 'partnerCodes',
        editable: true,
        inputType: 'select',
        width: '240px',
        selectOptions: totalAgencyOptions,
        render: (partnerCodes: string[]): string =>
          partnerCodes?.map(partnerCode => renderOldBizdictName(partnerCode, totalAgencyOptions)).join(', '),
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          mode: 'multiple',
          getPopupContainer: () => containerEl.current || document.body,
        },
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Sales Platform')}</span>,
        editable: true,
        dataIndex: 'relatedChannelCodeList',
        inputType: 'select',
        selectOptions: totalChannelOptions,
        render: (relatedChannelCodes: string[]): JSX.Element => {
          const codeText = relatedChannelCodes?.map(code => renderOldBizdictName(code, totalChannelOptions)).join(',');
          return (
            <Tooltip title={codeText}>
              <span
                style={{
                  display: 'inline-block',
                  maxWidth: 240,
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {codeText || t('- -')}
              </span>
            </Tooltip>
          );
        },
        width: '240px',
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          mode: 'multiple',
          getPopupContainer: () => containerEl.current || document.body,
        },
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Settlement Rule')}</span>,
        editable: true,
        dataIndex: 'settlementRule',
        inputType: 'select',
        initialValue: SettlementRule.basedOnPremiumOffset,
        selectOptions: settlementRuleOptions,
        render: (settlementRule: string): string => renderOldBizdictName(settlementRule, settlementRuleOptions),
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
        },
        width: '240px',
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Accumulated to Premium')}</span>,
        editable: true,
        dataIndex: 'accumulatedToPremium',
        inputType: 'select',
        initialValue: MktYesNo.No,
        selectOptions: yesNoOptions,
        render: (accumulatedToPremium: string): string =>
          accumulatedToPremium ? renderOldBizdictName(accumulatedToPremium, yesNoOptions) : t('- -'),
        width: '240px',
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
        },
      },
    ],
    [settlementRuleOptions, t, totalAgencyOptions, totalChannelOptions, yesNoOptions]
  );

  const saveSalesPartnerNew = useCallback(
    (list: SalesAttributesNS.FESalesPartnerAgency[] | null, formulalist: SalesPartnerFormula[] | null) => {
      // 如果没传取原始数据
      let goodsSalesPartnerList = agencySalesPartner?.goodsSalesPartnerList;
      if (list) {
        goodsSalesPartnerList = list;
      }
      const goodsSalesPartnerFormulaList = formulalist
        ? formulalist.map(row => {
            row.goodsId = goodsId;
            row.partnerType = partnerType;

            return row;
          })
        : agencySalesPartner?.goodsSalesPartnerFormulaList;

      submitService({
        goodsId,
        partnerType,
        goodsSalesPartnerList: goodsSalesPartnerList || [],
        goodsSalesPartnerFormulaList: goodsSalesPartnerFormulaList || [],
      }).then(() => {
        message.success(t('Submit Successfully'));
        reload();
      });
    },
    [agencySalesPartner, goodsId, partnerType, reload, submitService, t]
  );

  const saveFormulaList = useCallback(
    (list: SalesPartnerFormula[]) => {
      saveSalesPartnerNew(null, list);
    },
    [saveSalesPartnerNew]
  );

  // 把RelatedChannelCodeList里面的数据拆出来
  const fillRelatedChannelCodeList = useCallback(
    (tempData: SalesAttributesNS.FESalesPartnerAgency[]) => {
      const tempList: GoodsSalesPartner[] = [];
      // 补充关联的channel信息
      tempData?.forEach(row => {
        tempList.push(row);
        row.relatedChannelCodeList?.forEach(channelCode => {
          tempList.push({
            key: Math.random(),
            partnerType: PartnerType.salesChannel,
            partnerCode: channelCode,
            partnerCodes: [channelCode],
            goodsId,
          });
        });
      });

      return tempList;
    },
    [goodsId]
  );

  const onTableSubmit = useCallback(
    (
      rowData: SalesAttributesNS.FESalesPartnerAgency & {
        key: number;
        editing: boolean;
      },
      key,
      index: number
    ) => {
      const tempData = [...agencyPartnerList];
      if (tempData.find(row => !row.editing && row.partnerCode === rowData.partnerCode)) {
        message.error(t('Duplicate Sales Platform. Please change.'));
        return Promise.reject();
      }

      rowData.goodsId = goodsId;
      rowData.partnerType = partnerType;
      if (key === 'add') {
        rowData.key = Math.random();
        tempData.push(rowData);
      } else {
        tempData[index] = rowData;
      }

      // accumulatedToPremium 所有数据保持一致
      tempData.forEach(existRow => {
        existRow.accumulatedToPremium = rowData.accumulatedToPremium;
      });

      const tempList: GoodsSalesPartner[] = fillRelatedChannelCodeList(tempData);

      saveSalesPartnerNew([...tempList], null);
      return Promise.resolve();
    },
    [agencyPartnerList, fillRelatedChannelCodeList, goodsId, partnerType, saveSalesPartnerNew, t]
  );

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-return
  const commissionFormulaList = useMemo(
    () =>
      ((agencySalesPartner?.goodsSalesPartnerFormulaList as SalesPartnerFormula[]) || [])?.filter(
        item =>
          `${item.feeType}` === PartnerFeeType.commission &&
          `${item.formulaCategory}` === BizTopic.Commission &&
          !item?.agentCategory // 可能是老字段
      ) || [],
    [agencySalesPartner?.goodsSalesPartnerFormulaList]
  );

  const clawbackcommissionFormulaList = useMemo(
    () =>
      ((agencySalesPartner?.goodsSalesPartnerFormulaList as SalesPartnerFormula[]) || [])?.filter(
        item =>
          `${item.feeType}` === PartnerFeeType.commission &&
          `${item.formulaCategory}` === BizTopic.CommissionClawback &&
          !item?.agentCategory // 可能是老字段
      ) || [],
    [agencySalesPartner?.goodsSalesPartnerFormulaList]
  );

  return (
    <div
      style={{
        marginBottom: 24,
      }}
      ref={containerEl}
    >
      <EditTable
        disabled={disabled}
        columns={columns}
        data={agencyPartnerList}
        scroll={{ x: 'max-content' }}
        onSubmit={onTableSubmit}
        onDelete={(key: string, index: number) => {
          agencyPartnerList?.splice(index, 1);

          const tempList: GoodsSalesPartner[] = fillRelatedChannelCodeList(agencyPartnerList);

          saveSalesPartnerNew([...tempList], null);
        }}
        onEditBefore={handleEditBefore}
        onCancel={() => {
          agencyPartnerList.forEach(row => {
            row.editing = false;
          });
        }}
        onStateChange={(state: string) => {
          onEditStateChange(state === 'Editing');
        }}
        pagination={
          agencyPartnerList?.length > 10
            ? {
                defaultPageSize: 10,
              }
            : undefined
        }
      />
      <CommissionFormulaTable
        goodsId={goodsId}
        // refInstance={commissionFormulaRef}
        disabled={disabled}
        totalFormulaList={totalFormulaList}
        initData={commissionFormulaList}
        onEditStateChange={() => {}}
        saveFormulaList={formulaList => {
          saveFormulaList([...formulaList, ...clawbackcommissionFormulaList]);
        }}
      />
      <ClawbackCommissionFormula
        goodsId={goodsId}
        disabled={disabled}
        totalFormulaList={totalFormulaList}
        initData={clawbackcommissionFormulaList}
        onEditStateChange={() => {}}
        saveFormulaList={formulaList => {
          saveFormulaList([...commissionFormulaList, ...formulaList]);
        }}
      />
    </div>
  );
};

export default forwardRef(AgencyConfigTable);
