import React from 'react';
import { useTranslation } from 'react-i18next';

import { Form, FormInstance, Radio } from 'antd';

import { PolicyEffectiveWithoutCollection } from '@market/common/enums';
import { RuleCategory, SelectOption } from '@market/common/interface';
import { RuleOrRuleSet } from '@market/components/RuleOrRuleSet/RuleOrRuleSet';
import { useBizDictAsOptions } from '@market/hook/bizDict';

interface Props {
  disabled: boolean;
  data: {
    policyEffectiveWithoutCollectionRenewal: number;
    ruleCategoryRenewal: string;
    ruleCodeRenewal: string;
  };
  ruleCategoryOptions: SelectOption[];
  ruleList: SelectOption[];
  form: FormInstance;
}

export const PolicyEffectiveWithoutCollectionRenewal = ({
  form,
  disabled,
  data,
  ruleCategoryOptions,
  ruleList,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const policyEffectiveWithoutCollectionOptions = useBizDictAsOptions('policyEffectiveWithoutCollection');
  /* ============== 枚举使用end ============== */
  const [t] = useTranslation(['market', 'common']);

  return (
    <React.Fragment>
      <Form.Item
        label={t('Policy Effective Without Collection (Renewal)')}
        name="policyEffectiveWithoutCollectionRenewal"
        initialValue={data?.policyEffectiveWithoutCollectionRenewal || null}
      >
        <Radio.Group disabled={disabled}>
          {policyEffectiveWithoutCollectionOptions.map(item => (
            <Radio value={+item.value}>{item.label}</Radio>
          ))}
          <Radio value={null}>{t('Not Relevant')}</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) =>
          getFieldValue('policyEffectiveWithoutCollectionRenewal') === PolicyEffectiveWithoutCollection.ByRule ? (
            <RuleOrRuleSet
              form={form}
              disabled={disabled}
              ruleCategoryFieldName="ruleCategoryRenewal"
              ruleCodeFieldName="ruleCodeRenewal"
              ruleCategoryInitialValue={RuleCategory.PolicyEffectiveWithoutCollection}
              ruleCodeInitialValue={data.ruleCodeRenewal}
              ruleCategoryOptions={ruleCategoryOptions}
              ruleList={ruleList}
            />
          ) : null
        }
      </Form.Item>
    </React.Fragment>
  );
};

export default PolicyEffectiveWithoutCollectionRenewal;
