import { Ref, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { message } from 'antd';

import {
  FormulaProps,
  MktYesNo,
  PartnerFeeType,
  PartnerType,
  SalesPartnerServiceCompany,
  ServiceFeeFormula,
  SettlementRule,
} from 'genesis-web-service';

import { BizTopic } from '@market/common/enums';
import { SalesAttributesNS } from '@market/common/salesAttributes.interface';
import EditTable from '@market/components/F-EditableTable';
import { useBizDict, useBizDictAsOptions } from '@market/hook/bizDict';
import { useServiceCompanyList } from '@market/hook/channel.serivce';
import {
  mapBizdictToOldBizdict,
  mapChannelToOldBizdict,
  mapFormulaToOldBizdict,
  renderOldBizdictName,
  renderOptionName,
} from '@market/utils/enum';

import styles from './ServiceCompanyConfigTable.module.scss';

interface Props {
  goodsId: string | number;
  disabled: boolean;
  refInstance: Ref<{
    getServiceCompanyPartnerList: () => SalesPartnerServiceCompany[];
    getServiceFeeFormulaList: () => ServiceFeeFormula[];
    getServiceFeeClawbackFormulaList: () => ServiceFeeFormula[];
  }>;
  totalFormulaList: FormulaProps[];
  initData?: SalesPartnerServiceCompany[];
  initServiceFeeFormulaList: ServiceFeeFormula[];
  onEditStateChange: (isEditing: boolean) => void;
  saveSalesPartner: () => void;
}

export const ServiceCompanyConfigTable = ({
  goodsId,
  disabled,
  refInstance,
  totalFormulaList,
  initData,
  initServiceFeeFormulaList,
  onEditStateChange,
  saveSalesPartner,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const partnerTypeOptions = useBizDictAsOptions('partnerType');
  const settlementRuleBizDicts = useBizDict('settlementRule');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef<HTMLDivElement>(null);
  const [t] = useTranslation(['market', 'common']);
  const [serviceCompanyList, setServiceCompanyList] = useState<SalesAttributesNS.FESalesPartnerServiceCompany[]>([]);
  const companyList = useServiceCompanyList();

  useEffect(() => {
    if (initData && initData.length > 0) {
      setServiceCompanyList(
        initData.map(item => {
          const key = Math.random();
          const formula = initServiceFeeFormulaList.find(
            serviceFeeFormula =>
              serviceFeeFormula.partnerCode === item.partnerCode &&
              `${serviceFeeFormula.formulaCategory}` === BizTopic.Commission
          );
          const clawbackformula = initServiceFeeFormulaList.find(
            serviceFeeFormula =>
              serviceFeeFormula.partnerCode === item.partnerCode &&
              `${serviceFeeFormula.formulaCategory}` === BizTopic.CommissionClawback
          );

          return {
            ...item,
            key,
            id: key,
            formulaCategory: BizTopic.Commission,
            formulaCode: formula?.formulaCode || '',
            clawbackFormulaCode: clawbackformula?.formulaCode || '',
          };
        })
      );
    }
  }, []);

  useImperativeHandle(refInstance, () => ({
    getServiceCompanyPartnerList: () =>
      serviceCompanyList.map(serviceCompany => ({
        goodsId,
        partnerType: PartnerType.serviceComapny,
        partnerCode: serviceCompany.partnerCode,
        accumulatedToPremium: serviceCompany.accumulatedToPremium,
        settlementRule: serviceCompany.settlementRule,
      })),
    getServiceFeeFormulaList: (): ServiceFeeFormula[] =>
      serviceCompanyList
        .filter(serviceCompany => serviceCompany.formulaCode)
        .map(serviceCompany => ({
          goodsId,
          partnerType: PartnerType.serviceComapny,
          partnerCode: serviceCompany.partnerCode,
          formulaCategory: BizTopic.Commission,
          formulaCode: serviceCompany.formulaCode,
          feeType: PartnerFeeType.serviceFee,
        })),
    getServiceFeeClawbackFormulaList: (): ServiceFeeFormula[] =>
      serviceCompanyList
        .filter(serviceCompany => serviceCompany.clawbackFormulaCode)
        .map(serviceCompany => ({
          goodsId,
          partnerType: PartnerType.serviceComapny,
          partnerCode: serviceCompany.partnerCode,
          formulaCategory: BizTopic.CommissionClawback,
          formulaCode: serviceCompany.clawbackFormulaCode,
          feeType: PartnerFeeType.serviceFee,
        })),
  }));

  const handleEditBefore = useCallback(
    (record: SalesAttributesNS.FESalesPartnerServiceCompany) => {
      const tempTableData = [...serviceCompanyList];
      const editingRow = tempTableData.find(
        (row: SalesAttributesNS.FESalesPartnerServiceCompany) => row.key === record.key
      );
      if (editingRow) {
        editingRow.editing = true;
        editingRow.accumulatedToPremium = `${editingRow.accumulatedToPremium!}`;
        editingRow.settlementRule = `${editingRow.settlementRule}`;
      }
    },
    [serviceCompanyList]
  );

  const totalServiceCompanyOptions = useMemo(() => companyList.map(mapChannelToOldBizdict), [companyList]);
  const settlementRuleOptions = useMemo(
    () => settlementRuleBizDicts.map(mapBizdictToOldBizdict),
    [settlementRuleBizDicts]
  );

  const yesNoOptions = useMemo(
    () => [
      {
        itemExtend1: MktYesNo.Yes,
        itemName: t('yes'),
      },
      {
        itemExtend1: MktYesNo.No,
        itemName: t('no'),
      },
    ],
    [t]
  );

  const totalCommissionFormulaOptions = useMemo(
    () =>
      totalFormulaList
        .filter(formula => `${formula.formulaTableTypeId}` === BizTopic.Commission)
        .map(mapFormulaToOldBizdict),
    [totalFormulaList]
  );

  const totalCommissionClawbackFormulaOptions = useMemo(
    () =>
      totalFormulaList
        .filter(formula => `${formula.formulaTableTypeId}` === BizTopic.CommissionClawback)
        .map(mapFormulaToOldBizdict),
    [totalFormulaList]
  );

  const columns = useMemo(
    () => [
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Company Name')}</span>,
        dataIndex: 'partnerCode',
        editable: true,
        inputType: 'select',
        width: '240px',
        selectOptions: totalServiceCompanyOptions,
        render: (partnerCode: string): string => renderOldBizdictName(partnerCode, totalServiceCompanyOptions),
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
        },
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Settlement Rule')}</span>,
        editable: true,
        dataIndex: 'settlementRule',
        inputType: 'select',
        selectOptions: settlementRuleOptions,
        initialValue: SettlementRule.basedOnPremiumOffset,
        render: (settlementRule: string): string => renderOldBizdictName(settlementRule, settlementRuleOptions),
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
        },
        width: '240px',
      },
      {
        title: t('Formula Name'),
        editable: true,
        dataIndex: 'formulaCode',
        inputType: 'select',
        selectOptions: totalCommissionFormulaOptions,
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
        },
        width: '240px',
        render: (text: string): string => renderOldBizdictName(text, totalCommissionFormulaOptions) || t('- -'),
      },
      {
        title: t('Formula Name (Clawback)'),
        editable: true,
        dataIndex: 'clawbackFormulaCode',
        inputType: 'select',
        selectOptions: totalCommissionClawbackFormulaOptions,
        controllerprops: {
          allowClear: true,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
        },
        width: '240px',
        render: (text: string): string => renderOldBizdictName(text, totalCommissionClawbackFormulaOptions) || t('- -'),
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Accumulated to Premium')}</span>,
        editable: true,
        dataIndex: 'accumulatedToPremium',
        inputType: 'select',
        initialValue: MktYesNo.No,
        selectOptions: yesNoOptions,
        render: (accumulatedToPremium: string): string =>
          accumulatedToPremium ? renderOldBizdictName(accumulatedToPremium, yesNoOptions) : t('- -'),
        width: '240px',
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
        },
      },
    ],
    [totalCommissionFormulaOptions, settlementRuleOptions, t, totalServiceCompanyOptions, yesNoOptions]
  );

  const onTableSubmit = useCallback(
    (
      rowData: SalesAttributesNS.FESalesPartnerServiceCompany & {
        key: number;
        editing: boolean;
      },
      key,
      index: number
    ) => {
      const tempData = [...serviceCompanyList];
      if (tempData.find(row => !row.editing && row.partnerCode === rowData.partnerCode)) {
        message.error(t('Duplicate Company Name. Please change.'));
        return Promise.reject();
      }

      if (key === 'add') {
        rowData.key = Math.random();
        tempData.push(rowData);
      } else {
        tempData[index] = rowData;
      }
      rowData.formulaCategory = BizTopic.Commission;

      setServiceCompanyList([...tempData]);
      setTimeout(() => {
        saveSalesPartner();
      }, 300);
      return Promise.resolve();
    },
    [saveSalesPartner, serviceCompanyList, t]
  );

  return (
    <div ref={containerEl} className={styles.container}>
      <div className={styles.optionName}>{renderOptionName(`${PartnerType.serviceComapny}`, partnerTypeOptions)}</div>
      <EditTable
        disabled={disabled}
        columns={columns}
        data={serviceCompanyList}
        scroll={{ x: 'max-content' }}
        onSubmit={onTableSubmit}
        onDelete={(key: string, index: number) => {
          serviceCompanyList?.splice(index, 1);

          setServiceCompanyList([...serviceCompanyList]);
          setTimeout(() => {
            saveSalesPartner();
          }, 300);
        }}
        onEditBefore={handleEditBefore}
        onCancel={() => {
          serviceCompanyList.forEach(row => {
            row.editing = false;
          });
        }}
        onStateChange={(state: string) => {
          onEditStateChange(state === 'Editing');
        }}
        pagination={
          serviceCompanyList?.length > 10
            ? {
                defaultPageSize: 10,
              }
            : undefined
        }
      />
    </div>
  );
};

export default forwardRef(ServiceCompanyConfigTable);
