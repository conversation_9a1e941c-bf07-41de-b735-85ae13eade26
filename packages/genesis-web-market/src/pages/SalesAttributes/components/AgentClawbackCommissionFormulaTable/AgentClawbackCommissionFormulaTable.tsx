import { forwardRef, useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Dropdown, Menu } from 'antd';

import { DeleteAction, EditAction, Icon, Table } from '@zhongan/nagrand-ui';

import { AgentFormulaInfo, FormulaProps } from 'genesis-web-service';

import { BizTopic, CommissionSubCategory } from '@market/common/enums';
import { TableType } from '@market/common/salesAttributes.interface';
import { useBizDict } from '@market/hook/bizDict';
import { useCommissionFormulaOptions } from '@market/hook/goods.service';
import { mapFormulaToOldBizdict, renderEnumName, renderOldBizdictName } from '@market/utils/enum';

import AgentDrawer from '../AgentDrawer';

interface Props {
  disabled: boolean;
  submitLoading: boolean;
  saveSalesPartner: (arr: AgentFormulaInfo[], arrType: TableType) => void;
  totalFormulaList: FormulaProps[];
  agentClawbackCommissionFormulaList: AgentFormulaInfo[];
}

export const AgentClawbackCommissionFormulaTable = ({
  disabled,
  submitLoading,
  saveSalesPartner,
  totalFormulaList,
  agentClawbackCommissionFormulaList,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const bizTopicBizDicts = useBizDict('bizTopic');
  const agentCategoryBizDicts = useBizDict('agentCategory');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef<HTMLDivElement>(null);
  const [t] = useTranslation(['market', 'common']);
  const formulaCategory = BizTopic.CommissionClawback;
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [editingSubCategory, setEditingSubCategory] = useState<string>();
  const [agentCategory, setAgentCategory] = useState<string>();
  const [editRecordData, setEditRecordData] = useState<AgentFormulaInfo>();
  const [editRecordIndex, setEditRecordIndex] = useState<number>();

  const formulaSubCategoryOptions = useMemo(
    () =>
      bizTopicBizDicts
        ?.find(bizdict => bizdict.dictValue === BizTopic.CommissionClawback)
        ?.childList?.map(bizdict => ({
          value: bizdict.dictValue,
          label: bizdict.dictValueName,
        })) || [],
    [bizTopicBizDicts]
  );

  const formulaOptionsBySubCategory = useCommissionFormulaOptions(
    totalFormulaList,
    BizTopic.CommissionClawback,
    editingSubCategory
  );

  const totalCommissionFormulaOptions = useMemo(
    () =>
      totalFormulaList
        .filter(formula => `${formula.formulaTableTypeId}` === BizTopic.CommissionClawback)
        .map(mapFormulaToOldBizdict),
    [totalFormulaList]
  );

  const confirmDelete = useCallback(
    (record: AgentFormulaInfo, index: number) => {
      const tempAgentClawbackCommissionFormulaList = agentClawbackCommissionFormulaList;
      tempAgentClawbackCommissionFormulaList.splice(index, 1);
      tempAgentClawbackCommissionFormulaList
        .filter(item => item.agentCategory === record.agentCategory)
        .forEach((item, idx) => {
          item.formulaOrder = idx + 1;
          if (idx + 1 === 1) {
            item.rowSpan = tempAgentClawbackCommissionFormulaList.filter(
              agentitem => agentitem.agentCategory === record.agentCategory
            ).length;
          } else {
            item.rowSpan = 0;
          }
        });
      saveSalesPartner(tempAgentClawbackCommissionFormulaList, 'agentClawbackCommission');
    },
    [agentClawbackCommissionFormulaList, saveSalesPartner]
  );

  const handleEdit = useCallback((record: AgentFormulaInfo, index: number) => {
    setEditRecordData(record);
    setDrawerVisible(true);
    setEditRecordIndex(index);
    setAgentCategory(record.agentCategory);
  }, []);

  const columns = useMemo(
    () => [
      {
        title: t('Agent Category'),
        dataIndex: 'agentCategory',
        key: 'agentCategory',
        className: 'agentCategory',
        width: 240,
        render: (text: string, record: AgentFormulaInfo, index: number) => {
          const obj = {
            children: renderOldBizdictName(text, agentCategoryBizDicts) || t('- -'),
            props: { rowSpan: record.rowSpan || 0 },
          };
          return obj;
        },
      },
      {
        title: t('Formula Category'),
        dataIndex: 'formulaCategory',
        key: 'formulaCategory',
        width: 240,
        render: () =>
          renderEnumName(formulaCategory, bizTopicBizDicts, {
            labelKey: 'dictValueName',
            valueKey: 'dictValue',
          }),
      },
      {
        title: t('Formula Sub Category'),
        dataIndex: 'formulaSubCategory',
        key: 'formulaSubCategory',
        width: 240,
        render: (formulaSubCategory = CommissionSubCategory.GrossCommission): string =>
          renderOldBizdictName(
            formulaSubCategory,
            (formulaSubCategoryOptions || []).map(item => ({
              itemName: item.label,
              itemExtend1: item.value,
            }))
          ),
      },
      {
        title: t('Formula Name'),
        dataIndex: 'formulaCode',
        key: 'formulaCode',
        width: 240,
        render: (text: string): string => renderOldBizdictName(text, totalCommissionFormulaOptions) || t('- -'),
      },
      {
        title: t('Actions'),
        width: 100,
        render: (text: any, record: AgentFormulaInfo, index: number) =>
          !disabled && (
            <div>
              <EditAction onClick={() => handleEdit(record, index)} />
              <DeleteAction
                doubleConfirmType="popconfirm"
                onClick={() => confirmDelete(record, index)}
                deleteConfirmContent={t('Are you sure to delete this record?')}
              />
            </div>
          ),
      },
    ],
    [
      disabled,
      agentCategoryBizDicts,
      bizTopicBizDicts,
      formulaCategory,
      formulaSubCategoryOptions,
      t,
      totalCommissionFormulaOptions,
      confirmDelete,
      handleEdit,
    ]
  );

  const addAgentItem = useCallback((value: string) => {
    setDrawerVisible(true);
    setAgentCategory(value);
  }, []);

  const resetState = useCallback(() => {
    setDrawerVisible(false);
    setAgentCategory(undefined);
    setEditRecordData(undefined);
    setEditRecordIndex(undefined);
    setEditingSubCategory(undefined);
  }, []);

  const onSubmitList = useCallback(
    (arr: AgentFormulaInfo[]) => {
      saveSalesPartner(arr, 'agentClawbackCommission');
      resetState();
    },
    [resetState, saveSalesPartner]
  );

  return (
    <div
      style={{
        marginBottom: 24,
      }}
      ref={containerEl}
    >
      <div style={{ marginBottom: 12 }}>{t('Please select related formula for Commission Clawback')}</div>
      <div>
        {disabled ? null : (
          <Dropdown
            getPopupContainer={triggerNode => triggerNode.parentNode as HTMLElement}
            dropdownRender={() => (
              <Menu>
                {(agentCategoryBizDicts || []).map(item => (
                  <Menu.Item onClick={() => addAgentItem(item.dictValue)} style={{ textAlign: 'center' }}>
                    {item.dictValueName}
                  </Menu.Item>
                ))}
              </Menu>
            )}
          >
            <Button icon={<Icon type="add" />} style={{ width: '100%', marginBottom: 8 }} className="block-add-btn">
              {t('Add')}
            </Button>
          </Dropdown>
        )}
        <Table
          pagination={false}
          indentSize={0}
          loading={submitLoading}
          dataSource={agentClawbackCommissionFormulaList}
          columns={columns}
          scroll={{ x: 'max-content' }}
        />
      </div>
      <AgentDrawer
        disabled={disabled}
        visible={drawerVisible}
        agentCategory={agentCategory}
        editRecordData={editRecordData}
        formulaCategory={formulaCategory}
        editRecordIndex={editRecordIndex}
        onClose={() => resetState()}
        formulaSubCategoryOptions={formulaSubCategoryOptions}
        formulaOptionsBySubCategory={formulaOptionsBySubCategory}
        onSubmitList={onSubmitList}
        setEditingSubCategory={setEditingSubCategory}
        agentFormulaList={agentClawbackCommissionFormulaList}
      />
    </div>
  );
};

export default forwardRef(AgentClawbackCommissionFormulaTable);
