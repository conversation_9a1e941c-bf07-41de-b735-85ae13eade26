import { forwardRef, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { message } from 'antd';
import { Form } from 'antd';

import {
  AgentFormulaInfo,
  FormulaProps,
  PartnerType,
  SalesPartner,
  SalesPartnerChannel,
  SalesPartnerFormula,
  SettlementRule,
} from 'genesis-web-service';

import { BizTopic } from '@market/common/enums';
import { AgentCategoryType, SalesAttributesNS, TableType } from '@market/common/salesAttributes.interface';
import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDict, useBizDictAsOptions } from '@market/hook/bizDict';

import { partitionAgentFormulaList } from '../../dealServiceData';
import AgentClawbackCommissionFormulaTable from '../AgentClawbackCommissionFormulaTable';
import AgentCommissionFormulaTable from '../AgentCommissionFormulaTable';

interface Props {
  goodsId: number | string;
  agentSalesPartner?: SalesPartner;
  disabled: boolean;
  submitLoading: boolean;
  totalFormulaList: FormulaProps[];
  tiedAgentChannelCodeList: string[];
  submitService: (salesPartner: SalesPartner) => Promise<unknown>;
  reload: () => void;
}

export const AgentConfigTable = ({
  goodsId,
  agentSalesPartner,
  disabled,
  submitLoading,
  totalFormulaList,
  tiedAgentChannelCodeList,
  submitService,
  reload,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const settlementRuleBizDicts = useBizDictAsOptions('settlementRule');
  /* ============== 枚举使用end ============== */
  const partnerType = PartnerType.agent;
  const [form] = Form.useForm();
  const containerEl = useRef<HTMLDivElement>(null);
  const [t] = useTranslation(['market', 'common']);
  const [agentCommissionFormulaList, setAgentCommissionFormulaList] = useState<AgentFormulaInfo[]>([]);
  const [agentClawbackCommissionFormulaList, setAgentClawbackCommissionFormulaList] = useState<AgentFormulaInfo[]>([]);
  const [agentSettlementRule, setAgentSettlementRule] = useState<string>();

  useEffect(() => {
    const tempAgentPartner = (agentSalesPartner?.goodsSalesPartnerList as SalesPartnerChannel[])?.find(
      item => +item.salesChannelType === PartnerType.agentSalesChannel
    );
    if (tempAgentPartner && tempAgentPartner.settlementRule) {
      setAgentSettlementRule(`${tempAgentPartner.settlementRule}`);
      form.setFieldsValue({
        agentSettlementRule: `${tempAgentPartner.settlementRule}`,
      });
    } else {
      form.setFieldsValue({
        agentSettlementRule: undefined,
      });
      setAgentSettlementRule(undefined);
    }
    const goodsSalesPartnerFormulaList = (agentSalesPartner?.goodsSalesPartnerFormulaList as AgentFormulaInfo[]) || [];
    const tempAgentCommissionFormulaList: AgentFormulaInfo[] =
      goodsSalesPartnerFormulaList.filter(
        item => item?.agentCategory && item.formulaCategory === +BizTopic.Commission
      ) || [];
    if (tempAgentCommissionFormulaList.length > 0) {
      partitionAgentFormulaList(tempAgentCommissionFormulaList, setAgentCommissionFormulaList);
    } else {
      setAgentCommissionFormulaList([]);
    }

    const tempAgentClawbackCommissionFormulaList: AgentFormulaInfo[] =
      goodsSalesPartnerFormulaList.filter(
        (item: AgentFormulaInfo) => item?.agentCategory && item.formulaCategory === +BizTopic.CommissionClawback
      ) || [];
    if (tempAgentClawbackCommissionFormulaList.length > 0) {
      tempAgentClawbackCommissionFormulaList
        .filter(clawbackCommissionItem => +clawbackCommissionItem.agentCategory === +AgentCategoryType.BranchManager)
        .forEach((branchItem, index) => {
          branchItem.formulaOrder = index + 1;
        });
      tempAgentClawbackCommissionFormulaList
        .filter(clawbackCommissionItem => +clawbackCommissionItem.agentCategory === +AgentCategoryType.Agent)
        .forEach((agentItem, index) => {
          agentItem.formulaOrder = index + 1;
        });
      partitionAgentFormulaList([...tempAgentClawbackCommissionFormulaList], setAgentClawbackCommissionFormulaList);
    } else {
      setAgentClawbackCommissionFormulaList([]);
    }
  }, [agentSalesPartner]);

  const saveSalesPartnerNew = useCallback(
    (list: SalesAttributesNS.FESalesPartnerChannel[] | null, formulalist: AgentFormulaInfo[] | null) => {
      // 如果没传取原始数据
      let tempGoodsSalesPartnerList = agentSalesPartner?.goodsSalesPartnerList || [];
      if (list) {
        tempGoodsSalesPartnerList = list;
      }

      const goodsSalesPartnerFormulaList = formulalist
        ? formulalist.map(row => {
            row.goodsId = goodsId;
            row.partnerType = partnerType;

            return row;
          })
        : agentSalesPartner?.goodsSalesPartnerFormulaList;

      submitService({
        goodsId,
        partnerType,
        goodsSalesPartnerList: tempGoodsSalesPartnerList || [],
        goodsSalesPartnerFormulaList: goodsSalesPartnerFormulaList || [],
      }).then(() => {
        message.success(t('Submit Successfully'));
        reload();
      });
    },
    [agentSalesPartner, submitService, goodsId, partnerType, t, reload]
  );

  const getTiedChannelList = useCallback(
    (value: string) => {
      const channelList: SalesAttributesNS.FESalesPartnerChannel[] = tiedAgentChannelCodeList.map(channelCode => ({
        goodsId,
        partnerType: PartnerType.salesChannel,
        partnerCode: channelCode,
        partnerCodes: [channelCode],
        accumulatedToPremium: '',
        relationshipCode: '',
        salesChannelType: `${PartnerType.agentSalesChannel as number}`,
        settlementRule: value ? +value : undefined,
      }));

      return channelList;
    },
    [goodsId, tiedAgentChannelCodeList]
  );

  return (
    <div
      style={{
        marginBottom: 24,
      }}
      ref={containerEl}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          label={t('Settlement Rule')}
          colon={false}
          name="agentSettlementRule"
          rules={[
            {
              required: true,
              message: t('Please select'),
            },
          ]}
        >
          <GeneralSelect
            allowClear={false}
            option={settlementRuleBizDicts.filter(item => `${item.value}` !== SettlementRule.basedOnPremiumReceived)}
            disabled={disabled}
            onChange={value => {
              saveSalesPartnerNew(getTiedChannelList(value), null);
            }}
          />
        </Form.Item>
      </Form>
      <AgentCommissionFormulaTable
        disabled={disabled}
        submitLoading={submitLoading}
        saveSalesPartner={(arr: AgentFormulaInfo[]) => {
          saveSalesPartnerNew(getTiedChannelList(form.getFieldValue('agentSettlementRule')), [
            ...arr,
            ...agentClawbackCommissionFormulaList,
          ]);
        }}
        totalFormulaList={totalFormulaList}
        agentCommissionFormulaList={agentCommissionFormulaList}
      />

      <AgentClawbackCommissionFormulaTable
        disabled={disabled}
        submitLoading={submitLoading}
        saveSalesPartner={(arr: AgentFormulaInfo[]) => {
          saveSalesPartnerNew(getTiedChannelList(form.getFieldValue('agentSettlementRule')), [
            ...arr,
            ...agentCommissionFormulaList,
          ]);
        }}
        totalFormulaList={totalFormulaList}
        agentClawbackCommissionFormulaList={agentClawbackCommissionFormulaList}
      />
    </div>
  );
};

export default AgentConfigTable;
