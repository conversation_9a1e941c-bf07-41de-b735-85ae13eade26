import { Ref, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { QuestionCircleOutlined } from '@ant-design/icons';
import { Popover, message } from 'antd';

import { Table } from '@zhongan/nagrand-ui';

import { FormulaProps, PartnerFeeType, SalesPartnerFormula } from 'genesis-web-service';

import { BizTopic, CommissionSubCategory } from '@market/common/enums';
import { SalesAttributesNS } from '@market/common/salesAttributes.interface';
import EditTable from '@market/components/F-EditableTable';
import { useBizDict, useBizDictAsOptions } from '@market/hook/bizDict';
import { useCommissionFormulaOptions } from '@market/hook/goods.service';
import { mapFormulaToOldBizdict, renderEnumName, renderOldBizdictName, renderOptionName } from '@market/utils/enum';

interface Props {
  goodsId: string | number;
  disabled: boolean;
  totalFormulaList: FormulaProps[];
  refInstance?: Ref<{
    getCommissionFormulaList: () => SalesPartnerFormula[];
  }>;
  initData?: SalesPartnerFormula[];
  onEditStateChange: (isEditing: boolean) => void;
  saveFormulaList: (list: SalesPartnerFormula[]) => void;
}

export const CommissionFormulaTable = ({
  goodsId,
  disabled,
  totalFormulaList,
  refInstance,
  initData,
  onEditStateChange,
  saveFormulaList,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const bizTopicBizDicts = useBizDict('bizTopic');
  const partnerFeeTypeOptions = useBizDictAsOptions('partnerFeeType');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef<HTMLDivElement>(null);
  const [t] = useTranslation(['market', 'common']);
  const [tableData, setTableData] = useState<SalesAttributesNS.FESalesPartnerFormula[]>([]);
  const [editingSubCategory, setEditingSubCategory] = useState<string>();
  const formulaCategory = BizTopic.Commission;

  const formulaSubCategoryOptions = useMemo(
    () =>
      bizTopicBizDicts
        ?.find(bizdict => bizdict.dictValue === BizTopic.Commission)
        ?.childList?.map(bizdict => ({
          itemExtend1: bizdict.dictValue,
          itemName: bizdict.dictValueName,
        })) || [],
    [bizTopicBizDicts]
  );

  const formulaOptionsBySubCategory = useCommissionFormulaOptions(
    totalFormulaList,
    BizTopic.Commission,
    editingSubCategory
  );

  const totalCommissionFormulaOptions = useMemo(
    () =>
      totalFormulaList
        .filter(formula => `${formula.formulaTableTypeId}` === BizTopic.Commission)
        .map(mapFormulaToOldBizdict),
    [totalFormulaList]
  );

  useEffect(() => {
    if (initData) {
      setTableData(
        initData.map(formula => {
          const key = Math.random();
          return {
            ...formula,
            key,
            id: key,
          };
        })
      );
    }
  }, [initData]);

  const resetState = useCallback(() => {
    setEditingSubCategory(undefined);
  }, []);

  useImperativeHandle(refInstance, () => ({
    getCommissionFormulaList: () =>
      tableData.map(row => ({
        formulaCode: row.formulaCode,
        formulaOrder: row.formulaOrder,
        formulaCategory,
        formulaSubCategory: row?.formulaSubCategory,
        feeType: PartnerFeeType.commission,
        goodsId,
      })),
  }));

  const handleEditBefore = useCallback(
    (record: SalesAttributesNS.FESalesPartnerFormula) => {
      const tempTableData = [...tableData];
      const editingRow = tempTableData.find((row: SalesAttributesNS.FESalesPartnerFormula) => row.key === record.key);
      if (editingRow) {
        editingRow.editing = true;
        editingRow.formulaSubCategory = editingRow.formulaSubCategory && `${editingRow.formulaSubCategory}`;
        setEditingSubCategory(editingRow.formulaSubCategory);
      }
    },
    [tableData]
  );

  const renderOrderTooltip = useCallback(
    () => (
      <div>
        <p style={{ marginBottom: 6 }}>
          {t('If Commission GST is configured based on NetCommission, then we could configure as')}
        </p>
        <Table
          columns={[
            {
              title: t('Formula Category'),
              dataIndex: 'formulaType',
            },
            {
              title: t('Formula Sub Category'),
              dataIndex: 'formulaSubCategory',
            },
            {
              title: t('Formula Code'),
              dataIndex: 'formulaCode',
            },
            {
              title: t('Order'),
              dataIndex: 'formulaOrder',
            },
          ]}
          dataSource={[
            {
              formulaType: 'Service Fee & Commission',
              formulaCode: 'commNet',
              formulaOrder: 1,
              formulaSubCategory: 'Net Commission',
            },
            {
              formulaType: 'Service Fee & Commission',
              formulaCode: 'commGST',
              formulaOrder: 2,
              formulaSubCategory: 'Commission GST',
            },
          ]}
          pagination={false}
        />
        <p style={{ marginTop: 6 }}>
          {t(
            'and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula'
          )}
        </p>
      </div>
    ),
    [t]
  );

  const columns = useMemo(
    () => [
      {
        title: t('Formula Category'),
        dataIndex: 'formulaCategory',
        editable: false,
        width: '220px',
        render: () =>
          renderEnumName(formulaCategory, bizTopicBizDicts, {
            labelKey: 'dictValueName',
            valueKey: 'dictValue',
          }),
      },
      {
        title: t('Formula Sub Category'),
        editable: true,
        dataIndex: 'formulaSubCategory',
        inputType: 'select',
        selectOptions: formulaSubCategoryOptions,
        width: '240px',
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
          onChange: (formulaSubCategory: string) => {
            setEditingSubCategory(formulaSubCategory);
          },
        },
        render: (formulaSubCategory = CommissionSubCategory.GrossCommission): string =>
          renderOldBizdictName(formulaSubCategory, formulaSubCategoryOptions),
      },
      {
        title: t('Formula Name'),
        editable: true,
        dataIndex: 'formulaCode',
        inputType: 'select',
        selectOptions: formulaOptionsBySubCategory,
        controllerprops: {
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
          disabled: !editingSubCategory,
        },
        width: '240px',
        render: (text: string): string => renderOldBizdictName(text, totalCommissionFormulaOptions) || t('- -'),
      },
      {
        title: (
          <span>
            {t('Order')}
            <Popover
              placement="topRight"
              content={renderOrderTooltip()}
              getPopupContainer={() => containerEl.current || document.body}
            >
              <QuestionCircleOutlined style={{ marginLeft: '5px' }} />
            </Popover>
          </span>
        ),
        editable: true,
        dataIndex: 'formulaOrder',
        inputType: 'number',
        width: '100px',
        controllerprops: {
          required: false,
          style: { width: 160 },
          min: 1,
          precision: 0,
          placeholder: t('Please input'),
        },
        render: (text: string) => text || t('- -'),
      },
    ],
    [
      t,
      formulaSubCategoryOptions,
      formulaOptionsBySubCategory,
      editingSubCategory,
      renderOrderTooltip,
      formulaCategory,
      bizTopicBizDicts,
      totalCommissionFormulaOptions,
    ]
  );

  const onTableSubmit = useCallback(
    (rowData: SalesAttributesNS.FESalesPartnerFormula, key, index: number) => {
      // Order 不可以填写相同的数字
      if (rowData.formulaOrder && tableData.find(row => !row.editing && row.formulaOrder === rowData.formulaOrder)) {
        message.error(t('Duplicate Order. Please change.'));
        return Promise.reject();
      }
      // FormulaCategory + FormulaSubCategory确定唯一性
      if (
        tableData.find(
          row => !row.editing && row.formulaSubCategory && `${row.formulaSubCategory}` === rowData.formulaSubCategory
        )
      ) {
        message.error(
          t('Duplicate formula configuration for same formula category and sub category. Please edit original one.')
        );
        return Promise.reject();
      }

      const tempData = [...tableData];
      rowData.editing = false;
      rowData.feeType = PartnerFeeType.commission;
      rowData.formulaCategory = BizTopic.Commission;

      if (key === 'add') {
        rowData.key = Math.random();
        // setTableData([...tempData, rowData]);
        tempData.push(rowData);
      } else {
        tempData[index] = rowData;
        // setTableData(tempData);
      }
      // setTimeout(() => {
      //   saveSalesPartner();
      // }, 300);
      saveFormulaList(tempData);
      resetState();
      return Promise.resolve();
    },
    [tableData, saveFormulaList, resetState, t]
  );

  return (
    <div
      style={{
        marginBottom: 24,
        marginTop: 24,
      }}
      ref={containerEl}
    >
      <div style={{ marginBottom: 12 }}>{t('Please select related formula for Commission')}</div>
      <EditTable
        disabled={disabled}
        columns={columns}
        data={tableData}
        scroll={{ x: 'max-content' }}
        onSubmit={onTableSubmit}
        onDelete={(key: string, index: number) => {
          tableData?.splice(index, 1);

          setTableData([...tableData]);
          saveFormulaList([...tableData]);
          // setTimeout(() => {
          //   saveSalesPartner();
          // }, 300);
        }}
        onEditBefore={handleEditBefore}
        onCancel={() => {
          tableData.forEach(row => {
            row.editing = false;
          });

          resetState();
        }}
        onStateChange={(state: string) => {
          onEditStateChange(state === 'Editing');
        }}
        pagination={
          tableData?.length > 10
            ? {
                defaultPageSize: 10,
              }
            : undefined
        }
      />
    </div>
  );
};

export default forwardRef(CommissionFormulaTable);
