import { forwardRef, useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { Tooltip, message } from 'antd';

import { intersection } from 'lodash-es';

import { Icon } from '@zhongan/nagrand-ui';

import {
  FormulaProps,
  GoodsSalesPartner,
  MktYesNo,
  PartnerFeeType,
  PartnerType,
  SalesPartner,
  SalesPartnerChannel,
  SalesPartnerFormula,
  SettlementRule,
} from 'genesis-web-service';

import { BizTopic } from '@market/common/enums';
import { SalesAttributesNS } from '@market/common/salesAttributes.interface';
import EditTable from '@market/components/F-EditableTable';
import { useBizDict } from '@market/hook/bizDict';
import { useChannelAndSubChannelList } from '@market/hook/channel.serivce';
import { mapBizdictToOldBizdict, mapChannelToOldBizdict, renderOldBizdictName } from '@market/utils/enum';

import { renderTableSalesChannel } from '../../dealServiceData';
import ClawbackCommissionFormula from '../ClawbackCommissionFormula';
import CommissionFormulaTable from '../CommissionFormulaTable';

interface Props {
  goodsId: number | string;
  disabled: boolean;
  totalFormulaList: FormulaProps[];
  platformSalesPartner?: SalesPartner;
  onEditStateChange: (isEditing: boolean) => void;
  submitService: (salesPartner: SalesPartner) => Promise<unknown>;
  reload: () => void;
}

export const ChannelConfigTable = ({
  goodsId,
  totalFormulaList,
  platformSalesPartner,
  submitService,
  reload,
  disabled,
  onEditStateChange,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const settlementRuleBizDicts = useBizDict('settlementRule');
  const yesNoOptions = useBizDict('yesNo');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef<HTMLDivElement>(null);
  const [t] = useTranslation(['market', 'common']);

  const totalChannelList = useChannelAndSubChannelList();

  const partnerType = PartnerType.salesChannel;
  const goodsSalesPartnerList = (platformSalesPartner?.goodsSalesPartnerList as SalesPartnerChannel[]) || [];

  const channePartnerlList: SalesAttributesNS.FESalesPartnerChannel[] = useMemo(
    () =>
      goodsSalesPartnerList
        .filter(item => item.partnerType === partnerType)
        .filter(item => item.settlementRule) // 没有settlementRule说明是拆出来的关联的channel
        .map(item => {
          // 合并channel
          const code = item.partnerCodes[0];
          let partnerCodes: string[] = item.partnerCodes;
          if (item.relatedChannelCodeList) {
            partnerCodes = [
              item.subPartnerCode ? `${code}-${item.subPartnerCode}` : `${code}`,
              ...item.relatedChannelCodeList,
            ];
          }
          return {
            key: Math.random(),
            ...item,
            partnerCodes,
          };
        }) || [],
    [goodsSalesPartnerList, partnerType]
  );

  const handleEditBefore = useCallback(
    (record: SalesAttributesNS.FESalesPartnerChannel) => {
      const tempTableData = [...channePartnerlList];
      const editingRow = tempTableData.find((row: SalesAttributesNS.FESalesPartnerChannel) => row.key === record.key);
      if (editingRow) {
        editingRow.editing = true;
        editingRow.partnerCode = editingRow.subPartnerCode || editingRow.partnerCode;
        editingRow.accumulatedToPremium = `${editingRow.accumulatedToPremium!}`;
        editingRow.settlementRule = editingRow.settlementRule ? `${editingRow.settlementRule}` : undefined;
      }
    },
    [channePartnerlList]
  );

  const totalChannelOptions = useMemo(
    () =>
      totalChannelList
        .map(channel => ({
          ...channel,
          code: channel.parentCode ? `${channel.parentCode}-${channel.code}` : channel.code,
        }))
        .map(mapChannelToOldBizdict),
    [totalChannelList]
  );

  const settlementRuleOptions = useMemo(
    () => settlementRuleBizDicts.map(mapBizdictToOldBizdict),
    [settlementRuleBizDicts]
  );

  const columns = useMemo(
    () => [
      {
        title: (
          <span className="market-ant4-legacy-form-item-required">
            {t('Sales Platform')}
            <Tooltip
              title={() =>
                t(
                  'If sales platform is configured, all sub channels under this sales platform could sell this product.'
                )
              }
            >
              <Icon type="info-circle" style={{ marginLeft: 5, marginTop: '-3px' }} />
            </Tooltip>
          </span>
        ),
        dataIndex: 'partnerCodes',
        editable: true,
        inputType: 'select',
        width: '300px',
        selectOptions: totalChannelOptions,
        render: (partnerCodes: string[], record: SalesPartnerChannel) =>
          partnerCodes
            ?.map(partnerCode => renderTableSalesChannel(totalChannelList, partnerCode, record.subPartnerCode))
            .join(', '),
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          mode: 'multiple',
          getPopupContainer: () => containerEl.current || document.body,
        },
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Settlement Rule')}</span>,
        editable: true,
        dataIndex: 'settlementRule',
        inputType: 'select',
        initialValue: SettlementRule.basedOnPremiumOffset,
        selectOptions: settlementRuleOptions,
        render: (settlementRule: string): string =>
          renderOldBizdictName(settlementRule, settlementRuleOptions) || t('- -'),
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
        },
        width: '240px',
      },
      {
        title: <span className="market-ant4-legacy-form-item-required">{t('Accumulated to Premium')}</span>,
        editable: true,
        dataIndex: 'accumulatedToPremium',
        inputType: 'select',
        initialValue: MktYesNo.No,
        selectOptions: yesNoOptions,
        render: (accumulatedToPremium: string): string =>
          accumulatedToPremium ? renderOldBizdictName(accumulatedToPremium, yesNoOptions) : t('- -'),
        width: '240px',
        controllerprops: {
          required: true,
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
        },
      },
    ],
    [settlementRuleOptions, t, totalChannelList, totalChannelOptions, yesNoOptions]
  );

  const getChannelInfo = useCallback(code => totalChannelList.find(item => item.code === code), [totalChannelList]);

  // 多选的channel 一条拆多条数据
  const fillRelatedChannelCodeList = useCallback(
    (list: SalesAttributesNS.FESalesPartnerChannel[]) => {
      const tempList: SalesAttributesNS.FESalesPartnerChannel[] = [];
      // 多选的channel 一条拆多条数据
      list?.forEach(row => {
        const partnerCodes = row.partnerCodes;
        if (partnerCodes.length === 1) {
          tempList.push(row);
        } else {
          const restCodes = partnerCodes.slice(1);
          tempList.push({
            ...row,
            partnerCodes: [partnerCodes[0]],
            relatedChannelCodeList: restCodes,
          });

          restCodes.forEach(code => {
            tempList.push({
              partnerCodes: [code],
              partnerCode: code,
              goodsId,
              partnerType,
            });
          });
        }
      });

      // 如果下拉框里面选的是二级渠道，partnerCodes里面存一级渠道，subPartnerCode里面存二级渠道，relatedChannelCodeList里面存 parent-child 渠道数据
      tempList.forEach(item => {
        const partnerCode = item.partnerCodes[0];

        if (partnerCode.includes('-')) {
          const [parent, child] = partnerCode.split('-');
          item.partnerCodes = [parent];
          item.partnerCode = parent;
          item.subPartnerCode = child;
        }
      });

      return tempList;
    },
    [goodsId, partnerType]
  );

  const saveSalesPartnerNew = useCallback(
    (list: SalesAttributesNS.FESalesPartnerChannel[] | null, formulalist: SalesPartnerFormula[] | null) => {
      // 如果没传取原始数据
      let tempGoodsSalesPartnerList = platformSalesPartner?.goodsSalesPartnerList || [];
      if (list) {
        tempGoodsSalesPartnerList = list;
      }

      const goodsSalesPartnerFormulaList = formulalist
        ? formulalist.map(row => {
            row.goodsId = goodsId;
            row.partnerType = partnerType;

            return row;
          })
        : platformSalesPartner?.goodsSalesPartnerFormulaList;

      submitService({
        goodsId,
        partnerType,
        goodsSalesPartnerList: tempGoodsSalesPartnerList || [],
        goodsSalesPartnerFormulaList: goodsSalesPartnerFormulaList || [],
      }).then(() => {
        message.success(t('Submit Successfully'));
        reload();
      });
    },
    [platformSalesPartner, submitService, goodsId, partnerType, t, reload]
  );

  const saveFormulaList = useCallback(
    (list: SalesPartnerFormula[]) => {
      saveSalesPartnerNew(null, list);
    },
    [saveSalesPartnerNew]
  );

  const onTableSubmit = useCallback(
    (
      rowData: SalesAttributesNS.FESalesPartnerChannel & {
        key: number;
        editing: boolean;
      },
      key,
      index: number
    ) => {
      const tempData = [...channePartnerlList];
      if (tempData.find(row => !row.editing && intersection(row.partnerCodes, rowData.partnerCodes).length > 0)) {
        message.error(t('Duplicate Sales Platform. Please change.'));
        return Promise.reject();
      }

      const parentCodes = rowData.partnerCodes.map(partnerCode =>
        partnerCode.includes('-') ? partnerCode.split('-')[0] : partnerCode
      );

      // 当前选中的数据同时含有1、2级渠道
      if (
        rowData.partnerCodes.find(partnerCode =>
          rowData.partnerCodes.find(
            _partnerCode => _partnerCode !== partnerCode && _partnerCode.split('-')[0] === partnerCode
          )
        )
      ) {
        message.error(t('Channel and channnel - sub channel could not co-exisit. Please change.'));
        return Promise.reject();
      }

      // eslint-disable-next-line @typescript-eslint/prefer-for-of
      for (let i = 0; i < parentCodes.length; i++) {
        const parentCode = parentCodes[i];

        // 同一个一级渠道下可以配置多个二级渠道，但是不能同时配置一级渠道和对应二级渠道，二级渠道必有subPartnerCode
        const existFirstLevelChannel = tempData.find(
          row => !row.editing && row.partnerCodes.find(item => item.includes(parentCode))
        );
        if (existFirstLevelChannel) {
          message.error(t('Channel and channnel - sub channel could not co-exisit. Please change.'));
          return Promise.reject();
        }
      }

      rowData.goodsId = goodsId;
      rowData.partnerType = partnerType;
      if (key === 'add') {
        rowData.key = Math.random();
        tempData.push(rowData);
      } else {
        tempData[index] = rowData;
      }
      // accumulatedToPremium 所有数据保持一致
      tempData.forEach(existRow => {
        existRow.accumulatedToPremium = rowData.accumulatedToPremium;
      });
      const tempList = fillRelatedChannelCodeList(tempData);

      saveSalesPartnerNew([...tempList], null);
      return Promise.resolve();
    },
    [channePartnerlList, fillRelatedChannelCodeList, goodsId, partnerType, saveSalesPartnerNew, t]
  );

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-return
  const commissionFormulaList = useMemo(
    () =>
      ((platformSalesPartner?.goodsSalesPartnerFormulaList as SalesPartnerFormula[]) || [])?.filter(
        item => `${item.feeType}` === PartnerFeeType.commission && `${item.formulaCategory}` === BizTopic.Commission
      ) || [],
    [platformSalesPartner?.goodsSalesPartnerFormulaList]
  );

  const clawbackcommissionFormulaList = useMemo(
    () =>
      ((platformSalesPartner?.goodsSalesPartnerFormulaList as SalesPartnerFormula[]) || [])?.filter(
        item =>
          `${item.feeType}` === PartnerFeeType.commission && `${item.formulaCategory}` === BizTopic.CommissionClawback
      ) || [],
    [platformSalesPartner?.goodsSalesPartnerFormulaList]
  );

  return (
    <div
      style={{
        marginBottom: 24,
      }}
      ref={containerEl}
    >
      <EditTable
        disabled={disabled}
        columns={columns}
        data={channePartnerlList}
        scroll={{ x: 'max-content' }}
        onSubmit={onTableSubmit}
        onDelete={(key: string, index: number) => {
          channePartnerlList?.splice(index, 1);
          const tempList: GoodsSalesPartner[] = fillRelatedChannelCodeList(channePartnerlList);

          saveSalesPartnerNew([...tempList], null);
        }}
        onEditBefore={handleEditBefore}
        onCancel={() => {
          channePartnerlList.forEach(row => {
            row.editing = false;
          });
        }}
        onStateChange={(state: string) => {
          onEditStateChange(state === 'Editing');
        }}
        pagination={
          channePartnerlList?.length > 10
            ? {
                defaultPageSize: 10,
              }
            : undefined
        }
      />
      <CommissionFormulaTable
        goodsId={goodsId}
        disabled={disabled}
        totalFormulaList={totalFormulaList}
        initData={commissionFormulaList}
        onEditStateChange={() => {}}
        saveFormulaList={formulaList => {
          saveFormulaList([...formulaList, ...clawbackcommissionFormulaList]);
        }}
      />
      <ClawbackCommissionFormula
        goodsId={goodsId}
        disabled={disabled}
        totalFormulaList={totalFormulaList}
        initData={clawbackcommissionFormulaList}
        onEditStateChange={() => {}}
        saveFormulaList={formulaList => {
          saveFormulaList([...commissionFormulaList, ...formulaList]);
        }}
      />
    </div>
  );
};

export default forwardRef(ChannelConfigTable);
