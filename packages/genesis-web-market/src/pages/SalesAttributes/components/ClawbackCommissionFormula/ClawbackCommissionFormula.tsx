import { Ref, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { QuestionCircleOutlined } from '@ant-design/icons';
import { Tooltip, message } from 'antd';

import { FormulaProps, PartnerFeeType, SalesPartnerFormula } from 'genesis-web-service';

import { BizTopic, CommissionSubCategory } from '@market/common/enums';
import { SalesAttributesNS } from '@market/common/salesAttributes.interface';
import EditTable from '@market/components/F-EditableTable';
import { useBizDict } from '@market/hook/bizDict';
import { useCommissionFormulaOptions } from '@market/hook/goods.service';
import { mapFormulaToOldBizdict, renderEnumName, renderOldBizdictName } from '@market/utils/enum';

interface Props {
  goodsId: string | number;
  disabled: boolean;
  totalFormulaList: FormulaProps[];
  refInstance?: Ref<{
    getCommissionFormulaList: () => SalesPartnerFormula[];
  }>;
  initData?: SalesPartnerFormula[];
  onEditStateChange: (isEditing: boolean) => void;
  saveFormulaList: (list: SalesPartnerFormula[]) => void;
}

export const ClawbackCommissionFormula = ({
  goodsId,
  disabled,
  totalFormulaList,
  refInstance,
  initData,
  onEditStateChange,
  saveFormulaList,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const bizTopicBizDicts = useBizDict('bizTopic');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef(null);
  const [t] = useTranslation(['market', 'common']);
  const [tableData, setTableData] = useState<SalesAttributesNS.FESalesPartnerFormula[]>([]);
  const [editingSubCategory, setEditingSubCategory] = useState<string>();
  const formulaCategory = BizTopic.CommissionClawback;

  const formulaSubCategoryOptions = useMemo(
    () =>
      bizTopicBizDicts
        ?.find(bizdict => bizdict.dictValue === BizTopic.CommissionClawback)
        ?.childList?.map(bizdict => ({
          itemExtend1: bizdict.dictValue,
          itemName: bizdict.dictValueName,
        })) || [],
    [bizTopicBizDicts]
  );

  const formulaOptionsBySubCategory = useCommissionFormulaOptions(
    totalFormulaList,
    BizTopic.CommissionClawback,
    editingSubCategory
  );

  const totalCommissionFormulaOptions = useMemo(
    () =>
      totalFormulaList
        .filter(formula => `${formula.formulaTableTypeId}` === BizTopic.CommissionClawback)
        .map(mapFormulaToOldBizdict),
    [totalFormulaList]
  );

  useEffect(() => {
    if (initData) {
      setTableData(
        initData.map(formula => {
          const key = Math.random();
          return {
            ...formula,
            key,
            id: key,
          };
        })
      );
    }
  }, [initData]);

  const resetState = useCallback(() => {
    setEditingSubCategory(undefined);
  }, []);

  useImperativeHandle(refInstance, () => ({
    getCommissionFormulaList: () =>
      tableData.map(row => ({
        formulaCode: row.formulaCode,
        formulaCategory,
        formulaSubCategory: row?.formulaSubCategory,
        feeType: PartnerFeeType.commission,
        goodsId,
      })),
  }));

  const handleEditBefore = useCallback(
    (record: SalesAttributesNS.FESalesPartnerFormula) => {
      const tempTableData = [...tableData];
      const editingRow = tempTableData.find((row: SalesAttributesNS.FESalesPartnerFormula) => row.key === record.key);
      if (editingRow) {
        editingRow.editing = true;
        editingRow.formulaSubCategory = editingRow.formulaSubCategory && `${editingRow.formulaSubCategory}`;
        setEditingSubCategory(editingRow.formulaSubCategory);
      }
    },
    [tableData]
  );

  const renderClawbackFormulaLabel = useCallback(
    () => (
      <span>
        {t('Formula Name (Clawback)')}
        <Tooltip
          placement="top"
          title={t(
            'Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.'
          )}
        >
          <QuestionCircleOutlined style={{ marginLeft: '5px' }} />
        </Tooltip>
      </span>
    ),
    [t]
  );

  const columns = useMemo(
    () => [
      {
        title: t('Formula Category (Clawback)'),
        dataIndex: 'formulaCategory',
        editable: false,
        width: '220px',
        render: () =>
          renderEnumName(formulaCategory, bizTopicBizDicts, {
            labelKey: 'dictValueName',
            valueKey: 'dictValue',
          }),
      },
      {
        title: t('Formula Sub Category (Clawback)'),
        editable: true,
        dataIndex: 'formulaSubCategory',
        inputType: 'select',
        selectOptions: formulaSubCategoryOptions,
        width: '240px',
        controllerprops: {
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
          onChange: (formulaSubCategory: string) => {
            setEditingSubCategory(formulaSubCategory);
          },
        },
        render: (formulaSubCategory = CommissionSubCategory.GrossCommission): string =>
          renderOldBizdictName(formulaSubCategory, formulaSubCategoryOptions),
      },
      {
        title: renderClawbackFormulaLabel(),
        editable: true,
        dataIndex: 'formulaCode',
        inputType: 'select',
        selectOptions: formulaOptionsBySubCategory,
        controllerprops: {
          allowClear: false,
          style: { width: 240 },
          getPopupContainer: () => containerEl.current || document.body,
          disabled: !editingSubCategory,
        },
        width: '240px',
        render: (text: string): string => renderOldBizdictName(text, totalCommissionFormulaOptions) || t('- -'),
      },
    ],
    [
      t,
      formulaSubCategoryOptions,
      renderClawbackFormulaLabel,
      formulaOptionsBySubCategory,
      editingSubCategory,
      formulaCategory,
      bizTopicBizDicts,
      totalCommissionFormulaOptions,
    ]
  );

  const onTableSubmit = useCallback(
    (rowData: SalesAttributesNS.FESalesPartnerFormula, key, index: number) => {
      // FormulaCategory + FormulaSubCategory确定唯一性
      if (
        tableData.find(
          row => !row.editing && row.formulaSubCategory && `${row.formulaSubCategory}` === rowData.formulaSubCategory
        )
      ) {
        message.error(
          t('Duplicate formula configuration for same formula category and sub category. Please edit original one.')
        );
        return Promise.reject();
      }

      const tempData = [...tableData];
      rowData.editing = false;
      rowData.feeType = PartnerFeeType.commission;
      rowData.formulaCategory = BizTopic.CommissionClawback;

      if (key === 'add') {
        rowData.key = Math.random();
        tempData.push(rowData);
      } else {
        tempData[index] = rowData;
      }

      saveFormulaList(tempData);
      resetState();
      return Promise.resolve();
    },
    [tableData, saveFormulaList, resetState, t]
  );

  return (
    <div
      style={{
        marginBottom: 24,
      }}
      ref={containerEl}
    >
      <div style={{ marginBottom: 12 }}>{t('Please select related formula for Commission Clawback')}</div>
      <EditTable
        disabled={disabled}
        columns={columns}
        data={tableData}
        scroll={{ x: 'max-content' }}
        onSubmit={onTableSubmit}
        onDelete={(key: string, index: number) => {
          tableData?.splice(index, 1);

          setTableData([...tableData]);
          saveFormulaList([...tableData]);
        }}
        onEditBefore={handleEditBefore}
        onCancel={() => {
          tableData.forEach(row => {
            row.editing = false;

            resetState();
          });
        }}
        onStateChange={(state: string) => {
          onEditStateChange(state === 'Editing');
        }}
        pagination={
          tableData?.length > 10
            ? {
                defaultPageSize: 10,
              }
            : undefined
        }
      />
    </div>
  );
};

export default forwardRef(ClawbackCommissionFormula);
