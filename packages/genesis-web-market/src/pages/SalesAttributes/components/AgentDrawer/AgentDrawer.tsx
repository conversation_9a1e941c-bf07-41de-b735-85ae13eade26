import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Row, message } from 'antd';

import { orderBy } from 'lodash-es';

import { Drawer } from '@zhongan/nagrand-ui';

import { AgentFormulaInfo, PartnerFeeType } from 'genesis-web-service';

import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';

interface Props {
  visible: boolean;
  disabled: boolean;
  onClose: () => void;
  agentCategory?: string;
  editRecordIndex: number;
  formulaCategory: string;
  editRecordData?: AgentFormulaInfo;
  formulaSubCategoryOptions: { value: string; label: string }[];
  formulaOptionsBySubCategory: { itemExtend1: string; itemName: string }[];
  onSubmitList: (arr: AgentFormulaInfo[]) => void;
  setEditingSubCategory: (formulaSubCategory: string) => void;
  agentFormulaList: AgentFormulaInfo[];
}

export const AgentDrawer = ({
  onClose,
  visible,
  disabled,
  onSubmitList,
  agentCategory,
  editRecordData,
  editRecordIndex,
  formulaCategory,
  agentFormulaList,
  setEditingSubCategory,
  formulaSubCategoryOptions,
  formulaOptionsBySubCategory,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const bizTopicEnumOptions = useBizDictAsOptions('bizTopic');
  /* ============== 枚举使用end ============== */
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();

  const onSubmitChange = useCallback(() => {
    form
      .validateFields()
      .then((values: { formulaCode: string; formulaSubCategory: string; formulaCategory: string }) => {
        let tempAgentFormulaList = agentFormulaList;
        const tempFormulaOrder = agentFormulaList.filter(agentItem => agentItem.agentCategory === agentCategory).length;
        if (
          tempAgentFormulaList
            .filter(agentItem => agentItem.formulaOrder !== editRecordData?.formulaOrder)
            .find(
              row =>
                row.formulaSubCategory &&
                row.agentCategory === agentCategory &&
                `${row.formulaSubCategory}` === values.formulaSubCategory
            )
        ) {
          message.error(
            t('Duplicate formula configuration for same formula category and sub category. Please edit original one.')
          );
          return Promise.reject();
        }
        if (editRecordData) {
          tempAgentFormulaList[editRecordIndex].formulaCode = values.formulaCode;
          tempAgentFormulaList[editRecordIndex].formulaCategory = values.formulaCategory;
          tempAgentFormulaList[editRecordIndex].formulaSubCategory = values.formulaSubCategory;
        } else {
          tempAgentFormulaList.push({
            agentCategory,
            formulaCode: values.formulaCode,
            formulaOrder: tempFormulaOrder + 1,
            formulaCategory: values.formulaCategory,
            formulaSubCategory: values.formulaSubCategory,
            feeType: PartnerFeeType.commission,
          });
          tempAgentFormulaList = orderBy(tempAgentFormulaList, ['agentCategory', 'formulaOrder'], ['asc', 'asc']);
          tempAgentFormulaList.forEach(item => {
            if (item.formulaOrder === 1) {
              item.rowSpan = tempAgentFormulaList.filter(
                agentItem => agentItem.agentCategory === item.agentCategory
              ).length;
            } else {
              item.rowSpan = 0;
            }
          });
        }
        onSubmitList(tempAgentFormulaList);
        onClose();
      });
  }, [agentCategory, agentFormulaList, form, onClose, onSubmitList, t, editRecordIndex, editRecordData]);

  return (
    <Drawer
      open={visible}
      title={t('Agent')}
      onClose={onClose}
      size="mini"
      onSubmit={onSubmitChange}
    >
      <Form form={form} layout="vertical">
        <Row>
          <Col span={24}>
            <Form.Item
              label={t('Formula Category')}
              colon={false}
              name="formulaCategory"
              initialValue={formulaCategory}
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <GeneralSelect style={{ width: '240px' }} option={bizTopicEnumOptions} disabled />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label={t('Formula Sub Category')}
              colon={false}
              name="formulaSubCategory"
              initialValue={editRecordData?.formulaSubCategory}
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <GeneralSelect
                style={{ width: '240px' }}
                option={formulaSubCategoryOptions || []}
                disabled={disabled}
                onChange={(formulaSubCategory: string) => setEditingSubCategory(formulaSubCategory)}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label={t('Formula Name')}
              colon={false}
              name="formulaCode"
              initialValue={editRecordData?.formulaCode}
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <GeneralSelect
                style={{ width: '240px' }}
                option={(formulaOptionsBySubCategory || []).map(item => ({
                  label: item.itemName,
                  value: item.itemExtend1,
                }))}
                disabled={disabled}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Drawer>
  );
};
export default AgentDrawer;
