import { Component, createRef } from 'react';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';

import { Button, Layout, Modal, Skeleton, message } from 'antd';

import { differenceBy } from 'lodash-es';

import { MarketService, PartnerType } from 'genesis-web-service/lib/market';
import { withL10n } from 'genesis-web-shared/lib/l10n';

import { DetailPageMode } from '@market/common/interface';
import Section from '@market/components/Section/section';
import { selectPermissionCheckMap, selectUserModules } from '@market/redux/selector';
import { NewCalculatorService } from '@market/services/calculator/calculator.service.new';
import { NewMarketService } from '@market/services/market/market.service.new';

import { FMarketHeader } from '../../components/F-Market-Header';
import FMarketMenu from '../../components/F-Market-Menu';
import { urlQuery } from '../../util';
import FormBox from './Container/formBox.js';
import PartnerFeeCalculation from './components/PartnerFeeCalculation';
import VoucherCorrelation from './components/VoucherCorrelation';
import './style.scss';

const { Sider, Content } = Layout;

class SalesAttributes extends Component {
  constructor(props) {
    super(props);
    this.voucherCorrelationRef = createRef();

    this.state = {
      basicProp: {},
      salesProp: {},
      mode: (this.props.location && this.props.location.state && this.props.location.state.mode) || undefined,
      queryModel:
        (this.props.location && this.props.location.state && this.props.location.state.queryModel) || undefined,
      isShowEditButton: false,
      formExtendLimit: {
        triggerShow: false,
        renewalDisable: true,
      },
      formulaList: [],
      skeletonVisible: true,
      loading: false,
      drawerMode: DetailPageMode.add,
      dataSource: [],
      salesPartnerFormulaList: [],
      salesPartnerList: [],
      partnerId: false,
      totalChannelList: [],
      channelRelationList: [],
      isSaveVoucherCorrelation: false,
      newSalesPartnerList: [],
    };
  }

  componentDidMount() {
    this.goodsId = urlQuery('goodsId');
    this.layoutId = urlQuery('layoutId');
    MarketService.queryChannel().then(res => {
      if (res.success) {
        this.setState({
          totalChannelList: res.value.results,
        });
      }
    });
    MarketService.queryChannelRelation().then(res => {
      if (res.success) {
        this.setState({
          channelRelationList: res.value.results,
        });
      }
    });
    this.init();

    this.querySalesChannelList();
  }

  // 保存VoucherCorrelation前 校验
  saveVoucherCorrelationBefore = () => {
    this.setState({
      isSaveVoucherCorrelation: true,
    });
  };

  // 获取数据
  init = async () => {
    const { hasEditAuth, isSuperUser, userInfo } = this.props;
    const goodsId = urlQuery('goodsId');
    const dataRes = await NewMarketService.GoodMgmtService.find({
      goodsId,
      queryModel: this.state.queryModel,
    });
    if (dataRes.success && dataRes.value) {
      if (Object.prototype.toString.apply(dataRes.value.salesProp) === '[object Object]') {
        this.setState({
          salesProp: dataRes.value.salesProp,
          zoneId: dataRes.value.salesProp.zoneId,
        });
      }
      if (Object.prototype.toString.apply(dataRes.value.basicProp) === '[object Object]') {
        this.setState({
          isShowEditButton: (dataRes.value.basicProp.creator === `${userInfo.userId}` && hasEditAuth) || isSuperUser,
        });
      }
    }

    const limitRes = await NewMarketService.GoodMgmtService.validateGoods({
      goodsId,
    });
    if (limitRes.success && limitRes.value) {
      let renewalDisable = true;
      if (limitRes.value.allowRenewalList && Array.isArray(limitRes.value.allowRenewalList)) {
        renewalDisable = limitRes.value.allowRenewalList.filter(i => i.allowRenewal === 1).length === 0;
      }
      this.setState({
        formExtendLimit: {
          triggerShow: limitRes.value.triggerShow,
          renewalDisable,
        },
      });
    }
    this.queryFormulaList();

    this.setState({
      skeletonVisible: false,
    });
  };

  // 查询数据 formula list
  queryFormulaList = () => {
    NewCalculatorService.FormulaService.list({}).then(res => {
      if (res.success && res.value) {
        this.setState({
          formulaList: res.value,
        });
      }
    });
  };

  // 下一步
  onConfirmSubmit = async isSave => {
    const { l10n } = this.props;
    const { zoneId, newSalesPartnerList } = this.state;
    if (this.state.mode === 'view') {
      this.jumpToNext();
      return false;
    }

    if (!this.FormBoxRef.saveBefore()) {
      return false;
    }
    const salesProp = await this.FormBoxRef.getValue();
    // 判断上架时间与销售时间的校验，金额的校验
    if (
      salesProp.beginSalesDate &&
      salesProp.endSalesDate &&
      (Date.parse(salesProp.beginShelvesDate) > Date.parse(salesProp.beginSalesDate) ||
        Date.parse(salesProp.endSalesDate) > Date.parse(salesProp.endUnderShelvesDate) ||
        Date.parse(salesProp.beginSalesDate) > Date.parse(salesProp.endUnderShelvesDate) ||
        Date.parse(salesProp.endSalesDate) < Date.parse(salesProp.beginShelvesDate))
    ) {
      message.error(this.props.t('The Sales Time should be within the Launch Date'));
      return false;
    }

    salesProp.beginShelvesDate = l10n.dateFormat.formatTz(salesProp.beginShelvesDate, zoneId);
    salesProp.endUnderShelvesDate = l10n.dateFormat.formatTz(salesProp.endUnderShelvesDate, zoneId);
    salesProp.beginSalesDate = l10n.dateFormat.formatTz(salesProp.beginSalesDate, zoneId);
    salesProp.endSalesDate = l10n.dateFormat.formatTz(salesProp.endSalesDate, zoneId);
    if (salesProp.paymentMethod && Array.isArray(salesProp.paymentMethod)) {
      salesProp.paymentMethod = salesProp.paymentMethod.join(',');
    }
    if (salesProp.salesDistrict && Array.isArray(salesProp.salesDistrict)) {
      salesProp.salesDistrict = salesProp.salesDistrict.join(',');
    }
    if (salesProp.maxSalesCount === 'unlimited') {
      salesProp.maxSalesCount = 2147483647;
    } else {
      salesProp.maxSalesCount = salesProp.maxSalesCount_number;
    }
    delete salesProp.maxSalesCount_number;
    if (salesProp.isEnableWholesaleWarning === '2') {
      delete salesProp.enableWholesaleWarning;
    }
    delete salesProp.isEnableWholesaleWarning;

    // 校验销售渠道是不是有通知
    const currentPlatform = salesProp.salesChannelRequestDTOS;
    // let currentPlatform = this.state.salesChannelRequestDTOS;
    const originalPlatform = this.state.salesProp.salesChannelResponseDTOS || [];
    const allChannels = this.state.totalChannelList || [];
    const addedChannel =
      originalPlatform.length === 0 ? [] : differenceBy(currentPlatform, originalPlatform, 'channelCode');
    const addedChannelDisplay = []; // 新增的渠道
    allChannels.map(channel => {
      if (addedChannel.filter(i => i.channelCode === channel.code).length > 0) {
        addedChannelDisplay.push(channel.name);
      }
      return true;
    });
    const salesPartnerList =
      newSalesPartnerList.find(item => item.partnerType === PartnerType.salesChannel)?.goodsSalesPartnerList || [];
    if (salesPartnerList.length < allChannels.length) {
      const currentSalesChannel =
        salesPartnerList?.filter(item => item.partnerType === PartnerType.salesChannel).map(item => item.partnerCode) ||
        [];
      // 如果选择后的渠道为all，默认不删除任何的渠道，不走删除逻辑
      const checkRes = await NewMarketService.GoodMgmtService.checkoutSalesChannelConfig({
        basicProp: {
          goodsId: urlQuery('goodsId'),
        },
        salesProp: {
          salesPlatform: currentSalesChannel.join(','),
        },
      });
      if (!checkRes.success) {
        return;
      }

      //  GoodsVoucher配置保存信息
      if (this.voucherCorrelationRef.current) {
        const voucherInfoValues = await this.voucherCorrelationRef.current.getFormChangedData();
        const posRefundVoucherValues = await this.voucherCorrelationRef.current.getTableChangedData();
        const voucherParam = {
          goodsPosRefundVoucherList: posRefundVoucherValues,
          goodsNcbVoucher: voucherInfoValues,
        };
        NewMarketService.GoodsVoucherMgmtService.saveGoodsVouchers(
          urlQuery('goodsId'),
          voucherParam,
        )
      }

      if (checkRes.success && checkRes.value && checkRes.value.salesProp && checkRes.value.salesProp.salesPlatform) {
        // 如果校验删除的渠道中有已经绑定通知的渠道，走删除逻辑
        const existNoticePlatform = checkRes.value.salesProp.salesPlatform.split(',');
        const existNoticePlatformDisplay = [];
        allChannels.map(item => {
          if (existNoticePlatform.indexOf(item.code) > -1) {
            existNoticePlatformDisplay.push(item.name);
          }
          return true;
        });
        const canRestoreChannel = originalPlatform.filter(i => existNoticePlatform.includes(i.channelCode));
        const restoreChannel = currentPlatform.concat(canRestoreChannel);
        Modal.confirm({
          cancelText: this.props.t('Cancel'),
          okText: this.props.t('okText'),
          title: `${this.props.t('Are you sure you want to delete these sales channels')} (${existNoticePlatformDisplay.join(',')})?`,
          content: this.props.t(
            'Once deleted, the configured notifications which related with these sales channels will also be deleted.'
          ),
          onCancel: () => this.cancelDeleteNoticeChannel(restoreChannel),
          onOk: () => this.onDeleteRelatedSalesChannel(existNoticePlatform, addedChannelDisplay, salesProp, isSave),
        });
      } else {
        // 走新增渠道的逻辑
        this.addedChannelMethod(addedChannelDisplay, salesProp, isSave);
      }
    } else {
      // 走新增渠道的逻辑
      this.addedChannelMethod(addedChannelDisplay, salesProp, isSave);
    }
  };

  // 删除渠道弹框，选择cancel，重置被删除的有通知绑定的渠道
  cancelDeleteNoticeChannel = restoreChannel => {
    this.FormBoxRef.resetNoticeChannel(restoreChannel);
  };

  // 删除渠道弹框，选择confirm，删除有通知的渠道
  onDeleteRelatedSalesChannel = (deleteChannel, addedChannel, salesProp, isSave) => {
    NewMarketService.GoodMgmtService.deleteNotificationConfig({
      basicProp: {
        goodsId: urlQuery('goodsId'),
      },
      salesProp: {
        salesPlatform: deleteChannel && deleteChannel.join(','),
      },
    }).then(res => {
      if (res.success) {
        this.addedChannelMethod(addedChannel, salesProp, isSave);
      }
    });
  };

  // 判断是否有新增的渠道，有新增的渠道则显示新增弹框
  addedChannelMethod = (addedChannel, salesProp, isSave) => {
    if (addedChannel.length > 0) {
      // 有新增的渠道，那么提示新增的渠道
      Modal.info({
        okText: this.props.t('okText'),
        title: this.props.t(
          'The sales channels have related configured notifications, please update notification configurations if needed.'
        ),
        onOk: () => this.confirmToSubmitInfo(salesProp, isSave),
      });
    } else {
      // 没有新增的渠道，直接调用接口更新，下一步
      this.confirmToSubmitInfo(salesProp, isSave);
    }
  };

  // 调用接口保存当前页面的更改
  confirmToSubmitInfo = (salesProp, isSave) => {
    Promise.all([
      NewMarketService.GoodMgmtService.updateSalesInfo({
        basicProp: {
          ...this.state.basicProp,
          goodsId: urlQuery('goodsId'),
        },
        salesProp: {
          ...salesProp,
          // salesChannelRequestDTOS: this.state.salesChannelRequestDTOS,
          isCalculationCycle: salesProp.isCalculationCycle,
          cutOffDate: +salesProp.isCalculationCycle === 1 ? salesProp.cutOffDate : undefined,
          cutOffTime:
            +salesProp.isCalculationCycle === 1
              ? salesProp.cutOffTime && salesProp.cutOffTime.format('HH:mm:ss')
              : undefined,
          publicDisplayPeriod: +salesProp.isCalculationCycle === 1 ? salesProp.publicDisplayPeriod : undefined,
        },
      }),
    ]).then(res => {
      message.success(this.props.t('Save successfully'));
      if (Array.isArray(res) && res.every(res => res.success) && !isSave) {
        this.jumpToNext();
      }
    });
  };

  // 往下跳转
  jumpToNext = () => {
    this.props.navigate(`/market/goods/coverage-plan?goodsId=${urlQuery('goodsId')}&layoutId=${urlQuery('layoutId')}`, {
      state: {
        mode: this.state.mode,
        queryModel: this.state.queryModel,
      },
    });
  };

  backToLastHistory = () => {
    this.props.navigate(`/market/goods/basic?goodsId=${urlQuery('goodsId')}&layoutId=${urlQuery('layoutId')}`, {
      state: {
        mode: this.state.mode,
        queryModel: this.state.queryModel,
      },
    });
  };

  onEdit = () => {
    if (this.state.mode !== 'view') {
      return;
    }
    this.setState({ mode: 'edit' });
  };

  topicTitle = () => {
    return {
      1: this.props.t('Insurance Applicant'),
    };
  };

  edit = () => {
    this.setState({
      drawerMode: DetailPageMode.edit,
    });
  };

  querySalesChannelList = () => {
    this.setState({
      loading: true,
    });
    NewMarketService.GoodsSalesPartnerMgmtService.queryGoodsSalesPartners(this.goodsId).then(res => {
      this.setState({
        newSalesPartnerList: res || [],
      });
    });
  };

  setTimeZoneId = value => {
    this.setState({
      zoneId: value,
    });
  };

  renderEditSaveBtn() {
    const { isShowEditButton, mode } = this.state;
    if (!isShowEditButton || this.props.envConfig.env === 'prd') {
      return null;
    }

    if (mode === 'view') {
      return (
        <Button size="large" onClick={() => this.onEdit()}>
          {this.props.t('Edit')}
        </Button>
      );
    }

    return (
      <Button size="large" onClick={() => this.onConfirmSubmit(true)}>
        {this.props.t('Save')}
      </Button>
    );
  }

  render() {
    const { isShowEditButton, formExtendLimit, formulaList } = this.state;
    const readOnly = this.state.mode === 'view' || !isShowEditButton || this.props.envConfig.env === 'prd';
    return (
      <Layout className="goods-salesAttributes market-layout">
        <FMarketHeader backPath="/market/goods/search" subMenu="Marketing_Goods" />
        <div className="flex" style={{ height: 'calc(100% - 50px)' }}>
          <Sider width={208} className="market-sider">
            <FMarketMenu
              page="GOODS"
              defaultSelectedKeys={['2']}
              category={2}
              type={this.state.mode}
              layoutId={this.layoutId}
              goodsId={this.goodsId}
              navigate={this.props.navigate}
            />
          </Sider>
          <Content className="market-content">
            <Skeleton active loading={this.state.skeletonVisible}>
              <div className="right-content-wrapper">
                <Section title={this.props.t('Sales Attributes')}>
                  <PartnerFeeCalculation
                    goodsId={this.goodsId}
                    mode={this.state.mode}
                    totalFormulaList={formulaList}
                    initData={this.state.newSalesPartnerList}
                    onSubmit={() => {
                      this.querySalesChannelList();
                    }}
                  />
                  {this.props.hasCustomerLoyalty && (
                    <VoucherCorrelation
                      goodsId={this.goodsId}
                      initData={this.state.newSalesPartnerList}
                      disabled={readOnly}
                      refInstance={this.voucherCorrelationRef}
                    />
                  )}
                  <FormBox
                    data={this.state.salesProp}
                    myRef={ref => (this.FormBoxRef = ref)}
                    readOnly={readOnly}
                    goodsId={this.goodsId}
                    formExtendLimit={formExtendLimit}
                    zoneId={this.state.zoneId}
                    setTimeZoneId={this.setTimeZoneId}
                    tenant={this.props.tenant}
                  />
                </Section>
                <div className="bottom-action-bar">
                  {readOnly && (
                    <Button size="large" onClick={this.jumpToNext} type="primary">
                      {this.props.t('Next')}
                    </Button>
                  )}
                  {this.renderEditSaveBtn()}
                  {!readOnly && (
                    <Button size="large" onClick={() => this.onConfirmSubmit(false)} type="default">
                      {this.props.t('Next')}
                    </Button>
                  )}
                </div>
              </div>
            </Skeleton>
          </Content>
        </div>
      </Layout>
    );
  }
}

export default withL10n()(
  connect((state, ownProps) => {
    const permissionMap = selectPermissionCheckMap(state);
    const userModules = selectUserModules(state);

    return {
      envConfig: state.envConfig,
      hasEditAuth: !!permissionMap['market.edit'],
      hasCustomerLoyalty: userModules.includes('customer-loyalty'),
      isSuperUser: !!permissionMap['market.edit-all'],
      userInfo: state.userInfo,
      tenant: ownProps.tenant,
    };
  })(withTranslation(['market', 'common'])(SalesAttributes))
);
