.goods-salesAttributes {
  height: 100%;

  .calculation-container {
    border-top: 1px dashed var(--border-light);
    border-bottom: 1px dashed var(--border-light);
    padding-top: 22px;
    margin-bottom: 22px;

    .form-title {
      padding-bottom: 20px;
      font-weight: 600;
      color: var(--text-color);
    }
  }

  .coverage-plan-container {
    border-top: 1px dashed var(--border-light);
    padding-top: 22px;

    .plan-title {
      font-weight: 600;
      color: var(--text-color);

      &::before {
        display: inline-block;
        margin-right: 4px;
        color: var(--error-color);
        font-size: 14px;
        line-height: 1;
        content: '*';
      }
    }

    .plan-list-container {
      display: flex;
      flex-wrap: wrap;

      &.sort-start,
      &.disabled {
        .plan-drag-item:hover {
          border-color: var(--border-default);

          .action-bar .anticon {
            margin-right: 0px;
          }

          .drag-handler {
            display: none;
          }
        }
      }

      .plan-drag-item {
        width: 300px;
        height: 155px;
        padding: 16px;
        border-radius: 4px;
        border: 1px solid var(--border-default);
        margin-right: 16px;
        margin-top: 16px;
        display: flex;
        flex-direction: column;
        position: relative;

        .main-title {
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 14px;
          font-weight: 500;
          color: var(--text-color);
        }

        .sub-title {
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-size: 12px;
          color: $disabled-color;

          &.time {
            flex: 1;
            text-overflow: unset;
            overflow: visible;
            white-space: normal;
          }
        }

        .action-bar {
          display: flex;
          align-items: center;

          > div {
            flex: 1;

            .plan-status {
              font-size: 12px;
              padding: 2px 8px;
              border-radius: 10px;

              &.done {
                background: var(--success-color-bg);
                color: var(--success-color);
              }

              &.undone {
                background: var(--error-color-bg);
                color: var(--error-color);
              }
            }
          }

          .anticon {
            cursor: pointer;
            font-size: 14px;
            display: none;
            margin-right: 30px;
          }
        }

        .drag-handler {
          position: absolute;
          bottom: 17px;
          font-size: 20px;
          right: 16px;
          display: none;
          cursor: pointer;
        }

        &:hover {
          border-color: var(--primary-color);

          .action-bar .anticon {
            display: block;
          }

          .drag-handler {
            display: block;
          }
        }
      }
    }
  }

  .goods-salesAttributes-container-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--text-color);
    line-height: 1;
    border-bottom: 1px dashed var(--border-light);
    padding-bottom: 18px;
    margin-bottom: 16px;
  }

  .clear-fix:after {
    display: table;
    content: '';
    clear: both;
  }

  .table-form-datepicker {
    display: flex;
    align-items: center;
  }

  .star {
    &:before {
      display: inline-block;
      margin-right: 4px;
      color: var(--error-color);
      font-size: 14px;
      line-height: 1;
      content: '*';
    }
  }

  #salesAttributes-table-id {
    position: relative;
  }

  .add-sales-channel {
    display: flex;
    align-items: center;
    color: var(--primary-color);
    margin-bottom: 24px;
    cursor: pointer;
    width: 48px;

    > span {
      margin-left: 4px;
    }
  }
}

body {
  > div.plan-drag-item-active {
    width: 300px;
    height: 130px;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid var(--border-default);
    margin-right: 16px;
    display: flex;
    flex-direction: column;
    position: relative;
    border-color: var(--primary-color);
    margin-top: 16px;
    background-color: var(--white);

    .main-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-color);
    }

    .sub-title {
      font-size: 12px;
      color: $disabled-color;

      &.time {
        flex: 1;
      }
    }

    .action-bar {
      display: flex;
      align-items: center;

      > div {
        flex: 1;

        .plan-status {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 10px;

          &.done {
            background: var(--success-color-bg);
            color: var(--success-color);
          }

          &.undone {
            background: var(--error-color-bg);
            color: var(--error-color);
          }
        }
      }

      .anticon {
        cursor: pointer;
        font-size: 14px;
        margin-right: 30px;
      }
    }

    .drag-handler {
      position: absolute;
      bottom: 17px;
      right: 16px;
      font-size: 20px;
      cursor: pointer;
    }
  }
}

.coverage-plan-drawer {
  .market-ant4-drawer-wrapper-body {
    min-height: 100%;
    position: relative;

    .market-ant4-drawer-body {
      padding: 24px 0px;
      height: 100%;
    }

    .drawer-content {
      padding: 0px 32px 80px 24px;

      .drawer-title {
        margin-bottom: 24px;
        font-weight: bold;
        padding-bottom: 20px;
        border-bottom: 1px dashed var(--border-light);
      }

      .drawer-foot {
        position: fixed;
        right: 0;
        bottom: 0;
        width: 1024px;
        border-top: 1px solid var(--border-default);
        background: var(--white);
        text-align: right;
        height: 72px;
        padding: 16px 32px 16px;
        z-index: 2;
      }
    }
  }
}

.insuringProcessConfiguration {
  margin-bottom: 24px;
}
