import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Divider, Form, Row } from 'antd';

import classNames from 'classnames/bind';
import moment from 'moment';

import { Drawer, Table } from '@zhongan/nagrand-ui';

import { CustomerLoyaltyService } from 'genesis-web-service/lib/customer-loyalty';
import {
  CustomerVoucherBasicInfo,
  VoucherHolderInfo,
  VoucherQueryResponse,
  VoucherTransactionInfo,
} from 'genesis-web-service/lib/customer-loyalty/interface';

import { useBizDictAsOptions } from '@market/hook/bizDict';
import { renderOptionName } from '@market/utils/enum';

import styles from './CustomerVoucherDetailDrawer.module.scss';

const cx = classNames.bind(styles);

interface Props {
  visible: boolean;
  onClose: () => void;
  editRecordInfo?: VoucherQueryResponse;
  goodsInfoList: { value: string; label: string }[];
  channelOptions: { value: string; label: string }[];
}

export const CustomerVoucherDetailDrawer = ({
  onClose,
  visible,
  editRecordInfo,
  goodsInfoList,
  channelOptions,
}: Props): JSX.Element => {
  /* ============== 枚举使用start ============== */
  const policyIDTypeListOptions = useBizDictAsOptions('certiType');
  const voucherAmountTypeOptions = useBizDictAsOptions('voucherAmountType');
  /* ============== 枚举使用end ============== */
  const [t] = useTranslation(['market', 'common']);

  const [voucherHolderInfo, setVoucherHolderInfo] = useState<VoucherHolderInfo>();
  const [voucherBasicInfo, setVoucherBasicInfo] = useState<CustomerVoucherBasicInfo>();
  const [voucherTransactionInfoList, setVoucherTransactionInfoList] = useState<VoucherTransactionInfo[]>([]);

  useEffect(() => {
    if (editRecordInfo) {
      CustomerLoyaltyService.getVoucherInfo(editRecordInfo.voucherCode).then(res => {
        setVoucherHolderInfo(res?.voucherHolderInfo);
        setVoucherBasicInfo(res?.voucherBasicInfo);
        setVoucherTransactionInfoList(res?.voucherTransactionInfoList || []);
      });
    }
  }, [editRecordInfo]);

  return (
    <Drawer title={t('Voucher Detail')} open={visible} onClose={onClose} readonly>
      <Form layout="vertical">
        <div className={cx('voucher-common-title')}>{t('Belonging Info')}</div>
        <Row>
          <Col span={8}>
            <Form.Item label={t('Customer Name')} colon={false}>
              <span className={cx('common-text')}>{voucherHolderInfo?.name}</span>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={t('ID Type')} colon={false}>
                  <span className={cx('common-text')}>
                    {voucherHolderInfo?.certType
                      ? renderOptionName(`${voucherHolderInfo?.certType}`, policyIDTypeListOptions)
                      : ''}
                  </span>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={t('Customer ID No.')} colon={false}>
              <span className={cx('common-text')}>{voucherHolderInfo?.certNo}</span>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={t('Date of Birth')} colon={false}>
              <span className={cx('common-text')}>{moment(voucherHolderInfo?.birthday).format('YYYY-MM-DD')}</span>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={t('Gender')} colon={false}>
              <span className={cx('common-text')}>{voucherHolderInfo?.gender}</span>
            </Form.Item>
          </Col>
        </Row>
        <Divider/>
        <div className={cx('voucher-common-title')}>{t('Voucher Basic Info')}</div>
        <Row>
          <Col span={8}>
            <Form.Item label={t('Voucher Type - Face Amount')} colon={false}>
                  <span className={cx('common-text')}>
                    {renderOptionName(`${voucherBasicInfo?.voucherAmountType as number}`, voucherAmountTypeOptions)} -{' '}
                    {voucherBasicInfo?.voucherAmount}{' '}
                  </span>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={t('Associated Goods and Plans')} colon={false}>
                  <span className={cx('common-text')}>
                    {(voucherBasicInfo?.goodsIdList || [])
                      .map(goodsId => renderOptionName(`${goodsId}`, goodsInfoList))
                      .join('')}
                  </span>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={t('Voucher Application Channel')} colon={false}>
                  <span className={cx('common-text')}>
                    {(voucherBasicInfo?.channelCodeList || [])
                      .map(channelCode => renderOptionName(`${channelCode}`, channelOptions))
                      .join(',')}
                  </span>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label={t('Generation Date')} colon={false}>
                  <span className={cx('common-text')}>
                    {moment(voucherBasicInfo?.generateDate).format('YYYY-MM-DD')}
                  </span>
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <Divider/>
      <div className={cx('voucher-common-title')}>{t('Transaction Information')}</div>
      <Table
        columns={[
          {
            title: t('Transaction Date'),
            width: 150,
            dataIndex: 'transactionDate',
            render: (text: string) => moment(text).format('YYYY-MM-DD'),
          },
          {
            title: t('Transaction Amount'),
            dataIndex: 'amount',
            width: 220,
          },
          {
            title: t('Policy No.'),
            dataIndex: 'policyNo',
            width: 240,
          },
          {
            title: t('Channel'),
            dataIndex: 'channelCode',
            width: 140,
            render: (channelCode: string) => renderOptionName(`${channelCode}`, channelOptions),
          },
          {
            title: t('Goods'),
            dataIndex: 'goodsId',
            width: 140,
            render: (goodsId: string) => renderOptionName(`${goodsId}`, goodsInfoList),
          },
        ]}
        dataSource={voucherTransactionInfoList}
        pagination={{
          showSizeChanger: false,
          showQuickJumper: false,
        }}
        scroll={{ x: 'max-content' }}
        emptyType="text"
      />
    </Drawer>
  );
};
export default CustomerVoucherDetailDrawer;
