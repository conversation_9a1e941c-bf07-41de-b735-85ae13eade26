import { useMemo, useState } from 'react';

import { message } from 'antd';

import { usePagination } from 'ahooks';
import { cloneDeep } from 'lodash-es';

import { CustomerLoyaltyService, VoucherQueryResponse } from 'genesis-web-service/lib/customer-loyalty';
import { GoodsStatusType } from 'genesis-web-service/lib/market';

import { useChannelList } from '@market/hook/channel.serivce';
import { useGoodsList } from '@market/hook/goods.service';

import CustomerVoucherDetailDrawer from '../components/CustomerVoucherDetailDrawer/CustomerVoucherDetailDrawer';
import Content from './components/Content';
import Header from './components/Header';
import { VouchersSearchCondition } from './interface';

export const CustomerVoucherCenter = (): JSX.Element => {
  const [appliedCondition, setAppliedCondition] = useState<VouchersSearchCondition>({});
  const [editRecordInfo, setEditRecordInfo] = useState<VoucherQueryResponse>();
  const [drawerVisible, setDrawerVisible] = useState(false);

  const channelList = useChannelList();
  const goodsList = useGoodsList();
  const goodsInfoList = useMemo(
    () =>
      (goodsList || [])
        .filter(item => item.goodsBasicInfo.goodsStatus === GoodsStatusType.Effective)
        .map(goods => ({
          value: `${goods.goodsBasicInfo.goodsId}`,
          label: `${goods.goodsBasicInfo.goodsName} - ${goods.goodsBasicInfo.goodsVersion}`,
        })),
    [goodsList]
  );
  const channelOptions = useMemo(
    () =>
      (channelList || []).map(channel => ({
        label: channel.name,
        value: channel.code,
      })),
    [channelList]
  );

  const {
    data: searchResult,
    loading,
    pagination,
  } = usePagination(
    ({ current, pageSize }) => {
      const tempAppliedCondition = cloneDeep(appliedCondition);
      tempAppliedCondition.voucherEffectivePeriod = undefined;
      return CustomerLoyaltyService.queryVouchersPage(tempAppliedCondition, current - 1, pageSize).then(res => ({
        list: res.content,
        total: res.totalElements,
      }));
    },
    {
      refreshDeps: [appliedCondition],
      debounceWait: 500,
      defaultPageSize: 10,
      onError: error => {
        message.error(error.message);
      },
    }
  );

  return (
    <div className="relative">
      <div className="relative h-full overflow-auto">
        <Header
          setAppliedCondition={setAppliedCondition}
          appliedCondition={appliedCondition}
          goodsInfoList={goodsInfoList}
          channelOptions={channelOptions}
        />
        <Content
          onView={record => {
            setEditRecordInfo(record);
            setDrawerVisible(true);
          }}
          list={searchResult?.list || []}
          loading={loading}
          paginationOnChange={pagination.onChange}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
          }}
          channelOptions={channelOptions}
        />
      </div>
      <CustomerVoucherDetailDrawer
        visible={drawerVisible}
        editRecordInfo={editRecordInfo}
        goodsInfoList={goodsInfoList}
        channelOptions={channelOptions}
        onClose={() => {
          setEditRecordInfo(undefined);
          setDrawerVisible(false);
        }}
      />
    </div>
  );
};

export default CustomerVoucherCenter;
