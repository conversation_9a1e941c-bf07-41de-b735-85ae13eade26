import { useTranslation } from 'react-i18next';

import { VoucherQueryResponse } from 'genesis-web-service/lib/customer-loyalty';

import { PaginationConfig } from '@market/common/interface';

import VoucherTable from '../VoucherTable';

interface Props {
  pagination: PaginationConfig;
  onView: (voucher: VoucherQueryResponse) => void;
  list: VoucherQueryResponse[];
  loading: boolean;
  channelOptions: { value: string; label: string }[];
  paginationOnChange: (current: number, pageSize: number) => void;
}

export const Content = ({
  onView,
  list,
  loading,
  pagination,
  channelOptions,
  paginationOnChange,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  return (
    <VoucherTable
      list={list}
      pagination={pagination}
      loading={loading}
      onView={onView}
      channelOptions={channelOptions}
      paginationOnChange={paginationOnChange}
    />
  );
};

export default Content;
