import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import classNames from 'classnames/bind';

import { QueryResultContainer, Table, ViewAction } from '@zhongan/nagrand-ui';

import { VoucherQueryResponse } from 'genesis-web-service/lib/customer-loyalty';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { PaginationConfig } from '@market/common/interface';
import GeneralStatus from '@market/components/GeneralStatus';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { renderOptionName } from '@market/utils/enum';

import { VouchersStatusStyleMap } from '../../interface';
import styles from './VoucherTable.module.scss';

const cx = classNames.bind(styles);

interface Props {
  pagination: PaginationConfig;
  list: VoucherQueryResponse[];
  channelOptions: { value: string; label: string }[];
  loading: boolean;
  onView: (voucher: VoucherQueryResponse) => void;
  paginationOnChange: (current: number, pageSize: number) => void;
}

export const VoucherTable = ({ list, pagination, loading, onView, channelOptions, paginationOnChange }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  const L10nUtil = useL10n();
  /* ============== 枚举使用start ============== */
  const voucherStatusOptions = useBizDictAsOptions('voucherStatus');
  const policyIDTypeListOptions = useBizDictAsOptions('certiType');
  /* ============== 枚举使用end ============== */

  const getColumns = useMemo(
    () => [
      {
        title: t('Policy No.'),
        dataIndex: 'policyNo',
        width: 240,
        fixed: 'left',
      },
      {
        title: t('Customer ID No.'),
        dataIndex: 'certNo',
        width: 320,
        render: (certNo: string, record: VoucherQueryResponse) => {
          const certTypeText = record.certType ? renderOptionName(`${record.certType}`, policyIDTypeListOptions) : '';
          if (certTypeText === '') {
            return certNo;
          }
          return `${certTypeText}-${certNo}`;
        },
      },
      {
        title: t('Voucher Name'),
        width: 240,
        dataIndex: 'voucherName',
      },
      {
        title: t('Voucher Code'),
        width: 240,
        dataIndex: 'voucherCode',
      },
      // {
      //   title: t('Voucher Balance'),
      //   width: 240,
      //   dataIndex: 'remainingAmount',
      // },
      {
        title: t('Voucher Status'),
        width: 240,
        dataIndex: 'status',
        render: (status: number) => {
          const statusText = renderOptionName(`${status}`, voucherStatusOptions);
          return (
            <GeneralStatus
              text={`${statusText}`}
              location="table"
              status={VouchersStatusStyleMap[`${status}`] as any}
            />
          );
        },
      },
      {
        title: t('Voucher Channel'),
        width: 240,
        dataIndex: 'channelCode',
        render: (channelCode: number) => renderOptionName(`${channelCode}`, channelOptions) || '',
      },
      {
        title: t('Effective Period'),
        width: 240,
        dataIndex: 'effectivePeriod',
        render: (text: string, record: VoucherQueryResponse) => {
          if (record.effectiveDate && record.expiryDate) {
            return L10nUtil.l10n.dateFormat.getDateTimeRangeString(record.effectiveDate, record.expiryDate);
          }
          return '';
        },
      },
      {
        title: t('Actions'),
        fixed: 'right',
        align: 'right',
        render: (text: string, record: VoucherQueryResponse) => <ViewAction onClick={() => onView(record)} />,
      },
    ],
    [L10nUtil, channelOptions, onView, policyIDTypeListOptions, t, voucherStatusOptions]
  );

  return (
    <QueryResultContainer>
      <Table
        bordered={false}
        dataSource={list}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        loading={loading}
        scroll={{ x: 'max-content' }}
        columns={getColumns}
        onChange={({ pageSize, current }) => {
          paginationOnChange(current || 0, pageSize || 10);
        }}
        emptyType="icon"
      />
    </QueryResultContainer>
  );
};

export default VoucherTable;
