.center-header {
  background-color: var(--white);
  .center-header-content {
    padding: 16px 24px 32px;
    display: flex;
    justify-content: space-between;

    .left {
      .title {
        color: var(--text-color);
        font-size: $font-size-hg;
        line-height: 30px;
        font-weight: 700;
        margin-bottom: var(--gap-xs);
      }

      .description {
        color: var(--text-color-tertiary);
        font-size: $font-size-root;
        line-height: 20px;
      }
    }
  }
}
