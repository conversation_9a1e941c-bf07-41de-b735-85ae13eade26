import React, { CSSProperties, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { message } from 'antd';

import classNames from 'classnames/bind';
import { keyBy } from 'lodash-es';
import moment from 'moment';

import { SimplePageHeader } from '@zhongan/nagrand-ui';

import { useL10n } from 'genesis-web-shared/lib/l10n';

import ConditionSearch from '@market/components/ConditionSearch/ConditionSearch';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { useRenamedBizTypeOptions } from '@market/pages/CustomerLoyalty/hooks';

import { VouchersSearchCondition } from '../../interface';
import styles from './Header.module.scss';

const cx = classNames.bind(styles);

interface Props {
  appliedCondition: VouchersSearchCondition;
  liteStyle?: boolean;
  goodsInfoList: { value: string; label: string }[];
  setAppliedCondition: (value: VouchersSearchCondition) => void;
  style?: CSSProperties;
  channelOptions: { value: string; label: string }[];
}

export const Header = ({
  appliedCondition,
  goodsInfoList,
  liteStyle,
  style,
  channelOptions,
  setAppliedCondition,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const L10nUtil = useL10n();
  /* ============== 枚举使用start ============== */
  const voucherStatusOptions = useBizDictAsOptions('voucherStatus');
  const bizTypeOptions = useBizDictAsOptions('bizType');
  const policyIDTypeListOptions = useBizDictAsOptions('certiType');
  /* ============== 枚举使用end ============== */

  const reNamedBizTypeOptions = useRenamedBizTypeOptions(bizTypeOptions);

  const changeAppliedCondition = useCallback(
    (value: VouchersSearchCondition) => {
      if (Array.isArray(value?.voucherEffectivePeriod) && value?.voucherEffectivePeriod.length > 1) {
        value.effectiveDate = `${moment(value?.voucherEffectivePeriod[0]).format('YYYY-MM-DD')} 00:00:00`;
        value.expiryDate = `${moment(value?.voucherEffectivePeriod[1]).format('YYYY-MM-DD')} 23:59:59`;
      } else {
        value.effectiveDate = undefined;
        value.expiryDate = undefined;
      }
      setAppliedCondition(value);
    },
    [setAppliedCondition]
  );

  const onBeforeSearch = useCallback(
    (value: VouchersSearchCondition) => {
      if ((value?.certType && !value?.certNo) || (!value?.certType && value?.certNo)) {
        message.error(t('Please input both customer ID type and number.'));
        return false;
      }
      return true;
    },
    [t]
  );

  return (
    <React.Fragment>
      <SimplePageHeader>{t('Voucher Consumption Status')}</SimplePageHeader>
      <div style={style} className={cx('center-header')}>
        <div className={cx('center-header-content')}>
          <div className={cx('left')}>
            {liteStyle ? null : (
              <div style={{ marginTop: 24 }}>
                <ConditionSearch
                  outInputKey="voucherName"
                  outInputPlaceholder={t('Search Voucher Name')}
                  panelConditions={[
                    {
                      label: t('Voucher Name'),
                      fieldKey: 'voucherName',
                      type: 'input',
                    },
                    {
                      label: t('Voucher Status'),
                      fieldKey: 'status',
                      type: 'radio',
                      options: voucherStatusOptions,
                    },
                    {
                      label: t('Customer Info'),
                      fieldKey: 'customerInfo',
                      type: 'inputgroup',
                      groupList: [
                        {
                          label: '',
                          fieldKey: 'certType',
                          type: 'select',
                          options: policyIDTypeListOptions,
                        },
                        {
                          label: '',
                          fieldKey: 'certNo',
                          type: 'input',
                        },
                      ],
                    },
                    {
                      label: t('Policy No.'),
                      fieldKey: 'policyNo',
                      type: 'input',
                    },
                    {
                      label: t('Applicable Scenario'),
                      fieldKey: 'applicableScenario',
                      type: 'select',
                      options: reNamedBizTypeOptions,
                    },
                    {
                      label: t('Voucher Channel'),
                      fieldKey: 'channelCode',
                      type: 'select',
                      options: channelOptions || [],
                    },
                    {
                      label: t('Goods Name'),
                      fieldKey: 'goodsId',
                      type: 'select',
                      options: goodsInfoList || [],
                    },
                    {
                      label: t('Voucher Effective Period'),
                      fieldKey: 'voucherEffectivePeriod',
                      type: 'rangepicker',
                    },
                  ]}
                  onBeforeSearch={onBeforeSearch}
                  appliedCondition={appliedCondition}
                  onChange={changeAppliedCondition}
                  outCondition={{
                    fieldKey: 'status',
                    type: 'radio',
                    options: voucherStatusOptions,
                  }}
                  options={{
                    dateTimeRender: date => L10nUtil.l10n.dateFormat.getDateString(date)!,
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </React.Fragment>
  );
};

export default Header;
