/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { Moment } from 'moment';

import { VouchersStatusEnum } from 'genesis-web-service/lib/customer-loyalty';

export interface VouchersSearchCondition {
  voucherEffectivePeriod?: Moment[];
  voucherName?: string;
  certType?: string;
  certNo?: string;
  policyNo?: string;
  applicableScenario?: string;
  channelCode?: string;
  goodsId?: string;
  status?: string;
}

export const VouchersStatusStyleMap = {
  [VouchersStatusEnum.Expired]: 'error',
  [VouchersStatusEnum.Effective]: 'success',
  [VouchersStatusEnum.Used]: 'info',
  [VouchersStatusEnum.Occupied]: 'error',
  [VouchersStatusEnum.Withdrawal]: 'warning',
};
