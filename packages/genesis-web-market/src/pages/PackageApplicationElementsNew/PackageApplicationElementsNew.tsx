import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Button, Col, Form, Input, Layout, Row, Skeleton, message } from 'antd';

import classNames from 'classnames/bind';
import { keyBy } from 'lodash-es';

import { ApplicationElementsGroupQueryResult, MaxAllowedNumberParams } from 'genesis-web-service/lib/market';

import { DetailPageMode } from '@market/common/interface';
import FMarketMenu from '@market/components/F-Market-Menu';
import GeneralSelect from '@market/components/GeneralSelect';
import Section from '@market/components/Section/section';
import { useApplicationElementsGroupList } from '@market/hook/package.service';
import { usePermission } from '@market/hook/permission';
import { selectEnvConfig, selectUserState } from '@market/redux/selector';
import { RouteComponentProps } from '@market/router';
import { NewMarketService } from '@market/services/market/market.service.new';
import { generalConfirm, urlQuery } from '@market/util';

import { FMarketHeader } from '../../components/F-Market-Header';
import ApplicationElementsSectionList from '../ApplicationElementsGroup/components/ApplicationElementsSectionList';
import styles from './PackageApplicationElementsNew.module.scss';
import MaxAllowedNumberLimit from './components/MaxAllowedNumberLimit';
import { checkMaxNumberIsValid } from './dealServiceData';

const cx = classNames.bind(styles);

const { Sider, Content } = Layout;

interface Props extends RouteComponentProps {}

export const PackageApplicationElementsNew = ({ navigate, location }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const packageId = useMemo(() => urlQuery('packageId'), []);
  const [mode, setMode] = useState<DetailPageMode>(
    (location?.state as { mode: DetailPageMode })?.mode || DetailPageMode.view
  );
  const [isShowEditButton, setIsShowEditButton] = useState(false);
  const maxAllowedRef = useRef<{
    getChangedData: () => Promise<MaxAllowedNumberParams>;
    hasChanged: () => boolean;
  }>(null);
  const hasEditAuth = !!usePermission('market.edit');
  const isSuperUser = !!usePermission('market.edit-all');
  const userInfo: {
    userId: number;
  } = useSelector(selectUserState);
  const envConfig: { env?: string } = useSelector(selectEnvConfig);
  const [applicationElementsGroupInfo, setApplicationElementsGroupInfo] =
    useState<ApplicationElementsGroupQueryResult>();
  const [form] = Form.useForm();
  const readOnly = mode === 'view' || !isShowEditButton || envConfig?.env === 'prd';

  const groupList = useApplicationElementsGroupList();

  const groupMap = useMemo(() => keyBy(groupList || [], 'applicationElementsPackCode'), [groupList]);

  const onGroupCodeChange = useCallback(
    (code: string) => {
      const currentGroup = groupMap[code];
      if (currentGroup) {
        form.setFieldsValue(currentGroup);

        if (currentGroup?.id) {
          NewMarketService.ApplicationElementsPackService.query$GET$mgmt_v2_applicationelements_query_applicationElementsId(
            currentGroup.id
          ).then(res => {
            setApplicationElementsGroupInfo(res);
          });
        }
      }
    },
    [form, groupMap]
  );

  useEffect(() => {
    if (!packageId) {
      navigate('/market/package/search');
      return;
    }

    if (!groupList) {
      return;
    }

    NewMarketService.PackageProductMgmtService.queryConfigProducts({ packageId: +packageId }).then(
      packageConfigWithProducts => {
        onGroupCodeChange(packageConfigWithProducts.applicationElementsPackCode);
        setIsShowEditButton((packageConfigWithProducts.creator === `${userInfo.userId}` && hasEditAuth) || isSuperUser);
      }
    );
  }, [groupList]);

  const saveMaxAllowedNumberData = useCallback(async () => {
    let maxAllowedNumberParam: MaxAllowedNumberParams;
    if (maxAllowedRef.current) {
      maxAllowedNumberParam = await maxAllowedRef.current.getChangedData();
      const hasChangedStatus = maxAllowedRef.current.hasChanged();
      if (!hasChangedStatus) {
        return Promise.resolve();
      }
      if (!checkMaxNumberIsValid(maxAllowedNumberParam)) {
        message.error(t('Enter an integer greater than or equal to 1'));
        return Promise.reject();
      }
      return NewMarketService.PackageApplicationElementShareMgmtService.batchSaveApplicationElementLimit(
        maxAllowedNumberParam
      );
    }
  }, [t]);

  const gotoNextPage = useCallback(() => {
    navigate(`/market/package/declaration?packageId=${packageId!}`, {
      state: { mode: mode || 'edit' },
    });
  }, [navigate, mode, packageId]);

  const saveAndDoneStep = useCallback(
    (isSubmit: boolean) => {
      saveMaxAllowedNumberData().then(() => {
        NewMarketService.PackageGoodsStepInfoMgmtService.doneStep({
          // 记录步骤
          refId: packageId!,
          type: 1,
          stepNo: 4,
          isNew: false,
          isSubmit,
        }).then(res => {
          if (!res.success) {
            return;
          }
          if (isSubmit) {
            message.success(t('Publish successfully'));
            navigate('/market/package/search');
          } else {
            gotoNextPage();
          }
        });
      });
    },
    [gotoNextPage, navigate, packageId, saveMaxAllowedNumberData, t]
  );

  const onNextAndSubmit = useCallback(
    async (type: 'submit' | 'save' | 'next') => {
      try {
        const values = (await form.validateFields()) as { applicationElementsPackCode: string };

        if (type === 'submit') {
          await NewMarketService.ApplicationElementsPackService.applicationElementsPackLinkPackage(
            +packageId!,
            values.applicationElementsPackCode
          );
          const packageValidation = await NewMarketService.GoodsPackageValidateService.validatePackage(+packageId!);
          if (packageValidation.errors?.length) {
            generalConfirm({
              title: t('Validation Response'),
              content: (
                <ul>
                  {packageValidation.errors.map(msg => (
                    <li>{msg}</li>
                  ))}
                </ul>
              ),
              onOk: undefined,
            });
            return;
          }
          if (packageValidation.warning?.length) {
            generalConfirm({
              title: t('Validation Response'),
              content: (
                <ul>
                  {packageValidation.warning.map(msg => (
                    <li>{msg}</li>
                  ))}
                </ul>
              ),
              onOk: () => saveAndDoneStep(true),
              okText: t('Confirm'),
            });
          }
        } else {
          await NewMarketService.ApplicationElementsPackService.applicationElementsPackLinkPackage(
            +packageId!,
            values.applicationElementsPackCode
          );
          if (type === 'save') {
            saveMaxAllowedNumberData().then(() => {
              message.success('Save successfully');
            });
          } else {
            saveAndDoneStep(false);
          }
        }
        saveAndDoneStep(true);
      } catch (error) {
        const err = error as {
          errorFields: {
            name: (string | number)[];
            errors: string[];
          }[];
        };
        const firstErrorFieldName = err.errorFields[0]?.name[0];
        document
          ?.querySelector(`#${firstErrorFieldName}`)
          ?.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
      }
    },
    [form, packageId, saveAndDoneStep, t]
  );

  const onEdit = useCallback(() => {
    if (mode !== 'view') {
      return;
    }
    NewMarketService.PackageGoodsStepInfoMgmtService.doneStep({
      // 回退到第三步的步骤
      refId: packageId!,
      type: 1,
      stepNo: 3,
      isNew: false,
      isSubmit: false,
    }).then(() => {
      setMode(DetailPageMode.edit);
      NewMarketService.PackageGoodsStepInfoMgmtService.statusManage({
        refId: +packageId!,
        type: 1,
      });
    });
  }, [mode, packageId]);

  const renderEditSaveBtn = () => {
    if (!isShowEditButton || envConfig.env === 'prd') {
      return null;
    }

    if (mode === 'view') {
      return (
        <Button size="large" onClick={onEdit}>
          {t('Edit')}
        </Button>
      );
    }

    return (
      <Button size="large" onClick={() => onNextAndSubmit('save')} disabled={!isShowEditButton}>
        {t('Save')}
      </Button>
    );
  };

  return (
    <Layout className={cx('market-layout application-elements-container')}>
      <FMarketHeader backPath="/market/package/search" subMenu="Package" />
      <div className="flex" style={{ height: 'calc(100% - 50px)' }}>
        <Sider width={208} className="market-sider">
          <FMarketMenu
            page="PACKAGE"
            category={1}
            type={mode}
            defaultSelectedKeys={['4']}
            packageId={packageId}
            navigate={navigate}
          />
        </Sider>
        <Content className="market-content">
          <Skeleton loading={false}>
            <div className={cx('right-content-wrapper')}>
              <Section className="mx-6 mt-6" subTitle={t('Application Elements')}>
                <Form form={form} layout="vertical">
                  <Row>
                    <Col span={8}>
                      <Form.Item
                        name="applicationElementsPackCode"
                        required
                        label={t('Application Elements Group Code')}
                        rules={[
                          {
                            required: true,
                            message: t('Please input'),
                          },
                        ]}
                      >
                        <GeneralSelect
                          option={(groupList || []).map(group => ({
                            value: group.applicationElementsPackCode,
                            label: group.applicationElementsPackCode,
                          }))}
                          disabled={mode === DetailPageMode.view}
                          allowClear={false}
                          onChange={onGroupCodeChange}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item label={t('Application Elements Group Name')} name="applicationElementsPackName">
                        <Input disabled style={{ width: 240 }} />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Form.Item
                    noStyle
                    shouldUpdate={(prev: { applicationElementsPackCode: string }, current) =>
                      prev.applicationElementsPackCode !== current.applicationElementsPackCode
                    }
                  >
                    {({ getFieldValue }) => {
                      const groupCode = getFieldValue('applicationElementsPackCode') as string;

                      if (!groupCode) return null;

                      return (
                        <div>
                          <ApplicationElementsSectionList
                            displayPosition="package"
                            applicationElementsPackCode={groupCode}
                            mode={DetailPageMode.view}
                            applicationElementsGroupInfo={applicationElementsGroupInfo}
                            refInstance={null}
                          />
                          <MaxAllowedNumberLimit
                            packageId={packageId!}
                            disabled={mode === 'view' || !isShowEditButton}
                            refInstance={maxAllowedRef}
                            applicationElementsPackCode={groupCode}
                            supportBeneficiaryTypes={applicationElementsGroupInfo?.supportBeneficiaryTypes || []}
                          />
                        </div>
                      );
                    }}
                  </Form.Item>
                </Form>
              </Section>
            </div>
          </Skeleton>
        </Content>
        <div className={cx('bottom-action-bar')}>
          {readOnly ? (
            <Button size="large" onClick={gotoNextPage} type="primary">
              {t('Next')}
            </Button>
          ) : (
            <Button size="large" type="primary" onClick={() => onNextAndSubmit('submit')}>
              {t('Publish')}
            </Button>
          )}
          {renderEditSaveBtn()}
          {!readOnly && (
            <Button size="large" onClick={() => onNextAndSubmit('next')} type="default">
              {t('Next')}
            </Button>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default PackageApplicationElementsNew;
