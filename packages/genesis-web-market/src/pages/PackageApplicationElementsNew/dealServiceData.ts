import { MaxAllowedNumberParams } from 'genesis-web-service/lib/market';

// 检查MaxAllowedNumber中的Object number和 Insured Number是否都填写了 大于0 的数值
export const checkMaxNumberIsValid = (param: MaxAllowedNumberParams) => {
  let isInsuredValid = false;
  let isObjectValid = true;

  if (param.insuredItemLimits?.length === 0) {
    isInsuredValid = true;
  } else if (param.insuredItemLimits[0].maxAllowedNumber && param.insuredItemLimits[0].maxAllowedNumber > 0) {
    isInsuredValid = true;
  }

  if (Array.isArray(param.objectItemLimits) && param.objectItemLimits.length > 0) {
    param.objectItemLimits.forEach(item => {
      if (item.maxAllowedNumber <= 0) {
        isObjectValid = false;
      }
    });
  }
  return isObjectValid && isInsuredValid;
};
