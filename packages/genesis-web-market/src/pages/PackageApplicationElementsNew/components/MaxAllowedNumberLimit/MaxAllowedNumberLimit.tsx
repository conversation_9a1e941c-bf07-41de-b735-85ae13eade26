import { Ref, forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, InputNumber, Row, message } from 'antd';
import type { ColumnProps } from 'antd/es/table';

import { cloneDeep, keyBy, uniqBy } from 'lodash-es';

import { Table } from '@zhongan/nagrand-ui';

import { MaxAllowedNumberParams, SupportBeneficiaryType } from 'genesis-web-service';
import { ApplicationElementTopic } from 'genesis-web-service/lib/market';

import { ObjectCategoryType, ObjectSubCategoryType } from '@market/common/enums';
import { MaxAllowedNumberList } from '@market/common/interface';
import { useBizDict } from '@market/hook/bizDict';
import { NewMarketService } from '@market/services/market/market.service.new';

import styles from './MaxAllowedNumberLimit.module.scss';

const FormItem = Form.Item;
const objectSubCategoryDisabledArr = [
  ObjectSubCategoryType.ClaimExperience,
  ObjectSubCategoryType.CarOwner,
  ObjectSubCategoryType.Vehicle,
  ObjectSubCategoryType.VehicleLoan,
];
interface IProps {
  packageId: number | string;
  refInstance: Ref<{
    getChangedData: () => Promise<MaxAllowedNumberParams>;
    hasChanged: () => boolean;
  }>;
  disabled: boolean;
  applicationElementsPackCode: string;
  supportBeneficiaryTypes: number[];
}

const MaxAllowedNumberLimit = ({
  packageId,
  refInstance,
  disabled,
  applicationElementsPackCode,
  supportBeneficiaryTypes,
}: IProps) => {
  const [t] = useTranslation(['market', 'common']);
  /* ============== 枚举使用start ============== */
  const objectCategoryBizDicts = useBizDict('objectCategory');
  const objectSubCategoryBizDicts = useBizDict('objectSubCategory');
  /* ============== 枚举使用end ============== */
  const [uniqueObjectCategoryList, setUniqueObjectCategoryList] = useState<
    {
      objectCategory: number;
      objectSubCategory: number;
    }[]
  >([]);
  const [objectLimitsMap, setObjectLimitsMap] = useState<Record<string, MaxAllowedNumberList>>();
  const [hasChangedStatus, setHasChangedStatus] = useState<boolean>(false);
  const [isShowInsured, setIsShowInsured] = useState<boolean>(false);
  const [isShowBeneficiary, setIsShowBeneficiary] = useState<boolean>(false);

  const isShowObjectSection = uniqueObjectCategoryList && uniqueObjectCategoryList?.length > 0;
  const isDisplayBeneficiary =
    isShowBeneficiary && supportBeneficiaryTypes.includes(+SupportBeneficiaryType.DesignatedBeneficiary);
  const [form] = Form.useForm();

  const queryDeps = useCallback(
    () =>
      Promise.all([
        NewMarketService.ApplicationElementsPackService.query$POST$mgmt_v2_applicationelements_query_factors({
          topic: ApplicationElementTopic.object,
          applicationElementsPackCode,
        }).then(
          ({ factors: objectFactorList }) => {
            const configedObjectFactorList = (objectFactorList || []).filter(item => item.configured);
            const tempFilterObjectCategoryList: {
              objectCategory: number;
              objectSubCategory: number;
            }[] = uniqBy(
              configedObjectFactorList.map((item) => ({
                objectCategory: item.objectCategory as number,
                objectSubCategory: item.objectSubCategory as number,
              })),
              (item) => `${item.objectCategory}_${item.objectSubCategory}`
            );

            setUniqueObjectCategoryList(tempFilterObjectCategoryList);
            setHasChangedStatus(true);
            return tempFilterObjectCategoryList;
          }
        ),
        NewMarketService.ApplicationElementsPackService.query$POST$mgmt_v2_applicationelements_query_factors({
          topic: ApplicationElementTopic.insured,
          applicationElementsPackCode,
        }).then(
          ({ factors: insuredFactors }) => {
            setIsShowInsured((insuredFactors || []).filter(item => item.configured).length > 0);
          }
        ),
        NewMarketService.ApplicationElementsPackService.query$POST$mgmt_v2_applicationelements_query_factors({
          topic: ApplicationElementTopic.beneficiary,
          applicationElementsPackCode,
        }).then(({ factors: beneficiaryFactors }) => {
          setIsShowBeneficiary((beneficiaryFactors || []).filter(item => item.configured).length > 0);
        }),
      ]),
    [applicationElementsPackCode]
  );

  useEffect(() => {
    queryDeps().then(reslist => {
      NewMarketService.PackageApplicationElementShareMgmtService.queryApplicationElementLimit({
        packageId: +packageId,
      }).then(res => {
        const filterObjectCategoryList = reslist[0];
        const tempObjectLimitsMap = keyBy(
          filterObjectCategoryList.map(item => ({
            objectSubCategory: item.objectSubCategory,
            objectCategory: item.objectCategory,
            maxAllowedNumber: 1,
          })),
          (item) => `${item.objectCategory}_${item.objectSubCategory}`
        );

        if (Array.isArray(res.insuredLimits) && res.insuredLimits.length > 0) {
          form.setFieldValue('maxAllowedInsuredNumber', res?.insuredLimits[0]?.maxAllowedNumber);
        }
        if (Array.isArray(res.beneficiaryLimits) && res.beneficiaryLimits.length > 0) {
          form.setFieldValue('maxAllowedDesignatedBeneficiaryNumber', res?.beneficiaryLimits?.[0]?.maxAllowedNumber);
        }
        if (Array.isArray(res.objectLimits) && res.objectLimits.length > 0) {
          res.objectLimits.forEach(item => {
            if (tempObjectLimitsMap[`${item.objectCategory}_${item.objectSubCategory}`]) {
              tempObjectLimitsMap[`${item.objectCategory}_${item.objectSubCategory}`].maxAllowedNumber = item?.maxAllowedNumber || 1;
            }
          });
        }
        setObjectLimitsMap(tempObjectLimitsMap);
      });
    });
  }, [queryDeps]);

  useImperativeHandle(refInstance, () => ({
    getChangedData: (): Promise<MaxAllowedNumberParams> =>
      new Promise((resolve, reject) => {
        form
          .validateFields()
          .then(values => {
            const paramsInfo: MaxAllowedNumberParams = {
              packageId: +packageId,
              insuredItemLimits: [],
              objectItemLimits: [],
            };
            if (isShowInsured) {
              paramsInfo.insuredItemLimits = [
                {
                  customerType: 1,
                  customerRole: 2,
                  maxAllowedNumber: values.maxAllowedInsuredNumber,
                },
              ];
            }
            const tempObjectItemLimits: MaxAllowedNumberParams['objectItemLimits'] = [];
            if (isShowObjectSection) {
              uniqueObjectCategoryList.forEach(tableItem => {
                tempObjectItemLimits.push({
                  objectCategory: tableItem.objectCategory,
                  objectSubCategory: tableItem.objectSubCategory,
                  maxAllowedNumber: objectLimitsMap?.[`${tableItem.objectCategory}_${tableItem.objectSubCategory}`]?.maxAllowedNumber || 1,
                });
              });
            }
            paramsInfo.objectItemLimits = tempObjectItemLimits;

            if (isDisplayBeneficiary) {
              paramsInfo.beneficiaryItemLimits = [
                {
                  maxAllowedNumber: values.maxAllowedDesignatedBeneficiaryNumber,
                },
              ];
            }
            resolve(paramsInfo);
          })
          .catch((err: Error) => {
            if (err && !hasChangedStatus) reject(err);
          });
      }),
    hasChanged: () => {
      if (!isShowBeneficiary && !isShowInsured && !isShowObjectSection) {
        return false;
      }
      return hasChangedStatus;
    },
  }));

  const onChangeRecordNumber = useCallback(
    (record: MaxAllowedNumberList, value?: number) => {
      if (!value) {
        return message.error(t('Please input number'));
      }
      const tempObjectLimitsMap = cloneDeep(objectLimitsMap) || {};
      if (tempObjectLimitsMap[`${record.objectCategory}_${record.objectSubCategory}`]) {
        tempObjectLimitsMap[`${record.objectCategory}_${record.objectSubCategory}`].maxAllowedNumber = value;
      } else {
        tempObjectLimitsMap[`${record.objectCategory}_${record.objectSubCategory}`] = {
          objectSubCategory: record.objectSubCategory,
          maxAllowedNumber: value,
        };
      }
      setHasChangedStatus(true);
      setObjectLimitsMap(tempObjectLimitsMap);
    },
    [objectLimitsMap]
  );

  const isDisabled = useCallback((record: MaxAllowedNumberList) => {
    if (
      record.objectCategory === ObjectCategoryType.Auto &&
      objectSubCategoryDisabledArr.includes(record.objectSubCategory)
    ) {
      return true;
    }
    if (
      record.objectCategory === ObjectCategoryType.Travel &&
      record.objectSubCategory === ObjectSubCategoryType.Travel
    ) {
      return true;
    }
    return false;
  }, []);
  const columns = useMemo(() => {
    const tempColumns: ColumnProps<{
      objectCategory: number;
      objectSubCategory: number;
    }>[] = [
      {
        title: t('Object'),
        dataIndex: 'objectCategory',
        width: 450,
        render: (text: number) =>
          ((objectCategoryBizDicts || []).find(item => item.dictValue === `${text}`) || {}).dictValueName,
      },
      {
        title: t('Object Component'),
        dataIndex: 'objectSubCategory',
        width: 450,
        render: (text: number) =>
          ((objectSubCategoryBizDicts || []).find(item => item.dictValue === `${text}`) || {}).dictValueName,
      },
      {
        title: (
          <div className="flex items-center">
            <span className={styles.number}>*</span>
            <div>{t('Max Allowed Number')}</div>
          </div>
        ),
        dataIndex: 'maxAllowedNumber',
        key: 'name',
        width: 240,
        render: (text: number, record: MaxAllowedNumberList) => (
          <InputNumber
            min={1}
            max={100}
            disabled={isDisabled(record) || disabled}
            value={objectLimitsMap?.[`${record.objectCategory}_${record.objectSubCategory}`]?.maxAllowedNumber}
            onChange={value => onChangeRecordNumber(record, value)}
            style={{ width: 240 }}
            placeholder={t('Please input')}
          />
        ),
      },
    ];

    return tempColumns;
  }, [
    disabled,
    isDisabled,
    objectCategoryBizDicts,
    objectLimitsMap,
    objectSubCategoryBizDicts,
    onChangeRecordNumber,
    t,
  ]);

  if (!isShowBeneficiary && !isShowInsured && !isShowObjectSection) {
    return null;
  }

  return (
    <div className="add-table mb-6">
      {isDisplayBeneficiary ||
        (isShowInsured && <div style={{ fontWeight: 600 }}>{t('Max Allowed Number Limit')}</div>)}
      <Form
        form={form}
        initialValues={{
          maxAllowedDesignatedBeneficiaryNumber: 1,
          maxAllowedInsuredNumber: 1,
        }}
      >
        <Row>
          {isShowInsured && (
            <Col span={8}>
              <FormItem
                name="maxAllowedInsuredNumber"
                colon={false}
                label={t('Max Allowed Insured Number')}
                rules={[
                  {
                    required: true,
                    message: t('Please input'),
                  },
                ]}
              >
                <InputNumber
                  style={{ width: 240 }}
                  placeholder={t('Please input')}
                  min={1}
                  disabled={disabled}
                  onChange={() => setHasChangedStatus(true)}
                />
              </FormItem>
            </Col>
          )}
          {/* 如果受益人支持指定受益人，则展示且必填 */}
          {isDisplayBeneficiary && (
            <Col span={10}>
              <FormItem
                name="maxAllowedDesignatedBeneficiaryNumber"
                colon={false}
                label={t('Max Allowed Designated Beneficiary Number')}
                rules={[
                  {
                    required: true,
                    message: t('Please input'),
                  },
                ]}
              >
                <InputNumber
                  style={{ width: 240 }}
                  placeholder={t('Please input')}
                  min={1}
                  disabled={disabled}
                  onChange={() => setHasChangedStatus(true)}
                />
              </FormItem>
            </Col>
          )}
        </Row>
        {isShowObjectSection && (
          <div className="mt-4">
            <div style={{ fontWeight: 600 }}>{t('Max Allowed Object Number Limit')}</div>
            <Table columns={columns} dataSource={uniqueObjectCategoryList} pagination={false} scroll={{ y: 268 }} />
          </div>
        )}
      </Form>
    </div>
  );
};

export const MaxAllowedNumberForm = forwardRef(MaxAllowedNumberLimit);

export default MaxAllowedNumberForm;
