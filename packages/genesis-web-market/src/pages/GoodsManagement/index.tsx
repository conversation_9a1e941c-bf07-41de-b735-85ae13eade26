import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { Form, Input, Skeleton, Switch, Tooltip, message } from 'antd';
import { TablePaginationConfig } from 'antd/es/table';
import { FilterValue } from 'antd/es/table/interface';

import {
  AddNewButton,
  EllipsisWithCount,
  Modal,
  OperationContainer,
  QueryOperationSelect,
  QueryResultContainer,
  StatusTag,
  Table,
} from '@zhongan/nagrand-ui';
import type { StatusType } from '@zhongan/nagrand-ui/dist/components/StatusTag';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import { GoodsStatusType, QueryChannelResult } from 'genesis-web-service';
import {
  GoodSearchDetailResponseDTO,
  GoodsSalesChannelResponseDTO,
  PolicyScenarioEnum,
} from 'genesis-web-service/service-types/market-types/package';
import { useBizDict, usePermission } from 'genesis-web-shared/lib/hook';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { FEChannel } from '@market/common/interface';
import { queryTotalChannelList } from '@market/marketService/channel.service';
import QueryForm from '@market/pages/GoodsManagement/QueryForm';
import { selectEnvConfig } from '@market/redux/selector';
import { NewMarketService } from '@market/services/market/market.service.new';
import { generalConfirm } from '@market/util';

import { GoodsCodeValidator } from '../GoodsBasicConfig/validateConfig';
import { renderTableSalesChannel } from '../SalesAttributes/dealServiceData';
import { ActionButton } from './ActionButton';
import filterStyles from './filter.module.scss';
import './index.scss';

interface GoodsCategoryData {
  id: number;
  categoryName: string;
}

type TableData = GoodSearchDetailResponseDTO & {
  key?: number;
};

export enum StatusEnum {
  Draft = 1,
  Effective = 2,
  Ready = 3,
  Invalid = 4,
  Launched = 5,
}

const StatusTagMap: Record<number, StatusType> = {
  [StatusEnum.Draft]: 'no-status',
  [StatusEnum.Effective]: 'success',
  [StatusEnum.Launched]: 'success',
  [StatusEnum.Ready]: 'info',
  [StatusEnum.Invalid]: 'error',
};

const NewGoodsManagement = () => {
  const [form] = Form.useForm<{ goodsName: string; goodsCode: string }>();
  const [t] = useTranslation(['market', 'common']);
  const [searchQuery, setSearchQuery] = useRouterState<{
    [key: string]: any;
    showSelf: boolean;
    current: number;
    pageSize: number;
  }>({
    current: 0,
    showSelf: false,
    pageSize: 10,
  });
  const hasEditAuth = !!usePermission('market.edit');
  const isSuperUser = !!usePermission('market.edit-all');
  const envConfig = useSelector(selectEnvConfig) as { env: string };
  const goodsStatusBizDicts = useBizDict('goodsStatus');
  const goodsCategoryEnums = useBizDict('goodsCategory');

  const [channelList, setChannelList] = useState<Partial<Omit<QueryChannelResult, 'channelId'>>[]>([]);
  const [skeletonVisible, setSkeletonVisible] = useState(true);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<TableData[]>([]);
  const [total, setTotal] = useState(0);
  const [goodStatusList, setGoodStatusList] = useState<GoodsStatusType[]>([]);
  const QueryFormRef = useRef<{ handleSubmit: () => void } | null>(null);
  const { l10n } = useL10n();

  const [open, setOpen] = useState(false);
  const [copiedRecord, setCopiedRecord] = useState<TableData>({} as TableData);

  const navigate = useNavigate();

  const isCopyToNewVersion = useMemo(() => copiedRecord.key === 3, [copiedRecord]);

  const resetCopyModal = useCallback(() => {
    setOpen(false);
    setCopiedRecord({} as TableData);
    form.resetFields();
  }, [form]);

  const onCopyGoods = useCallback(
    (passedRecord: TableData) => {
      if (!copiedRecord.id && !passedRecord.id) {
        message.error(t('Goods id is required'));
        return;
      }
      const recordData = passedRecord.key ? passedRecord : copiedRecord;
      const key = recordData.key;
      if (recordData.key === 3) {
        NewMarketService.GoodMgmtService.copy({
          copyGoodsId: recordData.id,
          copyModel: key,
          status: recordData.goodStatus,
          goodsCode: recordData.goodsCode,
          goodsName: recordData.goodsName,
        }).then(res => {
          if (res.success && res.value) {
            resetCopyModal();
            navigate(`/market/goods/basic?goodsId=${res.value.goodsId ?? ''}&layoutId=${res.value.layoutId ?? ''}`, {
              state: {
                mode: 'edit',
                queryModel: key,
              },
            });
          }
        });
        return;
      }
      form.validateFields().then((values: { goodsName: string; goodsCode: string }) => {
        NewMarketService.GoodMgmtService.copy({
          copyGoodsId: recordData.id as number,
          copyModel: key,
          status: recordData.goodStatus as unknown as number,
          goodsCode: values.goodsCode ?? recordData.goodsCode,
          goodsName: values.goodsName ?? recordData.goodsName,
        }).then(res => {
          if (res.success && res.value) {
            resetCopyModal();
            navigate(`/market/goods/basic?goodsId=${res.value.goodsId ?? ''}&layoutId=${res.value.layoutId ?? ''}`, {
              state: {
                mode: 'edit',
                queryModel: key,
              },
            });
          }
        });
      });
    },
    [t, resetCopyModal, navigate, copiedRecord, form]
  );

  const renderSalesChannelAndServiceCompanyLabel = (
    salesChannel?: GoodsSalesChannelResponseDTO[],
    type?: 'salesChannel' | 'serviceCompany'
  ) => {
    const arr =
      salesChannel?.filter(item => (type === 'serviceCompany' ? item.isServiceCompany : !item.isServiceCompany)) ?? [];

    return (
      arr
        .map(i =>
          i.channelCode
            ?.split(',')
            ?.map(code => renderTableSalesChannel(channelList as FEChannel[], code, i.subChannelCode))
        )
        .flat()
        .filter(item => item)
        .sort((a, b) => a!.length - b!.length) ?? []
    );
  };

  const search = () => {
    const { current, pageSize, showSelf, ...condition } = searchQuery ?? {};
    setLoading(true);
    NewMarketService.GoodSearchMgmtService.queryGoods({
      page: {
        pageIndex: current + 1,
        limit: pageSize,
        condition: {
          goodStatusList: goodStatusList as unknown as number[],
          ...condition,
        },
      },
      showSelfGoods: !!showSelf,
    })
      .then(res => {
        if (Array.isArray(res?.value?.goods?.results)) {
          setTotal(res.value.goods.total!);
          setData(res.value.goods.results);
        } else {
          setTotal(0);
          setData([]);
        }
      })
      .finally(() => {
        setLoading(false);
        setSkeletonVisible(false);
      });
  };

  useEffect(() => {
    queryTotalChannelList().then(list => {
      setChannelList(list);
    });
  }, []);

  const goodsCategoryData = useMemo(
    () =>
      goodsCategoryEnums?.map(item => ({
        id: item.itemExtend1 as number,
        categoryName: item.itemName as string,
      })),
    [goodsCategoryEnums]
  );

  const handleCodeToName = useCallback(
    (text: string | number, enums_temp: GoodsCategoryData[]) =>
      text != null &&
      Array.isArray(enums_temp) &&
      enums_temp.filter((item: GoodsCategoryData) => item.id === text).length > 0 && (
        <Tooltip
          placement="topLeft"
          title={enums_temp.filter((item: GoodsCategoryData) => item.id === text)[0].categoryName}
        >
          <div
            style={{
              width: 118,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {enums_temp.filter((item: GoodsCategoryData) => item.id === text)[0].categoryName}
          </div>
        </Tooltip>
      ),
    []
  );

  const deleteGoods = useCallback(
    (record: TableData) => {
      NewMarketService.GoodMgmtService.delete({
        goodsId: record.id,
      }).then(() => {
        search();
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [QueryFormRef]
  );

  const launchGoods = useCallback(
    (goodsId: number) => {
      NewMarketService.GoodsMgmtControllerService.launchGoods(goodsId, {
        headers: {
          'Content-Type': 'application/json',
        },
      })
        .then(() => {
          search();
          message.success(t('Launch Successfully'));
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [search, t]
  );

  const validation = (record: TableData) => {
    setLoading(true);
    NewMarketService.GoodsPackageValidateService.validateGoods(record.id!).then(res => {
      if (res.errors?.length && res.errors?.length > 0) {
        generalConfirm({
          title: t('Validation Response'),
          content: (
            <ul>
              {res.errors.map(msg => (
                <li>{msg}</li>
              ))}
            </ul>
          ),
          onOk: undefined,
        });
        setLoading(false);
        return;
      }

      const isNormalOrGroupOrRelationalPolicy = record.simplePackages?.every(
        pkg =>
          pkg?.policyScenario === PolicyScenarioEnum.NORMAL ||
          pkg?.policyScenario === PolicyScenarioEnum.GROUP_POLICY ||
          pkg?.policyScenario === PolicyScenarioEnum.RELATIONAL
      );

      if (!isNormalOrGroupOrRelationalPolicy) {
        generalConfirm({
          title: t('Warning'),
          content: (
            <div>
              {t(
                'The marketing goods contain policy types other than Normal Policy or Group Policy. These types are not subject to standard validation checks before launching. Do you want to proceed?'
              )}
            </div>
          ),
          onOk: () => {
            launchGoods(record.id!);
          },
          okText: t('Confirm'),
        });
        setLoading(false);
        return;
      }

      launchGoods(record.id!);
    });
  };

  const handleTableChange = useCallback(
    (pagination: TablePaginationConfig, filters?: Record<string, FilterValue | null>) => {
      setGoodStatusList(filters?.goodStatus as GoodsStatusType[]);

      setSearchQuery({
        ...searchQuery,
        pageSize: pagination.pageSize!,
        current: pagination.current! - 1,
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [QueryFormRef, searchQuery, setGoodStatusList]
  );

  useEffect(() => {
    if (channelList?.length) {
      search();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, channelList]);

  const columns = [
    {
      title: t('Goods Code') as string,
      dataIndex: 'code',
      className: 'market-table-column-goods-code',
      width: 282,
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          <div
            style={{
              width: 220,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {text}
          </div>
        </Tooltip>
      ),
    },
    {
      title: t('Version') as string,
      dataIndex: 'version',
      width: 85,
    },
    {
      title: t('insurance_goods_name') as string,
      dataIndex: 'goodsName',
      width: 222,
      render: (text: string) => {
        if (!text) {
          return '--';
        }
        return (
          <Tooltip placement="topLeft" title={text}>
            <div
              style={{
                width: 190,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {text}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: t('Package Code') as string,
      dataIndex: 'simplePackages',
      width: 300,
      render: (packageList: { code: string }[]) => {
        if (!packageList || packageList.length === 0) {
          return t('- -') as string;
        }
        const items = packageList
          .filter((item: { code: string }) => item)
          .map((item: { code: string }) => `${item.code}`)
          .sort((a: string, b: string) => a.length - b.length);

        return <EllipsisWithCount width="300px" items={items} />;
      },
    },
    {
      title: t('status') as string,
      dataIndex: 'goodStatus',
      width: 115,
      filters: [
        ...(goodsStatusBizDicts
          ?.filter(
            item => ![GoodsStatusType.Ready, GoodsStatusType.Disabled].includes(item.dictValue as GoodsStatusType)
          )
          .map(item => ({
            value: Number(item.dictValue),
            text: item.dictValueName,
          })) ?? []),
      ],
      filteredValue: goodStatusList,
      render: (text: number) => {
        if (text != null && (goodsStatusBizDicts?.filter(item => Number(item?.dictValue) === text)?.length ?? 0) > 0) {
          return (
            <React.Fragment>
              <StatusTag
                statusI18n={goodsStatusBizDicts?.filter(item => Number(item.dictValue) === text)[0].dictValueName}
                type={StatusTagMap[text]}
                needDot
              />
            </React.Fragment>
          );
        }
        return '--';
      },
    },
    {
      title: t('Goods Category') as string,
      dataIndex: 'goodsType',
      width: 150,
      render: (text: string | number) =>
        handleCodeToName(
          text,
          goodsCategoryData as {
            id: number;
            categoryName: string;
          }[]
        ),
    },
    {
      title: t('Sales Channel') as string,
      dataIndex: 'salesChannel',
      width: 200,
      render: (arr: GoodsSalesChannelResponseDTO[]) => {
        const filterArr = renderSalesChannelAndServiceCompanyLabel(arr, 'salesChannel');
        if (!filterArr?.length) {
          return '--';
        }
        return <EllipsisWithCount width="200px" items={filterArr as string[]} />;
      },
    },
    {
      title: t('Service Company') as string,
      dataIndex: 'salesChannel',
      width: 200,
      render: (arr: GoodsSalesChannelResponseDTO[]) => {
        const filterArr = renderSalesChannelAndServiceCompanyLabel(arr, 'serviceCompany');
        if (!filterArr?.length) {
          return '--';
        }
        return <EllipsisWithCount width="200px" items={filterArr as string[]} />;
      },
    },
    {
      title: t('Sales Time') as string,
      dataIndex: 'salesTime',
      width: 280,
      render: (text: string, record: TableData) => {
        if (record.beginSalesDate && record.endSalesDate) {
          return (
            <span>
              {l10n.dateFormat.getDateTimeRangeString(record.beginSalesDate, record.endSalesDate, record.zoneId)}
            </span>
          );
        }
        return '--';
      },
    },
    {
      title: t('Goods Introduction') as string,
      dataIndex: 'goodsDesc',
      width: 250,
      render: (text: string) => {
        if (!text) {
          return t('--') as string;
        }
        return (
          <Tooltip placement="topLeft" title={text}>
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
              }}
            >
              {text}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: t('service_page_creator') as string,
      dataIndex: 'creatorName',
      width: 170,
      render: (text: string, record: TableData) => text || record.creator,
    },
    {
      title: t('Last Modifier') as string,
      width: 128,
      dataIndex: 'modifierName',
    },
    {
      title: t('Actions') as string,
      dataIndex: 'action',
      width: 110,
      fixed: 'right',
      align: 'right',
      render: (_, record: TableData) => (
        <ActionButton
          record={record}
          hasEditAuth={hasEditAuth}
          isSuperUser={isSuperUser}
          deleteGoods={deleteGoods}
          validation={validation}
          setOpen={setOpen}
          setCopiedRecord={setCopiedRecord}
          onCopyGoods={onCopyGoods}
        />
      ),
    },
  ];

  useEffect(() => {
    if (isCopyToNewVersion) {
      form.setFieldValue(['goodsCode'], copiedRecord.goodsCode);
    }
  }, [copiedRecord, form, isCopyToNewVersion]);

  return (
    <div className="management-container">
      <Skeleton active loading={skeletonVisible}>
        <QueryForm
          goodsCategoryEnums={goodsCategoryData!}
          loading={loading}
          salesChannelEnums={channelList?.map(channel => ({
            value: channel.code,
            label: channel.name,
          }))}
          search={values => {
            setSearchQuery({
              ...searchQuery,
              ...values,
              current: 0,
            });
          }}
        />

        <QueryResultContainer>
          <OperationContainer>
            {envConfig.env !== 'prd' && (
              <OperationContainer.Left>
                <AddNewButton
                  type="primary"
                  ghost
                  disabled={!(hasEditAuth || isSuperUser)}
                  onClick={() => {
                    navigate('/market/goods/basic', {
                      state: { mode: 'add' },
                    });
                  }}
                >
                  {t('Add New')}
                </AddNewButton>
              </OperationContainer.Left>
            )}
            <OperationContainer.Right>
              <QueryOperationSelect
                options={[
                  {
                    label: t('My Task') as string,
                    value: true,
                  },
                  {
                    label: t('Public Task') as string,
                    value: false,
                  },
                ]}
                value={!!searchQuery?.showSelf}
                onChange={(checked: boolean) => {
                  setSearchQuery({
                    ...searchQuery,
                    showSelf: checked,
                    current: 0,
                  });
                }}
              />
            </OperationContainer.Right>
          </OperationContainer>

          <Table
            rowKey="id"
            indentSize={0}
            columns={columns}
            loading={loading}
            dataSource={data}
            scroll={{ x: 'max-content' }}
            onChange={handleTableChange}
            pagination={{
              total,
              pageSize: searchQuery?.pageSize,
              current: searchQuery.current + 1,
            }}
            emptyType="icon"
            expandType="nestedTable"
            className={filterStyles.tableWrapper}
          />
        </QueryResultContainer>

        <Modal open={open} title={t('Copy to New Goods') as string} onCancel={resetCopyModal} onOk={onCopyGoods}>
          <Form form={form} name="copyModalForm" layout="vertical">
            <Form.Item
              name="goodsName"
              label={t('Goods Name') as string}
              rules={[{ required: true, message: t('Please input') as string }]}
            >
              <Input placeholder={t('Please input') as string} />
            </Form.Item>
            <Form.Item name="goodsCode" label={t('Goods Code') as string} rules={GoodsCodeValidator}>
              <Input placeholder={t('Please input') as string} />
            </Form.Item>
          </Form>
        </Modal>
      </Skeleton>
    </div>
  );
};

export default NewGoodsManagement;
