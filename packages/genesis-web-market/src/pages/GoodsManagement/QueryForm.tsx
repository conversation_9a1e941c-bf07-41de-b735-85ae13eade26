import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';

import { uniqBy } from 'lodash-es';

import { FieldType, QueryForm as NagrandQueryForm } from '@zhongan/nagrand-ui';

import { useRouterState } from 'genesis-web-component/lib/hook/router';

import { useGoodsList } from '@market/hook/goods.service';
import { NewMarketService } from '@market/services/market/market.service.new';

interface Option {
  value: string;
  label: string;
}

interface Props {
  goodsCategoryEnums: {
    id: number;
    categoryName: string;
  }[];
  salesChannelEnums: {
    label?: string;
    value?: string;
  }[];
  loading: boolean;
  search: (arg: Record<string, unknown>, arg2?: unknown) => void;
}

const QueryForm: FC<Props> = ({ goodsCategoryEnums, salesChannelEnums, loading, search }) => {
  const [form] = Form.useForm();
  const [t] = useTranslation(['market', 'common']);
  const goodsList = useGoodsList()?.map(({ goodsBasicInfo }) => goodsBasicInfo);
  const [packageEnums, setPackageEnums] = useState<Option[]>([]);
  const goodsCodeValue = Form.useWatch<string>('code', form);

  const [searchQuery] = useRouterState<Record<string, unknown>>();

  useEffect(() => {
    NewMarketService.PackageQueryMgmtService.query$POST$mgmt_package_queryByCondition({}).then(res => {
      setPackageEnums(
        res?.value?.packageList
          ? res.value.packageList.map(item => ({
              value: item.packageCode!,
              label: `${item.packageCode!}-${item.packageName!}`,
            }))
          : []
      );
    });
    form.setFieldsValue(searchQuery);
  }, []);

  return (
    <NagrandQueryForm
      needSearchAfterClear
      title={t('Marketing Goods')}
      formProps={{ form }}
      loading={loading}
      queryFields={[
        {
          col: 8,
          key: 'code',
          label: t('Goods Code'),
          type: FieldType.Select,
          extraProps: {
            options: uniqBy(goodsList, 'goodsCode')?.map(goods => ({
              value: goods.goodsCode,
              label: goods.goodsCode,
            })),
            onChange: () => {
              form.resetFields(['version']);
            },
          },
        },
        {
          col: 8,
          key: 'version',
          label: t('Goods Version'),
          type: FieldType.Select,
          extraProps: {
            disabled: !goodsCodeValue,
            options: goodsList
              ?.filter(goods => goods.goodsCode === goodsCodeValue)
              ?.map(goods => ({
                value: goods.goodsVersion,
                label: goods.goodsVersion,
              })),
          },
        },
        {
          col: 8,
          key: 'goodsName',
          label: t('Goods Name'),
          type: FieldType.Input,
        },
        {
          col: 8,
          key: 'goodsCategory',
          label: t('Goods Category'),
          type: FieldType.Select,
          extraProps: {
            options: goodsCategoryEnums?.map(goodsCategory => ({
              value: goodsCategory.id,
              label: goodsCategory.categoryName,
            })),
          },
        },
        {
          col: 8,
          key: 'salesPlatform',
          label: t('Sales Channel'),
          type: FieldType.Select,
          extraProps: {
            options: salesChannelEnums,
          },
        },
        {
          col: 8,
          key: 'packageCode',
          label: t('Package'),
          type: FieldType.Select,
          extraProps: {
            options: packageEnums,
          },
        },
      ]}
      onSearch={search}
    />
  );
};

export default QueryForm;
