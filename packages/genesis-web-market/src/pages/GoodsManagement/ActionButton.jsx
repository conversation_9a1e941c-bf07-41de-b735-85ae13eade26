import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import {
  CommonIconAction,
  DeleteAction,
  EditAction,
  Icon,
  TableActionsContainer,
  ViewAction,
} from '@zhongan/nagrand-ui';

import { selectEnvConfig, selectUserState } from '@market/redux/selector';

import './action-button.scss';

export const ActionButton = ({
  record,
  hasEditAuth,
  isSuperUser,
  deleteGoods,
  setOpen,
  setCopiedRecord,
  onCopyGoods,
  validation,
}) => {
  const [t] = useTranslation(['market', 'common']);
  const envConfig = useSelector(selectEnvConfig);
  const userInfo = useSelector(selectUserState);
  const navigate = useNavigate();
  // 获得查看操作的dom
  const getViewDom = record => {
    return (
      <ViewAction
        onClick={() => {
          navigate(`/market/goods/basic?goodsId=${record.id}&layoutId=${record.layoutId}`, {
            state: {
              mode: 'view',
            },
          });
        }}
      />
    );
  };

  const clickGoodsDetail = async record => {
    navigate(`/market/goods/basic?goodsId=${record.id}&layoutId=${record.layoutId}`, {
      state: {
        mode: 'edit',
      },
    });
  };

  const draftFlag = record.goodStatus === 1;
  const isEffective = record.goodStatus === 2;
  const currentUserIsCreator = `${userInfo.userId}` === record.creator;
  const canEdit = (currentUserIsCreator && hasEditAuth) || isSuperUser;
  const noCreatorJudgeEditAuth = hasEditAuth || isSuperUser;

  return envConfig && envConfig.env !== 'prd' ? (
    <div className="action-button-container">
      <TableActionsContainer>
        {getViewDom(record)}

        <EditAction onClick={() => clickGoodsDetail(record)} disabled={!canEdit} />

        {noCreatorJudgeEditAuth && (
          <CommonIconAction
            icon={<Icon type="copy" />}
            tooltipTitle={t('Copy to New Goods')}
            onClick={() => {
              setOpen(true);
              setCopiedRecord({ ...record, key: 2 });
            }}
          />
        )}

        {noCreatorJudgeEditAuth && !draftFlag && (
          <CommonIconAction
            icon={<Icon type="copy" />}
            tooltipTitle={t('Copy to New Version')}
            onClick={() => {
              onCopyGoods({ ...record, key: 3 });
            }}
          />
        )}

        {noCreatorJudgeEditAuth && draftFlag && canEdit && (
          <DeleteAction
            tooltipTitle={t('Delete')}
            doubleConfirmType="modal"
            onClick={() => {
              deleteGoods(record);
            }}
            deleteConfirmContent={t('Are you sure to delete this record?')}
          />
        )}

        {noCreatorJudgeEditAuth && isEffective && (
          <CommonIconAction
            icon={<Icon type="manual-operation-vertification" />}
            tooltipTitle={t('Launch')}
            onClick={() => validation(record)}
          />
        )}
      </TableActionsContainer>
    </div>
  ) : (
    getViewDom(draftFlag, record)
  );
};
