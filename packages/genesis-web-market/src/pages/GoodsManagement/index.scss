@import '../PackageManagement/table-filter-select.scss';

.nagrand-table-container {
  .status-icon {
    display: inline-block;
    vertical-align: middle;
    margin-right: 6px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
  .draft {
    @extend .status-icon;
    background-color: $disabled-color;
  }

  .effective {
    @extend .status-icon;
    background-color: var(--success-color);
  }

  .invalid {
    @extend .status-icon;
    background-color: var(--error-color);
  }

  .ready {
    @extend .status-icon;
    background-color: var(--primary-color);
  }
}

.management-container {
  // 纵向flex，让表格容器在没有数据的情况下，也能填满屏幕
  min-height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--layout-body-background);
  .table-container {
    flex-grow: 1;
    margin: 16px 16px;
    padding: 24px 32px;
    background-color: var(--white);
    .status-icon {
      display: inline-block;
      vertical-align: middle;
      margin-right: 6px;
      width: 6px;
      height: 6px;
      border-radius: 50%;
    }

    .draft {
      @extend .status-icon;
      background-color: $disabled-color;
    }

    .effective {
      @extend .status-icon;
      background-color: var(--success-color);
    }

    .invalid {
      @extend .status-icon;
      background-color: var(--error-color);
    }

    .ready {
      @extend .status-icon;
      background-color: var(--primary-color);
    }

    .cp {
      cursor: pointer;
    }

    .mr20 {
      margin-right: 20px;
    }
    .mr16 {
      margin-right: 16px;
    }

    .more {
      &:after {
        content: '...';
      }
    }

    .disabled {
      color: var(--text-disabled-color);
      cursor: not-allowed;
    }
  }
}
