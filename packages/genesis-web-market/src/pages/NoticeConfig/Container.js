import { useEffect, useRef, useState } from 'react';
import { withTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Button, Layout, Skeleton, message } from 'antd';

import { lessVars } from '@zhongan/nagrand-ui';

import { NotificationService, PartnerType } from 'genesis-web-service';

import Section from '@market/components/Section/section';
import { selectPermissionCheckMap } from '@market/redux/selector';
import { NewChannelService } from '@market/services/channel/channel.service.new';
import { NewMarketService } from '@market/services/market/market.service.new';

import EditableTable from '../../components/EditableTable';
import { FMarketHeader } from '../../components/F-Market-Header';
import FMarketMenu from '../../components/F-Market-Menu';
import { generalConfirm, urlQuery } from '../../util';
import './index.scss';

const { Sider, Content } = Layout;

const NoticeConfigContainer = props => {
  const { t, navigate, location } = props;
  const permissionMap = useSelector(selectPermissionCheckMap);
  const envConfig = useSelector(state => state.envConfig);
  const userInfo = useSelector(state => state.userInfo);
  const hasEditAuth = !!permissionMap['market.edit'];
  const isSuperUser = !!permissionMap['market.edit-all'];

  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [goodsSalesChannel, setGoodsSalesChannel] = useState([]);
  const [isShowEditButton, setIsShowEditButton] = useState(false);
  const [scenarioList, setScenarioList] = useState([]);
  const [scenarioDisabled, setScenarioDisabled] = useState(true);
  const [notificationCodeDisabled, setNotificationCodeDisabled] = useState(true);
  const [notificationEditing, setNotificationEditing] = useState(false);
  const [triggerList, setTriggerList] = useState([]);
  const [tableStateList, setTableStateList] = useState([]);
  const [notificationCodeListByTrigger, setNotificationCodeListByTrigger] = useState([]);
  const [notificationCodeList, setNotificationCodeList] = useState(null);
  const [totalChannelList, setTotalChannelList] = useState([]);
  const [skeletonVisible, setSkeletonVisible] = useState(true);
  const [mode, setMode] = useState('edit');
  const [queryModel, setQueryModel] = useState(null);
  const goodsId = urlQuery('goodsId');
  const layoutId = urlQuery('layoutId');
  const NoticeTableRef = useRef(null);

  useEffect(() => {
    if (!goodsId || !layoutId) {
      backToSearch();
      return;
    }

    NewChannelService.ChannelService.pageQueryChannel({
      queryParams: {
        pageIndex: 0,
        pageSize: 10000,
        type: 'SALE_CHANNEL',
      },
    }).then(res => {
      if (res.data) {
        setTotalChannelList(
          res.data.map(channel => ({
            itemExtend1: channel.code,
            itemName: channel.name,
          }))
        );
      }
    });

    NotificationService.getTrigger().then(response => {
      const triggers = response?.triggers || [];

      triggers.forEach(trigger => {
        trigger.itemExtend1 = trigger.marketCode;
        trigger.itemName = trigger.name;

        const { notifications, scenarios } = trigger;

        if (Array.isArray(notifications)) {
          notifications.forEach(notification => {
            notification.itemExtend1 = notification.code;
            notification.itemName = notification.code;
          });
        }

        if (Array.isArray(scenarios)) {
          scenarios.forEach(scenario => {
            scenario.itemExtend1 = scenario.marketCode;
            scenario.itemName = scenario.name;

            if (Array.isArray(scenario.notifications)) {
              scenario.notifications.forEach(notification => {
                notification.itemExtend1 = notification.code;
                notification.itemName = notification.code;
              });
            }
          });
        }
      });

      setTriggerList(triggers);
    });

    const { state } = location;
    const mode = state && state.mode;
    const queryModel = state && state.queryModel;
    setMode(mode || 'edit');
    setQueryModel(queryModel);

    const fetchData = async () => {
      const { salesAttributes } = await NewMarketService.MarketStructureMgmtService.queryGoodsRelating(goodsId);
      const res = await NewMarketService.GoodMgmtService.find({
        goodsId: +goodsId,
        queryModel,
      });
      let goodsSalesChannel = [];
      if (Array.isArray(salesAttributes?.salesPartnerList)) {
        goodsSalesChannel =
          salesAttributes.salesPartnerList
            .filter(salesPartner => salesPartner.partnerType === PartnerType.salesChannel)
            .map(salesPartner => salesPartner.partnerCode) || [];
      }
      setGoodsSalesChannel(goodsSalesChannel);
      setIsShowEditButton(
        (res.value.basicProp && res.value.basicProp.creator === `${userInfo.userId}` && hasEditAuth) || isSuperUser
      );
      getNotificationList();
    };

    fetchData();
  }, [goodsId, layoutId, location, hasEditAuth, isSuperUser, userInfo]);

  const getNotificationList = () => {
    NewMarketService.GoodsNotificationConfigMgmtService.queryNotification({
      goodsId: goodsId,
    })
      .then(res => {
        if (res.success) {
          const data = res.value || [];
          data.forEach((item, index) => {
            item.key = item.id;
            if (item.salesChannel) {
              item.salesChannel = item.salesChannel.split(',');
            }
          });
          setData(data);
          setLoading(false);
        }
      })
      .finally(() => {
        setSkeletonVisible(false);
      });
  };

  const saveAndNext = () => {
    if (tableStateList.includes('Editing')) {
      message.warning(t('Please confirm that all configurations have been saved before submission'));
      return;
    }
    if (notificationEditing) {
      message.warning(t('Please confirm that all configurations have been saved before submission'));
      return;
    }
    NewMarketService.GoodsPackageValidateService.validateGoods(goodsId).then(goodsValidation => {
      if (goodsValidation.errors?.length) {
        generalConfirm({
          title: t('Validation Response'),
          content: (
            <ul>
              {goodsValidation.errors.map(message => (
                <li>{message}</li>
              ))}
            </ul>
          ),
        });
        return;
      }

      NewMarketService.PackageGoodsStepInfoMgmtService.doneStep({
        // 记录步骤
        refId: goodsId, // 如果是产品组合就传对应的packageId
        type: 2, // 商品 2， 产品组合 1
        stepNo: 3, // 步骤，参见menu里面的数字
        isNew: false, // 传一个是否是add状态，只有在每一个的第一步才会有这个参数
        isSubmit: true, // 是否是submit操作，当同时存在next和submit的时候，都要调这个方法，用这个区分是否是submit
      }).then(res1 => {
        if (res1.success) {
          message.success(t('Publish successfully'));
          navigate('/market/goods/search');
        }
      });
    });
  };

  const backToSearch = () => {
    navigate('/market/goods/search');
  };

  // 处理销售渠道的下拉数据
  const getSalesChannelDropData = () => {
    if (totalChannelList && Array.isArray(totalChannelList) && totalChannelList.length > 0) {
      const returnArr = [{ itemExtend1: '0', itemName: t('ALL') }];
      let moreArr = [];
      if (goodsSalesChannel.length === 1 && goodsSalesChannel[0] === '0') {
        moreArr = returnArr.concat(totalChannelList);
      } else {
        moreArr = totalChannelList.filter(i => goodsSalesChannel.indexOf(i.itemExtend1) > -1);
      }
      return moreArr;
    }
    return [];
  };

  const onEditingRecord = record => {
    setNotificationEditing(true);
    if (record) {
      // 数据回显以及设置下拉选项
      const result = triggerList.find(i => {
        return i.itemExtend1 === record.notificationType;
      });
      if (result) {
        let notifications = result.notifications || [];
        const scenarioList = result.scenarios || [];
        if (record.notificationScenario) {
          notifications = scenarioList.find(senario => {
            return senario.itemExtend1 === record.notificationScenario;
          })?.notifications;
        }

        setScenarioList(scenarioList);
        setNotificationCodeListByTrigger(record.notificationScenario ? [] : notifications);
        setNotificationCodeList(record.notificationScenario ? notifications : null);
        setScenarioDisabled(false);
        setNotificationCodeDisabled(false);
      }
    } else {
      setScenarioDisabled(true);
      setNotificationCodeDisabled(true);
    }
  };

  const changeNotification = trigger => {
    setScenarioDisabled(!trigger);
    setNotificationCodeDisabled(!trigger);
    if (trigger) {
      const result = triggerList.find(i => {
        return i.itemExtend1 === trigger;
      });

      setScenarioList(result ? result.scenarios : []);
      setNotificationCodeListByTrigger(result ? result.notifications : []);
      if (NoticeTableRef.current && NoticeTableRef.current.props.form) {
        NoticeTableRef.current.props.form.setFieldsValue({
          notificationScenario: undefined,
          notificationCode: undefined,
        });
      }
    } else {
      setScenarioList([]);
      setNotificationCodeListByTrigger([]);
      if (NoticeTableRef.current && NoticeTableRef.current.props.form) {
        NoticeTableRef.current.props.form.setFieldsValue({
          notificationScenario: undefined,
          notificationCode: undefined,
        });
      }
    }
  };

  const changeNotificationScenario = (val, noNeedRefresh) => {
    if (val) {
      const result = scenarioList.find(i => {
        return i.itemExtend1 === val;
      });
      setNotificationCodeList(result ? result.notifications : []);
      if (!noNeedRefresh && NoticeTableRef.current && NoticeTableRef.current.props.form) {
        NoticeTableRef.current.props.form.setFieldsValue({
          notificationCode: undefined,
        });
      }
    } else {
      props.form.setFieldsValue({ notificationCode: undefined });
      setNotificationCodeList(null);
      if (!noNeedRefresh && NoticeTableRef.current && NoticeTableRef.current.props.form) {
        NoticeTableRef.current.props.form.setFieldsValue({
          notificationCode: undefined,
        });
      }
    }
  };

  const getColumns = () => {
    return [
      {
        title: (
          <span>
            <span style={{ color: lessVars['@error-color'] }}>*</span> {t('Sales Channel')}
          </span>
        ),
        dataIndex: 'salesChannel',
        inputType: 'select',
        controllerprops: {
          mode: 'multiple',
          showArrow: true,
          placeholder: t('Please select'),
          getPopupContainer: () => document.getElementsByClassName('notice-config-container')[0] || document.body,
        },
        normalize: value => {
          let newVal = [];
          if (value && value.length > 0) {
            if (value[value.length - 1] === '0') {
              newVal = ['0'];
            } else if (value[0] === '0' && value.length > 1) {
              newVal = value.slice(1);
            } else {
              newVal = value;
            }
          }
          return newVal;
        },
        editable: true,
        selectOptions: getSalesChannelDropData(),
        render: (text, record) => {
          if (!text || text.length === 0) {
            return <span>--</span>;
          }
          const showText = [];
          text.map(item => {
            const destObject = getSalesChannelDropData().filter(i => i.itemExtend1 === item);
            if (destObject && destObject.length > 0) {
              showText.push(destObject[0].itemName);
            }
          });
          return <span>{showText.join(',')}</span>;
        },
      },
      {
        title: (
          <span>
            <span style={{ color: lessVars['@error-color'] }}>*</span> {t('Notification Trigger')}
          </span>
        ),
        dataIndex: 'notificationType',
        inputType: 'select',
        controllerprops: {
          showArrow: true,
          allowClear: false,
          placeholder: t('Please select'),
          getPopupContainer: () => document.getElementsByClassName('notice-config-container')[0] || document.body,
          onChange: v => {
            changeNotification(v);
          },
        },
        editable: true,
        selectOptions: triggerList,
        render: (text, record) => {
          if (!text || text.length === 0) {
            text = '--';
          }
          const destObject = triggerList.filter(i => i.itemExtend1 === text.toString());
          if (destObject && destObject.length > 0) {
            text = destObject[0].itemName;
          }
          return <span>{text}</span>;
        },
      },
      {
        title: <span>{t('Notification Scenario')}</span>,
        dataIndex: 'notificationScenario',
        inputType: 'select',
        controllerprops: {
          showArrow: true,
          placeholder: t('Please select'),
          getPopupContainer: () => document.getElementsByClassName('notice-config-container')[0] || document.body,
          disabled: scenarioDisabled,
          onChange: v => {
            changeNotificationScenario(v);
          },
        },
        editable: true,
        selectOptions: scenarioList,
        validate: [
          {
            trigger: 'onChange',
            rules: [{ required: false, message: '' }],
          },
        ],
        render: (text, record) => {
          if (!text || text.length === 0) {
            text = '--';
          }
          if (triggerList && triggerList.length > 0) {
            const newScenarioList = triggerList.find(i => i.itemExtend1 === `${record.notificationType}`);
            if (newScenarioList && newScenarioList.scenarios && newScenarioList.scenarios.length > 0) {
              const destObject = newScenarioList.scenarios.find(i => i.itemExtend1 === `${text}`);
              if (destObject) {
                text = destObject.itemName;
              }
            }
          }
          return <span>{text}</span>;
        },
      },
      {
        title: (
          <span>
            <span style={{ color: lessVars['@error-color'] }}>*</span> {t('Notification Code')}
          </span>
        ),
        dataIndex: 'notificationCode',
        inputType: 'select',
        controllerprops: {
          showArrow: true,
          allowClear: false,
          placeholder: t('Please select'),
          getPopupContainer: () => document.getElementsByClassName('notice-config-container')[0] || document.body,
          disabled: notificationCodeDisabled,
        },
        editable: true,
        selectOptions: notificationCodeList || notificationCodeListByTrigger,
        render: (text, record) => {
          if (!text || text.length === 0) {
            text = '--';
          }
          return <span>{text}</span>;
        },
      },
    ];
  };

  const onSaveNotice = (value, key) => {
    return new Promise(resolve => {
      let req = null;
      const salesChannel = value.salesChannel;

      const params = {
        goodsId: goodsId,
        salesChannel: salesChannel && salesChannel.join(','),
        notificationCode: value.notificationCode,
        notificationType: value.notificationType,
        notificationScenario: value.notificationScenario,
      };
      if (key === 'add') {
        req = NewMarketService.GoodsNotificationConfigMgmtService.saveNotification.bind(
          NewMarketService.GoodsNotificationConfigMgmtService
        );
      } else {
        req = NewMarketService.GoodsNotificationConfigMgmtService.modifyNotification.bind(
          NewMarketService.GoodsNotificationConfigMgmtService
        );
        params.id = key;
      }
      req(params).then(res => {
        if (res.success) {
          message.success(t('Save successfully'));
          resolve();
          getNotificationList();
          setNotificationEditing(false);
          setNotificationCodeList(null);
          if (NoticeTableRef.current && NoticeTableRef.current.props.form) {
            NoticeTableRef.current.props.form.setFieldsValue({
              notificationScenario: undefined,
              notificationCode: undefined,
            });
          }
        }
      });
    });
  };

  const onDeleteNotice = key => {
    NewMarketService.GoodsNotificationConfigMgmtService.deleteNotification({
      id: key,
    }).then(res => {
      if (res.success) {
        message.success(t('Deleted successfully'));
        getNotificationList();
        setNotificationEditing(false);
      }
    });
  };

  const onEdit = () => {
    if (mode !== 'view') {
      return;
    }
    setMode('edit');
  };

  const renderEditSaveBtn = () => {
    if (!isShowEditButton || envConfig.env === 'prd') {
      return null;
    }

    if (mode === 'view') {
      return (
        <Button size="large" onClick={() => onEdit()}>
          {t('Edit')}
        </Button>
      );
    }

    // 假的Save不需要保存按钮
    return null;
  };

  const readOnly = mode === 'view' || !isShowEditButton || envConfig.env === 'prd';

  return (
    <Layout className="market-layout notice-config-container">
      <FMarketHeader backPath="/market/goods/search" subMenu="Marketing_Goods" />
      <div className="flex" style={{ height: 'calc(100% - 50px)' }}>
        <Sider width={208} className="market-sider">
          <FMarketMenu
            page="GOODS"
            goodsId={goodsId}
            defaultSelectedKeys={['4']}
            category={2}
            type={mode}
            navigate={navigate}
          />
        </Sider>
        <Content className="market-content">
          <Skeleton active loading={skeletonVisible}>
            <div className="right-content-wrapper">
              <Section title={t('Notification Configuration')}>
                <EditableTable
                  loading={loading}
                  wrapperClassName="notice-edit-table"
                  disabled={readOnly}
                  columns={getColumns()}
                  data={data}
                  onSubmit={onSaveNotice}
                  onDelete={onDeleteNotice}
                  onCancel={() => setNotificationEditing(false)}
                  myRef={ref => (NoticeTableRef.current = ref)}
                  onEditingRecord={onEditingRecord}
                  onStateChange={tableState => {
                    const newTableStateList = [...tableStateList];
                    newTableStateList[2] = tableState;
                    setTableStateList(newTableStateList);
                  }}
                />
              </Section>
            </div>
          </Skeleton>
        </Content>
        <div className="bottom-action-bar">
          {readOnly ? null : (
            <Button size="large" onClick={saveAndNext} type="primary">
              {t('Publish')}
            </Button>
          )}
          {renderEditSaveBtn()}
        </div>
      </div>
    </Layout>
  );
};

export default withTranslation(['market', 'common'])(NoticeConfigContainer);
