import { cloneDeep, mergeWith } from 'lodash-es';

import type { PackageInterest, PackageLiability, PackageProduct } from 'genesis-web-service/lib/market';
import { PolicyTypeOptions } from 'genesis-web-service/lib/product/enums';

export type ConfigedProductLiabilityInterestMap = Record<
  string,
  {
    selected: boolean;
    isOptional: number;
    liabilitys: Record<
      string,
      {
        selected: boolean;
        isOptional: number;
        interests: Record<string, boolean>;
      }
    >;
  }
>;

const generateConfigedInterestMap = (interestList: PackageInterest[]) => {
  const interestMap: Record<string, boolean> = {};
  interestList.forEach(interest => {
    if (interest.isSelected) {
      interestMap[interest.interestId] = true;
    }
  });
  return interestMap;
};

const generateConfigedLiabilityMap = (liabilityList: PackageLiability[]) => {
  const liabilityMap: Record<
    string,
    {
      selected: boolean;
      isOptional: number;
      interests: Record<string, boolean>;
    }
  > = {};

  liabilityList.forEach(liability => {
    const interestMap = generateConfigedInterestMap(liability.packageInterestList);
    if (liability.isSelect) {
      liabilityMap[liability.liabilityId] = {
        selected: true,
        isOptional: liability.isOptional,
        interests: interestMap,
      };
    }
  });

  return liabilityMap;
};

export const generateConfigedProductMap = (productList: PackageProduct[]) => {
  const productMap: ConfigedProductLiabilityInterestMap = {};

  productList.forEach(product => {
    const liabilityMap = generateConfigedLiabilityMap(product.packageLiabilityList);
    productMap[product.productId] = {
      selected: true,
      isOptional: product.isOptional,
      liabilitys: liabilityMap,
    };
  });

  return productMap;
};

export const getUnconfigedProduct = (
  configedProducLiabilitytList: PackageProduct[],
  totalProductLiabilityList: PackageProduct[]
) => {
  const unconfigedProductList: PackageProduct[] = [];
  const configedProductMap = generateConfigedProductMap(configedProducLiabilitytList);

  cloneDeep(totalProductLiabilityList).forEach(product => {
    const productMapDetail = configedProductMap[product.productId];

    if (!productMapDetail?.selected) {
      unconfigedProductList.push(product);
    }
  });

  return unconfigedProductList;
};

export const getConfigedProduct = (productList: PackageProduct[]) =>
  productList.map(product => ({
    ...product,
  }));

/* 将已配置的产品列表和这次新加的产品列表合并  */
export const mergeProductList = (
  configedProducLiabilitytList: PackageProduct[],
  addToProductLiabilityList: PackageProduct[]
) => {
  // configedProducLiabilitytList 拆分成重复的和非重复的
  const repeatedList1: PackageProduct[] = [];
  const unrepeatedList1: PackageProduct[] = [];
  // addToProductLiabilityList 拆分成重复的和非重复的
  const repeatedList2: PackageProduct[] = [];
  let unrepeatedList2: PackageProduct[] = [];

  configedProducLiabilitytList.forEach(configedProduct => {
    const tempRepeatedProduct = addToProductLiabilityList.find(
      addToProduct => addToProduct.productId === configedProduct.productId
    );
    if (tempRepeatedProduct) {
      repeatedList1.push(configedProduct);
      repeatedList2.push(tempRepeatedProduct);
    } else {
      unrepeatedList1.push(configedProduct);
    }
  });

  unrepeatedList2 = addToProductLiabilityList.filter(
    addToProduct => !repeatedList2.find(repeatedProduct => repeatedProduct.productId === addToProduct.productId)
  );

  const mergedProductList = [...unrepeatedList1, ...unrepeatedList2];

  repeatedList1.forEach((repeatedProduct, index) => {
    mergedProductList.push(
      mergeWith(
        repeatedProduct,
        repeatedList2[index],
        (objValue: PackageLiability[], srcValue: PackageLiability[], key) => {
          if (key === 'packageLiabilityList' && Array.isArray(objValue) && Array.isArray(srcValue)) {
            if (objValue.length === 0 || srcValue.length === 0) {
              return objValue.concat(srcValue);
            }
            const repeatedLiabilityList1: PackageLiability[] = [];
            const repeatedLiabilityList2: PackageLiability[] = [];
            const unrepeatedLiabilityList1: PackageLiability[] = [];
            let unrepeatedLiabilityList2: PackageLiability[] = [];

            objValue.forEach(configedLiability => {
              const tempRepeatedProduct = srcValue.find(
                addToLiability => addToLiability.liabilityId === configedLiability.liabilityId
              );
              if (tempRepeatedProduct) {
                repeatedLiabilityList1.push(configedLiability);
                repeatedLiabilityList2.push(tempRepeatedProduct);
              } else {
                unrepeatedLiabilityList1.push(configedLiability);
              }
            });

            unrepeatedLiabilityList2 = srcValue.filter(
              addToLiability =>
                !repeatedLiabilityList2.find(
                  repeatedLiability => repeatedLiability.liabilityId === addToLiability.liabilityId
                )
            );

            const resultPackageLiabilityList = [...unrepeatedLiabilityList1, ...unrepeatedLiabilityList2];
            repeatedLiabilityList1.forEach((repeatedLiability, liabilityIndex) => {
              resultPackageLiabilityList.push(
                mergeWith(repeatedLiability, repeatedLiabilityList2[liabilityIndex], (objValue2, srcValue2, key2) => {
                  if (key2 === 'packageInterestList' && Array.isArray(objValue2) && Array.isArray(srcValue2)) {
                    return objValue2.concat(srcValue2);
                  }
                })
              );
            });

            return resultPackageLiabilityList;
          }
        }
      )
    );
  });

  return mergedProductList;
};

// 能看到Enable Multi-Main Benefits 的Policy Type
export const SupportMultiMainBenefitsPolicyTypes = [
  PolicyTypeOptions.NormalPolicy,
  PolicyTypeOptions.MasterPolicy,
  PolicyTypeOptions.GroupPolicy,
];
