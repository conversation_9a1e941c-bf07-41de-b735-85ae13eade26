import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, message } from 'antd';

import { AddNewButton } from '@zhongan/nagrand-ui';

import { ProductTypeEnum } from 'genesis-web-service';
import { PackageProductInfoResponseDTO } from 'genesis-web-service/service-types/market-types/package';

import { DetailPageMode } from '@market/common/interface';
import { useDrawerState } from '@market/hook/useDrawerState';
import { NewMarketService } from '@market/services/market/market.service.new';

import ProductAndLiabilityTableNew from '../ProductAndLiabilityTable/ProductAndLiabilityTableNew';
import MainBenefitDrawer from './MainBenefitDrawer';
import MutuallyExclusiveSection from './MutuallyExclusiveSection';

interface Props {
  packageId: number;
  readOnly: boolean;
  originProducts: PackageProductInfoResponseDTO[];
  loading: boolean;
  sortMode: boolean;
  riderSortMode: boolean;
  onSortChange: () => void;
  queryConfigProducts: () => void;
  saveSort: () => void;
  startSort: () => void;
  startRiderSort: () => void;
}

export const MultiMainBenefitsSection = ({
  packageId,
  readOnly,
  originProducts,
  loading,
  sortMode,
  riderSortMode,
  onSortChange,
  queryConfigProducts,
  saveSort,
  startSort,
  startRiderSort,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [drawerState, setDrawerState] = useDrawerState<PackageProductInfoResponseDTO>();
  const [drawerProductType, setDrawerProductType] = useState<ProductTypeEnum>(ProductTypeEnum.MAIN);

  const mainProducts = useMemo(
    () => originProducts.filter(product => +product.productTypeCode! === +ProductTypeEnum.MAIN),
    [originProducts]
  );
  const riderProducts = useMemo(
    () => originProducts.filter(product => +product.productTypeCode! === +ProductTypeEnum.RIDER),
    [originProducts]
  );

  return (
    <div>
      <div className="mb-2">{t('Main Benefit')}</div>
      <div className="mb-2 flex justify-between">
        <AddNewButton
          disabled={readOnly}
          onClick={() => {
            setDrawerProductType(ProductTypeEnum.MAIN);
            setDrawerState({
              visible: true,
              mode: DetailPageMode.add,
            });
          }}
        >
          {t('Add New')}
        </AddNewButton>
        {mainProducts.length > 1 &&
          (sortMode ? (
            <Button className="mr-4" onClick={saveSort} disabled={readOnly}>
              {t('Save Sort')}
            </Button>
          ) : (
            <Button className="mr-4" onClick={startSort} disabled={readOnly}>
              {t('Sort')}
            </Button>
          ))}
      </div>
      <ProductAndLiabilityTableNew
        multiMainProduct
        disabled={readOnly}
        dataSource={mainProducts}
        loading={loading}
        draggable={sortMode}
        totalInterestListMap={{}}
        onSortChange={onSortChange}
        onView={record => {
          setDrawerProductType(ProductTypeEnum.MAIN);
          setDrawerState({
            visible: true,
            record,
            mode: DetailPageMode.view,
          });
        }}
        onEdit={record => {
          setDrawerProductType(ProductTypeEnum.MAIN);
          setDrawerState({
            visible: true,
            record,
            mode: DetailPageMode.edit,
          });
        }}
        onDelete={record => {
          NewMarketService.PackageProductMgmtService.singleDeletePackageProduct({
            packageId: record.packageId,
            productId: record.productId,
          }).then(() => {
            message.success(t('Delete successfully'));
            queryConfigProducts();
          });
        }}
      />
      <MutuallyExclusiveSection disabled={readOnly} originProducts={originProducts} packageId={packageId} />
      <div className="mb-2">{t('Rider')}</div>
      <div className="mb-2 flex justify-between">
        <AddNewButton
          // 没有加过主险的时候，不能添加附加险
          disabled={readOnly || originProducts.length === 0}
          onClick={() => {
            setDrawerProductType(ProductTypeEnum.RIDER);
            setDrawerState({
              visible: true,
              mode: DetailPageMode.add,
            });
          }}
        >
          {t('Add New')}
        </AddNewButton>
        {riderProducts.length > 1 &&
          (riderSortMode ? (
            <Button className="mr-4" onClick={saveSort} disabled={readOnly}>
              {t('Save Sort')}
            </Button>
          ) : (
            <Button className="mr-4" onClick={startRiderSort} disabled={readOnly}>
              {t('Sort')}
            </Button>
          ))}
      </div>
      <ProductAndLiabilityTableNew
        multiMainProduct
        disabled={readOnly}
        dataSource={riderProducts}
        loading={loading}
        draggable={riderSortMode}
        totalInterestListMap={{}}
        onSortChange={onSortChange}
        onView={record => {
          setDrawerProductType(ProductTypeEnum.RIDER);
          setDrawerState({
            visible: true,
            record,
            mode: DetailPageMode.view,
          });
        }}
        onEdit={record => {
          setDrawerProductType(ProductTypeEnum.RIDER);
          setDrawerState({
            visible: true,
            record,
            mode: DetailPageMode.edit,
          });
        }}
        onDelete={record => {
          NewMarketService.PackageProductMgmtService.singleDeletePackageProduct({
            packageId: record.packageId,
            productId: record.productId,
          }).then(() => {
            message.success(t('Delete successfully'));
            queryConfigProducts();
          });
        }}
      />
      <MainBenefitDrawer
        {...drawerState}
        productType={drawerProductType}
        packageId={packageId}
        configedProducts={originProducts}
        onClose={() => {
          setDrawerState({
            visible: false,
            record: undefined,
          });
          queryConfigProducts();
        }}
      />
    </div>
  );
};

export default MultiMainBenefitsSection;
