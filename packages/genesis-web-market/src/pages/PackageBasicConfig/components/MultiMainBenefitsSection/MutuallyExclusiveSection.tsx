import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { message } from 'antd';

import { EditableTable, FieldType } from '@zhongan/nagrand-ui';

import { ProductTypeEnum } from 'genesis-web-service';
import { IsOptional } from 'genesis-web-service/lib/market/enums';
import {
  PackageProductExclusionResponseDTO,
  PackageProductInfoResponseDTO,
} from 'genesis-web-service/service-types/market-types/package';

import { NewMarketService } from '@market/services/market/market.service.new';

interface Props {
  originProducts: PackageProductInfoResponseDTO[];
  packageId: number;
  disabled: boolean;
}

export const MutuallyExclusiveSection = ({ originProducts, packageId, disabled }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [dataSource, setDataSource] = useState<PackageProductExclusionResponseDTO[]>([]);

  const optionalMainProducts = useMemo(
    () =>
      originProducts.filter(
        product => product.isOptional === IsOptional.Optional && +product.productTypeCode! === +ProductTypeEnum.MAIN
      ),
    [originProducts]
  );

  const queryExclusionList = useCallback(() => {
    if (!packageId) return;
    NewMarketService.PackageProductMgmtService.queryPackageProductExclusionList(packageId).then(list => {
      setDataSource(list);
    });
  }, [packageId]);

  useEffect(() => {
    queryExclusionList();
  }, []);

  return (
    <React.Fragment>
      <div className="mb-2 mt-4">{t('Mutually Exclusive Main Benefits')}</div>
      <EditableTable
        addBtnProps={{
          disabled: optionalMainProducts.length < 2,
        }}
        readonly={disabled}
        className="mb-6"
        columns={[
          {
            title: t('Product Name'),
            dataIndex: 'productId',
            editable: true,
            formItemProps: {
              rules: [{ required: true, message: t('Please select') }],
            },
            fieldProps: (record: any) => ({
              type: FieldType.Select,
              extraProps: {
                options: optionalMainProducts?.map(product => ({
                  label: product.productName,
                  value: product.productId,
                })),
              },
            }),
          },
          {
            title: t('Mutually Exclusive With'),
            dataIndex: 'excludedProductId',
            editable: true,
            formItemProps: {
              rules: [{ required: true, message: t('Please select') }],
            },
            fieldProps: (record: any) => ({
              type: FieldType.Select,
              extraProps: {
                options: optionalMainProducts?.map(product => ({
                  label: product.productName,
                  value: product.productId,
                })),
              },
            }),
          },
        ]}
        editBtnProps={{
          visible: false,
        }}
        dataSource={dataSource}
        setDataSource={setDataSource}
        pagination={false}
        handleConfirm={record =>
          NewMarketService.PackageProductMgmtService.savePackageProductExclusion({
            packageId,
            ...record,
          }).then(() => {
            message.success(t('Save successfully'));
            queryExclusionList();
          })
        }
        deleteBtnProps={{
          handleDelete: (deleteIndex, record) =>
            NewMarketService.PackageProductMgmtService.deletePackageProductExclusion({
              packageId,
              ...record,
            }).then(() => {
              message.success(t('Delete successfully'));
              queryExclusionList();
            }),
        }}
      />
    </React.Fragment>
  );
};

export default MutuallyExclusiveSection;
