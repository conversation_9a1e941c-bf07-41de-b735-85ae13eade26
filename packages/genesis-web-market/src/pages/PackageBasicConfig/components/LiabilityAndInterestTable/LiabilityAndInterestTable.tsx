import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Table } from 'antd';
import { ColumnProps } from 'antd/es/table';

import { IsOptional, PackageInterest, PackageLiability } from 'genesis-web-service';

import styles from '../PlanConfigurationDrawer/PlanConfigurationDrawer.module.scss';

export const LiabilityAndInterestTable = (props: {
  liabilityList: PackageLiability[];
  optionalOrRequired: (
    record: PackageInterest | PackageLiability,
    onChange?: (isOptional: IsOptional) => void
  ) => React.ReactElement;
  disabled?: boolean;
}) => {
  const { liabilityList, optionalOrRequired, disabled = false } = props;
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [t] = useTranslation(['market', 'common']);

  useEffect(() => {
    setSelectedRowKeys(
      liabilityList
        ?.filter(packageLiability => packageLiability.isSelect)
        ?.map(packageLiability => packageLiability.liabilityId)
    );
  }, [liabilityList]);

  const OptionalOrRequiredNotSelectedCmp = (
    <span className="bg-[#ebf0f0] rounded text-@text-color w-[128px] h-[22px] inline-block px-[11px] cursor-not-allowed">
      {t('- -')}
    </span>
  );

  const liabilityColumns = [
    {
      title: 'Liability Name',
      dataIndex: 'liabilityName',
      key: 'liabilityName',
      width: 600,
    },
    {
      title: 'Mandatory',
      dataIndex: 'isOptional',
      key: 'isOptional',
      render: (text: IsOptional, record: PackageLiability) =>
        record.isSelect ? optionalOrRequired(record) : OptionalOrRequiredNotSelectedCmp,
    },
  ];

  const expandInterestRowRender = (packageLiability: PackageLiability, liabilityIndex: number) => {
    if (packageLiability.packageInterestList?.length === 0) {
      return null;
    }
    const interestColumn: ColumnProps<PackageInterest>[] = [
      {
        dataIndex: 'interestName',
        width: 552,
      },
      {
        dataIndex: 'isOptional',
        shouldCellUpdate: () => true,
        render: (_, record: PackageInterest) =>
          record.isSelected ? optionalOrRequired(record) : OptionalOrRequiredNotSelectedCmp,
      },
    ];

    return (
      <Table
        key={liabilityIndex}
        columns={interestColumn}
        pagination={false}
        rowKey="interestId"
        dataSource={packageLiability.packageInterestList}
        showHeader={false}
        rowSelection={{
          columnWidth: 48,
          defaultSelectedRowKeys: packageLiability.packageInterestList
            .filter(interest => interest.isSelected)
            .map(interest => interest.interestId),
          onSelect: (record, selected) => {
            record.isSelected = selected;
          },
          getCheckboxProps: record => ({
            disabled: record.canEdit === false || disabled,
          }),
        }}
      />
    );
  };

  // 纯定制样式，不需要替换nagrand表格
  return (
    <Table
      rowKey="liabilityId"
      expandable={{
        expandedRowKeys: selectedRowKeys.filter(
          key => liabilityList.find(liability => liability.liabilityId === key)?.packageInterestList?.length
        ),
        expandedRowRender: expandInterestRowRender,
        expandIconColumnIndex: -1,
      }}
      dataSource={liabilityList}
      size="small"
      showHeader={false}
      columns={liabilityColumns}
      rowSelection={{
        columnWidth: 48,
        selectedRowKeys,
        onSelect: (record, selected) => {
          /* 勾选责任，触发父组件，对参数修改 */
          record.isSelect = selected;
          if (selected) {
            setSelectedRowKeys([...selectedRowKeys, record.liabilityId]);
          } else {
            setSelectedRowKeys(selectedRowKeys.filter(id => id !== record.liabilityId));
          }
        },
        getCheckboxProps: record => ({
          disabled: record.canEdit === false || disabled,
        }),
      }}
      pagination={false}
      className={styles['product-table']}
    />
  );
};
