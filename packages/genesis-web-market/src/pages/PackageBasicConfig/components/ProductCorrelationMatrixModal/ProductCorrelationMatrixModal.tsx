/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Modal, Popconfirm, message } from 'antd';
import Upload, { UploadProps } from 'antd/lib/upload';

import { useRequest } from 'ahooks';
import cls from 'classnames';
import { isEqual } from 'lodash-es';

import { CommonIconAction, Icon } from '@zhongan/nagrand-ui';

import { MatrixTableRequestSpec, ProductStructureStyle, SchemaParam } from 'genesis-web-service';
import { CalculatorService } from 'genesis-web-service/lib/calculator/calculator.service';
import { ProductCategoryDictValue } from 'genesis-web-service/lib/product';
import { security } from 'genesis-web-shared/lib';
import { ProductLineI18n } from 'genesis-web-shared/lib/i18n/product-line-i18n';

import { BizTopic } from '@market/common/enums';
import { PolicySchema, SchemaField } from '@market/common/schema.interface';
import GeneralSelect from '@market/components/GeneralSelect';
import UploadDragger from '@market/pages/DocDisplay/FileManagement/components/UploadDragger';
import { NewCalculatorService } from '@market/services/calculator/calculator.service.new';
import { NewMarketService } from '@market/services/market/market.service.new';
import { NewProductService } from '@market/services/product/product.service.new';
import { downloadFile } from '@market/util';
import {
  collectSchemaParams,
  convertSchemaParamsToJSONPath,
  praseParameterPath,
  transformSchema,
} from '@market/utils/schemaUtils';

import styles from './index.module.scss';

const confirm = Modal.confirm;

interface Props {
  onClose: () => void;
  packageCode: string;
  productCategoryCode: string;
  productSubCategoryId: number;
  packageId: number;
  visible: boolean;
  fileName: string | undefined;
  setFileName: (fileName: string | undefined) => void;
}

export const ProductCorrelationMatrixModal = ({
  onClose,
  productCategoryCode,
  productSubCategoryId,
  packageId,
  packageCode,
  visible,
  fileName,
  setFileName,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();
  const [backupFactorShowTextList, setBackupFactorShowTextList] = useState<string[]>([]);
  let productCodeInitValue = 'product_productCode';

  if (ProductLineI18n.getInstance().productLineLable.style === ProductStructureStyle.Coverage) {
    productCodeInitValue = 'coverage_coverageCode';
  }
  const [factorShowTextList, setFactorShowTextList] = useState<string[]>([productCodeInitValue]);

  const [isSaved, setIsSaved] = useState<boolean>(false);
  const [parameterMap, setParameterMap] = useState<Record<string, string>>({});
  const [parameterList, setParameterList] = useState<SchemaField[]>([]);

  const { loading, runAsync: saveMatrixTable } = useRequest(
    (param: MatrixTableRequestSpec) => CalculatorService.saveMatrixTable(param),
    {
      manual: true,
    }
  );
  useEffect(() => {
    if (visible && backupFactorShowTextList.length) {
      setFactorShowTextList(backupFactorShowTextList);
    }
  }, [visible]);

  const matrixTableCode = useMemo(() => `${packageCode}_ProductCorrelationMatrix`, [packageCode]);

  const queryPolicySchemaService = useCallback(
    () =>
      NewProductService.ProductSchemaMgmtService.schemaFieldsWithCalculationFlag({
        productSubCategoryId,
        applicableFormulaCategory: BizTopic.ProductCorrelationMatrix,
      }).then((res: PolicySchema) => {
        // 这个场景不需要人相关的因子
        delete res.policyHolder;

        if (productCategoryCode !== ProductCategoryDictValue.Auto) {
          delete res.object;
        }

        const transformedData = transformSchema(res);
        setParameterList(transformedData.parameterList.filter(item => item.staticField !== 'NO'));
        setParameterMap(transformedData.parameterMap);

        return true;
      }),
    [productCategoryCode, productSubCategoryId]
  );

  useEffect(() => {
    queryPolicySchemaService();
  }, [queryPolicySchemaService]);

  useEffect(() => {
    if (!packageCode) {
      return;
    }
    CalculatorService.queryMatrixTable(matrixTableCode).then(matrixTableResponse => {
      if (matrixTableResponse) {
        const defaultList = convertSchemaParamsToJSONPath(parameterMap, matrixTableResponse.schemaFormulaFactorList);
        setBackupFactorShowTextList(defaultList);
        setFactorShowTextList(defaultList);
        setIsSaved(true);
        setFileName(matrixTableResponse.fileName);
      } else {
        setFactorShowTextList([productCodeInitValue]);
        setBackupFactorShowTextList([]);
        setFileName(undefined);
      }
    });
  }, [matrixTableCode, packageCode]);

  const confirmBeforeSubmit = useCallback((): Promise<boolean> => {
    const changeFlag = !isEqual(backupFactorShowTextList, factorShowTextList);
    return new Promise(resolve => {
      if (changeFlag) {
        if (fileName) {
          confirm({
            title: t('Confirm'),
            content: t('Parameter have been changed. The uploaded matrixtable will be cleared. Please confirm.'),
            onCancel: () => {
              setFactorShowTextList(backupFactorShowTextList);
            },
            onOk: () => {
              resolve(true);
            },
          });
        } else {
          resolve(true);
        }
      } else {
        resolve(false);
      }
    });
  }, [backupFactorShowTextList, factorShowTextList, fileName, t]);

  const save = useCallback(
    (parameterHasChanged: boolean) => {
      // 因子没有改变不需要调接口，因为这个页面只能编辑因子
      if (!parameterHasChanged) {
        return Promise.resolve();
      }
      if (factorShowTextList.length === 0) {
        message.warning('Select Parameter');
        return Promise.reject();
      }
      const factorCodeList = factorShowTextList.map((parameterPath: string) => {
        if (parameterPath.startsWith('middle_') || parameterPath.startsWith('Middle_')) {
          return parameterPath.replace('middle_', '').replace('Middle_', '');
        }
        if (!parameterMap[parameterPath]) {
          return parameterPath;
        }
        const { code } = praseParameterPath(parameterMap[parameterPath]);
        return code;
      });
      const schemaParams: SchemaParam[] = collectSchemaParams(factorShowTextList, parameterMap, parameterList, {
        isPOS: false,
        isGeneral: false,
      });
      const data: MatrixTableRequestSpec = {
        matrixTableCode,
        matrixTableName: matrixTableCode,
        description: '',
        productCategoryCode,
        matrixTableType: BizTopic.ProductCorrelationMatrix,
        factorCodeList,
        schemaFactorsList: schemaParams,
        resultCodeList: [
          ProductLineI18n.getInstance().productLineLable.style === ProductStructureStyle.Coverage
            ? 'result_relatingCoverageCode'
            : 'result_relatingProductCode',
          'result_relationship',
          'result_relatingLiabilityID',
          'result_liabilityRelationship',
          'result_message',
        ],
      };

      return saveMatrixTable(data).then(res => {
        setFileName(undefined);
        setBackupFactorShowTextList(factorShowTextList);

        return res;
      });
    },
    [factorShowTextList, matrixTableCode, parameterList, parameterMap, productCategoryCode, saveMatrixTable]
  );

  const onSubmit = useCallback(async () => {
    const parameterHasChanged = await confirmBeforeSubmit();
    save(parameterHasChanged).then(res => {
      // 当上传完文件之后再挂约定，防止走到错误的流程中
      if (fileName) {
        NewMarketService.PackageAgreementConfigMgmtService.saveCorrelationMatrix({
          packageId,
          productCorrelationMatrixCode: matrixTableCode,
        }).then(() => {
          message.success('Submit Successfully');
          onClose();
        });
      } else {
        message.success('Submit Successfully');
        onClose();
      }
    });
  }, [confirmBeforeSubmit, save, fileName, packageId, matrixTableCode, onClose]);

  const downloadTemplate = useCallback(async () => {
    const parameterHasChanged = await confirmBeforeSubmit();
    save(parameterHasChanged).then(submitResponse => {
      setIsSaved(true);
      downloadFile(`/api/calculator/matrixTable/generateTemplate/${matrixTableCode}`, {}, 'get', (res: string) => {
        if (res === 'error') {
          message.error(t('System error'));
        }
        if (res === 'success') {
          message.success(t('Download Successfully'));
        }
      });
    });
  }, [confirmBeforeSubmit, matrixTableCode, save, t]);

  const download = useCallback(() => {
    CalculatorService.queryMatrixTableRevision(matrixTableCode).then(res => {
      const index = res.length ? res.length - 1 : 0;
      downloadFile(
        `/api/calculator/v2/matrix-table/download`,
        {
          matrixTableCode,
          matrixTableRevisionId: res?.[index]?.id ?? -1,
        },
        'post'
      );
    });
  }, [matrixTableCode]);

  const uploadProps: UploadProps = useMemo(
    () => ({
      disabled: !(factorShowTextList.length > 0 && isSaved),
      action: '/api/calculator/matrixTable/upload',
      accept: '.xls, .xlsx, .csv',
      name: 'multipartFile',
      data: {
        matrixTableCode,
      },
      headers: {
        ...security.csrf(),
      },
      showUploadList: false,
      beforeUpload: callback_file => {
        const excelType = ['.xls', '.xlsx', '.csv'];
        const index = callback_file.name.lastIndexOf('.');
        const fileType = callback_file.name.slice(index);
        if (excelType.indexOf(fileType) < 0) {
          message.error(t('Please upload a CSV or Excel file.'));
          return false;
        }
        return true;
      },
      onChange: info => {
        if (info.file.status === 'done') {
          setFileName(info.file.name);
          message.success(t('Upload Successfully'));
        }
        if (info.file.status === 'error') {
          message.error(info.file.response?.message);
        }
      },
    }),
    [factorShowTextList.length, isSaved, matrixTableCode, t]
  );

  const clear = () => {
    NewCalculatorService.MatrixTableService.deleteMatrixTable(matrixTableCode).then(() => {
      NewMarketService.PackageAgreementConfigMgmtService.deleteCorrelationMatrix(packageId).then(() => {
        message.success(t('Delete Successfully'));
        setIsSaved(false);
        setFactorShowTextList([productCodeInitValue]);
        setBackupFactorShowTextList([]);
        setFileName(undefined);
      });
    });
  };

  return (
    <Modal
      open={visible}
      title={t('Product Correlation Matrix')}
      onCancel={onClose}
      closable={false}
      cancelText={t('Cancel')}
      onOk={onSubmit}
      okText={t('Submit')}
      width={800}
    >
      <div className="font-medium">
        <div className="bg-@info-color-bg text-root text-@info-color-text-dark rounded-lg p-4 mb-5">
          <Icon type="info-fill" className="mr-2" />
          {t('Tips: For relationship, you can configure as “attachable”, “non-attachable” or “compulsory”.')}
        </div>
        <Form form={form} layout="vertical">
          <Form.Item label={t('Select Parameter')} colon={false}>
            <GeneralSelect
              value={factorShowTextList}
              onChange={(_list: string[]) => setFactorShowTextList(_list)}
              option={parameterList.map(item => ({
                label: item.showText,
                value: item.showText,
                disabled: item.showText === productCodeInitValue,
              }))}
              placeholder={t('Please select')}
              style={{ width: 560 }}
              allowClear={false}
              mode="multiple"
            />
          </Form.Item>
          <div className="text-xs text-@disabled-color flex gap-1 font-medium mt-6 mb-5">
            {!fileName ? t('Please') : null}
            <span className="text-@primary-color underline cursor-pointer" onClick={downloadTemplate}>
              {t('Download Template')}
            </span>
            {!fileName ? t('first.') : null}
          </div>
          {fileName ? (
            <div
              className={cls(
                'rounded-lg border-solid border-[1px] border-@border-color-base py-4 px-6 w-full flex justify-between items-center',
                styles['upload-container']
              )}
            >
              <span className="flex gap-2">
                <Icon type="excel" className="text-2xl flex" />
                {fileName}
              </span>
              <div>
                <Upload {...uploadProps}>
                  <CommonIconAction icon={<Icon type="reload" />} className="mr-4" />
                </Upload>
                <CommonIconAction icon={<Icon type="download" onClick={download} />} className="mr-4" />
                <Popconfirm title={t('Are you sure to delete this record?')} onConfirm={clear}>
                  <CommonIconAction icon={<Icon type="delete" />} />
                </Popconfirm>
              </div>
            </div>
          ) : (
            <UploadDragger uploadProps={uploadProps} text={t('You can only upload XLSX/CSV')} />
          )}
        </Form>
      </div>
    </Modal>
  );
};

export default ProductCorrelationMatrixModal;
