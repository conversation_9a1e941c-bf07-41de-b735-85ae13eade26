import { useTranslation } from 'react-i18next';

import { Tooltip } from 'antd';
import type { ColumnProps } from 'antd/es/table';

import classNames from 'classnames/bind';

import { DeleteAction, EditAction, Table, TableActionsContainer, ViewAction } from '@zhongan/nagrand-ui';

import { ProductCategoryItemExtend1 } from 'genesis-web-service';
import { ProductTypeEnum } from 'genesis-web-service/lib/common.interface';
import { IsOptional, PackageProductResponse, WaiverProductRelationDTO } from 'genesis-web-service/lib/market';

import LinkIcon from '@market/asset/svg/link.svg';
import ProductTagLabel from '@market/components/ProductTagLabel';
import { t } from '@market/i18n';

import styles from './ProductAndLiabilityTableNew.module.scss';

const cx = classNames.bind(styles);

interface WaiverProductRelationDTOList extends WaiverProductRelationDTO {
  attachToProductName: string;
}

export interface DataSource extends PackageProductResponse {
  beAttachedByList: {
    label: string;
    beAttachedByProductId: number;
  }[];
  waiverProductRelationDTOList: WaiverProductRelationDTOList[];
  productCategoryName: string;
}
interface Props {
  dataSource: DataSource[];
  loading: boolean;
  totalInterestListMap: Record<
    string,
    {
      interestName: string;
    }
  >;
  draggable: boolean;
  onSortChange: (list: DataSource[]) => void;
  disabled: boolean;
  multiMainProduct?: boolean;
  onView?: (record: DataSource) => void;
  onEdit?: (record: DataSource) => void;
  onDelete?: (record: DataSource) => void;
}

export const ProductAndLiabilityTableNew = ({
  loading,
  dataSource,
  totalInterestListMap,
  draggable,
  onSortChange,
  disabled = false,
  multiMainProduct = false,
  onView,
  onEdit,
  onDelete,
}: Props): JSX.Element => {
  const columns: ColumnProps<DataSource>[] = [
    {
      title: t('Product Code/Name'),
      dataIndex: 'productCode',
      width: 400,
      render: (_, record) => ({
        children: (
          <div className="flex gap-3 py-4">
            {record.beAttachedByList ? (
              <Tooltip
                title={`${t('Be Attached By')}:${t(
                  'Waiver Product'
                )} - ${record.beAttachedByList?.map(i => i.label)?.join(',')}`}
              >
                <LinkIcon className="mt-1" />
              </Tooltip>
            ) : null}
            {record.waiverProductRelationDTOList?.length ? (
              <Tooltip
                title={`${t('Attached To')}:${record.waiverProductRelationDTOList
                  ?.map(i => i.attachToProductName)
                  ?.join(',')}`}
              >
                <LinkIcon className="mt-1" />
              </Tooltip>
            ) : null}
            <div>
              <div className="text-@text-color font-medium text-base mb-3 text-ellipsis whitespace-nowrap overflow-hidden w-[340px]">
                {record.productCategoryId !== ProductCategoryItemExtend1.Waiver
                  ? record.productName
                  : `${t('Waiver Product')} - ${record.productName}`}
              </div>
              <div className="font-medium text-@label text-xs mb-4 text-ellipsis whitespace-nowrap overflow-hidden w-[340px]">
                {record.productCode}
              </div>
              <div>
                {record.productTypeCode === ProductTypeEnum.MAIN ? (
                  <ProductTagLabel type="main" className="!ml-0" />
                ) : (
                  <ProductTagLabel type="rider" className="!ml-0" />
                )}
                <ProductTagLabel text={record.productCategoryName} className="ml-[10px]" />
                {multiMainProduct && (
                  <ProductTagLabel
                    text={record.isOptional === IsOptional.Optional ? t('Optional') : t('Mandatory')}
                    className="ml-[10px]"
                  />
                )}
              </div>
            </div>
          </div>
        ),
        props: {},
      }),
    },
    {
      title: t('Liability Name'),
      dataIndex: 'liabilityName',
      key: 'liabilityName',
      render: (_, product) => (
        <div className={cx('liability-list', 'py-4')}>
          {product.packageLiabilitys.map(packageLiability => (
            <div>
              {packageLiability.liabilityName}
              {multiMainProduct && packageLiability.isOptional === IsOptional.Optional ? (
                <span className="ml-1">({t('Optional')})</span>
              ) : (
                ''
              )}
              {packageLiability.liabilityInterestBenefitList ? (
                <ul className={cx('interest-list')}>
                  {packageLiability.liabilityInterestBenefitList?.map(liabilityInterest => (
                    <li>{totalInterestListMap[liabilityInterest.interestId]?.interestName}</li>
                  ))}
                </ul>
              ) : null}
            </div>
          ))}
        </div>
      ),
    },
  ];

  return (
    <div
      style={{
        marginTop: 8,
      }}
      className={cx('product-and-liabilit-table-new')}
    >
      <Table
        bordered
        draggable={draggable}
        rowKey="id"
        loading={loading}
        pagination={false}
        columns={
          multiMainProduct
            ? [
                ...columns,
                {
                  title: t('Actions'),
                  align: 'center',
                  className: '!align-middle',
                  width: 120,
                  render: (_, record) => (
                    <TableActionsContainer>
                      <ViewAction disabled={disabled} onClick={() => onView?.(record)} />
                      <EditAction disabled={disabled} onClick={() => onEdit?.(record)} />
                      <DeleteAction
                        disabled={disabled}
                        doubleConfirmPlacement="topLeft"
                        doubleConfirmType="popconfirm"
                        onClick={() => onDelete?.(record)}
                        deleteConfirmContent={t('Are you sure to delete this record?')}
                      />
                    </TableActionsContainer>
                  ),
                },
              ]
            : columns
        }
        dataSource={dataSource}
        // 单主险情况下，第一行是主险，禁止排序拖动
        onRow={multiMainProduct ? undefined : (record, index) => ({ disabled: index === 0 })}
        setDataSource={onSortChange}
      />
    </div>
  );
};

export default ProductAndLiabilityTableNew;
