.product-and-liabilit-table-new {
  .liability-list {
    max-height: 200px;
    overflow-y: auto;
    margin: 16px 0 16px 16px;
  }

  .interest-list {
    color: $label;
    padding-left: 16px;
    margin-bottom: 12px;
  }

  :global {
    .market-ant4-table-wrapper .market-ant4-table {
      border-radius: 8px;
      thead {
        > tr {
          .market-ant4-table-cell {
            font-size: 12px;
            font-weight: 700;
            color: $text-color-tertiary;
            border-inline-end: none !important;
          }
        }
      }
      tbody {
        > tr {
          :first-child {
            &.market-ant4-table-cell {
              padding: 16px !important;
              vertical-align: middle;
            }
          }
          :last-child {
            &.market-ant4-table-cell {
              padding: 0;
              vertical-align: top;
            }
          }
        }
      }
    }
  }
}
