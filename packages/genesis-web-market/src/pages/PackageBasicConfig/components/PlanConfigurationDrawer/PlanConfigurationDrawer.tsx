import React, { useEffect, useReducer, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Checkbox, Form, Table, message } from 'antd';

import { cloneDeep, pick } from 'lodash-es';

import { DeleteAction, Icon, Drawer } from '@zhongan/nagrand-ui';

import { PackageProductInfo } from 'genesis-web-service/service-types/market-types/package/index';
import { ProductTypeEnum } from 'genesis-web-service/lib/common.interface';
import type {
  PackageInterest,
  PackageLiability,
  PackageProduct,
  ProductInfoValue,
  WaiverProductRelationDTO,
} from 'genesis-web-service/lib/market';
import { CollocationBehaviorTypeEnum, IsOptional } from 'genesis-web-service/lib/market';
import { IsVirtualType, ProductCategoryItemExtend1 } from 'genesis-web-service/lib/product';

import { ProductsItemInfo } from '@market/common/packages.interface';
import Collapse from '@market/components/Collapse';
import GeneralSelect from '@market/components/GeneralSelect';
import ProductTagLabel from '@market/components/ProductTagLabel';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { NewMarketService } from '@market/services/market/market.service.new';
import { deduplicateArray } from '@market/utils/universal';

import { mergeProductList } from '../../util';
import { LiabilityAndInterestTable } from '../LiabilityAndInterestTable/LiabilityAndInterestTable';
import styles from './PlanConfigurationDrawer.module.scss';

type EditMode = 'add' | 'edit' | 'addTo';

interface Props {
  addedMainProduct: ProductInfoValue;
  mainProductList: ProductInfoValue[];
  isEditingLiability: boolean;
  editMode: EditMode;
  visible: boolean;
  packageCode: string;
  packageId: number;
  productLiabilityData: PackageProduct[];
  originConfiguredProduct: PackageProduct[];
  ridersWithoutWaiver: ProductsItemInfo[];
  queryConfigProducts: (packageId: number) => void;
  onClose: (stepNo?: number) => void;
  onSubmit: () => void;
  onChoseMainProduct: (productId: number) => void;
  isCoveragePlan: boolean;
  planId?: number;
  disabled?: boolean;
}

type WaiverAction =
  | { type: 'ADD_ROW'; productIndex: number }
  | {
      type: 'REMOVE_ROW';
      productIndex: number;
      index: number;
    }
  | {
      type: 'RESET';
      initialValue: PackageProduct[];
    };

export const PlanConfigurationDrawer = ({
  addedMainProduct,
  mainProductList = [],
  isEditingLiability,
  editMode,
  visible,
  packageId,
  packageCode,
  productLiabilityData,
  ridersWithoutWaiver,
  originConfiguredProduct,
  queryConfigProducts,
  onClose,
  onSubmit,
  onChoseMainProduct,
  isCoveragePlan = false,
  planId,
  disabled = false,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();

  const isOptionalOptions = useBizDictAsOptions('isOptional');
  /* ============== 枚举使用end ============== */

  const [selectedProductRowKeys, setSelectedProductRowKeys] = useState<number[]>([]);

  useEffect(() => {
    if (editMode === 'edit' || editMode === 'addTo') {
      form.setFieldsValue({
        productId: addedMainProduct?.productId,
      });
    }
  }, [addedMainProduct, form, editMode]);

  const waiverReducer = (state: PackageProduct[], action: WaiverAction): PackageProduct[] => {
    switch (action.type) {
      case 'ADD_ROW':
        return state.map((table, productIndex) => {
          if (productIndex === action.productIndex) {
            return {
              ...table,
              waiverProductRelationDTOList: [...table.waiverProductRelationDTOList, {}],
            };
          }
          return table;
        });
      case 'REMOVE_ROW':
        return state.map((table, productIndex) => {
          if (productIndex === action.productIndex) {
            return {
              ...table,
              waiverProductRelationDTOList: table.waiverProductRelationDTOList.filter(
                (row, index) => index !== action.index
              ),
            };
          }
          return table;
        });
      case 'RESET':
        return action.initialValue;
      default:
        return state;
    }
  };
  const [tables, dispatch] = useReducer(waiverReducer, []);

  const closeDrawer = () => {
    onClose();
    form.resetFields();
  };

  // 多主险
  const multiMainProducts = tables.filter(row => row.isMain === 1).length > 1;

  const optionalOrRequired = (
    record: PackageInterest | PackageLiability | PackageProduct | WaiverProductRelationDTO,
    onChange?: (isOptional: IsOptional) => void
  ) => (
    <GeneralSelect
      onClick={e => e.stopPropagation()}
      disabled={record.canEdit === false || disabled}
      allowClear={false}
      defaultValue={record.isOptional?.toString()}
      onChange={(isOptional: IsOptional) => {
        onChange?.(isOptional);
        record.isOptional = isOptional;
      }}
      option={isOptionalOptions.map(item => ({
        value: item.value,
        label: Number(item.value) === 1 ? t('Optional') : t('Mandatory'),
      }))}
      showSearch={false}
    />
  );

  useEffect(() => {
    const selectedProductsId = originConfiguredProduct.length
      ? originConfiguredProduct.filter(product => product.isSelect ?? true).map(i => i.productId)
      : productLiabilityData
          .filter(product => product.productType === ProductTypeEnum.MAIN || product.isOptional === IsOptional.Required)
          ?.map(product => product.productId);

    setSelectedProductRowKeys(selectedProductsId);

    const getInitialValues = (mode: EditMode) => {
      if (mode === 'addTo') {
        return productLiabilityData.filter(table => !selectedProductsId.includes(table.productId));
      }
      if (mode === 'edit') {
        return originConfiguredProduct;
      }
      return productLiabilityData;
    };

    dispatch({
      type: 'RESET',
      initialValue: getInitialValues(editMode).map(table => {
        if (table.productCategoryId === ProductCategoryItemExtend1.Waiver) {
          table.waiverProductRelationDTOList ??= [{}];
        }
        return table;
      }),
    });
  }, [productLiabilityData, editMode]);

  const waiverColumns = (waiverList: WaiverProductRelationDTO[], productIndex: number, productId: number) => [
    {
      title: t('Attach to Product'),
      dataIndex: 'attachToProductId',
      key: 'attachToProductId',
      width: 256,
      render: (text: number | undefined, record: WaiverProductRelationDTO, index: number) => {
        const options = productLiabilityData
          .filter(
            product =>
              selectedProductRowKeys.includes(product.productId) &&
              product.productCategoryId !== ProductCategoryItemExtend1.Waiver
          )
          .map(product => ({
            label: product.productName,
            value: product.productId,
          }));

        // 枚举没有对应product时清空value
        if (options.findIndex(option => option.value === text) === -1) {
          form.setFieldValue(`attachToProductId ${productIndex} ${index}`, undefined);
          text = undefined;
        }
        return (
          <Form.Item
            rules={[
              {
                message: t('Please select'),
                validator: (_, value) => {
                  if (!value && selectedProductRowKeys.includes(productId)) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
              },
            ]}
            name={`attachToProductId ${productIndex} ${index}`}
            initialValue={text}
            className="!mb-0"
          >
            <GeneralSelect
              option={options}
              onChange={val => {
                record.attachToProductId = val;
              }}
            />
          </Form.Item>
        );
      },
    },
    {
      title: t('Optional/Mandatory'),
      dataIndex: 'isOptional',
      key: 'isOptional',
      width: 256,
      render: (_: any, record: WaiverProductRelationDTO) => {
        record.isOptional = record.isOptional ?? IsOptional.Optional;
        return <span className="absolute top-2">{optionalOrRequired(record)}</span>;
      },
    },
    {
      render: (_: any, record: WaiverProductRelationDTO, index: number) => (
        <DeleteAction
          disabled={waiverList?.length === 1 && index === 0}
          className="float-end"
          onClick={() =>
            dispatch({
              type: 'REMOVE_ROW',
              productIndex,
              index,
            })
          }
        />
      ),
    },
  ];

  const confirmDrawer = async () => {
    let products = cloneDeep(tables).filter(product => selectedProductRowKeys.includes(product.productId));
    await form.validateFields();
    let emptyLiabilitys = false;
    const isVirtual = products?.some(
      product => product.productType === ProductTypeEnum.MAIN && product.isVirtual === IsVirtualType.Yes
    );
    const hasRider = products?.some(product => product.productType === ProductTypeEnum.RIDER);
    let hasWaiverDuplicated = false;
    products?.forEach((product: PackageProduct) => {
      if (!emptyLiabilitys) {
        // 如果是true，就不需要再判断了
        emptyLiabilitys = product.packageLiabilityList.every(item => !item.isSelect);
      }
      const waiverAttachToProductIdSet: Record<number, boolean> = {};

      if (product.productCategoryId === ProductCategoryItemExtend1.Waiver) {
        hasWaiverDuplicated = !hasWaiverDuplicated
          ? product.waiverProductRelationDTOList.findIndex(item => {
              if (item.attachToProductId) {
                const isDuplicated = waiverAttachToProductIdSet[item.attachToProductId];
                waiverAttachToProductIdSet[item.attachToProductId] = true;
                return isDuplicated;
              }
              return false;
            }) !== -1
          : true;
      }

      product.packageLiabilityList = product.packageLiabilityList.filter(item => item.isSelect);
      product.packageLiabilityList.forEach(packageLiability => {
        packageLiability.packageInterestList = packageLiability.packageInterestList?.filter(
          packageInterest => packageInterest.isSelected
        );
      });
      // 兼容一下历史接口，之前接口的productCode叫insuranceProductCode
      product.insuranceProductCode = product.productCode;
    });

    if (hasWaiverDuplicated) {
      message.error(t('Waiver Product Duplicated'));
      return;
    }

    // if it's Virtual main, must select one or more rider benefits
    if (isVirtual && !hasRider) {
      message.error(t('There must be one or more rider benefits under the virtual main benefit.'));
      return;
    }

    const hasRealProductIndex = products.findIndex(
      item => !item.canVirtual || !item.isVirtual || +item.isVirtual === IsVirtualType.No
    );

    if (hasRealProductIndex === -1) {
      message.warning('The number of real products in a package is not less than one.');
      return;
    }
    if (emptyLiabilitys && !isVirtual) {
      message.error(t('Liability is required'));
      return;
    }
    // addTo模式下组合数据 originConfiguredProduct
    if (editMode === 'addTo') {
      products = mergeProductList(cloneDeep(originConfiguredProduct), cloneDeep(products));
    }

    // Products与Waiver险关联关系返回产品数组信息的数组去除重复项
    products = deduplicateArray<PackageProduct>(products, 'productId');

    const data = { packageId, packageCode, products, planId };
    let res;
    if (isCoveragePlan) {
      const packageProductInfoList = cloneDeep(products) as (PackageProductInfo & PackageProduct)[];

      res = await NewMarketService.GoodsPlanMgmtService.updateProductAndLiability({
        planId,
        packageId,
        packageProductInfoList: packageProductInfoList.map(product => ({
          ...pick(product, ['productId', 'productName', 'productCode', 'isOptional']),
          isMain: product.productType,
          packageProductLiabilityInfoList: product.packageLiabilityList.map(liability => ({
            ...pick(liability, ['liabilityId', 'liabilityCode', 'liabilityName', 'isOptional']),
            isSelected: liability.isSelect ? 1 : 2,
          })),
        })),
      });
    } else {
      res = isEditingLiability
        ? await NewMarketService.PackageProductMgmtService.updatePackageProduct(data)
        : await NewMarketService.PackageProductMgmtService.addPackageProducts(data);
    }

    if (res.message) {
      return;
    }
    /* 再次请求基本信息 */
    queryConfigProducts(packageId);
    onSubmit();
    form.resetFields();
  };

  return (
    <Drawer
      open={visible}
      title={isEditingLiability ? t('Edit Product And Liability') : t('Add Product And Liability')}
      onClose={closeDrawer}
      onSubmit={confirmDrawer}
    >
      <Form form={form} layout="vertical">
        {!isCoveragePlan ? (
          <Form.Item name="productId" label={t('Main Product Code / Name')}>
            <GeneralSelect
              option={
                editMode === 'edit' || editMode === 'addTo'
                  ? [
                      {
                        label: `${addedMainProduct.insuranceProductCode} / ${addedMainProduct.productName}`,
                        value: addedMainProduct.productId,
                      },
                    ]
                  : mainProductList
                      ?.filter(
                        product =>
                          product.productCategoryId !== ProductCategoryItemExtend1.Waiver &&
                          product.productSubCategoryId !== undefined
                      )
                      .map(item => ({
                        value: item.productId,
                        label: `${item.productCode} / ${item.productName}`,
                      }))
              }
              style={{ width: 640 }}
              onChange={onChoseMainProduct}
              disabled={editMode === 'edit' || editMode === 'addTo'}
            />
          </Form.Item>
        ) : null}

        <div className={styles['add-liability-table-wrap']}>
          {/* 产品列表 */}
          {tables.map((product, productIndex) => {
            const mandatoryLiability = product.packageLiabilityList?.filter(
              liability => liability.isOptional === IsOptional.Required
            );
            const optionalLiability = product.packageLiabilityList?.filter(
              liability => liability.isOptional === IsOptional.Optional
            );

            // 多主险场景下、optional的主险可以支持二次定义
            const disabledMainProduct = product.productType === ProductTypeEnum.MAIN && !multiMainProducts && product.optionalInPackage !== IsOptional.Optional;

            return (
              <Collapse
                key={product.productId}
                title={
                  <div className={`flex items-center gap-4 ${styles.productCheckBoxStyle}`}>
                    <Checkbox
                      onClick={event => {
                        event.stopPropagation();
                      }}
                      defaultChecked={!product.canEdit || (editMode === 'edit' && (product.isSelect ?? true))}
                      onChange={e => {
                        const tempSelectedProductRowKeys = selectedProductRowKeys?.filter(
                          id => id !== product.productId
                        );
                        if (e.target.checked) {
                          setSelectedProductRowKeys([...tempSelectedProductRowKeys, product.productId]);
                        } else {
                          setSelectedProductRowKeys([...tempSelectedProductRowKeys]);
                        }
                      }}
                      disabled={
                        ridersWithoutWaiver.find(item => item.productId === product.productId)
                          ?.collocationBehavior === CollocationBehaviorTypeEnum.required ||
                        disabledMainProduct ||
                        !product.canEdit || // Column configuration not to be checked
                        disabled
                      }
                    />
                    <div className="w-full relative">
                      <div>
                        <div className="flex">
                          <span className="font-bold max-w-[60%] inline-block whitespace-nowrap overflow-hidden text-ellipsis">
                            {t('Product Name')} : {product.productName}
                          </span>
                          {product.productType === ProductTypeEnum.MAIN ? (
                            <ProductTagLabel type="main" />
                          ) : (
                            <ProductTagLabel type="rider" />
                          )}
                        </div>
                        <div className="text-xs font-medium text-@label max-w-[60%] whitespace-nowrap overflow-hidden text-ellipsis">
                          {t('Product Code')} : {product.productCode}
                        </div>
                      </div>
                      <span className={`absolute left-[624px] top-2 ${styles['product-table']}`}>
                        {!disabledMainProduct ? optionalOrRequired(product) : null}
                      </span>
                    </div>
                  </div>
                }
                visible
              >
                {optionalLiability?.length ? (
                  <Collapse subTitle={`${t('Optional Liability')}(${optionalLiability.length})`} visible>
                    <LiabilityAndInterestTable
                      liabilityList={optionalLiability}
                      optionalOrRequired={optionalOrRequired}
                      disabled={disabled}
                    />
                  </Collapse>
                ) : null}
                {mandatoryLiability?.length ? (
                  <Collapse subTitle={`${t('Mandatory Liability')}(${mandatoryLiability.length})`}>
                    <LiabilityAndInterestTable
                      liabilityList={mandatoryLiability}
                      optionalOrRequired={optionalOrRequired}
                      disabled={disabled}
                    />
                  </Collapse>
                ) : null}
                {product.productCategoryId === ProductCategoryItemExtend1.Waiver ? (
                  <div>
                    <div className="border-@disabled-color divide-x-half" />
                    <Table
                      // 纯定制样式，不需要替换nagrand表格
                      className={styles['product-waiver-table']}
                      key={productIndex}
                      columns={waiverColumns(product.waiverProductRelationDTOList, productIndex, product.productId)}
                      rowKey={(record, index) =>
                        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                        `${index} ${record.attachToProductId}`
                      }
                      size="small"
                      dataSource={product.waiverProductRelationDTOList}
                      pagination={false}
                    />
                    <Button
                      onClick={() => {
                        dispatch({ type: 'ADD_ROW', productIndex });
                      }}
                      className="text-@primary-color border-@primary-color m-2"
                    >
                      <Icon type="add" />
                      {t('Add')}
                    </Button>
                  </div>
                ) : null}
              </Collapse>
            );
          })}
        </div>
      </Form>
    </Drawer>
  );
};

export default PlanConfigurationDrawer;
