.add-liability-table-wrap {
  .product-table {
    :global {
      .market-ant4-select {
        width: 128px !important;
        height: 22px;
        .market-ant4-select-selector {
          border: none;
          background-color: #ebf0f0;
          border-radius: 4px;
          color: $text-color;
          box-shadow: none !important;
          .market-ant4-select-selection-search-input,
          .market-ant4-select-selection-item {
            line-height: 22px;
            font-weight: 500;
          }
        }
      }
    }
    :global {
      .market-ant4-table {
        border: none;
      }
      .market-ant4-table-row {
        > td {
          background-color: transparent !important;
          border-bottom: 1px solid $table-header-bg;
          padding: 8px;
        }
      }
      .market-ant4-table-expanded-row {
        .market-ant4-table-cell {
          background-color: $white;
        }
      }
    }
  }
  .product-waiver-table {
    :global {
      .market-ant4-table {
        border: none;
        background-color: transparent;
      }
      .market-ant4-table-thead {
        th {
          background-color: transparent;
          border: none;
          &::before {
            display: none;
          }
          color: $text-color-tertiary;
        }
      }
      .market-ant4-table-row {
        td {
          border: none;
        }
        .market-ant4-table-cell-row-hover {
          background-color: var(--table-header-bg);
          &:first-child {
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
          }
          &:last-child {
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
          }
        }
      }
    }
  }
}

.product-check-box-style {
  :global {
    .market-ant4-checkbox-wrapper {
      line-height: 1;
    }
  }
}
