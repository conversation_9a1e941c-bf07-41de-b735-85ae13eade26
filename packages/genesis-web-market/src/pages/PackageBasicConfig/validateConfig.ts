import { Rule } from 'antd/es/form';

import { t } from '@market/i18n';
import rules from '@market/shared/rules';

export const PackageCodeValidator: Rule[] = [
  {
    required: true,
    validator: (rule, value, callback) => {
      const patrn = rules.codeRule; // 校验特殊字符

      const test = patrn.test(value);
      if (!value) {
        const str = t('Please input');
        callback(str);
      }
      if (value && !test) {
        const str = t('The package code could only contain letters, numbers, and underscores (_).');
        callback(str);
      }
      callback();
    },
  },
];
