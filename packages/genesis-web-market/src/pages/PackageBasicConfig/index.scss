.product-basic-config {
  height: 100%;
  .right-content {
    width: 100%;
    .action-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 62px;
      padding-top: 130px;
      background: var(--white);
      border-bottom: 1px solid var(--border-gray1);
      position: relative;
      .table-header-action {
        position: absolute;
        width: 62px;
        height: 56px;
        top: 73px;
        border-top: 1px solid var(--border-gray1);
        border-bottom: 1px solid var(--border-gray1);
        background: var(--table-header-bg);
        span {
          display: inline-block;
          width: 62px;
          border-top: 1px dashed var(--border-light);
          position: absolute;
          height: 0px;
          top: -18px;
        }
      }
      span {
        cursor: pointer;
      }
    }
  }
  .disabled-action .anticon {
    color: $disabled-color;
    cursor: not-allowed;
    opacity: 1;
  }
  .disabled-delete {
    color: $disabled-color;
    cursor: not-allowed;
  }
  .column-money {
    vertical-align: top;
  }
}
.record-line-box {
  .product-name {
    color: var(--text-color);
    font-weight: normal;
    font-size: 14px;
    line-height: 20px;
    margin-right: 10px;
  }
  .product-Main {
    align-items: center;
    white-space: nowrap;
    width: 39px;
    height: 16px;
    padding: 0 6px;
    color: var(--white);
    font-weight: normal;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    background: var(--primary-color);
    border-radius: 10px;
  }
  .product-Rider {
    align-items: center;
    width: 39px;
    height: 16px;
    margin-left: 10px;
    padding: 0 6px;
    color: var(--primary-color);
    font-weight: normal;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    background: var(--info-color-bg);
    border-radius: 10px;
  }
  .product-code {
    color: var(--text-color);
    font-weight: normal;
    font-size: 12px;
    line-height: 18px;
  }
}
