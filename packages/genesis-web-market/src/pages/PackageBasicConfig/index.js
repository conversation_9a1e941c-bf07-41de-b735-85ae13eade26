import React, { Component, createRef } from 'react';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';

import { Button, Checkbox, Divider, Form, Input, Layout, Popconfirm, Radio, Skeleton, message } from 'antd';

import classNames from 'classnames/bind';
import { keyBy } from 'lodash-es';

import { Icon, lessVars } from '@zhongan/nagrand-ui';

import { PolicyTypeOptions, ProductCategoryItemExtend1, ProductTypeEnum, YesOrNo } from 'genesis-web-service';
import { CollocationBehaviorTypeEnum } from 'genesis-web-service/lib/market';
import { IsVirtualType } from 'genesis-web-service/lib/product/enums';

import CheckedSvg from '@market/asset/svg/checkSucc.svg';
import { DetailPageMode } from '@market/common/interface';
import GeneralSelect from '@market/components/GeneralSelect';
import LabelWithTooltip from '@market/components/LabelWithTooltip';
import Section from '@market/components/Section/section';
import { selectPermissionCheckMap } from '@market/redux/selector';
import { NewMarketService } from '@market/services/market/market.service.new';
import { NewProductService } from '@market/services/product/product.service.new';
import { mapBizDictToOptions } from '@market/utils/enum';

import { FMarketHeader } from '../../components/F-Market-Header';
import FMarketMenu from '../../components/F-Market-Menu';
import { urlQuery } from '../../util';
import styles from './PackageBasicConfig.module.scss';
import MultiMainBenefitsSection from './components/MultiMainBenefitsSection/MultiMainBenefitsSection';
import PlanConfigurationDrawer from './components/PlanConfigurationDrawer';
import ProductAndLiabilityTableNew from './components/ProductAndLiabilityTable/ProductAndLiabilityTableNew';
import ProductCorrelationMatrixModal from './components/ProductCorrelationMatrixModal';
import './index.scss';
import { SupportMultiMainBenefitsPolicyTypes } from './util';
import { PackageCodeValidator } from './validateConfig';

const cx = classNames.bind(styles);

const { Sider, Content } = Layout;

class PackageBasicConfig extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false, // table loading
      packageId: '',
      packageCode: '',
      productCategoryList: [], // 产品分类
      insuranceCompanyList: [], // 保险公司列表
      dataSource: [], // package上已经挂载的产品列表(排除waiver)
      isShowEditButton: false, // 是否有锁住之后的编辑权限
      ridersWithoutWaiver: [], // 非waiver类型的附加险，用来校验强附加险
      productLiabilityList: [], // 产品、责任列表
      currentProductCategoryId: undefined,
      originProducts: [],
      originConfiguredProduct: [],
      mainProductList: [], // 可选择的主险
      totalInterestListMap: {},
      editMode: 'add', // add | edit | addTo
      skeletonVisible: true,
      drawerVisible: false,
      matrixModalVisible: false,
      matrixFileName: '',
      formValidateFailed: false,
      sortMode: false,
      riderSortMode: false,
      isCopyFirstEnter: false,
      multiMainProduct: false,
    };
    this.formRef = createRef();
  }

  getAllInterests = () => {
    NewProductService.InsurableInterestMgmtService.queryAllInsurableInterests().then(interests => {
      this.setState({
        totalInterestListMap: keyBy(interests, 'id'),
      });
    });
  };

  async queryAllCompany() {
    const res = await NewMarketService.CompanyShareMgmtService.queryAllCompany();
    if (!res.success) return;
    const insuranceCompanyList = res.value || [];
    this.setState({ insuranceCompanyList });
  }

  async componentDidMount() {
    const { hasEditAuth, isSuperUser } = this.props;
    this.queryAllCompany();
    this.getAllInterests();
    const { state } = this.props.location;
    const parsedUrl = new URL(window.location.href);
    const packageId = parsedUrl.searchParams.get('packageId');
    const isCopyFirstEnter = parsedUrl.searchParams.get('isCopyFirstEnter');
    this.packageId = packageId;
    this.setState({
      mode: state?.mode ?? 'view',
      isCopyFirstEnter: isCopyFirstEnter === YesOrNo.YES,
      isShowEditButton: hasEditAuth || isSuperUser,
    });
    /* 新增产品时候，生成packageId */
    !packageId && this.generateMarketId();

    /* 组合产品类型 */
    this.queryCategory();
    if (packageId) {
      this.setState({ packageId });

      // 查询已经添加的产品
      const products = await this.queryConfigProducts(packageId);

      if (products && products.length > 0) {
        // 查询全量可添加的产品，因为要判断能否继续追加Rider，所以需要全量的产品来对比看看是否已经全部添加完毕
        const mainProduct = products.find(product => product.productTypeCode === ProductTypeEnum.MAIN);

        this.queryProductByConditionContainsRiders({
          productCategory: mainProduct.productCategoryId,
          productCode: mainProduct.productCode,
          productId: mainProduct.productId,
          productType: 1,
          productVersion: mainProduct.productVersion,
        });
      }
    } else {
      this.setState({
        formValidateFailed: true,
      });
    }
  }

  async componentDidUpdate(prevProps) {
    if (prevProps.productCategory.length !== this.props.productCategory.length) {
      /* 组合产品类型 */
      this.queryCategory();
      if (this.packageId) {
        /* 处理编辑状态的逻辑 */
        this.queryConfigProducts(this.packageId);
      }
    }
  }

  // 获取package上面挂载的主险
  getAddedMainProduct = () => {
    return {
      ...this.state.dataSource.find(product => product.productTypeCode === ProductTypeEnum.MAIN),
    };
  };

  edit = () => {
    const { dataSource = [] } = this.state;
    const mainProduct = this.getAddedMainProduct();
    const productIds = dataSource?.map(item => {
      return item.productId;
    });

    this.queryProductByConditionContainsRiders({
      productCategory: mainProduct.productCategoryId,
      productCode: mainProduct.productCode,
      productId: mainProduct.productId,
      productType: 1,
      productVersion: mainProduct.productVersion,
    });

    this.queryEditLiabilitys(productIds, 'edit');
  };

  addTo = async () => {
    const { originProducts, dataSource = [] } = this.state;
    const productIds = dataSource?.map(item => {
      return item.productId;
    });
    const mainProduct = originProducts.find(product => product.productTypeCode === 1);

    await this.queryProductByConditionContainsRiders({
      productCategory: mainProduct.productCategoryId,
      productCode: mainProduct.productCode,
      productId: mainProduct.productId,
      productType: 1,
      productVersion: mainProduct.productVersion,
    });

    this.queryEditLiabilitys(productIds, 'addTo');
  };

  // 设置waiver数据
  setupWaiverData = mainProductRes => {
    const waiverList = [];
    const productsIdArr = [];
    const noWaiverList = [];

    productsIdArr.push(`${mainProductRes.productId}`);
    if (mainProductRes.children && mainProductRes.children.length > 0) {
      mainProductRes.children
        .filter(item => item.collocationBehavior !== CollocationBehaviorTypeEnum.NonAttachable)
        .forEach(sub => {
          productsIdArr.push(`${sub.productId}`);
          sub.productCode = sub.insuranceProductCode; // 转换名称
          /* 因为productId不是唯一，但是存在不同版本概念 */
          const mainKey = `${sub.productId}-${sub.productVersion}_Main`;
          sub.key = mainKey;

          if (sub.productCategoryId === ProductCategoryItemExtend1.Waiver) {
            waiverList.push(sub);
          } else {
            noWaiverList.push(sub);
          }
        });
    }
    this.setState({
      displayWaiverData: waiverList,
      ridersWithoutWaiver: noWaiverList,
    });

    return productsIdArr;
  };

  // TODO 待优化
  // 根据主险查询所有附加险
  async queryProductByConditionContainsRiders(params) {
    const mainProductRes =
      await NewMarketService.PackageProductMgmtService.queryProductByConditionContainsRiders(params);
    if (mainProductRes && !mainProductRes.message) {
      const productsIdArr = this.setupWaiverData(mainProductRes);
      await this.setupProductLiabilitys(productsIdArr);
    }
  }

  async queryEditLiabilitys(productIds, editMode) {
    const { originConfiguredProduct } = this.state;
    /* 查询已经配置的产品责任数据 */
    const { packageProductResponseList } =
      await NewMarketService.PackageProductMgmtService.queryPackageProductLiabilitys({
        packageId: this.packageId,
        productIds,
      });

    packageProductResponseList.forEach(i => {
      i.waiverProductRelationDTOList = originConfiguredProduct.find(
        j => i.productId === j.productId
      )?.waiverProductRelationDTOList;
    });
    this.setState({
      drawerVisible: true,
      // addTo模式下需要全量提交，用于提交时拼接数据
      originConfiguredProduct: [...packageProductResponseList],
      isEditingLiability: true,
      editMode,
    });
  }

  /* 查询全部的产品和责任  */
  async setupProductLiabilitys(productIdList) {
    const res = await NewMarketService.PackageProductMgmtService.queryLiabilitys({
      packageId: this.packageId,
      productIds: productIdList,
    });
    if (res && !res.message) {
      let products = res.products;

      const arr = products.filter(
        // 虚拟主险本身就没有责任，不需要校验
        product => product.packageLiabilityList.length === 0 && product.isVirtual !== IsVirtualType.Yes
      );
      if (arr.length > 0) {
        this.setState({
          productLiabilityList: [],
        });
        return message.error(
          this.props.t('The product lacks liabilities, and please add the configuration in the product center')
        );
      }

      // 设置key
      products = products.map(product => {
        // TODO:Remove 兼容历史逻辑
        product.key = `${product.productId}-${product.productVersion}`;

        if (product.packageLiabilityList) {
          product.packageLiabilityList = product.packageLiabilityList.map(sub => {
            sub.key = `${sub.liabilityId}`;
            return sub;
          });
        }
        return product;
      });

      this.setState({
        productLiabilityList: products,
      });
    }
    return res;
  }

  delete = async () => {
    const mainProduct = this.getAddedMainProduct();
    const data = {
      packageId: mainProduct.packageId,
      productId: mainProduct.productId,
    };
    const res = await NewMarketService.PackageProductMgmtService.deletePackageProduct(data);

    if (res.message) {
      return;
    }

    this.setState({
      dataSource: [],
      originProducts: [],
      productLiabilityList: [],
      originConfiguredProduct: [],
    });
  };

  getLabelAndValue(list, value, valueKey, labelKey) {
    const arr = list.filter(item => item[valueKey] === value);
    if (arr.length === 0) {
      return { label: '', key: '' };
    }

    const targetObj = arr[0];
    return {
      label: targetObj[labelKey],
      key: targetObj[valueKey],
    };
  }

  queryConfigProducts = async packageId => {
    const { hasEditAuth, isSuperUser, userInfo } = this.props;
    if (!packageId) {
      packageId = urlQuery('packageId') ?? this.state.packageId;
    }
    this.setState({ loading: true });
    const {
      packageCode,
      packageName,
      packageRemark,
      packageCategory,
      scope,
      creator,
      policyScenario,
      products: configProducts = [],
      openEndPolicy,
      multiMainProduct,
    } = await NewMarketService.PackageProductMgmtService.queryConfigProducts({ packageId });

    this.setState({
      loading: false,
      skeletonVisible: false,
      packageFormInitialValues: {
        packageCode,
        packageName,
        packageCategory,
        packageRemark,
        policyScenario: policyScenario?.toString(),
        openEndPolicy,
      },
      multiMainProduct,
    });

    configProducts
      .sort((a, b) => {
        // 主险放在附加险前
        return a.productTypeCode - b.productTypeCode;
      })
      .forEach(product => {
        product.productCode = product.insuranceProductCode || ''; // 转换名称

        // 将waiver上关联的产品反选到产品上
        product.waiverProductRelationDTOList?.forEach(attachToProduct => {
          const item = configProducts.find(i => i.productId === attachToProduct.attachToProductId);
          if (item) {
            attachToProduct.attachToProductName = item.productName;
            if (!item.beAttachedByList) {
              item.beAttachedByList = [];
            }
            item.beAttachedByList.push({
              beAttachedByProductId: attachToProduct.attachToProductId,
              label: product.productName,
            });
          }
        });
      });

    /*
        两种情况可以编辑：
        1、有edit权限的时候，并且当前产品是自己创建的
        2、有super edit权限
      */
    this.setState({
      isShowEditButton: scope !== 'LIBRARY' && ((creator === `${userInfo.userId}` && hasEditAuth) || isSuperUser),
      dataSource: configProducts,
      packageId,
      packageCode,
      originProducts: configProducts,
      originConfiguredProduct: configProducts,
    });

    if (scope === 'LIBRARY') {
      message.warning('This package is read only.');
      this.setState({
        mode: 'view',
      });
    }
    return configProducts;
  };

  queryPackageLiabilitys = async () => {
    const { packageId, packageCode } = this.state;
    const data = { packageId, packageCode };
    await NewMarketService.PackageProductMgmtService.queryPackageProductLiabilitys(data);
  };

  generateMarketId = async () => {
    const res = await NewMarketService.PackageBasicInfoSaveMgmtService.generateId({
      idTypeEnum: 'PACKAGE',
    });
    if (res && res.success) {
      this.setState({
        packageId: res.value.id,
        skeletonVisible: false,
      });
    }
  };

  async queryMainProductList(params) {
    const data = { productType: '1', ...params };
    const res = await NewMarketService.PackageProductMgmtService.queryProductByCondition(data);
    if (res && res.success && res.value) {
      res.value.map(item => {
        item.productCode = item.insuranceProductCode; // 赋值转换
        // TODO:Remove 兼容历史逻辑
        item.key = `${item.productId}-${item.productVersion}_Main`;
      });
      this.setState({ mainProductList: res.value });
    }
  }

  async queryCategory() {
    const { productCategory } = this.props;
    const catetories =
      productCategory &&
      productCategory.map(item => {
        return {
          id: item.itemExtend1,
          categoryName: item.itemName,
        };
      });
    const productCategoryList = catetories || [];
    this.setState({ productCategoryList });
  }

  saveBasicInfo = async multiMainProduct => {
    /*
      @desc: 点击表格顶部Add, 保存基本信息，成功之后打开抽屉操作产品和责任添加
    */
    const { getFieldsValue, validateFields } = this.formRef.current;
    await validateFields();
    const { packageId, mode, insuranceCompanyList } = this.state;
    const values = getFieldsValue(); // 子组件组合的基本信息
    if (!values || mode === 'view') return;
    if (insuranceCompanyList.length > 0) {
      values.orgCode = insuranceCompanyList[0].companyCode;
      values.orgId = insuranceCompanyList[0].companyId;
    }
    values.packageId = packageId;
    values.packageCategory = values.packageCategory.key;
    values.multiMainProduct = multiMainProduct;

    /* 保存基本信息 */
    const res = await NewMarketService.PackageBasicInfoSaveMgmtService.save(values);
    res.value = {
      ...values,
      ...res.value,
    };
    return res;
  };

  addBtnAction = async actionFrom => {
    const res = await this.saveBasicInfo(this.state.multiMainProduct);

    /* 查询组合产品product code list */
    if (res.success) {
      this.packageId = res.value.packageId;
      this.queryMainProductList({ productCategory: res.value.packageCategory });
      if (actionFrom && actionFrom === 'next') {
        this.setState({
          isCopyFirstEnter: false,
          editMode: 'edit',
          packageFormInitialValues: {
            packageCode: res.value.packageCode,
            packageName: res.value.packageName,
            packageCategory: res.value.packageCategory,
            packageRemark: res.value.packageRemark,
            policyScenario: res.value.policyScenario,
          },
        });
        return true;
      }
      this.setState({
        drawerVisible: true,
        packageCode: res.value.packageCode,
        isEditingLiability: false,
        isCopyFirstEnter: false,
        editMode: 'add',
        currentProductCategoryId: res.value.packageCategory,
        productLiabilityList: [],
      });
    }
  };

  onChoseMainProduct = async selectedProductId => {
    if (!selectedProductId) {
      this.setState({
        productLiabilityList: [],
      });
      return;
    }
    const mainProduct = this.state.mainProductList.find(item => item.productId === selectedProductId);
    const params = {
      productCategory: mainProduct.productCategoryId,
      productCode: mainProduct.productCode,
      productId: mainProduct.productId,
      productType: 1,
      productVersion: mainProduct.productVersion,
    };
    const mainProductRes = await NewMarketService.PackageProductMgmtService.queryProductByConditionContainsRiders(
      params
    ).catch(() => {
      this.setState({
        productLiabilityList: [],
      });
    });

    const productsIdArr = this.setupWaiverData(mainProductRes);
    /* 根据产品ID请求责任 */
    await this.setupProductLiabilitys(productsIdArr);
  };

  goToNextPage = async () => {
    const { packageId, mode } = this.state;
    this.props.navigate(`/market/package/benefit-configuration?packageId=${packageId}`, {
      state: {
        mode: mode === 'add' || mode === 'copy' ? 'edit' : mode,
      },
    });
  };

  nextToBenefit = async isNext => {
    /* 下一步跳转到 benefit-configuration */
    const { dataSource, sortMode, riderSortMode } = this.state;
    const { t } = this.props;

    if (sortMode || riderSortMode) {
      return message.error(
        t('There is unfinished editing in <{{title}}>. Please complete before proceeding.', {
          title: t('Add Product And Liability'),
        })
      );
    }

    const canNext = await this.addBtnAction('next');
    if (!canNext) return;

    if (dataSource.length === 0) {
      message.error(this.props.t('Product and liability are required'));
      return;
    }
    message.success(this.props.t('Save successfully'));
    if (isNext) {
      this.goToNextPage();
    }
  };

  onEdit = () => {
    if (this.state.mode !== 'view') {
      return;
    }
    this.setState({ mode: 'edit' });
  };

  renderEditSaveBtn = () => {
    const { isShowEditButton, mode } = this.state;
    if (!isShowEditButton || this.props.envConfig.env === 'prd') {
      return null;
    }

    if (mode === 'view') {
      return (
        <Button size="large" onClick={() => this.onEdit()}>
          {this.props.t('Edit')}
        </Button>
      );
    }

    return (
      <Button size="large" onClick={() => this.nextToBenefit()}>
        {this.props.t('Save')}
      </Button>
    );
  };

  handleSortChange = list => {
    const { originProducts } = this.state;
    const sortList = list.toSorted((itemA, itemB) => itemA.productTypeCode - itemB.productTypeCode);

    // 多主险的情况下，主险和附加险是分开排序的，入参list数据不全，需要补全
    if (list.length < originProducts.length) {
      const productTypeCode = list[0].productTypeCode;
      const otherList = originProducts.filter(product => product.productTypeCode != productTypeCode);
      // 保持主险在附加险前面
      if (productTypeCode === ProductTypeEnum.MAIN) {
        sortList.push(...otherList);
      } else {
        sortList.unshift(...otherList);
      }
    }

    this.setState({
      originProducts: sortList,
    });
  };

  saveSort = async () => {
    const { packageId, originProducts } = this.state;
    const productOrders = originProducts?.map((item, index) => ({
      productId: item.productId,
      orderNo: index,
    }));
    const data = { packageId, productOrders };
    await NewMarketService.PackageProductMgmtService.sortProduct(data);
    message.success('Save successfully');
    this.setState({
      sortMode: false,
      riderSortMode: false,
    });

    this.queryConfigProducts(packageId);
  };

  isPolicyTypeDisabled() {
    const { mode, isCopyFirstEnter, isShowEditButton } = this.state;

    // 第一次copy进入的时候需要放开编辑
    if (mode === 'edit' && isCopyFirstEnter) {
      return false;
    }

    // 数据为空时可编辑（历史package 编辑场景）
    if (mode === 'edit' && !this.state.packageFormInitialValues?.policyScenario) {
      return false;
    }

    // 新增场景
    if (mode === 'add' && (isShowEditButton || !!this.packageId)) {
      return false;
    }

    return true;
  }

  renderMultiMainProductSwitch(readOnly) {
    const { originProducts } = this.state;
    const { t } = this.props;
    const policyScenario = this.formRef?.current?.getFieldValue('policyScenario');

    if (!SupportMultiMainBenefitsPolicyTypes.includes(policyScenario)) {
      return <span />;
    }

    if (originProducts && originProducts.length > 0) {
      return (
        <Popconfirm
          title={t(
            'Changing this setting will clear all configured coverage details and related information. Are you sure to continue?'
          )}
          onConfirm={async () => {
            await this.saveBasicInfo(!this.state.multiMainProduct);
            await this.delete();
            this.setState(prevState => ({
              multiMainProduct: !prevState.multiMainProduct,
            }));
          }}
        >
          <Checkbox className="mb-4" checked={this.state.multiMainProduct} disabled={readOnly}>
            {t('Enable Multi-Main Benefits')}
          </Checkbox>
        </Popconfirm>
      );
    }

    return (
      <Checkbox
        className="mb-4"
        onChange={() => {
          this.saveBasicInfo(!this.state.multiMainProduct);
          this.setState({
            multiMainProduct: !this.state.multiMainProduct,
          });
        }}
        checked={this.state.multiMainProduct}
      >
        {t('Enable Multi-Main Benefits')}
      </Checkbox>
    );
  }

  render() {
    const {
      packageCode,
      mainProductList = [],
      mode = 'view',
      isEditingLiability,
      editMode,
      packageId,
      loading,
      isShowEditButton,
      productLiabilityList,
      ridersWithoutWaiver,
      originProducts,
      originConfiguredProduct,
      drawerVisible,
      sortMode,
      riderSortMode,
    } = this.state;
    const { t } = this.props;

    const readOnly =
      mode === 'view' || (!isShowEditButton && mode !== 'copy' && mode !== 'add') || this.props.envConfig.env === 'prd';

    const mainProduct = this.getAddedMainProduct();
    const productsWithoutMain = originProducts?.filter(product => product.productTypeCode !== 1);

    const policyTypeOptions =
      mode === 'add'
        ? this.props.enums?.policyScenarioType.filter(d => d.dictValue !== PolicyTypeOptions.RelationalPolicy)
        : this.props.enums?.policyScenarioType;

    return (
      <Layout className="market-layout product-basic-config">
        <FMarketHeader backPath="/market/package/search" subMenu="Package" />
        <div className="flex" style={{ height: 'calc(100% - 50px)' }}>
          <Sider width={208} className="market-sider">
            <FMarketMenu
              disableAfterPages={this.state.dataSource.length === 0 ? '1' : undefined}
              page="PACKAGE"
              type={mode}
              defaultSelectedKeys={['1']}
              packageId={packageId}
              category={1}
              navigate={this.props.navigate}
            />
          </Sider>
          <Content className="market-content">
            <Skeleton active loading={this.state.skeletonVisible}>
              <div
                style={{
                  background: lessVars['@white'],
                }}
                className="right-content"
              >
                {/* 基本信息添加 */}
                <Section subTitle={t('Package')} className="mt-6 mx-6">
                  <Form
                    layout="vertical"
                    ref={this.formRef}
                    className="grid grid-cols-3"
                    disabled={mode === 'view'}
                    onValuesChange={() => {
                      setTimeout(() => {
                        this.formRef.current
                          ?.validateFields({
                            validateOnly: true,
                          })
                          .then(() => {
                            this.setState({
                              formValidateFailed: false,
                            });
                          })
                          .catch(() => {
                            this.setState({
                              formValidateFailed: true,
                            });
                          });
                      });
                    }}
                  >
                    <React.Fragment>
                      <Form.Item
                        label={this.props.t('Package Code')}
                        name="packageCode"
                        initialValue={this.state.packageFormInitialValues?.packageCode}
                        rules={
                          !(mode === 'view' || mode === 'edit') || !this.state.packageFormInitialValues?.packageCode
                            ? PackageCodeValidator
                            : []
                        }
                      >
                        <Input
                          placeholder={this.props.t('Please input')}
                          disabled={
                            !!this.packageId && mode !== 'copy' && !!this.formRef?.current?.getFieldValue('packageCode')
                          }
                        />
                      </Form.Item>
                      <Form.Item
                        label={this.props.t('Package Name')}
                        name="packageName"
                        initialValue={this.state.packageFormInitialValues?.packageName}
                        rules={[
                          {
                            required: true,
                            message: this.props.t('Please input'),
                          },
                        ]}
                      >
                        <Input placeholder={this.props.t('Please input')} />
                      </Form.Item>
                      <Form.Item
                        label={this.props.t('Package Category')}
                        name="packageCategory"
                        initialValue={
                          this.state.packageFormInitialValues?.packageCategory &&
                          this.getLabelAndValue(
                            this.state.productCategoryList,
                            this.state.packageFormInitialValues?.packageCategory,
                            'id',
                            'categoryName'
                          )
                        }
                        rules={[
                          {
                            required: true,
                            message: this.props.t('Please select'),
                          },
                        ]}
                      >
                        <GeneralSelect
                          getPopupContainer={triggerNode => triggerNode.parentNode}
                          labelInValue
                          optionValue="id" // 下拉框options的value
                          optionLabel="categoryName" // 下拉框options显示的label
                          options={mapBizDictToOptions(this.state.productCategoryList, {
                            optionValue: 'id',
                            optionName: 'categoryName',
                          })}
                          placeholder={this.props.t('Please select')}
                          disabled={!!this.packageId}
                        />
                      </Form.Item>
                      <Form.Item
                        label={this.props.t('Policy Type')}
                        name="policyScenario"
                        initialValue={
                          this.state.packageFormInitialValues?.policyScenario || PolicyTypeOptions.NormalPolicy
                        }
                        rules={[
                          {
                            required: true,
                            message: this.props.t('Please select'),
                          },
                        ]}
                      >
                        <GeneralSelect
                          getPopupContainer={triggerNode => triggerNode.parentNode}
                          options={
                            policyTypeOptions?.map(item => ({
                              label: item.dictValueName,
                              value: item.dictValue,
                            })) || []
                          }
                          placeholder={this.props.t('Please select')}
                          disabled={this.isPolicyTypeDisabled(readOnly)}
                          onChange={type => {
                            // 如果打开开关再切换policy type，重设一下状态
                            if (!SupportMultiMainBenefitsPolicyTypes.includes(type)) {
                              this.setState({
                                multiMainProduct: false,
                              });
                            }
                          }}
                        />
                      </Form.Item>
                      <Form.Item
                        label={this.props.t('Summary')}
                        name="packageRemark"
                        initialValue={this.state.packageFormInitialValues?.packageRemark}
                        className="col-span-2"
                      >
                        <Input.TextArea placeholder={this.props.t('Please input')} className="!w-[560px]" />
                      </Form.Item>
                      <Form.Item
                        label={
                          <LabelWithTooltip
                            title={this.props.t('Is Open End Policy')}
                            tooltip={this.props.t(
                              'An open-end policy does not have a fixed expiry date. Coverage continues unless terminated, and is typically reviewed periodically.'
                            )}
                          />
                        }
                        name="openEndPolicy"
                        initialValue={this.state.packageFormInitialValues?.openEndPolicy}
                        className="horizontal-radio-group mb-6 mr-[-12px]"
                      >
                        <Radio.Group>
                          {this.state.packageFormInitialValues?.openEndPolicy === false ? (
                            <Popconfirm
                              title={t(
                                'Open End Policy cannot be used together with Renewal Agreement, Expiry Date Rule, Coverage Period, Pre-calculate Premium installment method or Single Premium (Payment Period & Frequency), please check these configurations before submit.'
                              )}
                              overlayStyle={{ width: 500 }}
                              onCancel={() => {
                                this.formRef.current.setFieldValue('openEndPolicy', false);
                              }}
                            >
                              <Radio value>{t('Yes')}</Radio>
                            </Popconfirm>
                          ) : (
                            <Radio value>{t('Yes')}</Radio>
                          )}
                          <Radio value={false}>{t('No')}</Radio>
                        </Radio.Group>
                      </Form.Item>
                    </React.Fragment>
                  </Form>
                </Section>
                {/* 新增模式下，表单校验失败不支持走下一步 */}
                {this.state.formValidateFailed && mode === DetailPageMode.add ? null : (
                  <React.Fragment>
                    <Divider
                      style={{
                        margin: '0 32px',
                        width: 'auto',
                        minWidth: 'auto',
                      }}
                    />
                    <div className={cx('product-table-wrapper')}>
                      <Section subTitle={t('Add Product And Liability')} />
                      {this.renderMultiMainProductSwitch(readOnly)}
                      {this.state.multiMainProduct ? (
                        <React.Fragment>
                          {/* 多主险组件 */}
                          <MultiMainBenefitsSection
                            packageId={this.state.packageId}
                            originProducts={this.state.originProducts}
                            onSortChange={this.handleSortChange}
                            sortMode={sortMode}
                            riderSortMode={riderSortMode}
                            queryConfigProducts={this.queryConfigProducts}
                            saveSort={this.saveSort}
                            startSort={() =>
                              this.setState({
                                sortMode: true,
                              })
                            }
                            startRiderSort={() =>
                              this.setState({
                                riderSortMode: true,
                              })
                            }
                            readOnly={readOnly}
                          />
                        </React.Fragment>
                      ) : (
                        <React.Fragment>
                          {/* 单主险相关组件 */}
                          <div className="flex justify-between mt-2">
                            <div className="flex items-center">
                              <Button
                                icon={<Icon type="add" />}
                                className="text-@primary-color border-@primary-color"
                                onClick={originProducts.length > 0 ? this.addTo : this.addBtnAction}
                                disabled={
                                  readOnly ||
                                  (originConfiguredProduct.length > 0 &&
                                    originConfiguredProduct.length === productLiabilityList.length)
                                }
                              >
                                {originProducts.length > 0 ? t('Add Rider') : t('Add')}
                              </Button>
                              <Button
                                className="ml-4"
                                onClick={this.edit}
                                disabled={originProducts.length === 0 || readOnly || sortMode}
                              >
                                {t('Edit')}
                              </Button>
                              <Button
                                className="ml-4"
                                disabled={originProducts.length === 0 || readOnly || sortMode}
                                onClick={() => {
                                  this.setState({
                                    matrixModalVisible: true,
                                  });
                                }}
                              >
                                {t('Product Correlation Matrix')}
                                {this.state.matrixFileName ? <CheckedSvg /> : null}
                              </Button>
                            </div>
                            <div>
                              {/* 2个及以上附加险时候才会展示sort */}
                              {productsWithoutMain.length > 1 &&
                                (sortMode ? (
                                  <Button className="mr-4" onClick={this.saveSort} disabled={readOnly}>
                                    {t('Save Sort', {
                                      ns: ['market', 'common'],
                                    })}
                                  </Button>
                                ) : (
                                  <Button
                                    className="mr-4"
                                    onClick={() => {
                                      this.setState({ sortMode: true });
                                    }}
                                    disabled={readOnly}
                                  >
                                    {t('Sort', { ns: ['market', 'common'] })}
                                  </Button>
                                ))}

                              <Popconfirm
                                placement="left"
                                title={t('Are you sure to delete this record?')}
                                onConfirm={this.delete}
                                disabled={originProducts.length === 0 || readOnly || sortMode}
                              >
                                <Button disabled={originProducts.length === 0 || readOnly || sortMode}>
                                  {t('Clear All')}
                                </Button>
                              </Popconfirm>
                            </div>
                          </div>
                          <ProductAndLiabilityTableNew
                            dataSource={originProducts}
                            loading={loading}
                            draggable={sortMode}
                            totalInterestListMap={this.state.totalInterestListMap}
                            onSortChange={this.handleSortChange}
                          />
                          {/* 新增与编辑的抽屉弹出层 */}
                          <PlanConfigurationDrawer
                            onChoseMainProduct={this.onChoseMainProduct}
                            mainProductList={mainProductList}
                            addedMainProduct={mainProduct}
                            visible={drawerVisible}
                            packageCode={packageCode}
                            packageId={packageId}
                            isEditingLiability={isEditingLiability}
                            editMode={editMode}
                            productLiabilityData={productLiabilityList}
                            originConfiguredProduct={originConfiguredProduct}
                            ridersWithoutWaiver={ridersWithoutWaiver}
                            queryConfigProducts={this.queryConfigProducts}
                            onClose={() => {
                              this.setState({
                                ridersWithoutWaiver: [],
                                mainProductList: [],
                                drawerVisible: false,
                              });
                            }}
                            onSubmit={() => {
                              this.setState({
                                ridersWithoutWaiver: [],
                                drawerVisible: false,
                                mode: 'edit',
                              });
                            }}
                          />
                          <ProductCorrelationMatrixModal
                            visible={this.state.matrixModalVisible}
                            onClose={() => {
                              this.setState({
                                matrixModalVisible: false,
                              });
                            }}
                            fileName={this.state.matrixFileName}
                            setFileName={fileName => {
                              this.setState({
                                matrixFileName: fileName,
                              });
                            }}
                            packageCode={packageCode}
                            packageId={packageId}
                            productId={mainProduct.productId}
                            productCategoryCode={mainProduct.productCategoryCode}
                            productSubCategoryId={mainProduct.productSubCategoryId}
                          />
                        </React.Fragment>
                      )}
                    </div>
                  </React.Fragment>
                )}
              </div>
            </Skeleton>
          </Content>

          <div className="bottom-action-bar">
            {readOnly && (
              <Button size="large" onClick={this.goToNextPage} type="primary">
                {this.props.t('Next')}
              </Button>
            )}
            {this.renderEditSaveBtn()}
            {!readOnly && (
              <Button size="large" onClick={() => this.nextToBenefit(true)} type="default">
                {this.props.t('Next')}
              </Button>
            )}
          </div>
        </div>
      </Layout>
    );
  }
}

export default withTranslation(['market', 'common'])(
  connect(state => {
    const enums = state.enums;
    const permissionMap = selectPermissionCheckMap(state);
    return {
      userId: (state.userInfo || {}).userId,
      envConfig: state.envConfig,
      hasEditAuth: !!permissionMap['market.edit'],
      isSuperUser: !!permissionMap['market.edit-all'],
      productCategory: enums.productCategory || [],
      userInfo: state.userInfo,
    };
  })(PackageBasicConfig)
);
