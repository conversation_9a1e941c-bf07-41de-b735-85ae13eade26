export enum RenewableType {
  GuaranteedRenewable = '1',
  Renewable = '2',
  NotRenewable = '3',
}

export enum DueDateCompareEnum {
  Before = '13',
  SameAs = '14',
  After = '15',
}

export enum CoveragePeriodType {
  ToCertainYear = '5',
  ToCertainAge = '6',
}

export enum FieldType {
  Radio = 'radio',
  InputGroup = 'inputGroup',
  Select = 'select',
  Input = 'input',
  Number = 'number',
  InputGroupRange = 'inputGroupRange',
  InputGroupGuaranteed = 'inputGroupGuaranteed',
}

export enum GracePeriodUnitEnum {
  Days = '1',
  Months = '2',
}

export enum OverdueDateType {
  Lapsed = '1',
  Terminate = '2',
}
