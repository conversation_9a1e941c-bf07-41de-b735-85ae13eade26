import React, { Ref, forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Checkbox, Form, Modal, message } from 'antd';

import { RenewalAgreementBase } from 'genesis-web-service/service-types/market-types/package';

import AgreementContainerLayout from '@market/components/AgreementContainerLayout';

import RenewalAgreementFormNew from './components/RenewalAgreementFormNew/RenewalAgreementFormNew';
import { RenewableType } from './interface';

interface Props {
  disabled: boolean;
  initialValues: RenewalAgreementBase;
  refInstance: Ref<any>;
}

const RenewalConfiguration: React.FC<Props> = ({ disabled, initialValues = {}, refInstance }): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();
  const [allowRenewal, setAllowRenewal] = useState<boolean>(false);
  const [hasChangedStatus, setHasChangedStatus] = useState<boolean>(false);

  useEffect(() => {
    if (initialValues && initialValues.renewableType) {
      setAllowRenewal(
        [RenewableType.Renewable, RenewableType.GuaranteedRenewable].includes(
          String(initialValues.renewableType) as RenewableType
        )
      );
      if (
        initialValues.renewalExtractionPeriodDayBefore !== undefined &&
        initialValues.renewalExtractionPeriodDayAfter !== undefined
      ) {
        initialValues.RenewalExtractionPeriod = [
          initialValues.renewalExtractionPeriodDayBefore,
          initialValues.renewalExtractionPeriodDayAfter,
        ];
      }
      form.setFieldsValue({
        ...initialValues,
        maxGuaranteedRenewableUnit: initialValues.maxGuaranteedRenewableUnit?.toString(),
        renewalEffectiveTriggerCompare: initialValues.renewalEffectiveTriggerCompare?.toString(),
        renewalGracePeriodUnit: initialValues.renewalGracePeriodUnit?.toString(),
        renewalPolicyEffectiveDateRule: initialValues.renewalPolicyEffectiveDateRule?.toString(),
        renewalPolicyGenerateDateCompare: initialValues.renewalPolicyGenerateDateCompare?.toString(),
        productOverdueStatus: initialValues.productOverdueStatus?.toString(),
      });
    }
  }, [initialValues]);

  useImperativeHandle(refInstance, () => ({
    getDataForSubmit: async () => {
      const values: RenewalAgreementBase = await form.validateFields();
      if (!allowRenewal) {
        return {
          validated: true,
          data: {
            renewalAgreement: {
              renewableType: RenewableType.NotRenewable,
            },
          },
        };
      }

      const [renewalExtractionPeriodDayBefore, renewalExtractionPeriodDayAfter] = values?.RenewalExtractionPeriod || [];
      values.RenewalExtractionPeriod = undefined;
      return {
        validated: true,
        data: {
          renewalAgreement: {
            ...values,
            renewalExtractionPeriodDayBefore,
            renewalExtractionPeriodDayAfter,
            renewableType: RenewableType.Renewable,
          },
        },
      };
    },
    allowRenewal,
    setAllowRenewal,
    hasChanged: () => hasChangedStatus,
  }));

  return (
    <AgreementContainerLayout title={t('Renewal Agreement')} agreementCode="renewal">
      <Form
        layout="vertical"
        form={form}
        onValuesChange={() => {
          setHasChangedStatus(true);
        }}
      >
        <Form.Item noStyle>
          <Form.Item valuePropName="checked">
            <Checkbox
              onChange={evt => {
                // 这个字段不在表单里，所以要手动设置
                setHasChangedStatus(true);
                setAllowRenewal(evt.target.checked);
              }}
              disabled={disabled}
              checked={allowRenewal}
            >
              {t('Allow Renewal')}
            </Checkbox>
          </Form.Item>
        </Form.Item>
        {allowRenewal && (
          <React.Fragment>
            <RenewalAgreementFormNew disabled={disabled} />
          </React.Fragment>
        )}
      </Form>
    </AgreementContainerLayout>
  );
};

export default RenewalConfiguration;
