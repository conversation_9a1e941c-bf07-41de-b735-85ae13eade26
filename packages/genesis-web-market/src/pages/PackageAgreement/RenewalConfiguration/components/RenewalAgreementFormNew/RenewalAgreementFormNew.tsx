import { useTranslation } from 'react-i18next';

import { Form, Input, InputNumber, Radio } from 'antd';

import { DateCompareEnum } from 'genesis-web-service/service-types/product-types/package/index';

import { YesOrNo } from '@market/common/enums';
import GeneralSelect from '@market/components/GeneralSelect';
import LabelWithTooltip from '@market/components/LabelWithTooltip';
import NumberRange from '@market/components/NumberRange';
import { useBizDictAsOptions } from '@market/hook/bizDict';

import {
  CoveragePeriodType,
  DueDateCompareEnum,
  FieldType,
  GracePeriodUnitEnum,
  OverdueDateType,
} from '../../interface';
import { renewalEffectiveTriggerCompareRuleRender } from './fieldValidator';

interface Props {
  disabled?: boolean;
}

export const RenewalAgreementFormNew = ({ disabled }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  /* ============== 枚举使用start ============== */
  const coveragePeriodTypeOptions = useBizDictAsOptions('coveragePeriodType');
  const gracePeriodUnitOptions = useBizDictAsOptions('gracePeriodUnit');
  const dueDateCompareOptions = useBizDictAsOptions('dueDateCompare');
  const dateCompareOptions = useBizDictAsOptions('dateCompare');
  const yesNoOptions = useBizDictAsOptions('yesNo', {
    labelKey: 'dictValueName',
    valueKey: 'enumItemName',
  });
  const overdueStatusOptions = useBizDictAsOptions('overdueStatus');
  const renewalPolicyEffectiveDateRuleOptions = useBizDictAsOptions('renewalPolicyEffectiveDateRule');
  /* ============== 枚举使用end ============== */

  const form = Form.useFormInstance();

  return (
    <div className="market-field-wrapper">
      <Form.Item name="maxGuaranteedRenewable" label={t('Max Guaranteed Renewable')}>
        <InputNumber
          placeholder={t('Number')}
          disabled={disabled}
          min={1}
          max={999}
          addonAfter={
            <Form.Item name="maxGuaranteedRenewableUnit" noStyle initialValue={CoveragePeriodType.ToCertainYear}>
              <GeneralSelect
                style={{ width: '140px' }}
                disabled={disabled}
                option={coveragePeriodTypeOptions.filter(
                  item =>
                    item.value.toString() === CoveragePeriodType.ToCertainAge ||
                    item.value.toString() === CoveragePeriodType.ToCertainYear
                )}
                allowClear={false}
              />
            </Form.Item>
          }
        />
      </Form.Item>
      <Form.Item
        label={
          <LabelWithTooltip
            title={t('Renewal Extraction Period')}
            tooltip={t(
              'Define the day before policy expired date and days after expired date to extract renewal policy'
            )}
          />
        }
        name="RenewalExtractionPeriod"
        required
        rules={[
          {
            validator: async (_, value) => {
              if (!value) {
                return Promise.reject(t('Please input'));
              }
              const [min, max] = value;
              if ((min || min === 0) && (max || max === 0)) {
                return Promise.resolve();
              }
              return Promise.reject(t('Please input'));
            },
          },
        ]}
      >
        <NumberRange
          disabled={disabled}
          extraProps={[{}, { addonAfter: t('Day(s)') }]}
          className={FieldType.InputGroupRange}
          autoChangeMinMax={false}
        />
      </Form.Item>
      <Form.Item
        name="autoDeductionAdvanceDays"
        label={
          <LabelWithTooltip
            title={t('Auto Deduction Date')}
            tooltip={t(
              'Deduction Date Compare to renewal Policy effective date.e.g. if deduct on effective date, enter 0 if deduct prior to effective date, enter 1, 2, 3 etc.'
            )}
          />
        }
      >
        <InputNumber min={0} disabled={disabled} placeholder={t('Number')} addonAfter={t('Day(s)')} />
      </Form.Item>
      <Form.Item name="renewalPolicyEffectiveDateRule" label={t('Renewal Policy Effective Date Rule')}>
        <GeneralSelect
          option={renewalPolicyEffectiveDateRuleOptions}
          disabled={disabled}
          placeholder={t('Please select')}
          allowClear
        />
      </Form.Item>
      <div className="form-item-column-2">
        <Form.Item label={t('Offset from X Days Compare to Expired Date')} required>
          <Input.Group compact>
            <Form.Item
              key="1"
              name="renewalEffectiveTriggerCompare"
              noStyle
              validateTrigger="onChange"
              rules={[renewalEffectiveTriggerCompareRuleRender(t('Please select'), t('Please input'))]}
            >
              <GeneralSelect
                option={dueDateCompareOptions}
                disabled={disabled}
                allowClear={false}
                placeholder={t('Please select')}
                style={{ width: 'calc((100% - 40px) / 2)' }}
                onChange={value => {
                  form.validateFields(['renewalEffectiveTriggerPeriodDay']);
                  if (value === DueDateCompareEnum.SameAs) {
                    form.setFieldValue('renewalEffectiveTriggerPeriodDay', undefined);
                  }
                }}
              />
            </Form.Item>
            <Form.Item key="2" shouldUpdate={() => true} noStyle>
              {({ getFieldValue }) => (
                <Form.Item
                  key="3"
                  name="renewalEffectiveTriggerPeriodDay"
                  noStyle
                  validateTrigger="onChange"
                  rules={[renewalEffectiveTriggerCompareRuleRender('', '')]}
                >
                  <InputNumber
                    min={0}
                    disabled={disabled || getFieldValue('renewalEffectiveTriggerCompare') === DueDateCompareEnum.SameAs}
                    placeholder={t('Number')}
                    addonAfter={t('Day(s)')}
                    onChange={() => {
                      form.validateFields(['renewalEffectiveTriggerCompare']);
                    }}
                  />
                </Form.Item>
              )}
            </Form.Item>
          </Input.Group>
        </Form.Item>
      </div>
      <div className="form-item-row-full">
        <Form.Item
          name="renewalPolicyEffectiveWithoutCollection"
          label={t('Renewal Policy Effective Without Collection')}
          required
          rules={[
            {
              required: true,
              message: t('Please select'),
            },
          ]}
        >
          <Radio.Group>
            {yesNoOptions.map(option => (
              <Radio value={option.value} disabled={disabled as boolean}>
                {option.label}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
      </div>
      <div className="form-item-column-2">
        <Form.Item
          label={
            <LabelWithTooltip
              title={t('Renewal Proposal Submit Date')}
              tooltip={t(
                'Define auto submit time for renewal proposal by comparing with previous policy expired date. If not configured, a manual submit is needed.'
              )}
            />
          }
        >
          <Input.Group compact>
            <Form.Item key="1" noStyle name="renewalPolicyGenerateDateCompare">
              <GeneralSelect
                option={dateCompareOptions}
                disabled={disabled}
                placeholder={t('Please select')}
                allowClear
                style={{ width: 'calc((100% - 40px) / 2)' }}
                onChange={value => {
                  if (value === DateCompareEnum.SAME_AS) {
                    form.setFieldValue('renewalPolicyGenerateDateDays', undefined);
                  }
                }}
              />
            </Form.Item>
            <Form.Item key="2" shouldUpdate={() => true} noStyle>
              {({ getFieldValue: getFieldValueInner }) => (
                <Form.Item key="3" noStyle name="renewalPolicyGenerateDateDays">
                  <InputNumber
                    min={0}
                    disabled={
                      disabled || getFieldValueInner('renewalPolicyGenerateDateCompare') === DateCompareEnum.SAME_AS
                    }
                    placeholder={t('Number')}
                    addonAfter={t('Day(s)')}
                  />
                </Form.Item>
              )}
            </Form.Item>
          </Input.Group>
        </Form.Item>
      </div>
      <div className="form-item-row-full">
        <Form.Item
          name="generateNewPolicyNumberForRenewal"
          label={t('Generate New Policy Number For Renewal')}
          required
          rules={[
            {
              required: true,
              message: t('Please select'),
            },
          ]}
        >
          <Radio.Group>
            {yesNoOptions.map(option => (
              <Radio value={option.value} disabled={disabled as boolean}>
                {option.label}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
      </div>
      <Form.Item
        name="renewalGracePeriod"
        label={t('Renewal Grace Period')}
        required
        rules={[
          {
            required: true,
            message: t('Please input'),
          },
        ]}
      >
        <InputNumber
          placeholder={t('Number')}
          disabled={disabled}
          min={1}
          max={999}
          addonAfter={
            <Form.Item name="renewalGracePeriodUnit" noStyle initialValue={GracePeriodUnitEnum.Days}>
              <GeneralSelect
                style={{ width: '140px' }}
                disabled={disabled}
                option={gracePeriodUnitOptions}
                allowClear={false}
              />
            </Form.Item>
          }
        />
      </Form.Item>
      <div className="form-item-row-full">
        <Form.Item
          name="productOverdueStatus"
          label={t('Product Overdue Status')}
          required
          rules={[
            {
              required: true,
              message: t('Please select'),
            },
          ]}
        >
          <Radio.Group>
            {overdueStatusOptions
              .filter(
                item =>
                  item.value.toString() === OverdueDateType.Lapsed ||
                  item.value.toString() === OverdueDateType.Terminate
              )
              .map(option => (
                <Radio value={option.value} disabled={disabled as boolean}>
                  {option.label}
                </Radio>
              ))}
          </Radio.Group>
        </Form.Item>
      </div>
      <div className="form-item-row-full">
        <Form.Item
          name="renewalOverdueAutoDeduction"
          label={t('Overdue Auto Deduction')}
          initialValue={YesOrNo.NO}
          required
          rules={[
            {
              required: true,
              message: t('Please select'),
            },
          ]}
        >
          <Radio.Group>
            {yesNoOptions.map(option => (
              <Radio value={option.value} disabled={disabled as boolean}>
                {option.label}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
      </div>
    </div>
  );
};

export default RenewalAgreementFormNew;
