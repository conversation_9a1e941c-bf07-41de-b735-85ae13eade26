import { RuleRender } from 'antd/es/form';

import { DueDateCompareEnum } from '../../interface';

export const renewalEffectiveTriggerCompareRuleRender =
  (errorMsg: string, errorMsg2: string): RuleRender =>
  form => ({
    validator: async () => {
      const renewalEffectiveTriggerCompare = form.getFieldValue('renewalEffectiveTriggerCompare');
      const renewalEffectiveTriggerPeriodDay = form.getFieldValue('renewalEffectiveTriggerPeriodDay');

      if (!renewalEffectiveTriggerCompare) {
        return Promise.reject(errorMsg);
      }

      if (renewalEffectiveTriggerCompare !== DueDateCompareEnum.SameAs && !renewalEffectiveTriggerPeriodDay) {
        return Promise.reject(errorMsg2);
      }

      return Promise.resolve();
    },
  });
