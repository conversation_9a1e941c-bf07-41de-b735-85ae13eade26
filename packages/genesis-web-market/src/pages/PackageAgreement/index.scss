.package-agreement-container.market-ant4-layout {
  height: 100%;
  overflow: hidden;
  .market-content.market-ant4-layout-content {
    .right-content-wrapper {
      background-color: transparent !important;
    }
  }
  .effective-date-rule {
    .market-ant4-row.market-ant4-legacy-form-item {
      margin-bottom: 9px;
    }
    .backdating-input-wrapper {
      .market-ant4-row.market-ant4-legacy-form-item {
        display: inline-block !important;
      }
      .input-group-value {
        border-bottom-right-radius: 0;
        border-top-right-radius: 0;
      }
    }
    .effective-date-rule-radio-group {
      .market-ant4-radio-wrapper {
        display: block;
      }
      .market-ant4-radio-wrapper + .market-ant4-radio-wrapper {
        margin-top: 16px;
      }
    }
    .effective-date-rule-tips {
      margin-top: 5px;
      margin-bottom: 0;
      font-size: 12px;
      color: var(--text-color-tertiary);
    }
    .effective-date-rule-enhancement {
      .market-ant4-radio-wrapper + .market-ant4-radio-wrapper {
        margin-top: 0;
      }
    }
  }
}
.market-ant4-drawer-content {
  .market-drawer {
    padding-bottom: 72px;
    overflow: auto;
  }
  .drawer-foot {
    width: 1024px;
    position: fixed;
    height: 72px;
  }
}

// 往user-input radio中插入checkbox，下面间距太大，去掉下边距
.effective-date-user-input.market-ant4-radio-wrapper::after {
  display: none;
}
