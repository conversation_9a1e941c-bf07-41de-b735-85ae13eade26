import { Ref, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, InputNumber, Row, Space } from 'antd';

import classNames from 'classnames/bind';

import { VehicleMarketValueResponse } from 'genesis-web-service/service-types/product-types/package/index';

import { BizTopic } from '@market/common/enums';
import AgreementContainerLayout from '@market/components/AgreementContainerLayout';
import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { useFormulaList } from '@market/hook/product.service';
import { formErrorHandler } from '@market/utils/formUtils';

import styles from './VehicleRelatedConfiguration.module.scss';

const cx = classNames.bind(styles);

interface Props {
  disabled: boolean;
  initialValues: VehicleMarketValueResponse;
  refInstance: Ref<any>;
}
export const VehicleRelatedConfiguration = (props: Props) => {
  const { initialValues, disabled, refInstance } = props;
  const [t] = useTranslation(['market', 'common']);
  const [hasChangedStatus, setHasChangedStatus] = useState<boolean>(false);
  const [form] = Form.useForm();
  const bizTopicOptions = useBizDictAsOptions('bizTopic');

  const formulaList = useFormulaList({
    formulaCategoryCode: +BizTopic.VehicleMarketValue,
    isIncludeCommonTemplate: true,
  });

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue({
        floatUpAdjustedVehicleMarketValueRatio: initialValues?.floatUpAdjustedVehicleMarketValueRatio,
        floatDownAdjustedVehicleMarketValueRatio: initialValues?.floatDownAdjustedVehicleMarketValueRatio,
        vehicleMarketValueFormula: initialValues?.vehicleMarketValueFormula?.formulaCode,
      });
    }
    form.setFieldsValue({
      formulaType: BizTopic.VehicleMarketValue,
    });
  }, [initialValues]);

  useImperativeHandle(refInstance, () => ({
    getDataForSubmit: () =>
      form
        .validateFields()
        .then((values: VehicleMarketValueResponse) => ({
          validated: true,
          data: {
            vehicleMarketValueAgreement: values,
          },
        }))
        .catch(formErrorHandler(form)),
    hasChanged: () => hasChangedStatus,
  }));

  return (
    <AgreementContainerLayout title={t('Vehicle Related Configuration')} agreementCode="vehicleRelatedConfiguration">
      <Form
        onValuesChange={() => {
          setHasChangedStatus(true);
        }}
        form={form}
        colon={false}
        layout="vertical"
      >
        <div className="agreement-detail-block">
          <Row style={{ marginTop: 8 }}>
            <Col span={24}>
              <Space.Compact>
                <Form.Item label={t('Vehicle Market Value Formula')} required={hasChangedStatus}>
                  <GeneralSelect value={BizTopic.VehicleMarketValue} option={bizTopicOptions} disabled />
                </Form.Item>
                <Form.Item
                  name="vehicleMarketValueFormula"
                  label=" "
                  required={false}
                  rules={
                    hasChangedStatus
                      ? [
                          {
                            required: true,
                            message: t('Please select'),
                          },
                        ]
                      : undefined
                  }
                >
                  <GeneralSelect
                    option={formulaList.map(formula => ({
                      label: formula.formulaCode,
                      value: formula.formulaCode,
                    }))}
                    disabled={disabled}
                  />
                </Form.Item>
              </Space.Compact>
            </Col>
            <Col span={24}>
              <div className={cx('tip-box')}>{t('Adjusted Market Value Floating Ratio')}</div>
            </Col>
            <Col span={8}>
              <Form.Item
                colon={false}
                label={t('Max Up-Regulation Ratio')}
                name="floatUpAdjustedVehicleMarketValueRatio"
              >
                <InputNumber
                  style={{ width: 240 }}
                  min={0}
                  placeholder={t('Please input')}
                  disabled={disabled}
                  onChange={() => setHasChangedStatus(true)}
                  addonAfter="%"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                colon={false}
                label={t('Max Down-Regulation Ratio')}
                name="floatDownAdjustedVehicleMarketValueRatio"
              >
                <InputNumber
                  style={{ width: 240 }}
                  min={0}
                  placeholder={t('Please input')}
                  disabled={disabled}
                  onChange={() => setHasChangedStatus(true)}
                  addonAfter="%"
                />
              </Form.Item>
            </Col>
          </Row>
        </div>
      </Form>
    </AgreementContainerLayout>
  );
};

export default VehicleRelatedConfiguration;
