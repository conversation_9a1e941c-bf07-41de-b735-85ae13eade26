/* eslint-disable @typescript-eslint/restrict-template-expressions */
import { Ref, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';

import { Table } from '@zhongan/nagrand-ui';

import { ProductInfoValue } from 'genesis-web-service';

import { PackageProductPaymentMethodInfo } from '@market/common/interface';
import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';

interface Props {
  riderList: ProductInfoValue[];
  refInstance: Ref<unknown>;
  packageId: number;
  disabled: boolean;
  packageProductPaymentMethodResponseList: PackageProductPaymentMethodInfo[];
}

export const RiderPremiumPaymentMethod = ({
  riderList,
  refInstance,
  packageId,
  disabled,
  packageProductPaymentMethodResponseList,
}: Props) => {
  const [form] = Form.useForm();
  /* ============== 枚举使用start ============== */
  const ilpPaymentMethodOptions = useBizDictAsOptions('ilpPaymentMethod');
  /* ============== 枚举使用end ============== */
  const [t] = useTranslation(['market', 'common']);
  const [riderPremiumPaymentMethodList, setRiderPremiumPaymentMethodList] = useState<PackageProductPaymentMethodInfo[]>(
    []
  );

  useEffect(() => {
    setRiderPremiumPaymentMethodList(
      riderList.map(productItem => ({
        packageId,
        productId: productItem.productId,
        paymentMethod: packageProductPaymentMethodResponseList?.find(item => item.productId === productItem.productId)
          ?.paymentMethod,
      }))
    );
  }, [packageId, packageProductPaymentMethodResponseList, riderList]);

  useImperativeHandle(refInstance, () => ({
    getFormChangedData: form.validateFields,
    getTableChangedData: (): PackageProductPaymentMethodInfo[] => riderPremiumPaymentMethodList,
  }));

  const columns = [
    {
      title: t('Rider Code'),
      dataIndex: 'insuranceProductCode',
      key: 'insuranceProductCode',
    },
    {
      title: t('Rider Name'),
      dataIndex: 'productName',
      key: 'productName',
    },
    {
      title: t('Rider Premium Payment Method'),
      dataIndex: 'paymentMethod',
      key: 'paymentMethod',
      render: (text: string, record: ProductInfoValue, index: number) => (
        <Form.Item
          label={null}
          style={{ marginBottom: 0 }}
          name={`paymentMethod${index}`}
          initialValue={
            packageProductPaymentMethodResponseList.find(item => item.productId === record.productId)?.paymentMethod
              ? `${
                  packageProductPaymentMethodResponseList.find(item => item.productId === record.productId)
                    ?.paymentMethod
                }`
              : undefined
          }
          rules={[{ required: true, message: t('Please select') }]}
        >
          <GeneralSelect
            allowClear={false}
            disabled={disabled}
            style={{ width: '240px' }}
            placeholder={t('Please select')}
            onChange={(value: string) => {
              riderPremiumPaymentMethodList[index].paymentMethod = value;
            }}
            option={ilpPaymentMethodOptions}
            getPopupContainer={() => document.body}
          />
        </Form.Item>
      ),
    },
  ];

  return (
    <div className="w-full mb-6">
      <div className="market-ant4-legacy-form-item-required" style={{ marginBottom: 8 }}>
        {t('Rider Premium Payment Method')}
      </div>
      <Table columns={columns} dataSource={riderList} pagination={false} scroll={{ y: 268 }} emptyType="text" />
    </div>
  );
};

export default RiderPremiumPaymentMethod;
