/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import React, { Key, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, FormInstance, InputNumber, Radio, Row } from 'antd';
import { StoreValue } from 'antd/es/form/interface';

import { pick } from 'lodash-es';

import { PackagePeriodRule } from '@market/common/enums';
import { SelectOption } from '@market/common/interface';
import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { PaymentDeferPeriodTypeEnum, PaymentFrequencyBaseDict, PaymentPeriodTypeEnum } from '@market/request/interface';

interface Props {
  disabled: boolean;
  form: FormInstance;
  participatingProductData: Record<string, number>;
}

export const AnnuityConfiguration = ({ form, disabled, participatingProductData }: Props) => {
  /* ============== 枚举使用start ============== */
  const packagePeriodRuleOptions = useBizDictAsOptions('packagePeriodRule');
  const survivalPaymentDrawTypeOptions = useBizDictAsOptions('survivalPaymentDrawType');
  const paymentFrequencyBaseOptions = useBizDictAsOptions('paymentFrequencyBase');
  const paymentDeferPeriodTypeOptions = useBizDictAsOptions('paymentDeferPeriodType');
  const paymentPeriodTypeOptions = useBizDictAsOptions('paymentPeriodType');
  const yesNoOptions = useBizDictAsOptions('yesNo', {
    labelKey: 'itemName',
    valueKey: 'enumItemName',
  });
  /* ============== 枚举使用end ============== */
  const [t] = useTranslation(['market', 'common']);

  useEffect(() => {
    const annuityConfigurationFormValue = Object.fromEntries(
      Object.entries(participatingProductData).map(([key, value]) => [key, value?.toString()])
    );
    form.setFieldsValue({
      ...pick(annuityConfigurationFormValue, [
        'annuityPaymentOption',
        'annuityPaymentFrequency',
        'annuityPaymentDeferPeriodType',
        'annuityPaymentPeriodType',
        'annuityPaymentPeriod',
        'annuityWithGuaranteePeriodType',
        'annuityWithGuaranteePeriod',
      ]),
      ...pick(participatingProductData, [
        'annuityPaymentPeriod',
        'annuityPaymentDeferPeriod',
        'annuityWithGuaranteePeriod',
      ]),
      annuityRule: participatingProductData.annuityRule || PackagePeriodRule.FollowEachProduct,
    });
  }, [participatingProductData]);

  const renderCustomFields = useCallback(
    (getFieldValue: (name: any) => StoreValue) => (
      <React.Fragment>
        <Col span={8}>
          <Form.Item
            label={t('Payment Option')}
            name="annuityPaymentOption"
            rules={[
              {
                required: true,
                message: t('Please select'),
              },
            ]}
          >
            <GeneralSelect style={{ width: '240px' }} option={survivalPaymentDrawTypeOptions} disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={16}>
          <Form.Item
            label={t('Payment Frequency')}
            name="annuityPaymentFrequency"
            rules={[
              {
                required: true,
                message: t('Please select'),
              },
            ]}
          >
            <GeneralSelect
              style={{ width: '240px' }}
              option={paymentFrequencyBaseOptions.filter((bizDict: SelectOption) =>
                [
                  PaymentFrequencyBaseDict.Yearly,
                  PaymentFrequencyBaseDict.SemiYearly,
                  PaymentFrequencyBaseDict.Quarterly,
                  PaymentFrequencyBaseDict.Monthly,
                ].includes(bizDict.value as PaymentFrequencyBaseDict)
              )}
              disabled={disabled}
            />
          </Form.Item>
        </Col>
        <Row className="w-full">
          <Col span={8}>
            <Form.Item
              label={t('Payment Defer Period Type')}
              name="annuityPaymentDeferPeriodType"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <GeneralSelect
                style={{ width: '240px' }}
                option={paymentDeferPeriodTypeOptions.filter((bizDict: SelectOption) =>
                  [PaymentDeferPeriodTypeEnum.Immediate, PaymentDeferPeriodTypeEnum.DeferredFixedPolicyYear].includes(
                    bizDict.value as PaymentDeferPeriodTypeEnum
                  )
                )}
                disabled={disabled}
              />
            </Form.Item>
          </Col>
          {getFieldValue('annuityPaymentDeferPeriodType') === PaymentDeferPeriodTypeEnum.DeferredFixedPolicyYear ? (
            <Col span={16}>
              <Form.Item
                label={t('Defer Period')}
                name="annuityPaymentDeferPeriod"
                rules={[
                  {
                    required: true,
                    message: t('Please input'),
                  },
                ]}
              >
                <InputNumber style={{ width: '240px' }} placeholder={t('Please input')} />
              </Form.Item>
            </Col>
          ) : null}
        </Row>
        <Row className="w-full">
          <Col span={8}>
            <Form.Item
              label={t('Payment Period Type')}
              name="annuityPaymentPeriodType"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <GeneralSelect style={{ width: '240px' }} option={paymentPeriodTypeOptions} disabled={disabled} />
            </Form.Item>
          </Col>
          {[PaymentPeriodTypeEnum.ForCertainYear, PaymentPeriodTypeEnum.UpToCertainAge].includes(
            getFieldValue('annuityPaymentPeriodType')
          ) ? (
            <Col span={16}>
              <Form.Item
                label={t('Payment Period')}
                name="annuityPaymentPeriod"
                rules={[
                  {
                    required: true,
                    message: t('Please input'),
                  },
                ]}
              >
                <InputNumber style={{ width: '240px' }} placeholder={t('Please input')} />
              </Form.Item>
            </Col>
          ) : null}
        </Row>
        {[PaymentPeriodTypeEnum.WholeLife, PaymentPeriodTypeEnum.UpToCertainAge].includes(
          getFieldValue('annuityPaymentPeriodType')
        ) ? (
          <Col span={8}>
            <Form.Item
              label={t('With Guarantee Period')}
              name="annuityWithGuaranteePeriodType"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <GeneralSelect style={{ width: '240px' }} option={yesNoOptions} disabled={disabled} />
            </Form.Item>
          </Col>
        ) : null}
        {[PaymentPeriodTypeEnum.WholeLife, PaymentPeriodTypeEnum.UpToCertainAge].includes(
          getFieldValue('annuityPaymentPeriodType')
        ) && getFieldValue('annuityWithGuaranteePeriodType') === 'YES' ? (
          <Col span={16}>
            <Form.Item
              label={t('Guarantee Period')}
              name="annuityWithGuaranteePeriod"
              rules={[
                {
                  required: true,
                  message: t('Please input'),
                },
              ]}
            >
              <InputNumber style={{ width: '240px' }} placeholder={t('Please input')} />
            </Form.Item>
          </Col>
        ) : null}
      </React.Fragment>
    ),
    [
      participatingProductData,
      form,
      yesNoOptions,
      survivalPaymentDrawTypeOptions,
      paymentFrequencyBaseOptions,
      paymentDeferPeriodTypeOptions,
      paymentPeriodTypeOptions,
    ]
  );

  return (
    <Row>
      <Col span={24}>
        <Form.Item
          label={t('Annuity Configuration')}
          name="annuityRule"
          rules={[
            {
              required: true,
              message: t('Please select'),
            },
          ]}
        >
          <Radio.Group disabled={disabled}>
            {packagePeriodRuleOptions.map(j => (
              <Radio key={j.value as Key} value={+j.value}>
                {j.label}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
      </Col>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) =>
          getFieldValue('annuityRule') === PackagePeriodRule.UnifiedRules ? renderCustomFields(getFieldValue) : null
        }
      </Form.Item>
    </Row>
  );
};

export default AnnuityConfiguration;
