import React, { Component } from 'react';

import { Button, message } from 'antd';

import { Drawer, Icon, Table } from '@zhongan/nagrand-ui';

import { t } from '@market/i18n';
import { NewMarketService } from '@market/services/market/market.service.new';

import AddPaymentPeriodDrawer from './InsurancePaymentPeriodAddDrawer';

let insurancePaymentPeriodDrawer;
class InsurancePaymentPeriodDrawer extends Component {
  static open() {
    if (insurancePaymentPeriodDrawer) {
      insurancePaymentPeriodDrawer.setState({
        visible: true,
      });
    }
    insurancePaymentPeriodDrawer.setInitData();
  }

  constructor(props) {
    super(props);
    insurancePaymentPeriodDrawer = this;

    this.state = {
      visible: false,
      addDrawerVisible: false,
      selectedRowKeys: [],
      data: [],
    };
  }

  close = () => {
    this.setState({
      visible: false,
      selectedRowKeys: [],
      data: [],
    });
  };

  submitAgreement = () => {
    const { data } = this.props;
    const { selectedRowKeys } = this.state;
    const params = {
      packageId: data.packageId,
      customizeCode: data.customizeCode,
      paymentPeriods: this.state.data.map(item => ({
        ...item,
        isAdded: selectedRowKeys.includes(item.index),
      })),
    };
    NewMarketService.PackageAgreementConfigMgmtService.savePayment(params).then(res => {
      if (res.success) {
        this.close();
        this.props.onRefresh && this.props.onRefresh();
      }
    });
  };

  setInitData = () => {
    const { data } = this.props;
    if (!data.paymentPeriods) {
      return;
    }
    const tableData = data.paymentPeriods.filter(i => i.isAdded);
    this.setState({
      data: data.paymentPeriods,
      selectedRowKeys: tableData.map(i => i.index),
    });
  };

  showAddDrawer = visible => {
    this.setState({
      addDrawerVisible: visible,
    });
  };

  isDuplicate = (data1, data2) => {
    if (+data1.agreementType === +data2.premiumPeriodType) {
      // Whole Life 和 Single Premium
      if ([3, 4].includes(+data1.agreementType)) {
        return true;
      }
      // 检测 Value 是否相等
      if (+data1.valueType === +data2.valueType && +data2.valueType === 2) {
        return +data1.elementValue === +data2.premiumPeriodValue;
      } else if (
        // 检测 Range 是否相等
        +data1.valueType === +data2.valueType &&
        +data2.valueType === 1
      ) {
        return +data1.minPeriod === +data2.premiumPeriodRange[0] && +data1.maxPeriod === +data2.premiumPeriodRange[1];
      }
    }
    return false;
  };

  addPremiumPeriodItem = periodItem => {
    // 检测新增的数据是否跟已有的重复
    if (this.state.data.find(item => this.isDuplicate(item, periodItem))) {
      message.error(t('Duplicated data exists'));
      return false;
    }

    const newDataLen = this.state.data.length;

    this.setState({
      addDrawerVisible: false,
      // 将新增的那条数据自动否选上
      selectedRowKeys: [...this.state.selectedRowKeys, newDataLen],
      data: [
        ...this.state.data,
        {
          agreementType: periodItem.premiumPeriodType,
          elementValue: periodItem.premiumPeriodValue,
          valueType: periodItem.valueType,
          premiumEndDateCalculationRule: periodItem.premiumEndDateCalculationRule,
          minPeriod: periodItem.premiumPeriodRange?.[0],
          maxPeriod: periodItem.premiumPeriodRange?.[1],
          isAdded: false,
          index: newDataLen,
        },
      ],
    });
  };

  render() {
    const { selectedRowKeys, visible, addDrawerVisible, data } = this.state;
    const { columns, packageCategory } = this.props;
    const rowSelection = {
      selectedRowKeys,
      onChange: (selectedRowKey, selectedRows) => {
        this.setState({
          selectedRowKeys: selectedRowKey,
        });
      },
    };

    return (
      <React.Fragment>
        <Drawer
          open={visible}
          title={t('Premium Period')}
          onClose={this.close}
          onSubmit={this.submitAgreement}
          sendText={t('Confirm')}
        >
          <div className="flex justify-between mb-md font-bold">
            {t('Select from the premium periods in the associated products, or click "Add New" to create a new one.')}
            <Button
              icon={<Icon type="add" />}
              disabled={false}
              onClick={() => this.showAddDrawer(true)}
              className="long-add-btn"
            >
              {t('Add New')}
            </Button>
          </div>
          <Table
            rowKey="index"
            pagination={false}
            rowSelection={rowSelection}
            columns={columns}
            scroll={{ y: 540 }}
            dataSource={data}
          />
        </Drawer>

        <AddPaymentPeriodDrawer
          addDrawerVisible={addDrawerVisible}
          setAddDrawerVisible={() => this.showAddDrawer(false)}
          packageCategory={packageCategory}
          addPremiumPeriodItem={this.addPremiumPeriodItem}
          openEndPolicy={this.props.openEndPolicy}
        />
      </React.Fragment>
    );
  }
}

export default InsurancePaymentPeriodDrawer;
