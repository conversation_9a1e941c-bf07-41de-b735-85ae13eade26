import { Ref, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { MinusCircleOutlined } from '@ant-design/icons';
import { Button, Checkbox, Col, Divider, Form, Input, Radio, Row, Space, Tooltip, message } from 'antd';

import { Drawer, Icon } from '@zhongan/nagrand-ui';

import GeneralSelect from '@market/components/GeneralSelect';
import MoreButton from '@market/components/MoreButton/MoreButton';
import { useBizDict, useBizDictAsOptions } from '@market/hook/bizDict';
import { selectTenantTimeFormatByZeus } from '@market/redux/selector';
import { formErrorHandler } from '@market/utils/formUtils';

import { convertDefaultTimeForDisplay, convertDefaultTimeForSubmit } from './defaultTime';
import styles from './styles.module.scss';

// Note: Effective Date Rule 有两套数据模型，仅有一条数据时走老数据模型，二条及以上走新模型

const INIT_FORM_VALUE = {
  effectiveDateRule: undefined,
  enableDefaultTime: undefined,
  defaultTime: undefined,
  defaultTimeEditable: undefined,
  effectiveDateDefinedFactor: undefined,
};

export interface EffectiveDateRuleDrawerForm {
  enableDefaultTime: boolean;
  defaultTime: string;
  defaultTimeEditable: boolean;
  effectiveDateRule?: string;
  effectiveDateDefinedFactor?: string;
}

export const enum ProductEffectiveDateRule {
  EffectiveImmediately = '2',
  UserInput = '3',
  EffectiveFromPreviousFixedDueDate = '4',
  BasedOnDefinedApplicationElements = '9',
  EffectiveFromCollectionDate = '12',
  EffectiveFromTheLaterOfUserInputDateTimeAndCollectionDateTime = '13',
  EffectiveFromNextMonthOfInsureDate = '15',
  NextDayWithTime = '16',
  NextMonthWithTime = '17',
  EffectiveFromNextMonthOfCollection = '18',
  EffectiveFromInsureDateWithTime = '19',
  EffectiveFromNextDayOfInsureDateWithTime = '20',
  EffectiveFromEarliestEffectiveDateOfPolicyProducts = '21',
}

export interface EffectiveDateRuleFormValues {
  rules: Array<EffectiveDateRuleDrawerForm>; // 老数据兼容
  effectiveDateRule: {
    condition: string;
    rules: Array<EffectiveDateRuleDrawerForm>;
  };
  effectiveDateDefinedFactor?: string;
  enableDefaultTime?: boolean;
  defaultTimeEditable?: boolean;
  defaultTime?: string;
}

interface Props {
  disabled: boolean;
  condition: string;
  rules: Array<EffectiveDateRuleDrawerForm>;
  refInstance: Ref<any>;
  isTravel?: boolean;
  onEffectiveDateRulesChange: (rules: Array<EffectiveDateRuleDrawerForm>) => void;
}

export const EffectiveDateRule = ({
  disabled,
  condition,
  rules,
  refInstance,
  isTravel = false,
  onEffectiveDateRulesChange,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();
  const [drawerForm] = Form.useForm();
  /* ============== 枚举使用start ============== */
  const effectiveDateRuleBizDict = useBizDict('techProductEffectiveDateRule');
  const coveragePeriodBasedOnApplicationElementsOptions = useBizDictAsOptions(
    'coveragePeriodBasedOnApplicationElements'
  );
  const yesNoOptions = useBizDictAsOptions('yesNo').map(option => {
    option.value = (option.label === 'Yes') as unknown as string;
    return option;
  });
  /* ============== 枚举使用end ============== */
  const tenantTimeFormatByZeus = useSelector(selectTenantTimeFormatByZeus);
  const [open, setOpen] = useState(false);
  const [drawerFormName, setDrawerFormName] = useState(-1);

  useImperativeHandle(refInstance, () => ({
    getDataForSubmit: async () => {
      const formValues: EffectiveDateRuleFormValues & {
        validated: boolean;
      } = await form.validateFields().catch(formErrorHandler(form));
      if (formValues?.rules?.length === 1) {
        if (
          formValues.rules[0].effectiveDateRule === ProductEffectiveDateRule.BasedOnDefinedApplicationElements &&
          !formValues.rules[0].effectiveDateDefinedFactor
        ) {
          message.warning('Effective Date Rule is incomplete. Defined Application Elements is required.');
          return;
        }
      }

      return {
        validated: formValues.validated !== false,
        data: {
          effectiveDateRule: {
            ...formValues,
            effectiveDateRule: formValues.rules[0]?.effectiveDateRule?.toString(),
          },
        },
      };
    },
    hasChanged: () => true,
  }));

  const effectiveDateRuleOptions = useMemo(
    () =>
      effectiveDateRuleBizDict
        .filter(option =>
          isTravel ? true : option.dictValue !== ProductEffectiveDateRule.BasedOnDefinedApplicationElements
        )
        .sort((a, b) => (a?.orderNo ?? 0) - (b?.orderNo ?? 0))
        .map(optionItem => ({
          value: optionItem.dictValue,
          label: optionItem.dictValueName,
        })),
    [effectiveDateRuleBizDict, isTravel]
  );

  const resetDrawer = () => {
    setOpen(false);
    setDrawerFormName(-1);
    drawerForm.resetFields();
  };

  useEffect(() => {
    if (condition) {
      form.setFieldValue(['condition'], condition);
    }
    if (rules) {
      form.setFieldValue(
        ['rules'],
        rules.map(item => ({
          ...item,
          effectiveDateRule: item.effectiveDateRule?.toString(),
          effectiveDateDefinedFactor: item.effectiveDateDefinedFactor?.toString(),
        }))
      );
    }
  }, [condition, rules, form]);

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        condition: 'LATEST_OF',
        rules: [INIT_FORM_VALUE],
      }}
      onValuesChange={(changedValues, values) => {
        onEffectiveDateRulesChange?.(values.rules);
      }}
    >
      <Row>
        <Form.Item noStyle shouldUpdate>
          {({ getFieldValue, setFieldValue }) => {
            const rulesValue = getFieldValue(['rules']) as string[];
            const isEarliest = getFieldValue(['condition']) === 'EARLIEST_OF';
            if (rulesValue?.length > 1) {
              return (
                <Col
                  flex="122px"
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    marginRight: 16,
                  }}
                >
                  <Divider type="vertical" style={{ height: 'calc(100% - 56px)', marginTop: 25 }} />
                  <Form.Item noStyle name="condition" />
                  <Button
                    type="primary"
                    disabled={disabled}
                    style={{
                      fontWeight: 800,
                      position: 'absolute',
                      top: 'calc(50% - 20px)',
                      color: disabled ? 'var(--text-disabled-color)' : 'var(--menu-item-hover-color)',
                      backgroundColor: disabled ? 'var(--primary-disabled-color)' : 'var(--info-color-bg)',
                    }}
                    onClick={() => {
                      setFieldValue(['condition'], isEarliest ? 'LATEST_OF' : 'EARLIEST_OF');
                    }}
                  >
                    {isEarliest ? t('EARLIEST OF') : t('LATEST OF')}
                  </Button>
                </Col>
              );
            }
            return null;
          }}
        </Form.Item>
        <Col flex="auto">
          <Form.List name="rules">
            {(fields, { add, remove }) => (
              <>
                <div style={{ marginBottom: 4, display: 'flex' }}>
                  <div style={{ width: 588 }}>
                    <span className="text-[var(--error-color)] mr-1">*</span>
                    {t('Effective Date Rule')}
                  </div>
                  <Form.Item noStyle shouldUpdate>
                    {({ getFieldValue }) =>
                      getFieldValue(['rules', 0, 'effectiveDateRule']) ===
                        ProductEffectiveDateRule.BasedOnDefinedApplicationElements && (
                        <div className="market-ant4-legacy-form-item-required">{t('Defined Application Elements')}</div>
                      )
                    }
                  </Form.Item>
                </div>
                <Form.Item noStyle shouldUpdate>
                  {({ getFieldValue }) => (
                    <>
                      <Space direction="vertical">
                        {fields.map(({ key, name, ...restField }) => {
                          const defaultTimeEditableValue = getFieldValue(['rules', name, 'defaultTimeEditable']) as
                            | boolean
                            | undefined;
                          const effectiveDateRuleValue = getFieldValue([
                            'rules',
                            name,
                            'effectiveDateRule',
                          ]) as ProductEffectiveDateRule;
                          const showMoreButton = ![
                            ProductEffectiveDateRule.EffectiveImmediately,
                            ProductEffectiveDateRule.EffectiveFromEarliestEffectiveDateOfPolicyProducts,
                          ].includes(effectiveDateRuleValue);
                          const defaultTimeValue = (getFieldValue(['rules', name, 'defaultTime']) ??
                            getFieldValue(['rules', name, 'effectiveDateDefinedFactor'])) as string;
                          const filteredOptions =
                            name === 0
                              ? effectiveDateRuleOptions
                              : effectiveDateRuleOptions.filter(
                                  item =>
                                    ![
                                      ProductEffectiveDateRule.EffectiveImmediately,
                                      ProductEffectiveDateRule.BasedOnDefinedApplicationElements,
                                    ].includes(item.value as ProductEffectiveDateRule)
                                );
                          const resultOptions =
                            fields.length > 1
                              ? filteredOptions.filter(
                                  item =>
                                    item.value !==
                                    (ProductEffectiveDateRule.EffectiveFromTheLaterOfUserInputDateTimeAndCollectionDateTime &&
                                      ProductEffectiveDateRule.EffectiveFromEarliestEffectiveDateOfPolicyProducts)
                                )
                              : filteredOptions;

                          const isBased =
                            name === 0 &&
                            getFieldValue(['rules', 0, 'effectiveDateRule']) ===
                              ProductEffectiveDateRule.BasedOnDefinedApplicationElements;

                          return (
                            <Space className={styles.line}>
                              <Space.Compact>
                                <Form.Item
                                  {...restField}
                                  name={[name, 'effectiveDateRule']}
                                  rules={[{ required: true, message: t('Please select') }]}
                                  className="mb-0"
                                >
                                  <GeneralSelect
                                    disabled={disabled}
                                    style={{ width: 580 }}
                                    option={resultOptions}
                                    onChange={(value: string) => {
                                      if (
                                        value === ProductEffectiveDateRule.EffectiveImmediately ||
                                        value === ProductEffectiveDateRule.BasedOnDefinedApplicationElements
                                      ) {
                                        form.setFieldValue(
                                          ['rules'],
                                          [
                                            {
                                              ...INIT_FORM_VALUE,
                                              effectiveDateRule: value,
                                            },
                                          ]
                                        );
                                        return;
                                      }

                                      form.setFieldValue(['rules', name], {
                                        ...INIT_FORM_VALUE,
                                        effectiveDateRule: value,
                                      });
                                    }}
                                  />
                                </Form.Item>
                                {showMoreButton && !isBased && (
                                  <MoreButton
                                    disabled={!effectiveDateRuleValue}
                                    style={{ width: 50 }}
                                    onClick={() => {
                                      setOpen(true);
                                      setDrawerFormName(name);
                                      drawerForm.setFieldsValue({
                                        enableDefaultTime: (form.getFieldValue(['rules', name, 'enableDefaultTime']) ??
                                          false) as boolean,
                                        defaultTime: convertDefaultTimeForDisplay(
                                          form.getFieldValue(['rules', name, 'defaultTime']),
                                          tenantTimeFormatByZeus?.timeRangeFormat
                                        ),
                                        defaultTimeEditable: (form.getFieldValue([
                                          'rules',
                                          name,
                                          'defaultTimeEditable',
                                        ]) ?? false) as boolean,
                                      });
                                    }}
                                  />
                                )}
                              </Space.Compact>
                              <Form.Item {...restField} noStyle name={[name, 'enableDefaultTime']} />
                              <Form.Item {...restField} noStyle name={[name, 'defaultTime']} />
                              <Form.Item {...restField} noStyle name={[name, 'defaultTimeEditable']} />
                              {isBased && (
                                <Form.Item
                                  {...restField}
                                  noStyle
                                  name={[name, 'effectiveDateDefinedFactor']}
                                  rules={[
                                    {
                                      required: true,
                                      message: t('Please select'),
                                    },
                                  ]}
                                >
                                  <GeneralSelect
                                    disabled={disabled}
                                    style={{ width: 240 }}
                                    option={coveragePeriodBasedOnApplicationElementsOptions}
                                  />
                                </Form.Item>
                              )}
                              {showMoreButton && effectiveDateRuleValue && defaultTimeValue && !isBased && (
                                <span>
                                  {defaultTimeEditableValue === true
                                    ? `${convertDefaultTimeForDisplay(defaultTimeValue, tenantTimeFormatByZeus?.timeRangeFormat)!} (${t('Editable')})`
                                    : convertDefaultTimeForDisplay(
                                        defaultTimeValue,
                                        tenantTimeFormatByZeus?.timeRangeFormat
                                      )!}
                                </span>
                              )}
                              {!disabled && fields.length > 1 && <MinusCircleOutlined onClick={() => remove(name)} />}
                            </Space>
                          );
                        })}
                      </Space>
                      <div style={{ minHeight: 24 }}>
                        {!disabled &&
                          ![
                            ProductEffectiveDateRule.EffectiveImmediately,
                            ProductEffectiveDateRule.BasedOnDefinedApplicationElements,
                            ProductEffectiveDateRule.EffectiveFromEarliestEffectiveDateOfPolicyProducts,
                          ].includes(getFieldValue(['rules', 0, 'effectiveDateRule'])) && (
                            <Button
                              type="link"
                              style={{ paddingLeft: 0 }}
                              onClick={() => {
                                if (
                                  fields.length === 1 &&
                                  getFieldValue(['rules', 0, 'effectiveDateRule']) ===
                                    ProductEffectiveDateRule.EffectiveFromTheLaterOfUserInputDateTimeAndCollectionDateTime
                                ) {
                                  const splitRule = getFieldValue(['rules', 0]) as EffectiveDateRuleDrawerForm;
                                  remove(0);
                                  add({
                                    ...splitRule,
                                    effectiveDateRule: ProductEffectiveDateRule.UserInput,
                                  });
                                  add({
                                    ...splitRule,
                                    effectiveDateRule: ProductEffectiveDateRule.EffectiveFromCollectionDate,
                                  });
                                } else {
                                  add(INIT_FORM_VALUE);
                                }
                              }}
                            >
                              {t('Add a Condition')}
                            </Button>
                          )}
                      </div>
                    </>
                  )}
                </Form.Item>
              </>
            )}
          </Form.List>

          <Drawer
            open={open}
            maskClosable
            title={t('Effective Date Rule')}
            onClose={resetDrawer}
            className={styles.drawer}
            submitBtnShow={!disabled}
            onSubmit={() => {
              drawerForm
                .validateFields()
                .then((values: EffectiveDateRuleDrawerForm) => {
                  form.setFieldValue(['rules', drawerFormName, 'enableDefaultTime'], values.enableDefaultTime);
                  form.setFieldValue(
                    ['rules', drawerFormName, 'defaultTime'],
                    convertDefaultTimeForSubmit(values.defaultTime, tenantTimeFormatByZeus?.timeRangeFormat)
                  );
                  form.setFieldValue(['rules', drawerFormName, 'defaultTimeEditable'], values.defaultTimeEditable);
                })
                .then(() => {
                  const formValues = form.getFieldsValue()?.rules ?? [];
                  // TODO
                  // saveAgreementV2Service({
                  //   productId: productInfo.id,
                  //   productLevelAgreements: {
                  //     effectiveDateRule: {
                  //       ...formValues,
                  //     }
                  //   },
                  // });
                  resetDrawer();
                });
            }}
          >
            <Form name="drawerForm" form={drawerForm} layout="vertical">
              <div>
                <Form.Item name="enableDefaultTime" valuePropName="checked">
                  <Checkbox disabled={disabled} style={{ marginTop: 26 }}>
                    {t('Set Default Time')}
                  </Checkbox>
                </Form.Item>
              </div>

              <div style={{ display: 'flex' }}>
                <Form.Item noStyle shouldUpdate>
                  {({ getFieldValue }) =>
                    (getFieldValue('enableDefaultTime') as string) && (
                      <>
                        <Form.Item
                          name="defaultTime"
                          label={t('Default Time')}
                          validateTrigger="onBlur"
                          rules={[
                            {
                              // ? /^(00:0[1-9]|00:[1-5][0-9]|1[0-9]:[0-5][0-9]|2[0-3]:[0-5][0-9]|24:00)$/ // 00:01~24.00
                              pattern:
                                tenantTimeFormatByZeus?.timeRangeFormat === 'zeroToTwentyFour'
                                  ? /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$|^24:00$/ // 00:00~24.00
                                  : /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, // 00:00~23.59
                              message: t("Please input in this format: 'HH:mm'. e.g. 12:00"),
                            },
                            { required: true, message: t('Please input') },
                          ]}
                        >
                          <Input placeholder="HH:mm" disabled={disabled} style={{ width: 240, marginRight: 120 }} />
                        </Form.Item>

                        <Form.Item
                          name="defaultTimeEditable"
                          label={t('Default Time Editable')}
                          rules={[{ required: true, message: t('Please select') }]}
                        >
                          <Radio.Group
                            options={yesNoOptions}
                            disabled={
                              disabled ||
                              ![
                                ProductEffectiveDateRule.UserInput,
                                ProductEffectiveDateRule.EffectiveFromTheLaterOfUserInputDateTimeAndCollectionDateTime,
                              ].includes(form.getFieldValue(['rules', drawerFormName, 'effectiveDateRule']))
                            }
                          />
                        </Form.Item>
                      </>
                    )
                  }
                </Form.Item>
              </div>
            </Form>
          </Drawer>
        </Col>
      </Row>
    </Form>
  );
};

export default EffectiveDateRule;
