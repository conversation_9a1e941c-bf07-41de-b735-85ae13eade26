import { TimeRangeFormatEnum } from 'genesis-web-service/lib/system/system.interface';

// GIS-99314
// GIS-100762
// 前端展示只需要时分，提交的时候带上秒
export const convertDefaultTimeForSubmit = (
  defaultTime: string | undefined,
  timeRangeFormat: TimeRangeFormatEnum = TimeRangeFormatEnum.TwentyThree
): string | undefined => {
  if (!defaultTime) {
    return defaultTime;
  }
  // 只有放开到了24点，并且配置到了24点，才需要转换
  if (timeRangeFormat === TimeRangeFormatEnum.TwentyFour && defaultTime === '24:00') {
    return '23:59:59';
  }

  const temp = defaultTime.split(':');
  if (temp.length > 2) {
    return defaultTime;
  }

  return `${defaultTime}:00`; // add seconds
};

export const convertDefaultTimeForDisplay = (
  defaultTime: string | undefined,
  timeRangeFormat: TimeRangeFormatEnum = TimeRangeFormatEnum.TwentyThree
): string | undefined => {
  if (!defaultTime) {
    return defaultTime;
  }
  if (timeRangeFormat === TimeRangeFormatEnum.TwentyFour && defaultTime === '23:59:59') {
    return '24:00';
  }

  return defaultTime.substring(0, 5); // remove seconds
};
