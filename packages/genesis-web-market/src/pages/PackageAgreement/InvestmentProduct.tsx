/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Col, Divider, Form, Radio, Row } from 'antd';

import { cloneDeep } from 'lodash-es';

import { EditableTable } from '@zhongan/nagrand-ui';

import { PackagePeriodRule } from '@market/common/enums';
import GeneralSelect from '@market/components/GeneralSelect/GeneralSelect';
import Section from '@market/components/Section/section';
import { useValidFunds } from '@market/hook/investment.serivce';
import { selectEnums } from '@market/redux/selector';
import {
  AllocationTable,
  BizDict,
  Fund,
  InvestmentProductProps,
  OptEnum,
  PremiumAllocationData,
  PremiumTypeEnum,
} from '@market/request/interface';

import styles from './ParticipatingProduct.module.scss';
import { InvestColumns } from './page-config';

const InvestmentProduct = ({
  disabled = false,
  packageId,
  investmentPremiumAllocationData,
  saveInvestmentData,
  refInstance,
}: InvestmentProductProps<PremiumAllocationData>): JSX.Element => {
  const [form] = Form.useForm();
  const [tableForm] = Form.useForm();
  const enums: Record<string, Array<BizDict>> = useSelector(selectEnums);
  const [t] = useTranslation(['market', 'common']);
  const [editKey, setEditKey] = useState<string>('');
  const funds = useValidFunds();

  const allocationTable = useMemo<AllocationTable[]>(
    () =>
      investmentPremiumAllocationData.investmentPremiumAllocationList?.map(item => ({
        ...item,
        key:
          item?.key === OptEnum.Add
            ? OptEnum.Add
            : `${item.premiumType ? item.premiumType : ''}${item.fundCode ? item.fundCode : ''}`,
        premiumType: item.premiumType?.toString(),
      })) || [],
    [investmentPremiumAllocationData]
  );

  const premiumTypeEnums = useMemo(
    () =>
      (enums.premiumType || []).filter(
        (item: BizDict) =>
          item.itemExtend1 !== PremiumTypeEnum.Protection && item.itemExtend1 !== PremiumTypeEnum.Saving
      ),
    [enums]
  );

  const fundEnums = useMemo(
    () =>
      funds.map((fund: Fund) => ({
        itemExtend1: fund.code,
        itemName: `${fund.code} _ ${fund.name}`,
        pureName: fund.name,
      })),
    [funds]
  );

  useImperativeHandle(refInstance, () => ({
    form,
  }));

  const plannedPremiumRule = Form.useWatch('plannedPremiumRule', form);

  const handleOnSubmit = (value: AllocationTable, key: string) =>
    new Promise(async (resolve, reject) => {
      const { id, premiumType, fundCode } = value || {};
      try {
        await tableForm.validateFields();

        const tempTableData = cloneDeep(allocationTable);
        value.key = id || `${premiumType ? premiumType : ''}${fundCode ? fundCode : ''}`;
        value.packageId = packageId;

        if (key === OptEnum.Add) {
          const addIndex = tempTableData.findIndex(item => item.key === 'add');
          tempTableData[addIndex] = value;
        } else {
          const editIndex = tempTableData.findIndex(row => row.key === key);
          tempTableData[editIndex] = value;
        }
        saveInvestmentData(tempTableData);
        resolve(true);
      } catch {
        reject();
      }
    });

  return (
    <Col span={24}>
      <div style={{ marginBottom: 24 }} className={styles.participatewrap} id="investmentProduct">
        <Section subTitle={t('Investment Product')}>
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              plannedPremiumRule:
                investmentPremiumAllocationData?.plannedPremiumRule?.toString() ||
                `${PackagePeriodRule.FollowEachProduct}`,
              plannedPremiumCalculationMethod:
                investmentPremiumAllocationData?.plannedPremiumCalculationMethod?.toString(),
            }}
          >
            <Row>
              <Col span={24}>
                <Form.Item name="plannedPremiumRule" label={t('Planned Premium Configuration')} required>
                  <Radio.Group
                    options={enums.packagePeriodRule.map(item => ({
                      label: item.itemName,
                      value: item.itemExtend1,
                    }))}
                  />
                </Form.Item>
              </Col>
              {plannedPremiumRule === '1' && (
                <Col span={24}>
                  <Form.Item
                    name="plannedPremiumCalculationMethod"
                    label={t('Calculation Method')}
                    required
                    rules={[{ required: true, message: t('Please select') }]}
                  >
                    <GeneralSelect
                      option={enums.calculationMethod.map(item => ({
                        label: item.itemName,
                        value: item.itemExtend1,
                      }))}
                    />
                  </Form.Item>
                </Col>
              )}
            </Row>
          </Form>
          <div>{t('Premium Allocation')}</div>
          <div id="premiumAllocationTable" className="mt-2">
            <EditableTable
              columns={InvestColumns({
                premiumTypeEnums,
                allocationTable,
                fundEnums,
                editKey,
                tableForm,
              })}
              outForm={tableForm}
              dataSource={allocationTable}
              setDataSource={saveInvestmentData}
              pagination={false}
              scroll={{ x: 'max-content' }}
              addBtnProps={{
                type: 'dashed',
                addTitle: t('Add'),
                disabled,
                handleAdd: (value) => setEditKey(value?.key as string),
              }}
              editBtnProps={{
                disabled: () => disabled,
                handleEdit: (record: AllocationTable) => setEditKey(record?.key as string),
              }}
              deleteBtnProps={{ disabled: () => disabled }}
              handleConfirm={handleOnSubmit}
            />
          </div>
        </Section>
      </div>
      <Divider style={{ marginTop: 0 }} />
    </Col>
  );
};

export default InvestmentProduct;
