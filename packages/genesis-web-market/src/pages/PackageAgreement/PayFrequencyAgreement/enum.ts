export enum DueDateCompareEnum {
  Before = '13',
  SameAs = '14',
  After = '15',
}

export enum OverdueHandingType {
  FollowDunningSetting = '1',
  FollowGracePeriodSetting = '2',
}

export enum OverdueStatusType {
  Lapsed = '1',
  Terminate = '2',
  OtherNonForfeitureOptions = '2',
}

export enum InstallmentPremiumCalculationBasisType {
  MonthlyBasis = '1',
  DailyBasis = '2',
}

export enum YesNoDictValue {
  YES = '1',
  NO = '2',
}

export enum YesOrNo {
  YES = 'YES',
  NO = 'NO',
}

export enum CoveragePeriodType {
  ToCertainYear = '5',
  ToCertainAge = '6',
}

export enum GracePeriodUnitEnum {
  Days = '1',
  Months = '2',
}

export enum OverdueDateType {
  Lapsed = '1',
  Terminate = '2',
}

export enum InstallmentCalculationMethodCategoryType {
  ReCalculate = '1',
  PreCalculate = '2',
}

export enum PremiumPeriodType {
  PayToCertainYear = 1,
  PayToCertainAge = 2,
  WholeLife = 3,
  SinglePremium = 4,
  PayToCertainMonth = 5,
}

export enum ValueType {
  SelfDefined = '1',
  Predefined = '2',
}

export enum PaymentFrequencyBaseDict {
  SinglePremium = '1',
  Yearly = '2',
  SemiYearly = '3',
  Quarterly = '4',
  Monthly = '5',
  Daily = '6',
  EveryNYears = '7',
}

export enum PremiumFrequencyLapseDateTypeEnum {
  NextPremiumDueDate = '1',
  NextPremiumDueDateGracePeriod = '2',
}

export enum AnnuityKey {
  Add = 'add',
}

export enum FieldType {
  Radio = 'radio',
  InputGroup = 'inputGroup',
  Select = 'select',
  Input = 'input',
  Number = 'number',
  InputGroupRange = 'inputGroupRange',
  InputGroupGuaranteed = 'inputGroupGuaranteed',
}

export enum DueDateRuleType {
  FixedDueDateRule = '1',
  Rule1 = '2',
  Rule2 = '3',
  Rule3 = '4',
  SameDateAsMonthlyAnniversaryDate = '5',
}
