import { Rule } from 'antd/es/form';
import { ColumnProps } from 'antd/es/table';

import { BizDict } from '@market/common/interface';

interface ValidatorType {
  trigger?: string;
  rules?: Rule[];
}

export interface TableColumn<T> extends ColumnProps<T> {
  editable?: boolean;
  inputType?: string;
  selectOptions?: BizDict[];
  initialValue?: string;
  controllerprops?: Record<string, any>;
  validate?: ValidatorType[];
}

export interface PayFrequencyDrawerFormInfo {
  calculatePremiumForInstallment: string;
  installmentPremiumCalculationBasis?: string;
  paymentFrequencyType: string;
  modalFactor: string;
  initialPremiumPeriod: string;
  dueDateRule: string;
  fixedDueDate: string;
  extractDays: string;
  offsetDays: string;
  payAutoDeductionAdvanceDays: string;
  gracePeriod: string;
  gracePeriodUnit: string;
  additionalGracePeriod: string;
  overdueStatus: string;
  overdueHanding: string;
  dunningRule: string;
  dunningRuleValue: string;
}

export interface PremiumFrequencyAndInstallment<T> {
  getDataForSubmit: () => Promise<
    | {
        validated: false;
      }
    | {
        validated: true;
        data: T;
      }
  >;
  hasChanged: () => boolean;
  clearFields?: () => void;
}

export interface PayFrequencyDrawerAgreementRequest {
  premiumFrequencyItem?: {
    paymentFrequencyType?: string;
    modalFactor?: string;
    initialPremiumPeriod?: string;
  };
  installmentItemDetail?: {
    extractPremiumFromXDaysPriorToDueDate?: string; //
    offsetFromXDaysPriorToDueDate?: string; //
    fixedDueDate?: string;
    gracePeriod?: string;
    gracePeriodUnit?: string;
    additionalGracePeriodDays?: string; //
    overdueStatus?: string;
    dueDateRule?: string;
    autoDeductionAdvanceDays?: string; //
    overdueAutoDeduction?: string; //
    overdueHanding?: string;
    dunningRule?: string;
    dunningRuleValue?: string;
    extractionMethodType?: string;
    autoDeductionDateType?: string;
  };
}
