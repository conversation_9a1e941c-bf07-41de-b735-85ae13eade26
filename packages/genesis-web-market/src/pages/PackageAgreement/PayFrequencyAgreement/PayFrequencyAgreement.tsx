import React, { ReactN<PERSON>, Ref, useCallback, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Col, Form, Modal, Radio, Tooltip, message } from 'antd';

import { PremiumFrequencyAndInstallmentItemResponse } from 'product-types';

import { Icon } from '@zhongan/nagrand-ui';

import AgreementContainerLayout from '@market/components/AgreementContainerLayout';
import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import {
  InstallmentCalculationMethodCategoryType,
  InstallmentPremiumCalculationBasisType,
} from '@market/pages/PackageAgreement/PayFrequencyAgreement/enum';
import {
  PayFrequencyDrawerAgreementRequest,
  PremiumFrequencyAndInstallment,
} from '@market/pages/PackageAgreement/PayFrequencyAgreement/interface';
import { NewMarketService } from '@market/services/market/market.service.new';

import PayFrequencyDrawer from './components/PayFrequencyDrawer';
import PayFrequencyTable from './components/PayFrequencyTable';

interface Props {
  disabled: boolean;
  initialValues: Record<string, any>;
  reload: () => void;
  packageInfo: {
    packageId: number;
    openEndPolicy?: boolean;
  };
  showFormula: (formulaCode: string, agreementCode: string) => void;
  pageInfo: Record<string, any>;
  payFrequencyRefInstance: Ref<
    PremiumFrequencyAndInstallment<{
      premiumFrequencyAndInstallment: {
        installmentCalculationMethod: number;
        installmentPremiumCalculationBasis: number;
      };
    }>
  >;
  insurancePaymentTable: ReactNode;
  multiMainProduct?: boolean;
}
export const PayFrequencyAgreement = (props: Props) => {
  const {
    initialValues,
    disabled,
    reload,
    packageInfo,
    payFrequencyRefInstance,
    insurancePaymentTable,
    multiMainProduct,
  } = props;
  const [form] = Form.useForm();
  const [t, i18n] = useTranslation(['market', 'common']);
  /* ============== 枚举使用start ============== */
  const installmentCalculationMethodCategoryOptions = useBizDictAsOptions('calculatePremiumForInstallmentCategory');
  const installmentPremiumCalculationBasisOptions = useBizDictAsOptions('installmentPremiumCalculationBasis');
  /* ============== 枚举使用end ============== */

  const [drawerVisible, setDrawerVisible] = useState(false);
  const [hasChangedStatus, setHasChangedStatus] = useState(false);

  const [editRecordInfo, setEditRecordInfo] = useState<Record<string, any>>();

  useImperativeHandle(payFrequencyRefInstance, () => ({
    hasChanged: () => hasChangedStatus,
    getDataForSubmit: () =>
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-call
      form
        .validateFields()
        .then((values: { installmentCalculationMethod: number; installmentPremiumCalculationBasis: number }) => ({
          validated: true,
          data: {
            premiumFrequencyAndInstallment: {
              installmentCalculationMethod: +values.installmentCalculationMethod,
              installmentPremiumCalculationBasis: +values.installmentPremiumCalculationBasis,
            },
          },
        })),
  }));

  const agreementTableData =
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call
    initialValues?.premiumFrequencyAndInstallment?.premiumFrequencyAndInstallmentList?.map(
      (item: PayFrequencyDrawerAgreementRequest) => {
        const { premiumFrequencyItem, installmentItemDetail, ...rest } = item;
        return {
          ...(premiumFrequencyItem ?? {}),
          ...(installmentItemDetail ?? {}),
          ...rest,
        };
      }
    ) ?? [];

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const installmentPremiumCalculationBasisValue = useMemo(
    () =>
      // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call
      initialValues?.premiumFrequencyAndInstallment?.installmentPremiumCalculationBasis?.toString(),
    [initialValues]
  );

  const openPayFrequencyDrawer = useCallback((record?: Record<string, any>) => {
    setDrawerVisible(true);
    if (record) {
      setEditRecordInfo(record);
    }
  }, []);

  const resetDrawerFields = useCallback(() => {
    setDrawerVisible(false);
    setEditRecordInfo(undefined);
    form.setFieldsValue({
      payFrequencyType: undefined,
      payCoefficient: undefined,
      initialPremiumPeriod: undefined,
      dueDateRule: undefined,
      fixedDueDate: undefined,
      extractDays: undefined,
      offsetDays: undefined,
      payAutoDeductionAdvanceDays: undefined,
      gracePeriod: undefined,
      gracePeriodUnit: undefined,
      additionalGracePeriod: undefined,
      overdueStatus: undefined,
    });
  }, [form]);

  const renderInstallmentPremiumCalculationBasisLabel = useCallback(
    () => (
      <span>
        {t('Installment Premium Calculation Basis')}
        <Tooltip
          title={
            <span>
              {t(
                'Monthly Basis: The system calculates the monthly premium and then calculates each installment premium based on the number of months in that installment period. (For a full-year policy, the premiums of each installment are the same)'
              )}
              <br />
              {t(
                'Daily Basis: The system calculates the daily premium and then calculates each installment premium based on the number of days in that installment period. (For a full-year policy, the premiums of each installment could be different)'
              )}
            </span>
          }
        >
          <Icon
            type="exclamation-circle"
            style={{
              marginLeft: 4,
            }}
          />
        </Tooltip>
      </span>
    ),
    []
  );

  const deleteAgreement = useCallback(
    (params: {
      record?: PremiumFrequencyAndInstallmentItemResponse[] | PremiumFrequencyAndInstallmentItemResponse;
    }) => {
      const { record } = params;
      if (record) {
        let deleteIds = [];
        if (Array.isArray(record)) {
          deleteIds = record.map(item => ({
            dynamicParamsGroupNo: item.dynamicParamsGroupNo,
          }));
        } else {
          deleteIds = [
            {
              dynamicParamsGroupNo: record.dynamicParamsGroupNo,
            },
          ];
        }

        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call
        NewMarketService.PackageAgreementConfigMgmtService.deletePremiumFrequencyAndInstallment({
          packageId: packageInfo.packageId,
          premiumFrequencyAndInstallment: {
            premiumFrequencyAndInstallmentList: deleteIds,
          },
        }).then(() => {
          message.success('Deleted successfully');
          reload();
        });
      }
    },
    [t, reload, initialValues]
  );

  return (
    <AgreementContainerLayout agreementCode="premiumPeriod" title={t('Premium Period & Installment')}>
      {insurancePaymentTable}
      <Form
        layout="vertical"
        onChange={() => {
          setHasChangedStatus(true);
        }}
        form={form}
      >
        <Form.Item noStyle shouldUpdate>
          {({ getFieldValue, setFieldsValue }) => (
            <React.Fragment>
              <Col span={16} style={{ zIndex: 10 }}>
                <Form.Item
                  colon={false}
                  label={t('Installment Premium Calculation Method')}
                  name="installmentCalculationMethod"
                  initialValue={
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call
                    initialValues?.premiumFrequencyAndInstallment?.installmentCalculationMethod?.toString() ||
                    InstallmentCalculationMethodCategoryType.ReCalculate
                  }
                  rules={[{ required: hasChangedStatus || multiMainProduct, message: t('Please select') }]}
                >
                  <Radio.Group
                    disabled={disabled}
                    onChange={value =>
                      new Promise((resolve, reject) => {
                        Modal.confirm({
                          title: null,
                          content: t(
                            'Change installment premium calculation method will clear the existing configuration, please confirm.'
                          ),
                          cancelText: t('Cancel'),
                          okText: t('Confirm'),
                          onOk: () => {
                            resetDrawerFields();
                            setHasChangedStatus(true);
                            deleteAgreement({
                              // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
                              record: initialValues?.premiumFrequencyAndInstallment?.premiumFrequencyAndInstallmentList,
                            });
                            resolve(value.target.value);
                          },
                          onCancel: () => {
                            setFieldsValue({
                              installmentCalculationMethod: '1',
                            });
                          },
                        });
                      })
                    }
                  >
                    {(installmentCalculationMethodCategoryOptions || []).map(option => (
                      <Radio value={option.value}>{option.label}</Radio>
                    ))}
                  </Radio.Group>
                </Form.Item>
              </Col>
              {getFieldValue('installmentCalculationMethod') ===
              InstallmentCalculationMethodCategoryType.PreCalculate ? (
                <Col span={16} style={{ zIndex: 10 }}>
                  <Form.Item
                    colon={false}
                    label={renderInstallmentPremiumCalculationBasisLabel()}
                    name="installmentPremiumCalculationBasis"
                    initialValue={installmentPremiumCalculationBasisValue}
                    rules={[
                      {
                        required: true,
                        message: t('Please select'),
                      },
                    ]}
                  >
                    <GeneralSelect
                      disabled={disabled}
                      style={{ width: 240 }}
                      option={installmentPremiumCalculationBasisOptions.filter(
                        option => option.value !== InstallmentPremiumCalculationBasisType.DailyBasis
                      )}
                    />
                  </Form.Item>
                </Col>
              ) : null}
            </React.Fragment>
          )}
        </Form.Item>
        <Form.Item label={t('Installment Premium Frequency')} required={multiMainProduct}>
          {disabled ? null : (
            <Button
              icon={<Icon type="add" />}
              style={{ width: '100%', marginBottom: 8, borderStyle: 'dashed' }}
              onClick={() => openPayFrequencyDrawer()}
            >
              {t('Add')}
            </Button>
          )}
          <PayFrequencyTable
            data={agreementTableData}
            disabled={disabled}
            onDelete={deleteAgreement}
            openPayFrequencyDrawer={openPayFrequencyDrawer}
          />
        </Form.Item>
      </Form>
      {drawerVisible ? (
        <PayFrequencyDrawer
          visible={drawerVisible}
          packageInfo={packageInfo}
          onClose={() => {
            resetDrawerFields();
            reload();
          }}
          editRecordInfo={editRecordInfo}
          calculatePremiumValue={form.getFieldValue('installmentCalculationMethod')}
          openEndPolicy={packageInfo?.openEndPolicy}
        />
      ) : null}
    </AgreementContainerLayout>
  );
};

export default PayFrequencyAgreement;
