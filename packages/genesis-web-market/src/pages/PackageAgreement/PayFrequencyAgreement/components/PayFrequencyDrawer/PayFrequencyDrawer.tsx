import React, { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Input, InputNumber, Radio, Row, Space, Tooltip, message } from 'antd';

import { Icon, Drawer } from '@zhongan/nagrand-ui';

import { ProductCategoryDictValue } from 'genesis-web-service/lib/product';

import ColumnRequiredTitle from '@market/components/ColumnRequiredTitle';
import GeneralSelect from '@market/components/GeneralSelect';
import LabelWithTooltip from '@market/components/LabelWithTooltip';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import {
  DueDateRuleType,
  GracePeriodUnitEnum,
  InstallmentCalculationMethodCategoryType,
  OverdueHandingType,
  OverdueStatusType,
  PaymentFrequencyBaseDict,
  PremiumFrequencyLapseDateTypeEnum,
  YesNoDictValue,
} from '@market/pages/PackageAgreement/PayFrequencyAgreement/enum';
import { PayFrequencyDrawerFormInfo } from '@market/pages/PackageAgreement/PayFrequencyAgreement/interface';
import { NewMarketService } from '@market/services/market/market.service.new';

import styles from './PayFrequencyDrawer.module.scss';

export const ILPCategoryCodeList = [
  ProductCategoryDictValue.UnitLinked,
  ProductCategoryDictValue.Universal,
  ProductCategoryDictValue.VariableAnnuity,
];

interface Props {
  visible: boolean;
  onClose: () => void;
  packageInfo: {
    packageId: number;
  };
  editRecordInfo?: Record<string, string | number>;
  calculatePremiumValue?: string;
  openEndPolicy?: boolean;
}

export const PayFrequencyDrawer = ({
  visible,
  packageInfo,
  onClose,
  editRecordInfo,
  calculatePremiumValue,
  openEndPolicy,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();

  /* ============== 枚举使用start ============== */
  const premiumFrequencyTypeOptions = useBizDictAsOptions('premiumFrequencyType');
  const dueDateRuleOptions = useBizDictAsOptions('dueDateRule');
  const gracePeriodUnitOptions = useBizDictAsOptions('gracePeriodUnit');
  const overdueStatusOptions = useBizDictAsOptions('overdueStatus');
  const yesNoOptions = useBizDictAsOptions('yesNo');
  const overdueHandingOptions = useBizDictAsOptions('overdueHanding');
  const dunningRuleOptions = useBizDictAsOptions('dunningRule');
  const premiumFrequencyLapseDateTypeOptions = useBizDictAsOptions('premiumFrequencyLapseDateType');
  const extractionMethodOptions = useBizDictAsOptions('extractionMethod');
  const autoDeductionDateOptions = useBizDictAsOptions('autoDeductionDate');
  /* ============== 枚举使用end ============== */

  const paymentFrequencyDisabledFields = [PaymentFrequencyBaseDict.Daily];
  const paymentFrequencyOptionalFields = [PaymentFrequencyBaseDict.SinglePremium, PaymentFrequencyBaseDict.Daily];
  const fixedDueDateDisabledFields = [DueDateRuleType.FixedDueDateRule];

  // 判断是否是ILP产品
  const isILPNeedCategory = useMemo(
    () => ILPCategoryCodeList.includes(packageInfo.categoryCode as ProductCategoryDictValue),
    [packageInfo.categoryCode]
  );

  const overdueHandingInitialValue = useMemo(() => {
    if (!editRecordInfo || paymentFrequencyDisabledFields.includes(editRecordInfo?.paymentFrequencyType)) {
      return undefined;
    }
    if (editRecordInfo && editRecordInfo?.overdueHanding) {
      // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
      return `${editRecordInfo?.overdueHanding}`;
    }
    return OverdueHandingType.FollowGracePeriodSetting;
  }, [editRecordInfo, paymentFrequencyDisabledFields]);

  useEffect(() => {
    if (visible && editRecordInfo) {
      form.setFieldsValue({
        // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
        dueDateRule: editRecordInfo.dueDateRule,
      });
    }
  }, [editRecordInfo, visible]);

  const collectParams = (values: PayFrequencyDrawerFormInfo) => {
    const { paymentFrequencyType, modalFactor, initialPremiumPeriod, ...rest } = values;
    return {
      installmentItemDetail: rest,
      premiumFrequencyItem: {
        paymentFrequencyType,
        modalFactor,
        initialPremiumPeriod,
      },
    };
  };

  const onSubmit = useCallback(() => {
    form.validateFields().then((values: PayFrequencyDrawerFormInfo) => {
      const params = collectParams(values);
      const editInfo = editRecordInfo
        ? {
            agreementId: editRecordInfo.agreementId,
            dynamicParamsGroupNo: editRecordInfo.dynamicParamsGroupNo,
          }
        : {};

      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-unsafe-call
      NewMarketService.PackageAgreementConfigMgmtService.savePremiumFrequencyAndInstallment({
        packageId: packageInfo.packageId,
        premiumFrequencyAndInstallment: {
          premiumFrequencyAndInstallmentList: [
            {
              ...params,
              ...editInfo,
            },
          ],
          ...editInfo,
        },
      }).then(() => {
        message.success(t('Add successfully'));
        onClose();
      });
    });
  }, [collectParams, editRecordInfo, form, onClose, packageInfo.packageId, t]);

  const onChangePayFrequencyType = useCallback(
    (value: string) => {
      if (value === PaymentFrequencyBaseDict.SinglePremium) {
        form.setFieldsValue({
          modalFactor: 1,
          initialPremiumPeriod: undefined,
          dueDateRule: undefined,
          fixedDueDate: undefined,
          extractPremiumFromXDaysPriorToDueDate: undefined,
          offsetFromXDaysPriorToDueDate: undefined,
          autoDeductionAdvanceDays: undefined,
          gracePeriod: undefined,
          gracePeriodUnit: GracePeriodUnitEnum.Days,
          additionalGracePeriodDays: undefined,
          overdueStatus: undefined,
          extractionMethodType: undefined,
          autoDeductionDateType: undefined,
        });
      }

      if (value === PaymentFrequencyBaseDict.Daily) {
        form.setFieldsValue({
          dueDateRule: undefined,
          fixedDueDate: undefined,
        });
      }
    },
    [form]
  );

  const renderOverdueHandingOptions = useCallback(
    (overdueHandingValue: OverdueHandingType, payFrequencyTypeValue) => {
      if (overdueHandingValue === OverdueHandingType.FollowDunningSetting) {
        return (
          <React.Fragment>
            <Col span={16}>
              <Form.Item label={<ColumnRequiredTitle title={t('Dunning Rule')} />}>
                <Input.Group compact className={styles['dunning-input-group']}>
                  <Form.Item
                    noStyle
                    name="dunningRule"
                    initialValue={editRecordInfo?.dunningRule?.toString() || undefined}
                    rules={[
                      {
                        required: true,
                        message: t('Please select'),
                      },
                    ]}
                  >
                    <GeneralSelect
                      style={{ width: 240 }}
                      option={dunningRuleOptions}
                      disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
                    />
                  </Form.Item>
                  <span style={{ margin: '4px 5px 0' }}>—</span>
                  <Form.Item
                    noStyle
                    name="dunningRuleValue"
                    initialValue={editRecordInfo?.dunningRuleValue}
                    rules={[
                      {
                        required: true,
                        message: t('Please input'),
                      },
                    ]}
                  >
                    <InputNumber
                      min={0}
                      max={100}
                      precision={0}
                      style={{ width: '240px' }}
                      placeholder={t('Please input')}
                      disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
                      addonBefore={<span className={styles['addonBefore-box']}>{t('After')}</span>}
                      addonAfter={<span className={styles['addonAfter-box']}>{t('Day(s)')}</span>}
                    />
                  </Form.Item>
                </Input.Group>
              </Form.Item>
            </Col>
          </React.Fragment>
        );
      }

      if (overdueHandingValue === OverdueHandingType.FollowGracePeriodSetting) {
        return (
          <React.Fragment>
            <Col span={8}>
              <Form.Item
                label={
                  <LabelWithTooltip
                    title={t('Grace Period')}
                    tooltip={t(
                      'The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59'
                    )}
                  />
                }
                name="gracePeriod"
                initialValue={editRecordInfo?.gracePeriod}
              >
                <InputNumber
                  min={0}
                  precision={0}
                  placeholder={t('Please input')}
                  style={{ width: '240px' }}
                  disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
                  addonAfter={
                    <Form.Item
                      noStyle
                      name="gracePeriodUnit"
                      initialValue={editRecordInfo?.gracePeriodUnit?.toString() || GracePeriodUnitEnum.Days}
                    >
                      <GeneralSelect
                        style={{ width: '100px' }}
                        option={gracePeriodUnitOptions}
                        disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
                      />
                    </Form.Item>
                  }
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label={t('Additional Grace Period')}
                name="additionalGracePeriodDays"
                initialValue={editRecordInfo?.additionalGracePeriodDays}
              >
                <InputNumber
                  placeholder={t('Please input')}
                  style={{ width: '240px' }}
                  min={0}
                  precision={0}
                  disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
                  addonAfter={<span className={styles['addonAfter-box']}>{t('Day(s)')}</span>}
                />
              </Form.Item>
            </Col>
            {!isILPNeedCategory && (
              <Col span={8}>
                <Form.Item
                  label={t('Lapse Date')}
                  name="premiumFrequencyLapseDateType"
                  initialValue={
                    editRecordInfo?.premiumFrequencyLapseDateType?.toString() ||
                    PremiumFrequencyLapseDateTypeEnum.NextPremiumDueDate
                  }
                >
                  <GeneralSelect
                    style={{ width: 240 }}
                    option={premiumFrequencyLapseDateTypeOptions}
                    disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
                  />
                </Form.Item>
              </Col>
            )}
          </React.Fragment>
        );
      }
      return null;
    },
    [
      dunningRuleOptions,
      editRecordInfo,
      gracePeriodUnitOptions,
      premiumFrequencyLapseDateTypeOptions,
      paymentFrequencyDisabledFields,
      t,
      isILPNeedCategory,
    ]
  );

  const renderPayFrequency = useCallback(
    (
      payFrequencyTypeValue: PaymentFrequencyBaseDict,
      extractPremiumFromXDaysPriorToDueDateValue: number,
      offsetFromXDaysPriorToDueDateValue: number,
      dueDateRuleValue: DueDateRuleType
    ) => (
      <React.Fragment>
        {calculatePremiumValue === InstallmentCalculationMethodCategoryType.PreCalculate ? null : (
          <Col span={8}>
            <Form.Item
              label={t('Modal Factor')}
              name="modalFactor"
              initialValue={editRecordInfo?.modalFactor || undefined}
            >
              <Input
                style={{ width: 240 }}
                placeholder={t('Please input')}
                disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
              />
            </Form.Item>
          </Col>
        )}
        <Col span={8}>
          <Form.Item
            label={t('Initial Premium Period')}
            name="initialPremiumPeriod"
            initialValue={editRecordInfo?.initialPremiumPeriod}
          >
            <InputNumber
              min={0}
              precision={0}
              style={{ width: 240 }}
              placeholder={t('Please input')}
              disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
            />
          </Form.Item>
        </Col>
        <div className={styles['installment-detail-box']}>
          <div className={styles['installment-detail-header']}>{t('Installment Detail')}</div>
          <div className={styles['installment-detail-main']}>
            <React.Fragment>
              <Row>
                <Col span={8}>
                  <Form.Item
                    label={
                      <span>
                        {t('Premium Due Date Rule')}
                        &nbsp;
                        <Tooltip
                          title={
                            <div>
                              {t('Fixed due date rule：Fixed due date or period end date')}
                              <br />
                              {t('Rule1：Corresponding to effective date or period end date')}
                              <br />
                              {t('Rule2：Corresponding to effective date or next date of period end date')}
                              <br />
                              {t(
                                'Rule3: Corresponding to effective date or period end date and fixed Feb 28 as period end date'
                              )}
                            </div>
                          }
                          overlayStyle={{ width: 350 }}
                        >
                          <Icon
                            type="info-circle"
                            style={{
                              marginLeft: 5,
                              marginTop: '-3px',
                              fontSize: 14,
                            }}
                          />
                        </Tooltip>
                      </span>
                    }
                    name="dueDateRule"
                    initialValue={editRecordInfo?.dueDateRule?.toString()}
                    rules={[
                      {
                        required: !paymentFrequencyOptionalFields.includes(payFrequencyTypeValue),
                        message: t('Please select'),
                      },
                    ]}
                  >
                    <GeneralSelect
                      style={{ width: 240 }}
                      option={(dueDateRuleOptions || [])
                        .filter(item => item.value !== DueDateRuleType.SameDateAsMonthlyAnniversaryDate)
                        .filter(item => {
                          if (
                            calculatePremiumValue === InstallmentCalculationMethodCategoryType.PreCalculate &&
                            item.value === DueDateRuleType.FixedDueDateRule
                          ) {
                            return false;
                          }
                          return true;
                        })}
                      disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
                      onChange={() => {
                        form.setFieldsValue({
                          fixedDueDate: undefined,
                        });
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={
                      <LabelWithTooltip
                        title={t('Day of Fixed Due Date')}
                        tooltip={t(
                          'The fixed due date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.'
                        )}
                      />
                    }
                    name="fixedDueDate"
                    initialValue={editRecordInfo?.fixedDueDate}
                    rules={
                      dueDateRuleValue === DueDateRuleType.FixedDueDateRule
                        ? [
                            {
                              required: true,
                              message: t('Please input'),
                            },
                          ]
                        : []
                    }
                  >
                    <InputNumber
                      min={1}
                      max={31}
                      precision={0}
                      style={{ width: '240px' }}
                      placeholder={t('Please input')}
                      disabled={
                        paymentFrequencyDisabledFields.includes(payFrequencyTypeValue) ||
                        !fixedDueDateDisabledFields.includes(dueDateRuleValue)
                      }
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={t('Extraction Method')}
                    name="extractionMethodType"
                    initialValue={editRecordInfo ? editRecordInfo.extractionMethodType || 'AUTO_EXTRACTION' : undefined}
                    rules={[
                      {
                        required: !paymentFrequencyOptionalFields.includes(payFrequencyTypeValue),
                        message: t('Please select'),
                      },
                    ]}
                  >
                    <GeneralSelect
                      option={extractionMethodOptions}
                      onChange={val => {
                        if (val === 'EXTERNAL_TRIGGERED') {
                          form.setFieldValue('extractPremiumFromXDaysPriorToDueDate', undefined);
                        }
                      }}
                    />
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item
                    shouldUpdate={(pre, curr) =>
                      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                      pre.extractionMethodType !== curr.extractionMethodType
                    }
                    noStyle
                  >
                    {({ getFieldValue }) => (
                      <Form.Item
                        label={
                          <LabelWithTooltip
                            title={t('Extract Bill Date')}
                            tooltip={t('Extract premium from X days prior to due date')}
                          />
                        }
                        name="extractPremiumFromXDaysPriorToDueDate"
                        initialValue={editRecordInfo?.extractPremiumFromXDaysPriorToDueDate}
                        rules={[
                          {
                            required: !(
                              paymentFrequencyOptionalFields.includes(payFrequencyTypeValue) ||
                              getFieldValue('extractionMethodType') === 'EXTERNAL_TRIGGERED'
                            ),
                            message: t('Please input'),
                          },
                          ({ getFieldValue: _getFieldValue }) => ({
                            validator(_, value) {
                              if (parseFloat(value) < parseFloat(_getFieldValue('offsetFromXDaysPriorToDueDate'))) {
                                return Promise.reject(
                                  new Error(t('Extract day(s) must be greater than offset day(s)'))
                                );
                              }
                              return Promise.resolve();
                            },
                          }),
                        ]}
                      >
                        <InputNumber
                          min={0}
                          precision={0}
                          style={{ width: '240px' }}
                          placeholder={t('Please input')}
                          disabled={
                            paymentFrequencyDisabledFields.includes(payFrequencyTypeValue) ||
                            getFieldValue('extractionMethodType') === 'EXTERNAL_TRIGGERED'
                          }
                          addonAfter={<span className={styles['addonAfter-box']}>{t('Day(s)')}</span>}
                        />
                      </Form.Item>
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={
                      <LabelWithTooltip title={t('Offset Date')} tooltip={t('Offset from X days prior to due date')} />
                    }
                    name="offsetFromXDaysPriorToDueDate"
                    initialValue={editRecordInfo?.offsetFromXDaysPriorToDueDate}
                    rules={[
                      {
                        required: !paymentFrequencyOptionalFields.includes(payFrequencyTypeValue),
                        message: t('Please input'),
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (parseFloat(value) > parseFloat(getFieldValue('extractPremiumFromXDaysPriorToDueDate'))) {
                            return Promise.reject(new Error(t('Extract day(s) must be greater than offset day(s)')));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  >
                    <InputNumber
                      min={-1}
                      precision={0}
                      style={{ width: '240px' }}
                      placeholder={t('Please input')}
                      disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
                      addonAfter={<span className={styles['addonAfter-box']}>{t('Day(s)')}</span>}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    noStyle
                    shouldUpdate={(pre, curr) =>
                      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                      pre.autoDeductionDateType !== curr.autoDeductionDateType
                    }
                  >
                    {({ getFieldValue, setFieldValue }) => (
                      <Form.Item
                        label={
                          <LabelWithTooltip
                            title={t('Deduction Date')}
                            tooltip={t('For Fixed Day of Month, please input number from 1 and 28')}
                          />
                        }
                      >
                        <Space.Compact>
                          <Form.Item
                            noStyle
                            name="autoDeductionDateType"
                            initialValue={editRecordInfo?.autoDeductionDateType}
                          >
                            <GeneralSelect
                              option={autoDeductionDateOptions}
                              className="!w-[180px]"
                              onChange={value => {
                                if (value !== 'FIXED_DATE_BEFORE_DUE_DATE') {
                                  const autoDeductionAdvanceDaysValue = getFieldValue('autoDeductionAdvanceDays');
                                  if (autoDeductionAdvanceDaysValue > 28) {
                                    setFieldValue('autoDeductionAdvanceDays', 28);
                                  } else if (autoDeductionAdvanceDaysValue < 1) {
                                    setFieldValue('autoDeductionAdvanceDays', 1);
                                  }
                                }
                              }}
                            />
                          </Form.Item>
                          <Form.Item
                            noStyle
                            name="autoDeductionAdvanceDays"
                            initialValue={editRecordInfo?.autoDeductionAdvanceDays}
                          >
                            <InputNumber
                              min={getFieldValue('autoDeductionDateType') === 'FIXED_DATE_BEFORE_DUE_DATE' ? 0 : 1}
                              max={
                                getFieldValue('autoDeductionDateType') === 'FIXED_DATE_BEFORE_DUE_DATE' ? undefined : 28
                              }
                              precision={0}
                              style={{ width: '60px' }}
                              disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
                            />
                          </Form.Item>
                        </Space.Compact>
                      </Form.Item>
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={t('Overdue Handling')}
                    name="overdueHanding"
                    initialValue={overdueHandingInitialValue}
                    rules={[
                      {
                        required: !paymentFrequencyOptionalFields.includes(payFrequencyTypeValue),
                        message: t('Please input'),
                      },
                    ]}
                  >
                    <GeneralSelect
                      style={{ width: 240 }}
                      option={overdueHandingOptions}
                      disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
                      onChange={() => {
                        form.setFieldsValue({
                          overdueStatus: undefined,
                          overdueAutoDeduction: undefined,
                          gracePeriod: undefined,
                          additionalGracePeriodDays: undefined,
                          dunningRule: undefined,
                          dunningRuleValue: undefined,
                        });
                      }}
                    />
                  </Form.Item>
                </Col>
                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues: PayFrequencyDrawerFormInfo, currentValues) =>
                    prevValues?.overdueHanding !== currentValues?.overdueHanding
                  }
                >
                  {({ getFieldValue }) =>
                    renderOverdueHandingOptions(getFieldValue('overdueHanding'), payFrequencyTypeValue)
                  }
                </Form.Item>
                <Col span={8}>
                  <Form.Item noStyle shouldUpdate={() => true}>
                    {({ getFieldValue }) => (
                      <Form.Item
                        label={t('Overdue Status')}
                        name="overdueStatus"
                        initialValue={editRecordInfo?.overdueStatus?.toString()}
                        rules={[
                          {
                            required: !paymentFrequencyOptionalFields.includes(payFrequencyTypeValue),
                            message: t('Please select'),
                          },
                        ]}
                      >
                        <GeneralSelect
                          style={{ width: '240px' }}
                          option={overdueStatusOptions.filter(item =>
                            getFieldValue('overdueHanding') === OverdueHandingType.FollowDunningSetting
                              ? item.value === OverdueStatusType.Terminate
                              : true
                          )}
                          disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
                        />
                      </Form.Item>
                    )}
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label={t('Overdue Auto Deduction')}
                    name="overdueAutoDeduction"
                    initialValue={editRecordInfo?.overdueAutoDeduction?.toString() || YesNoDictValue.YES}
                    rules={[
                      {
                        required: !paymentFrequencyOptionalFields.includes(payFrequencyTypeValue),
                        message: t('Please select'),
                      },
                    ]}
                  >
                    <Radio.Group
                      style={{ width: '240px' }}
                      options={yesNoOptions}
                      disabled={paymentFrequencyDisabledFields.includes(payFrequencyTypeValue)}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </React.Fragment>
          </div>
        </div>
      </React.Fragment>
    ),
    [
      calculatePremiumValue,
      t,
      editRecordInfo,
      paymentFrequencyDisabledFields,
      paymentFrequencyOptionalFields,
      dueDateRuleOptions,
      fixedDueDateDisabledFields,
      extractionMethodOptions,
      overdueHandingInitialValue,
      overdueHandingOptions,
      yesNoOptions,
      form,
      autoDeductionDateOptions,
      renderOverdueHandingOptions,
      overdueStatusOptions,
    ]
  );

  return (
    <Drawer
      open={visible}
      title={t('Premium Period & Installment')}
      onClose={onClose}
      onSubmit={() => onSubmit()}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          noStyle
          shouldUpdate={(prevValues: PayFrequencyDrawerFormInfo, currentValues) =>
            prevValues.paymentFrequencyType !== currentValues.paymentFrequencyType
          }
        >
          <Row>
            <Col span={8}>
              <Form.Item
                label={t('Premium Frequency')}
                name="paymentFrequencyType"
                initialValue={editRecordInfo?.paymentFrequencyType?.toString()}
                rules={[
                  {
                    required: true,
                    message: t('Please select'),
                  },
                ]}
              >
                <GeneralSelect
                  style={{ width: 240 }}
                  option={premiumFrequencyTypeOptions
                    .filter(item => item.value !== PaymentFrequencyBaseDict.Daily)
                    .filter(item => (openEndPolicy ? item.value !== PaymentFrequencyBaseDict.SinglePremium : true))}
                  onChange={(value: string) => onChangePayFrequencyType(value)}
                />
              </Form.Item>
            </Col>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues: PayFrequencyDrawerFormInfo, currentValues) =>
                prevValues?.paymentFrequencyType !== currentValues?.paymentFrequencyType ||
                prevValues?.offsetFromXDaysPriorToDueDate !== currentValues?.offsetFromXDaysPriorToDueDate ||
                prevValues?.extractPremiumFromXDaysPriorToDueDate !==
                  currentValues?.extractPremiumFromXDaysPriorToDueDate ||
                prevValues?.dueDateRule !== currentValues?.dueDateRule
              }
            >
              {({ getFieldValue }) =>
                renderPayFrequency(
                  getFieldValue('paymentFrequencyType'),
                  getFieldValue('extractPremiumFromXDaysPriorToDueDate'),
                  getFieldValue('offsetFromXDaysPriorToDueDate'),
                  getFieldValue('dueDateRule')
                )
              }
            </Form.Item>
          </Row>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default PayFrequencyDrawer;
