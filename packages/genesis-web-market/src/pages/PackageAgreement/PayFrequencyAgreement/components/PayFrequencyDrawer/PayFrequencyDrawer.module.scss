.installment-detail-box {
  width: 100%;
  display: inline-block;
  border: 1px solid var(--border-default);
  border-radius: 4px;
  .installment-detail-header {
    border-bottom: 1px solid var(--border-default);
    font-size: 14px;
    font-weight: 700;
    height: 48px;
    line-height: 48px;
    text-indent: 25px;
  }
  .installment-detail-main {
    padding: 24px;
  }
  .dunning-input-group {
    :global {
      .#{$market-prefix}-select {
        .#{$market-prefix}-select-selector {
          border-radius: 8px;
        }
      }
    }
  }
  .addonBefore-box {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    padding: 0 8px;
  }
  .addonAfter-box {
    display: inline-block;
    width: 65px;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    text-align: center;
  }
  :global {
    .#{$market-prefix}-select-selector {
      color: var(--text-color) !important;
    }
  }
}
