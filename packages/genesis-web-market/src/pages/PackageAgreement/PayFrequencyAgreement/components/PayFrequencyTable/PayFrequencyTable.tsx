import * as React from 'react';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { ColumnProps } from 'antd/es/table';

import { DeleteAction, EditAction, Table } from '@zhongan/nagrand-ui';

import { useBizDictAsOptions } from '@market/hook/bizDict';
import {
  DueDateCompareEnum,
  OverdueHandingType,
  PaymentFrequencyBaseDict,
  YesNoDictValue,
} from '@market/pages/PackageAgreement/PayFrequencyAgreement/enum';
import { renderOptionName } from '@market/utils/enum';

import { paymentFrequencyDisabledFields } from '../../config';

interface Props {
  data: Record<string, any>[];
  disabled: boolean;
  openPayFrequencyDrawer: (record: Record<string, any>) => void;
  onDelete: (record: Record<string, any>) => void;
}

const handleUndefinedValue = (text: string | number | undefined, processedText: string) => {
  if (text !== undefined) {
    return processedText;
  }
  return '- -';
};

export const PayFrequencyTable = ({ data, disabled, onDelete, openPayFrequencyDrawer }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  /* ============== 枚举使用start ============== */
  const premiumFrequencyTypeOptions = useBizDictAsOptions('premiumFrequencyType');
  const dueDateRuleOptions = useBizDictAsOptions('dueDateRule');
  const overdueStatusOptions = useBizDictAsOptions('overdueStatus');
  const yesNoOptions = useBizDictAsOptions('yesNo');
  const overdueHandingOptions = useBizDictAsOptions('overdueHanding');
  const dunningRuleOptions = useBizDictAsOptions('dunningRule');
  const gracePeriodUnitOptions = useBizDictAsOptions('gracePeriodUnit');
  const extractionMethodOptions = useBizDictAsOptions('extractionMethod');
  const autoDeductionDateOptions = useBizDictAsOptions('autoDeductionDate');
  const premiumFrequencyLapseDateTypeOptions = useBizDictAsOptions('premiumFrequencyLapseDateType');

  /* ============== 枚举使用end ============== */

  const columns: ColumnProps<Record<string, any>>[] = useMemo(() => {
    const tempColumns = [
      {
        title: t('number'),
        width: 100,
        render: (text: any, record: any, index: number) => index + 1,
      },
      {
        title: t('Premium Frequency'),
        dataIndex: 'paymentFrequencyType',
        render: (text: number) => renderOptionName(text?.toString(), premiumFrequencyTypeOptions) || t('- -'),
      },
      {
        title: t('Modal Factor'),
        dataIndex: 'modalFactor',
      },
      {
        title: t('Initial Premium Period'),
        dataIndex: 'initialPremiumPeriod',
      },
      {
        title: t('Premium Due Date Rule'),
        dataIndex: 'dueDateRule',
        render: (text: string) => renderOptionName(text?.toString(), dueDateRuleOptions) || t('- -'),
      },
      {
        title: t('Day of Fixed Due Date'),
        dataIndex: 'fixedDueDate',
      },
      {
        title: t('Extraction Method'),
        dataIndex: 'extractionMethodType',
        render: (text: string) => renderOptionName(text?.toString(), extractionMethodOptions) || t('- -'),
      },
      {
        title: t('Extract Bill Date'),
        dataIndex: 'extractPremiumFromXDaysPriorToDueDate',
        render: (text: string) => handleUndefinedValue(text, `${text} Day(s)`),
      },
      {
        title: t('Offset Date'),
        dataIndex: 'offsetFromXDaysPriorToDueDate',
        render: (text: string | number) => handleUndefinedValue(text, `${text} Day(s) Before Due Date`),
      },
      {
        title: t('Deduction Date'),
        dataIndex: 'autoDeductionAdvanceDays',
        render: (
          text: string,
          record: {
            autoDeductionDateType: string;
          }
        ) => {
          const deductionDateLabel = renderOptionName(record.autoDeductionDateType?.toString(), autoDeductionDateOptions);
          if (text !== undefined && deductionDateLabel) {
            return `${deductionDateLabel} : ${text}`;
          }
          return t('- -');
        },
      },
      {
        title: t('Overdue Handling'),
        dataIndex: 'overdueHanding',
        render: (text: number, record: { payFrequencyType: PaymentFrequencyBaseDict }) => {
          if (!record || paymentFrequencyDisabledFields.includes(record?.payFrequencyType)) {
            return undefined;
          }
          return (
            renderOptionName(text?.toString() || OverdueHandingType.FollowGracePeriodSetting, overdueHandingOptions) ||
            t('- -')
          );
        },
      },
      {
        title: t('Dunning Rule'),
        dataIndex: 'dunningRuleValue',
        render: (text: string, record: { dunningRule: string }) =>
          handleUndefinedValue(
            text,
            `${renderOptionName(record.dunningRule?.toString(), dunningRuleOptions)}  After ${text} Day(s)`
          ),
      },
      {
        title: t('Grace Period'),
        dataIndex: 'gracePeriod',
        render: (text: string, record: Record<string, any>) =>
          handleUndefinedValue(text, `${text} ${renderOptionName(record?.gracePeriodUnit?.toString(), gracePeriodUnitOptions)}`),
      },
      {
        title: t('Additional Grace Period'),
        dataIndex: 'additionalGracePeriodDays',
        render: (text: string) => handleUndefinedValue(text, `${text} Day(s)`),
      },
      {
        title: t('Lapse Date'),
        dataIndex: 'premiumFrequencyLapseDateType',
        render: (text: string) => renderOptionName(text?.toString(), premiumFrequencyLapseDateTypeOptions) || t('- -'),
      },
      {
        title: t('Overdue Status'),
        dataIndex: 'overdueStatus',
        render: (text: string) => renderOptionName(text?.toString(), overdueStatusOptions) || t('- -'),
      },
      {
        title: t('Overdue Auto Deduction'),
        dataIndex: 'overdueAutoDeduction',
        render: (text: string) => renderOptionName(text?.toString() || YesNoDictValue.YES, yesNoOptions),
      },
    ];

    if (!disabled) {
      tempColumns.push({
        title: t('Actions'),
        dataIndex: 'key',
        width: 100,
        fixed: 'right',
        align: 'right',
        className: 'agreement-action-column',
        render: (_text: string, record: Record<string, any>, _index: number) => (
          <React.Fragment>
            <EditAction onClick={() => openPayFrequencyDrawer(record)} />
            <DeleteAction
              doubleConfirmType="popconfirm"
              onClick={() => onDelete({ record })}
              deleteConfirmContent={t('Are you sure to delete this record?')}
            />
          </React.Fragment>
        ),
      });
    }

    return tempColumns;
  }, [
    autoDeductionDateOptions,
    disabled,
    dueDateRuleOptions,
    dunningRuleOptions,
    extractionMethodOptions,
    gracePeriodUnitOptions,
    onDelete,
    openPayFrequencyDrawer,
    overdueHandingOptions,
    overdueStatusOptions,
    premiumFrequencyLapseDateTypeOptions,
    premiumFrequencyTypeOptions,
    t,
    yesNoOptions,
  ]);

  return (
    <Table
      bordered={false}
      columns={columns}
      dataSource={data}
      rowKey="index"
      scroll={{ x: 'max-content' }}
      pagination={false}
      emptyType="text"
    />
  );
};

export default PayFrequencyTable;
