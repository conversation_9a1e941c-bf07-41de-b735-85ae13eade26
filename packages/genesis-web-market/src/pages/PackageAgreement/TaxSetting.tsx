import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Button, Col, Form, InputNumber, Radio, Row, Space } from 'antd';

import { DeleteAction, Drawer, Icon, Table } from '@zhongan/nagrand-ui';

import { TaxSettingParam } from 'genesis-web-service';

import { YesNoOptions } from '@market/common/enums';
import { BizDict, TaxTypeKeyType, TaxValueTypeKeyType } from '@market/common/interface';
import GeneralSelect from '@market/components/GeneralSelect';
import { selectEnums } from '@market/redux/selector';
import { NewMarketService } from '@market/services/market/market.service.new';
import { NewProductService } from '@market/services/product/product.service.new';
import { renderOptionName } from '@market/utils/enum';

interface FormulaCodeList {
  value: string;
  label: string;
}

interface Props {
  disabled: boolean;
  packageId: number;
}

const TaxSetting: React.FC<Props> = ({ disabled, packageId }): JSX.Element => {
  const enums: Record<string, Array<BizDict>> = useSelector(selectEnums);
  const [t] = useTranslation(['market', 'common']);
  const [visible, setVisible] = useState(false);
  const [formulaCodeList, setFormulaCodeList] = useState<FormulaCodeList[] | undefined>();
  const [taxSettingList, setTaxSettingList] = useState<TaxSettingParam[]>([]);

  const [form] = Form.useForm();

  const formulaCategoryChange = useCallback(() => {
    const data = {
      formulaCategoryCode: 32,
      isIncludeCommonTemplate: false,
    };

    NewProductService.ProductSchemaFormulaMgmtService.list(data).then(res => {
      const list = res.map(item => ({
        value: item.formulaCode!,
        label: item.formulaCode!,
      }));
      setFormulaCodeList(list);
    });
  }, []);

  const getTaxSettingList = useCallback(() => {
    NewMarketService.PackageAgreementConfigMgmtService.queryPackageTaxSetting(packageId).then(res => {
      setTaxSettingList(res);
    });
  }, [packageId]);

  useEffect(() => {
    getTaxSettingList();
    formulaCategoryChange();
  }, [formulaCategoryChange, getTaxSettingList]);

  const formulaCategoryOptions = useMemo(
    () =>
      (enums?.bizTopic?.filter(enumItem => enumItem.dictValue === '32') || []).map((item: BizDict) => ({
        value: item.dictValue,
        label: item.dictValueName,
      })),
    [enums]
  );

  const deleteItem = useCallback(
    (record: TaxSettingParam) => {
      const data = {
        id: record.id,
        packageId,
        taxType: +record.taxType,
        taxCalculateType: +record.taxCalculateType,
        taxFormulaCode: record.taxFormulaCode,
        taxAmount: record.taxAmount,
        accumulated: record.accumulated,
      };

      NewMarketService.PackageAgreementConfigMgmtService.deletePackageTaxSetting(data).then(res => {
        getTaxSettingList();
      });
    },
    [getTaxSettingList, packageId]
  );

  const onAdd = useCallback(() => {
    setVisible(true);
  }, []);

  const onSubmitChange = useCallback(async () => {
    try {
      const values: TaxSettingParam = await form.validateFields();
      const data = {
        packageId,
        taxType: +values.taxType,
        taxCalculateType: +values.taxCalculateType,
        taxFormulaCode: values.taxFormulaCode,
        taxAmount: values.taxAmount ? values.taxAmount : '',
        accumulated: values.accumulated,
      };

      NewMarketService.PackageAgreementConfigMgmtService.savePackageTaxSetting(data).then(res => {
        setVisible(false);
        getTaxSettingList();
        form.resetFields();
      });
    } catch (error) {
      console.log(error);
    }
  }, [form, getTaxSettingList, packageId]);

  const onCancelChange = useCallback(() => {
    setVisible(false);
    form.resetFields();
  }, [form]);

  const columns = [
    {
      title: t('Tax Type'),
      dataIndex: 'taxType',
      render: (text: number) => (
        <span>{(enums.packageTaxType || []).find(enumitem => enumitem.dictValue === `${text}`)?.dictValueName}</span>
      ),
    },
    {
      title: t('Tax Value Type'),
      dataIndex: 'taxCalculateType',
      render: (text: number) => (
        <span>
          {(enums.packageTaxCalculateType || []).find(enumitem => enumitem.dictValue === `${text}`)?.dictValueName}
        </span>
      ),
    },
    {
      title: t('Formula Code'),
      dataIndex: 'taxFormulaCode',
      render: (text: string) => <span>{text || t('- -')}</span>,
    },
    {
      title: t('Tax Amount'),
      dataIndex: 'taxAmount',
      render: (text: string) => <span>{text || t('- -')}</span>,
    },
    {
      title: t('Accumulated to Premium'),
      dataIndex: 'accumulated',
      render: (text: string) => renderOptionName(text, YesNoOptions),
    },
    {
      title: t('Actions'),
      dataIndex: 'function',
      key: 'function',
      fix: 'right',
      className: 'action',
      render: (text: any, record: TaxSettingParam, index: number) => (
        <DeleteAction
          disabled={disabled}
          doubleConfirmType="popconfirm"
          onClick={() => deleteItem(record)}
          deleteConfirmContent={t('Are you sure to delete this record?')}
        />
      ),
    },
  ];

  const checkTaxValueType = useCallback(
    (value: string) => {
      if (value && value === TaxTypeKeyType.Levy) {
        form.setFieldsValue({
          taxCalculateType: TaxValueTypeKeyType.ByFormula,
          formulaCategory: formulaCategoryOptions?.[0].value,
          taxFormulaCode: undefined,
        });
      }
      if (value && value === TaxTypeKeyType.StampDuty) {
        form.setFieldsValue({
          taxCalculateType: TaxValueTypeKeyType.ByAmount,
        });
      }
    },
    [form, formulaCategoryOptions]
  );

  const taxCalculateType = Form.useWatch('taxCalculateType', form);
  const taxType = Form.useWatch('taxType', form);

  return (
    <div className="customer-service agreement-block">
      {disabled ||
      (Array.isArray(taxSettingList) && taxSettingList.length === (enums.packageTaxType || []).length) ? null : (
        <Button icon={<Icon type="add" />} style={{ width: '100%' }} className="long-add-btn" onClick={onAdd}>
          {t('Add')}
        </Button>
      )}
      <Table
        columns={columns}
        dataSource={taxSettingList}
        scroll={{ x: 'max-content' }}
        pagination={false}
        emptyType="text"
        style={{ width: '100%' }}
      />
      <Drawer
        open={visible}
        title={t('Tax Setting')}
        onClose={onCancelChange}
        onSubmit={onSubmitChange}
      >
        <Form form={form} colon={false} layout="vertical">
          <Row type="flex">
            <Col span={12}>
              <Form.Item
                label={t('Tax Type')}
                name="taxType"
                rules={[
                  {
                    required: true,
                    message: t('Please select'),
                  },
                ]}
              >
                <GeneralSelect
                  option={(enums.packageTaxType || [])
                    .filter(
                      enumTaxType => !taxSettingList.find(listItem => `${listItem.taxType}` === enumTaxType.dictValue)
                    )
                    .map((item: BizDict) => ({
                      value: item.dictValue,
                      label: item.dictValueName,
                    }))}
                  style={{ width: 360 }}
                  onChange={(value: string) => checkTaxValueType(value)}
                  placeholder={t('Please select')}
                />
              </Form.Item>
            </Col>
            <Col style={{ display: 'flex' }} span={12}>
              <Form.Item
                label={t('Tax Value Type')}
                name="taxCalculateType"
                rules={[
                  {
                    required: true,
                    message: t('Please select'),
                  },
                ]}
              >
                <GeneralSelect
                  option={(enums.packageTaxCalculateType || []).map((item: BizDict) => ({
                    value: item.dictValue,
                    label: item.dictValueName,
                  }))}
                  style={{ width: 360 }}
                  placeholder={t('Please select')}
                  disabled={taxType === TaxTypeKeyType.Levy}
                />
              </Form.Item>
            </Col>
            {taxCalculateType === TaxValueTypeKeyType.ByFormula ? (
              <Col span={12} hidden={taxCalculateType !== TaxValueTypeKeyType.ByFormula}>
                <Form.Item label={t('Formula')} required>
                  <Space.Compact>
                    <Form.Item
                      name="formulaCategory"
                      rules={[
                        {
                          required: true,
                          message: t('Please select'),
                        },
                      ]}
                      initialValue={formulaCategoryOptions?.[0]?.value}
                    >
                      <GeneralSelect
                        option={formulaCategoryOptions}
                        style={{ width: 120 }}
                        disabled
                        placeholder={t('Please select')}
                      />
                    </Form.Item>
                    <Form.Item
                      name="taxFormulaCode"
                      rules={[
                        {
                          required: true,
                          message: t('Please select'),
                        },
                      ]}
                    >
                      <GeneralSelect option={formulaCodeList} placeholder={t('Please select')} />
                    </Form.Item>
                  </Space.Compact>
                </Form.Item>
              </Col>
            ) : null}
            {taxCalculateType === TaxValueTypeKeyType.ByAmount ? (
              <Col style={{ display: 'flex' }} span={12}>
                <Form.Item
                  label={t('Tax Amount')}
                  name="taxAmount"
                  rules={[
                    {
                      required: true,
                      message: t('Please input'),
                    },
                  ]}
                >
                  <InputNumber style={{ width: 360 }} placeholder={t('Please input')} />
                </Form.Item>
              </Col>
            ) : null}
            <Col style={{ display: 'flex' }} span={12}>
              <Form.Item
                label={t('Accumulated to Premium')}
                name="accumulated"
                rules={[
                  {
                    required: true,
                    message: t('Please input'),
                  },
                ]}
                initialValue
              >
                <Radio.Group>
                  <Radio value>{t('Yes')}</Radio>
                  <Radio value={false}>{t('No')}</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Drawer>
    </div>
  );
};

export default TaxSetting;
