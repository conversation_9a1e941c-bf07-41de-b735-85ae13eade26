/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { useEffect, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Col, Form, Radio, Row } from 'antd';

import { PackageParticipatingParams } from 'genesis-web-service';

import AgreementContainerLayout from '@market/components/AgreementContainerLayout';
import GeneralSelect from '@market/components/GeneralSelect';
import { selectEnums } from '@market/redux/selector';
import { BizDict, ParticipatingProductProps } from '@market/request/interface';

import styles from './ParticipatingProduct.module.scss';

/**
 * 格式化小数：正整数
 * example
 * 1. input: -1.232445, output: 1
 */
export const formatDecimals2Int = (value: string | number): string => {
  const reg = /^(\-)*(\d+).*$/;
  if (typeof value === 'string') {
    return !isNaN(+value) ? value.replace(reg, '$2') : '';
  }
  if (typeof value === 'number') {
    return !isNaN(value) ? String(value).replace(reg, '$2') : '';
  }
  return '';
};

const ParticipatingProduct = ({
  disabled,
  participatingProductData,
  refInstance,
}: ParticipatingProductProps<PackageParticipatingParams>): JSX.Element => {
  const [form] = Form.useForm();
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const enums: Record<string, Array<BizDict>> = useSelector(selectEnums);
  const [t] = useTranslation(['market', 'common']);

  useImperativeHandle(refInstance, () => ({
    form,
  }));

  useEffect(() => {
    form.setFieldsValue({
      cashBonusRule: participatingProductData?.cashBonusRule?.toString() || '2',
      cashBonusPaymentOption: participatingProductData?.cashBonusPaymentOption?.toString(),
    });
  }, [participatingProductData]);

  return (
    <div className={`w-full ${styles.participatewrap}`}>
      <AgreementContainerLayout title={t('Participating Product')} agreementCode="participatingProduct">
        <Form layout="vertical" form={form}>
          <Row style={{ marginTop: 24 }}>
            <Col span={24}>
              <Form.Item
                required
                label={t('Cash Bonus Configuration')}
                name="cashBonusRule"
                rules={[
                  {
                    required: true,
                    message: t('Please select'),
                  },
                ]}
              >
                <Radio.Group
                  disabled={disabled}
                  options={enums.packagePeriodRule.map(item => ({
                    label: item.itemName,
                    value: item.itemExtend1,
                  }))}
                />
              </Form.Item>
            </Col>
            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) =>
                getFieldValue('cashBonusRule') === '1' && (
                  <Col span={24}>
                    <Form.Item
                      required
                      label={t('Payment Option')}
                      name="cashBonusPaymentOption"
                      rules={[
                        {
                          required: true,
                          message: t('Please select'),
                        },
                      ]}
                    >
                      <GeneralSelect
                        disabled={disabled}
                        option={enums.survivalPaymentDrawType.map(item => ({
                          label: item.itemName,
                          value: item.itemExtend1,
                        }))}
                      />
                    </Form.Item>
                  </Col>
                )
              }
            </Form.Item>
          </Row>
        </Form>
      </AgreementContainerLayout>
    </div>
  );
};

export default ParticipatingProduct;
