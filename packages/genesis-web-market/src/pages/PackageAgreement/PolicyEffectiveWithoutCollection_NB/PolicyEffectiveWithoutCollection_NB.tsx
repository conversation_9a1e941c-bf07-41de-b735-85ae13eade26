import { Ref, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Checkbox, Col, Form, Input, InputNumber, Row } from 'antd';

import { YesOrNoDictValue } from 'genesis-web-service';

import GeneralSelect from '@market/components/GeneralSelect';
import LabelWithTooltip from '@market/components/LabelWithTooltip';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { formErrorHandler } from '@market/utils/formUtils';

import { GracePeriodUnitEnum, PEfWithoutPayResponse, PEfWithoutPayValueInfo } from './interface';

interface Props {
  disabled: boolean;
  refInstance: Ref<any>;
  initialValues: PEfWithoutPayResponse;
}

export const PolicyEffectiveWithoutCollection_NB = (props: Props) => {
  const { initialValues, disabled, refInstance } = props;
  const [t, i18n] = useTranslation(['market', 'common']);
  const [hasChangedStatus, setHasChangedStatus] = useState<boolean>(false);

  /* ============== 枚举使用start ============== */
  const withoutPayOverdueStatusOptions = useBizDictAsOptions('withoutPayOverdueStatus');
  const gracePeriodUnitOptions = useBizDictAsOptions('gracePeriodUnit');
  /* ============== 枚举使用end ============== */
  const [form] = Form.useForm();

  useEffect(() => {
    if (initialValues) {
      const allowPolicyEffectiveWithoutCollectionValue = initialValues?.allowPolicyEffectiveWithoutCollection
        ? `${initialValues?.allowPolicyEffectiveWithoutCollection}` === YesOrNoDictValue.YES
        : initialValues?.withoutPayOverdueStatus || initialValues?.autoDeductionDateDiffDueDate;
      form.setFieldsValue({
        allowPolicyEffectiveWithoutCollection: allowPolicyEffectiveWithoutCollectionValue,
        autoDeductionDateDiffDueDate: initialValues?.autoDeductionDateDiffDueDate || undefined,
        withoutPayGracePeriodUnit: initialValues?.withoutPayGracePeriodUnit
          ? `${initialValues?.withoutPayGracePeriodUnit}`
          : GracePeriodUnitEnum.Days,
        withoutPayOverdueStatus: initialValues?.withoutPayOverdueStatus
          ? `${initialValues?.withoutPayOverdueStatus}`
          : undefined,
        withoutPayGracePeriod: initialValues?.withoutPayGracePeriod || undefined,
      });
    }
  }, [initialValues]);

  const clearFormInfo = () => {
    form.setFieldsValue({
      allowPolicyEffectiveWithoutCollection: false,
      autoDeductionDateDiffDueDate: undefined,
      withoutPayGracePeriodUnit: GracePeriodUnitEnum.Days,
      withoutPayOverdueStatus: undefined,
      withoutPayGracePeriod: undefined,
    });
  };

  useImperativeHandle(refInstance, () => ({
    getDataForSubmit: () =>
      form
        .validateFields()
        .then((values: PEfWithoutPayValueInfo) => {
          const param = values.allowPolicyEffectiveWithoutCollection
            ? {
                ...values,
                autoDeductionDateDiffDueDateUnit: '1', // 固定单位为 day
                allowPolicyEffectiveWithoutCollection: YesOrNoDictValue.YES,
              }
            : {
                allowPolicyEffectiveWithoutCollection: YesOrNoDictValue.NO,
              };

          return {
            validated: true,
            data: {
              policyEffectiveWithoutPay: param,
            },
          };
        })
        .catch(formErrorHandler(form)),
    hasChanged: () => hasChangedStatus,
  }));

  const renderPolicyEffectiveFormItems = useCallback(
    () => (
      <Row className="w-full">
        <Col span={8}>
          <Form.Item
            label={
              <LabelWithTooltip
                title={t('Auto Deduction Date Compare to Due Date')}
                tooltip={t(
                  'Deduction Date Compare to Due Date.e.g. if deduct on due date, enter 0 if deduct prior to due date, enter 1, 2, 3 etc.'
                )}
                className="whitespace-nowrap"
              />
            }
            name="autoDeductionDateDiffDueDate"
            rules={[
              {
                required: true,
                message: t('Please input'),
              },
            ]}
          >
            <InputNumber
              min={0}
              maxLength={9}
              precision={0}
              placeholder={t('Please input')}
              disabled={disabled}
              onChange={() => setHasChangedStatus(true)}
              addonAfter={t('Day(s)')}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            required
            label={
              <LabelWithTooltip
                title={t('Grace Period')}
                tooltip={t(
                  'The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59'
                )}
              />
            }
          >
            <Form.Item
              noStyle
              name="withoutPayGracePeriod"
              rules={[
                {
                  required: true,
                  message: t('Please input'),
                },
              ]}
            >
              <InputNumber
                min={0}
                maxLength={9}
                precision={0}
                placeholder={t('Please input')}
                disabled={disabled}
                onChange={() => setHasChangedStatus(true)}
                addonAfter={
                  <Form.Item
                    noStyle
                    name="withoutPayGracePeriodUnit"
                    rules={[{ required: true, message: t('Please select') }]}
                  >
                    <GeneralSelect
                      disabled={disabled}
                      style={{ width: 110 }}
                      option={gracePeriodUnitOptions}
                      onChange={() => setHasChangedStatus(true)}
                    />
                  </Form.Item>
                }
              />
            </Form.Item>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={t('Overdue Status')}
            name="withoutPayOverdueStatus"
            rules={[{ required: true, message: t('Please select') }]}
          >
            <GeneralSelect
              disabled={disabled}
              option={withoutPayOverdueStatusOptions}
              onChange={() => setHasChangedStatus(true)}
            />
          </Form.Item>
        </Col>
      </Row>
    ),
    [disabled, gracePeriodUnitOptions, t, withoutPayOverdueStatusOptions]
  );

  return (
    <Form
      form={form}
      layout="vertical"
      onValuesChange={() => {
        setHasChangedStatus(true);
      }}
      className="mb-6 w-full"
    >
      <div className="py-2">
        <Form.Item noStyle name="allowPolicyEffectiveWithoutCollection" valuePropName="checked">
          <Checkbox
            disabled={disabled}
            onChange={value => {
              clearFormInfo();
              form.setFieldsValue({
                allowPolicyEffectiveWithoutCollection: value.target.checked,
              });
            }}
            size="small"
            className="mr-2"
          />
        </Form.Item>
        {t('Policy Effective Without Collection (NB)')}
      </div>
      <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => true}>
        {({ getFieldValue }) =>
          getFieldValue('allowPolicyEffectiveWithoutCollection') ? renderPolicyEffectiveFormItems() : null
        }
      </Form.Item>
    </Form>
  );
};

export default PolicyEffectiveWithoutCollection_NB;
