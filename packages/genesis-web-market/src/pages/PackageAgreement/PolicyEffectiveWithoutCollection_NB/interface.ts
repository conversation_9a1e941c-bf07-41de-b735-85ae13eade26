export interface PEfWithoutPayValueInfo {
  autoDeductionDateDiffDueDate?: string;
  withoutPayGracePeriod?: string;
  withoutPayGracePeriodUnit: string;
  withoutPayOverdueStatus?: string;
  allowPolicyEffectiveWithoutCollection?: boolean | string;
}

export type PEfWithoutPayKeyName =
  | 'autoDeductionDateDiffDueDate'
  | 'withoutPayGracePeriod'
  | 'withoutPayGracePeriodUnit'
  | 'withoutPayOverdueStatus';

export interface PEfWithoutPayAgreementEditList {
  key?: string;
  editing?: boolean;
  withoutPayPremiumNoticeRule?: string | number;
  withoutPayPremiumNoticeDateDiffDueDate?: string;
}

export interface PEfWithoutPayResponse {
  allowPolicyEffectiveWithoutCollection: string;
  autoDeductionDateDiffDueDate?: string;
  withoutPayGracePeriod?: string;
  autoDeductionDateDiffDueDateUnit?: string;
  withoutPayGracePeriodUnit?: string;
  withoutPayOverdueStatus?: string;
  withoutPayPremiumNoticeAgreementList?: {
    withoutPayPremiumNoticeRule?: string | number;
    withoutPayPremiumNoticeDateDiffDueDate?: string;
    withoutPayPremiumNoticeDateDiffDueDateUnit: number | string;
  }[];
}

export enum GracePeriodUnitEnum {
  Days = '1',
  Months = '2',
}
