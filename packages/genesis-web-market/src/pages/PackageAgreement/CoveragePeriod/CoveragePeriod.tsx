import { useTranslation } from 'react-i18next';

import { <PERSON><PERSON>, Col, Popconfirm } from 'antd';

import { Icon, Table } from '@zhongan/nagrand-ui';

import { ProductCategoryItemExtend1 } from 'genesis-web-service/lib/product';
import { CoveragePeriodDTO } from 'genesis-web-service/service-types/market-types/package';

import { DetailPageMode } from '@market/common/interface';
import { useBizDict } from '@market/hook/bizDict';
import { renderEnumName } from '@market/utils/enum';

import InsuranceCoveragePeriodDrawer from './InsuranceCoveragePeriodDrawer';

interface Props {
  coveragePeriodData: {
    coveragePeriods: CoveragePeriodDTO[];
  };
  mode: DetailPageMode;
  isShowEditButton: boolean;
  selectedExpiryDateRule: string;
  deletePeriod: (key: string, data: CoveragePeriodDTO) => void;
  queryCoveragePeriods: () => void;
  packageCategory: ProductCategoryItemExtend1;
}

enum ExpiryDateRuleEnum {
  UserInput = '1',
}

export const CoveragePeriod = ({
  coveragePeriodData,
  mode,
  isShowEditButton,
  deletePeriod,
  queryCoveragePeriods,
  packageCategory,
  selectedExpiryDateRule,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  /* ============== 枚举使用start ============== */
  const coveragePeriodTypeBizDicts = useBizDict('coveragePeriodType');
  const expiryDateCalMethodBizDicts = useBizDict('expiryDateCalMethod');
  const coveragePeriodValueTypeBizDicts = useBizDict('coveragePeriodValueType');
  const coverageTimeAgreementTypeBaseBizDicts = useBizDict('coverageTimeAgreementTypeBase');
  const expiryDateAdjustmentTypeBizDicts = useBizDict('expiryDateAdjustmentType');
  /* ============== 枚举使用end ============== */

  if (!coveragePeriodData.coveragePeriods) {
    coveragePeriodData.coveragePeriods = [];
  }
  const isHideExpiryFields = selectedExpiryDateRule === ExpiryDateRuleEnum.UserInput;
  const showTableData = coveragePeriodData.coveragePeriods.filter(i => i.isAdded);

  let columns = [
    {
      title: t('Coverage Period Type'),
      dataIndex: 'coveragePeroidAgreementType',
      width: 180,
      render: (text: string) => renderEnumName(text, coveragePeriodTypeBizDicts),
    },
    {
      title: t('Coverage Period Value Type'),
      dataIndex: 'coveragePeriodValueType',
      width: 220,
      render: (text: string) => {
        const renderText = renderEnumName(text, coveragePeriodValueTypeBizDicts);
        return <span>{renderText || '--'}</span>;
      },
    },
    {
      title: t('Coverage Period'),
      width: 150,
      render: (_, record: CoveragePeriodDTO) => {
        const { coveragePeroidAgreementType, elementValue, maxCoveragePeriod, minCoveragePeriod } = record;
        const suffix =
          +coveragePeroidAgreementType === 6
            ? t('Age')
            : renderEnumName(coveragePeroidAgreementType, coveragePeriodTypeBizDicts);

        if (maxCoveragePeriod && minCoveragePeriod) {
          return `${minCoveragePeriod} - ${maxCoveragePeriod} ${suffix}`;
        }

        if (elementValue) {
          return `${elementValue} ${suffix}`;
        }

        return '--';
      },
    },
    {
      title: t('Expiry Time Agreement Type'),
      dataIndex: 'coverageTimeAgreementType',
      width: '280px',
      render: (text: string) => renderEnumName(text, coverageTimeAgreementTypeBaseBizDicts),
    },
    {
      title: t('Expiry Date Calculation Method'),
      dataIndex: 'expiryDateCalMethod',
      width: '200px',
      render: (text: string) => renderEnumName(text, expiryDateCalMethodBizDicts) || t('--'),
    },
    {
      title: t('Expiry Date Adjustment'),
      dataIndex: 'expiryDateAdjustmentType',
      width: '200px',
      render: (text: string) => renderEnumName(text, expiryDateAdjustmentTypeBizDicts) || t('--'),
    },
  ];
  const readOnly = mode === 'view' || !isShowEditButton;

  if (isHideExpiryFields) {
    columns = columns.slice(0, -3);
  }

  const tableColumn = columns.concat([
    {
      title: t('Actions'),
      dataIndex: 'action',
      align: 'right',
      fixed: 'right',
      width: '120px',
      render: (_, record: CoveragePeriodDTO) => (
        <Popconfirm
          placement="topLeft"
          title={t('Are you sure to delete this record?')}
          onConfirm={() => deletePeriod('queryCoveragePeriods', record)}
          okText={t('yes')}
          cancelText={t('no')}
          disabled={readOnly}
        >
          <Icon type="delete" disabled={readOnly} />
        </Popconfirm>
      ),
    },
  ]);

  return (
    <Col span={24}>
      <Button
        icon={<Icon type="add" />}
        disabled={readOnly}
        onClick={() => InsuranceCoveragePeriodDrawer.open()}
        className="long-add-btn"
      >
        {t('Add')}
      </Button>
      <InsuranceCoveragePeriodDrawer
        coveragePeriodData={coveragePeriodData}
        columns={columns}
        onRefresh={queryCoveragePeriods}
        packageCategory={packageCategory}
        isHideExpiryFields={isHideExpiryFields}
      />
      <Table
        dataSource={showTableData}
        rowKey="index"
        style={{ marginBottom: 24 }}
        columns={tableColumn}
        scroll={{ x: 'max-content' }}
        emptyType="text"
        pagination={false}
      />
    </Col>
  );
};

export default CoveragePeriod;
