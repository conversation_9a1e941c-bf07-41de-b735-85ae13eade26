import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, InputNumber, Radio, RadioChangeEvent, Row } from 'antd';
import { Drawer } from '@zhongan/nagrand-ui';

import { CoveragePeriodType } from 'genesis-web-service/lib/common.interface';
import { ProductCategoryItemExtend1 } from 'genesis-web-service/lib/product';

import GeneralSelect from '@market/components/GeneralSelect';
import LabelWithTooltip from '@market/components/LabelWithTooltip';
import { useBizDict } from '@market/hook/bizDict';

import { ExpiryDateCalMethodType } from './interface';

interface Props {
  drawerOpen: boolean;
  setDrawerOpen: (visible: boolean) => void;
  onSubmit: (values: Record<string, any>) => boolean;
  type?: 'brief' | 'full';
  packageCategory: ProductCategoryItemExtend1;
  isHideExpiryFields: boolean;
}

export const CustomizeCoveragePeriod = ({
  drawerOpen,
  setDrawerOpen,
  onSubmit,
  type = 'full',
  packageCategory,
  isHideExpiryFields,
}: Props) => {
  const [t] = useTranslation(['market', 'common']);

  /* ============== 枚举使用start ============== */
  const coveragePeriodTypeBizDicts = useBizDict('coveragePeriodType');
  const expiryDateCalMethodBizDicts = useBizDict('expiryDateCalMethod');
  const coveragePeriodValueTypeBizDicts = useBizDict('coveragePeriodValueType');
  const coverageTimeAgreementTypeBaseBizDicts = useBizDict('coverageTimeAgreementTypeBase');
  const expiryDateAdjustmentTypeBizDicts = useBizDict('expiryDateAdjustmentType');
  /* ============== 枚举使用end ============== */
  const [valueTypeDisabled, setValueTypeDisabled] = useState(true);
  const [valueRequired, setValueRequired] = useState(false);
  const [coverageTimeTypeRequired, setCoverageTimeTypeRequired] = useState(false);
  const [expiryDateCalMethodDisabled, setExpiryDateCalMethodDisabled] = useState(false);
  const [expiryDateCalMethodRequired, setExpiryDateCalMethodRequired] = useState(false);
  const [rangeShow, setRangeShow] = useState(false);
  const [form] = Form.useForm();
  const [rangeUnit, setRangeUnitState] = useState<string>();
  const [coveragePeriodRangeError, setCoveragePeriodRangeError] = useState(false);

  const needDisplayExpiryDateAdjustmentType = [CoveragePeriodType.Month, CoveragePeriodType.ToCertainYear].includes(
    +form.getFieldValue('coveragePeroidAgreementType')
  );

  const setRangeUnit = (value: string) => {
    let text = '';
    if (value === '5') {
      text = t('Year');
    } else if (value === '6') {
      text = t('Age');
    } else if (coveragePeriodTypeBizDicts.length > 0) {
      const periodType = coveragePeriodTypeBizDicts.find(i => i.dictValue === value);
      if (periodType) {
        text = periodType.dictValueName;
      }
    } else {
      text = value;
    }

    setRangeUnitState(text);
  };

  const changeCoveragePeriodType = (value: string) => {
    let _valueTypeDisabled = valueTypeDisabled;
    let _valueRequired = valueRequired;
    setRangeUnit(value);
    let _rangeShow = true;
    if (type === 'full') {
      let _coverageTimeTypeRequired = false;
      let _expiryDateCalMethodDisabled = true;
      let _expiryDateCalMethodRequired = false;
      // Day,Week,Month,To certain year,To certain age,Hour,Minutes 需要设置 Expiry Time Agreement Type
      if (['1', '2', '3', '5', '6', '9', '10'].indexOf(value) > -1) {
        _coverageTimeTypeRequired = true;
      }
      setCoverageTimeTypeRequired(_coverageTimeTypeRequired);
      if (value === '6') {
        _expiryDateCalMethodDisabled = false;
        _expiryDateCalMethodRequired = true;
      }
      setExpiryDateCalMethodDisabled(_expiryDateCalMethodDisabled);
      setExpiryDateCalMethodRequired(_expiryDateCalMethodRequired);

      if (value === '7') {
        _valueTypeDisabled = true;
      } else {
        _valueTypeDisabled = false;
      }
      setValueTypeDisabled(_valueTypeDisabled);
      if (_valueTypeDisabled) {
        setTimeout(() => {
          form.setFieldsValue({
            coveragePeriodValueType: '2',
          });
        }, 50);
      }

      if (
        // Day,Week,Month,To certain year,To certain age,Hour,Minutes 需要设置 Expiry Time Agreement Type
        ['1', '2', '3', '5', '6', '9', '10'].indexOf(value) > -1 &&
        (_valueTypeDisabled || form.getFieldValue('coveragePeriodValueType') === '2')
      ) {
        _valueRequired = true;
      } else {
        _valueRequired = false;
      }
      setValueRequired(_valueRequired);
      if (!_valueRequired) {
        setTimeout(() => {
          form.resetFields(['elementValue']);
        }, 50);
      }

      if (['7'].indexOf(value) > -1 || form.getFieldValue('coveragePeriodValueType') !== '1') {
        _rangeShow = false;
      }
      setRangeShow(_rangeShow);
    } else {
      if (['7'].indexOf(value) > -1) {
        _rangeShow = false;
      }
      setRangeShow(_rangeShow);
    }
  };

  const changeValueType = (value: RadioChangeEvent) => {
    let _valueRequired = valueRequired;
    const coveragePeriodType = form.getFieldValue('coveragePeroidAgreementType');
    if (value.target.value === '1') {
      _valueRequired = false;
    } else if (coveragePeriodType === '7') {
      _valueRequired = false;
    } else {
      _valueRequired = true;
    }

    setValueRequired(_valueRequired);
    setRangeShow(value.target.value === '1');
    if (!_valueRequired) {
      setTimeout(() => {
        form.resetFields(['elementValue']);
      }, 50);
    }
  };

  const getCoveragePeriodTypeOption = () => {
    // EB => Hide the option: Seconds, Minutes, Hour, To certain Age, Whole Life
    const EBhiderr = [
      CoveragePeriodType.ToCertainAge,
      CoveragePeriodType.WholeLife,
      CoveragePeriodType.Hour,
      CoveragePeriodType.Minutes,
    ];
    if (packageCategory === ProductCategoryItemExtend1.GroupEmployeeBenefit) {
      return (coveragePeriodTypeBizDicts || []).filter(item => !EBhiderr.includes(Number(item.dictValue)));
    }
    return coveragePeriodTypeBizDicts || [];
  };

  const coveragePeriodRangeValidator = () => {
    const { getFieldValue } = form;
    const min = getFieldValue('minCoveragePeriod');
    const max = getFieldValue('maxCoveragePeriod');

    const isMinGreaterThanMax = !(!min && min !== 0 && !max && max !== 0) && min >= max;

    setCoveragePeriodRangeError(isMinGreaterThanMax);
    if (isMinGreaterThanMax) {
      return Promise.reject();
    }
    return Promise.resolve();
  };

  const submit = () => {
    form.validateFields().then(values => {
      const isSuccess = onSubmit(values);
      if (isSuccess) {
        form.resetFields();
      }
    });
  };

  return (
    <Drawer
      open={drawerOpen}
      title={t('Coverage Period')}
      onClose={() => {
        setDrawerOpen(false);
        form.resetFields();
      }}
      onSubmit={submit}
    >
      <Form form={form} layout="vertical">
        <Row className="coverage-period-drawer">
          <Col span={24}>
            <Form.Item
              label={t('Coverage Period Type')}
              colon={false}
              name="coveragePeroidAgreementType"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <GeneralSelect
                onChange={changeCoveragePeriodType}
                option={getCoveragePeriodTypeOption().map((item, index) => ({
                  value: item.dictValue,
                  label: item.dictValueName,
                }))}
                style={{ width: 240 }}
              />
            </Form.Item>
          </Col>
          {type === 'full' && (
            <Col span={8}>
              <Form.Item
                label={t('Coverage Period Value Type')}
                colon={false}
                name="coveragePeriodValueType"
                rules={[
                  {
                    required: true,
                    message: t('Please select'),
                  },
                ]}
              >
                <Radio.Group
                  disabled={valueTypeDisabled}
                  options={(coveragePeriodValueTypeBizDicts || []).map((item, index) => ({
                    value: item.dictValue,
                    label: item.dictValueName,
                  }))}
                  onChange={changeValueType}
                  style={{ width: 240 }}
                />
              </Form.Item>
            </Col>
          )}
          {type === 'full' && valueRequired && (
            <Col span={16}>
              <Form.Item
                label={t('Element')}
                colon={false}
                name="elementValue"
                rules={[
                  {
                    required: true,
                    message: t('Please select'),
                  },
                ]}
              >
                <InputNumber
                  min={0}
                  max={9999999999}
                  maxLength={10}
                  placeholder={t('Please input')}
                  style={{ width: 240 }}
                />
              </Form.Item>
            </Col>
          )}
          {rangeShow && (
            <Col span={16}>
              <div>{t('Coverage Period Range')}</div>
              <div className="coverage-period-range" style={{ marginTop: 8, paddingLeft: 0, marginBottom: 24 }}>
                <Form.Item
                  colon={false}
                  name="minCoveragePeriod"
                  rules={[
                    {
                      validator: coveragePeriodRangeValidator,
                    },
                  ]}
                  noStyle
                >
                  <InputNumber min={0} max={999999999} precision={0} style={{ width: 84 }} />
                </Form.Item>
                <span className="mx-1">-</span>
                <Form.Item
                  colon={false}
                  name="maxCoveragePeriod"
                  rules={[
                    {
                      validator: coveragePeriodRangeValidator,
                    },
                  ]}
                  noStyle
                >
                  <InputNumber min={0} max={999999999} precision={0} style={{ width: 84 }} />
                </Form.Item>
                <span className="mx-1">{rangeUnit}</span>
              </div>
              {coveragePeriodRangeError ? (
                <div className="coverage-period-range-error">
                  {t('The second value should greater than the first value')}
                </div>
              ) : null}
            </Col>
          )}
          {type === 'full' && !isHideExpiryFields && (
            <Col span={24}>
              <Form.Item
                label={t('Expiry Time Agreement Type')}
                colon={false}
                name="coverageTimeAgreementType"
                rules={[
                  {
                    required: coverageTimeTypeRequired,
                    message: t('Please select'),
                  },
                ]}
              >
                <Radio.Group disabled={valueTypeDisabled || !coverageTimeTypeRequired}>
                  {coverageTimeAgreementTypeBaseBizDicts.map((item, index) => (
                    <Radio key={index} value={item.dictValue}>
                      {item.dictValueName}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </Col>
          )}
          {type === 'full' && expiryDateCalMethodRequired && !isHideExpiryFields && (
            <Col span={8}>
              <Form.Item
                label={t('Expiry date calculation method')}
                colon={false}
                name="expiryDateCalMethod"
                rules={[
                  {
                    required: expiryDateCalMethodRequired,
                    message: t('Please select'),
                  },
                ]}
                initialValue={ExpiryDateCalMethodType.FirstPolicyAnniversaryDateAfterInsuredXAg}
              >
                <GeneralSelect
                  disabled={expiryDateCalMethodDisabled}
                  option={(expiryDateCalMethodBizDicts || [])
                    .filter(option => option.dictValue !== ExpiryDateCalMethodType.BasedOnLeastPaymentFrequency)
                    .map((item, index) => ({
                      key: index,
                      value: item.dictValue,
                      label: item.dictValueName,
                      title: item.dictValueName,
                    }))}
                  style={{ width: 240 }}
                />
              </Form.Item>
            </Col>
          )}
          {needDisplayExpiryDateAdjustmentType && !isHideExpiryFields ? (
            <Col span={16}>
              <Form.Item
                label={
                  <LabelWithTooltip
                    title={t('Expiry Date Adjustment')}
                    tooltip={
                      <React.Fragment>
                        <div>
                          <span className="font-bold relative pl-4 before:content-[''] before:absolute before:left-0 before:top-[6px] before:-translate-y-1/2 before:w-1.5 before:h-1.5 before:rounded-full before:bg-white">
                            {t('Effective Date Month End to Expiry Date Month End')}
                          </span>
                          <span className="mx-1">:</span>

                          {t(
                            'If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If "Effective Date + Coverage Period - 1s" 20230228 00:00:00 ~ 20240228 23:59:59)'
                          )}
                        </div>
                        <div>
                          <span className="font-bold relative pl-4 before:content-[''] before:absolute before:left-0 before:top-[6px] before:-translate-y-1/2 before:w-1.5 before:h-1.5 before:rounded-full before:bg-white">
                            {t('Adjust to 1st if Effective Day Not in Expiry Month')}
                          </span>
                          <span className="mx-1">:</span>
                          {t(
                            'If the effective date falls on a day that does not exist in the expiry month, and the calculated expiry date is set to the last day of that month, this adjustment will shift the expiry date to the 1st of the following month. (e.g. 2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1)'
                          )}
                        </div>
                      </React.Fragment>
                    }
                  />
                }
                colon={false}
                name="expiryDateAdjustmentType"
              >
                <GeneralSelect
                  option={(expiryDateAdjustmentTypeBizDicts || []).map(item => ({
                    value: item.dictValue,
                    label: item.dictValueName,
                  }))}
                  className="!w-full"
                />
              </Form.Item>
            </Col>
          ) : null}
        </Row>
      </Form>
    </Drawer>
  );
};
