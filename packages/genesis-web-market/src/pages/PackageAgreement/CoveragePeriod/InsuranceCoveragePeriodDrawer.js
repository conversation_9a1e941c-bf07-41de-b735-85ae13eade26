import React, { Component } from 'react';

import { Button, message } from 'antd';

import { Icon, Table, Drawer } from '@zhongan/nagrand-ui';

import { t } from '@market/i18n';
import { NewMarketService } from '@market/services/market/market.service.new';

import { CustomizeCoveragePeriod } from './CustomizeCoveragePeriod';

let insuranceCoveragePeriodDrawer;

class InsuranceCoveragePeriodDrawer extends Component {
  static open() {
    if (insuranceCoveragePeriodDrawer) {
      insuranceCoveragePeriodDrawer.setState({
        visible: true,
      });
    }
    insuranceCoveragePeriodDrawer.setInitData();
  }

  constructor(props) {
    super(props);
    insuranceCoveragePeriodDrawer = this;

    this.state = {
      visible: false,
      selectedRowKeys: [],
      data: [],
      customizeDrawerVisible: false,
    };
  }

  close = () => {
    this.setState({
      visible: false,
      selectedRowKeys: [],
      data: [],
    });
  };

  submitAgreement = () => {
    const { coveragePeriodData } = this.props;
    const { selectedRowKeys } = this.state;

    const params = {
      packageId: coveragePeriodData.packageId,
      customizeCode: coveragePeriodData.customizeCode,
      coveragePeriods: this.state.data.map(item => ({
        ...item,
        isAdded: selectedRowKeys.includes(item.index),
      })),
    };
    NewMarketService.PackageAgreementConfigMgmtService.saveCoveragePeriod(params).then(res => {
      if (res.success) {
        this.close();
        this.props.onRefresh && this.props.onRefresh();
      }
    });
  };

  setInitData = () => {
    const { coveragePeriodData } = this.props;
    if (!coveragePeriodData.coveragePeriods) {
      return;
    }
    const tableData = coveragePeriodData.coveragePeriods.filter(i => i.isAdded);
    this.setState({
      data: coveragePeriodData.coveragePeriods,
      selectedRowKeys: tableData.map(i => i.index),
    });
  };

  isSameCoveragePeriod = (data1, data2) => {
    if (+data1.coveragePeroidAgreementType === +data2.coveragePeroidAgreementType) {
      if (+data1.coveragePeroidAgreementType === 7) {
        // Whole Life情况
        return true;
      }
      if (+data1.coveragePeriodValueType === +data2.coveragePeriodValueType && +data2.coveragePeriodValueType === 2) {
        return +data1.elementValue === +data2.elementValue;
      } else if (
        +data1.coveragePeriodValueType === +data2.coveragePeriodValueType &&
        +data2.coveragePeriodValueType === 1
      ) {
        return (
          +data1.maxCoveragePeriod === +data2.maxCoveragePeriod && +data1.minCoveragePeriod === +data2.minCoveragePeriod
        );
      }
    }
    return false;
  };

  render() {
    const { selectedRowKeys, visible, data } = this.state;
    const { columns, isHideExpiryFields } = this.props;
    const rowSelection = {
      selectedRowKeys,
      onChange: (selectedRowKey, selectedRows) => {
        this.setState({
          selectedRowKeys: selectedRowKey,
        });
      },
    };

    return (
      <Drawer
        open={visible}
        title={t('Coverage Period')}
        onClose={this.close}
        onSubmit={() => this.submitAgreement()}
      >
        <div className="flex justify-between mb-md font-bold">
          <span>
            {t(
              'Select from the coverage periods in the associated products, or click "Add New" to create a new one.'
            )}
          </span>
          <Button
            onClick={() => {
              this.setState({
                customizeDrawerVisible: true,
              });
            }}
            icon={<Icon type="add"/>}
          >
            {t('Add New')}
          </Button>
        </div>
        <Table
          rowKey="index"
          pagination={false}
          rowSelection={rowSelection}
          columns={columns}
          scroll={{y: 540}}
          dataSource={data}
        />
        <CustomizeCoveragePeriod
          drawerOpen={this.state.customizeDrawerVisible}
          isHideExpiryFields={isHideExpiryFields}
          setDrawerOpen={visible => {
            this.setState({
              customizeDrawerVisible: visible,
            });
          }}
          packageCategory={this.props.packageCategory}
          onSubmit={newData => {
            if (data.find(item => this.isSameCoveragePeriod(item, newData))) {
              message.error(t('Duplicated data exists'));
              return false;
            }
            newData.index = data.length;
            this.setState({
              data: [...data, newData],
              selectedRowKeys: [...this.state.selectedRowKeys, newData.index],
              customizeDrawerVisible: false,
            });
            return true;
          }}
        />
      </Drawer>
    );
  }
}

export default InsuranceCoveragePeriodDrawer;
