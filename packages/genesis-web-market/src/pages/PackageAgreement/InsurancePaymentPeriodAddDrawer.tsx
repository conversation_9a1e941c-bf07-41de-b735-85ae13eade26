/* eslint-disable no-nested-ternary */
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, InputNumber, Radio, Row } from 'antd';

import { Drawer } from '@zhongan/nagrand-ui';

import { ProductCategoryItemExtend1 } from 'genesis-web-service/lib/product';

import { BizDict } from '@market/common/interface';
import GeneralSelect from '@market/components/GeneralSelect';
import NumberRange from '@market/components/NumberRange';
import { useBizDict } from '@market/hook/bizDict';
import { PremiumPeriodType, ValueType } from '@market/pages/PackageAgreement/PayFrequencyAgreement/enum';

interface Props {
  packageCategory: ProductCategoryItemExtend1;
  addDrawerVisible: boolean;
  setAddDrawerVisible: (val: boolean) => void;
  addPremiumPeriodItem: (val: PaymentPeriodItem) => void;
  openEndPolicy?: boolean;
}

export interface PaymentPeriodItem {
  premiumEndDateCalculationRule: string;
  premiumPeriodRange: string[];
  premiumPeriodType: PremiumPeriodType;
  valueType: string;
}

const AddPaymentPeriodDrawer = (props: Props) => {
  const { packageCategory, addDrawerVisible, setAddDrawerVisible, addPremiumPeriodItem, openEndPolicy } = props;
  const [form] = Form.useForm();
  const [t] = useTranslation(['market', 'common']);
  const [isShowField, setIsShowField] = useState<boolean>(false);

  const premiumPeriodTypeEnums: BizDict[] = useBizDict('premiumPeriodType').filter(item =>
    openEndPolicy ? +item.dictValue !== PremiumPeriodType.SinglePremium : true
  );
  const coveragePeriodValueTypeEnums: BizDict[] = useBizDict('coveragePeriodValueType');
  const premiumEndDateCalculationRuleEnums: BizDict[] = useBizDict('premiumEndDateCalculationRule');

  const resetDrawerFields = useCallback(() => {
    setAddDrawerVisible(false);
    form.resetFields();
  }, [form, setAddDrawerVisible, addDrawerVisible]);

  const getPremiumPeriodTypeOption = useCallback(() => {
    const EBhiderr: number[] = [PremiumPeriodType.PayToCertainAge, PremiumPeriodType.WholeLife];
    if (packageCategory === ProductCategoryItemExtend1.GroupEmployeeBenefit) {
      return (premiumPeriodTypeEnums || []).filter(item => !EBhiderr.includes(+item.dictValue));
    }
    return premiumPeriodTypeEnums || [];
  }, [premiumPeriodTypeEnums, packageCategory]);

  return (
    <Drawer
      open={addDrawerVisible}
      title={t('Premium Period')}
      onClose={resetDrawerFields}
      onSubmit={() => {
        addPremiumPeriodItem(form.getFieldsValue() as PaymentPeriodItem);
        setAddDrawerVisible(false);
        form.resetFields();
      }}
    >
      <Form colon={false} form={form} layout="vertical">
        <Row>
          <Col span={8}>
            <Form.Item
              label={t('Premium Period Type')}
              name="premiumPeriodType"
              initialValue={[]}
              rules={[{ required: true, message: t('Please select') }]}
            >
              <GeneralSelect
                style={{ width: '240px' }}
                option={getPremiumPeriodTypeOption().map(bizDict => ({
                  key: bizDict.itemExtend1,
                  value: bizDict.itemExtend1,
                  label: bizDict.itemName,
                  title: bizDict.itemName,
                }))}
                onChange={value =>
                  setIsShowField(![PremiumPeriodType.WholeLife, PremiumPeriodType.SinglePremium].includes(+value))
                }
              />
            </Form.Item>
          </Col>
          {isShowField && (
            <React.Fragment>
              <Col span={8}>
                <Form.Item
                  label="Value Type"
                  name="valueType"
                  initialValue={[]}
                  rules={[{ required: true, message: t('Please select') }]}
                >
                  <Radio.Group
                    style={{ width: '240px' }}
                    options={(coveragePeriodValueTypeEnums || []).map((bizDict: BizDict) => ({
                      key: bizDict.itemExtend1,
                      value: bizDict.itemExtend1,
                      label: bizDict.itemName,
                      title: bizDict.itemName,
                    }))}
                  />
                </Form.Item>
              </Col>

              <Form.Item noStyle shouldUpdate>
                {({ getFieldValue }) =>
                  getFieldValue('valueType') === ValueType.SelfDefined ? (
                    <Col span={8}>
                      <Form.Item
                        required
                        label={t('Premium Period')}
                        name="premiumPeriodRange"
                        initialValue={[]}
                        rules={[
                          {
                            validator: (_rule, inputValue: [], callback: (msg?: string) => void) => {
                              if (inputValue.some(value => Number.isNaN(parseFloat(value)))) {
                                callback(t('Please input'));
                              } else {
                                callback();
                              }
                            },
                          },
                        ]}
                      >
                        <NumberRange />
                      </Form.Item>
                    </Col>
                  ) : (
                    <Col span={8}>
                      <Form.Item
                        required
                        label={t('Premium Period')}
                        name="premiumPeriodValue"
                        rules={[{ required: true, message: t('Please input') }]}
                      >
                        <InputNumber
                          max={999999}
                          placeholder={t('Please input')}
                          style={{
                            width: 240,
                          }}
                        />
                      </Form.Item>
                    </Col>
                  )
                }
              </Form.Item>

              <Col span={24}>
                <Form.Item
                  label={t('Premium End Date Calculation Rule')}
                  name="premiumEndDateCalculationRule"
                  rules={[{ required: true, message: t('Please select') }]}
                >
                  <Radio.Group>
                    {premiumEndDateCalculationRuleEnums.map(item => (
                      <Radio
                        key={item.dictValue}
                        value={item.dictValue}
                        style={{
                          display: 'block',
                          lineHeight: '30px',
                        }}
                      >
                        {item.dictValueName}
                      </Radio>
                    ))}
                  </Radio.Group>
                </Form.Item>
              </Col>
            </React.Fragment>
          )}
        </Row>
      </Form>
    </Drawer>
  );
};

export default AddPaymentPeriodDrawer;
