/* eslint-disable react/no-access-state-in-setstate */
import React, { Component, createRef } from 'react';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';

import {
  Button,
  Checkbox,
  Col,
  Divider,
  Form,
  InputNumber,
  Layout,
  Modal,
  Radio,
  Row,
  Skeleton,
  Tooltip,
  message,
} from 'antd';

import { isEmpty } from 'lodash-es';

import { DeleteAction, Icon, Select, Table } from '@zhongan/nagrand-ui';

import { MoreConfigurationContainer } from 'genesis-web-component/lib/components/MoreConfigurationContainer';
import { ProductCategoryItemExtend1, ProductTypeEnum, YesNoType } from 'genesis-web-service';

import { PackageEffectiveDateRuleEnhancementType } from '@market/common/enums';
import AgreementContainerLayout from '@market/components/AgreementContainerLayout';
import GeneralSelect from '@market/components/GeneralSelect';
import ProductAnchor from '@market/components/ProductAnchor';
import PayFrequencyAgreement from '@market/pages/PackageAgreement/PayFrequencyAgreement';
import { saveEnum } from '@market/redux/action';
import { selectPermissionCheckMap } from '@market/redux/selector';
import { NewMarketService } from '@market/services/market/market.service.new';
import { NewProductService } from '@market/services/product/product.service.new';
import { renderEnumName } from '@market/utils/enum';

import { FMarketHeader } from '../../components/F-Market-Header';
import FMarketMenu from '../../components/F-Market-Menu';
import { urlQuery } from '../../util';
import AnnuityConfiguration from './AnnuityConfiguration';
import CoveragePeriod from './CoveragePeriod/CoveragePeriod';
import EffectiveDateRule, { ProductEffectiveDateRule } from './EffectiveDateRule/EffectiveDateRule';
import ExpiryDateRule, { ProductExpiryDateRule } from './ExpiryDateRule/ExpiryDateRule';
import InsurancePaymentPeriodDrawer from './InsurancePaymentPeriodDrawer';
import InvestmentProduct from './InvestmentProduct.tsx';
import ParticipatingProduct from './ParticipatingProduct.tsx';
import PolicyEffectiveWithoutCollection_NB from './PolicyEffectiveWithoutCollection_NB/PolicyEffectiveWithoutCollection_NB';
import RenewalConfiguration from './RenewalConfiguration/RenewalConfiguration';
import { RenewableType } from './RenewalConfiguration/interface';
import RiderPremiumPaymentMethod from './RiderPremiumPaymentMethod';
import SurvivalBenefitConfiguration from './SurvivalBenefitConfiguration';
import TaxSetting from './TaxSetting.tsx';
import VehicleRelatedConfiguration from './VehicleRelatedConfiguration/VehicleRelatedConfiguration';
import './index.scss';
import styles from './styles.module.scss';

const { Sider, Content } = Layout;
const FormItem = Form.Item;

class PackageAgreementContainer extends Component {
  constructor(props) {
    super(props);
    this.ref = createRef();
    this.investRef = createRef();
    this.vehicleRelatedRef = createRef();
    this.riderPremiumPaymentMethodRef = createRef();
    this.effectiveDateRuleRef = createRef();
    this.expiryDateRuleRef = createRef();
    this.RenewalRef = createRef();
    this.PolicyEffectiveWithoutCollection_NBRef = createRef();
    this.payFrequencyRef = createRef();

    this.navList = [];
    this.state = {
      initLoading: false,
      tableLoading: false,
      skeletonVisible: true,
      backdatingChecked: false,
      forward_datingChecked: false,
      packageInitialValue: {
        // 默认值
        effectiveDateRule: undefined,
        allowBackdating: '2',
        insuredGender: '1',
        ageCalBase: '1',
        issuingModel: '1',
      },
      unitMap: {
        backdatingLatestUnit: 'D',
        backdatingEarlistUnit: 'D',
        forwarddatingLatestUnit: 'D',
        forwarddatingEarlistUnit: 'D',
      },
      backdatingLatestValidation: {
        validateStatus: 'success',
        errorMsg: null,
      },
      backdatingEarlistValidation: {
        validateStatus: 'success',
        errorMsg: null,
      },
      forwarddatingLatestValidation: {
        validateStatus: 'success',
        errorMsg: null,
      },
      forwarddatingEarlistValidation: {
        validateStatus: 'success',
        errorMsg: null,
      },
      coveragePeriodData: {},
      paymentsData: {},
      mode: 'edit',
      isShowEditButton: false,
      tableStateList: [],
      drawerVisible: false,
      participatingProductData: {},
      investmentPremiumAllocationData: [],
      productCategoryId: '',
      showParticipating: false,
      showInvestment: false,
      packageCategory: undefined,
      riderList: [],
      isShowRiderPremiumPaymentMethod: false,
      packageProductPaymentMethodResponseList: [],
      watchedRules: [], // Effective Date Rule表单更新，实时更新这个值，用来做联动处理
      selectedExpiryDateRule: '',
      reviewRecurrenceUnit: 'YEARS',
      multiMainProduct: false,
    };
    this.formRef = createRef();
  }

  showSurvivalBenefit = () => {
    return (
      this.state.packageCategory &&
      [ProductCategoryItemExtend1.Endowment, ProductCategoryItemExtend1.Annuity].includes(this.state.packageCategory)
    );
  };

  showAnnuity = () => {
    return this.state.packageCategory && [ProductCategoryItemExtend1.Annuity].includes(this.state.packageCategory);
  };

  onGotoNext = isSave => {
    const { getFieldValue } = this.formRef.current;

    // 如果没有编辑过forwarddatingEarlist 这些input框，submit的时候需要校验住
    if (getFieldValue('effectiveDateRule')?.toString() === ProductEffectiveDateRule.UserInput) {
      if (getFieldValue('forward_dating')) {
        this.handleDatingChange(
          getFieldValue('forwarddatingEarlist'),
          getFieldValue('forwarddatingLatest'),
          'forwarddatingEarlist',
          'forwarddatingLatest',
          'forwarddatingLatest'
        );
      }

      if (getFieldValue('backdating')) {
        this.handleDatingChange(
          getFieldValue('backdatingLatest'),
          getFieldValue('backdatingEarlist'),
          'backdatingLatest',
          'backdatingEarlist',
          'backdatingEarlist'
        );
      }
    }
    // handleDatingChange 设置错误信息是异步的，所以保存函数需要等当前的js队列执行完，使用setTimeout来达到效果
    setTimeout(() => {
      this.saveAndNext(isSave);
    }, 50);
  };

  saveAndNext = async isSave => {
    if (this.state.tableStateList.includes('Editing')) {
      message.warning(this.props.t('Changes have not been saved.'));
      return;
    }
    const {
      unitMap,
      riderList,
      coveragePeriodData,
      paymentsData,
      backdatingEarlistValidation,
      backdatingLatestValidation,
      forwarddatingEarlistValidation,
      forwarddatingLatestValidation,
      isShowRiderPremiumPaymentMethod,
      packageInitialValue,
      openEndPolicy,
      multiMainProduct,
    } = this.state;

    // effectiveDateRule 有历史数据格式需要兼容
    // 放在validateFields里面会导致提交时会先渲染成修改之前的值，然后再渲染成修改之后的值
    const effectiveDateRuleValues = await this.effectiveDateRuleRef.current.getDataForSubmit();
    if (!effectiveDateRuleValues.validated) {
      return;
    }
    const { condition, rules, effectiveDateRule } = effectiveDateRuleValues.data.effectiveDateRule;
    packageInitialValue.effectiveDateRule = effectiveDateRule;

    const values = await this.formRef.current?.validateFields();
    const expiryDateRuleValues = await this.expiryDateRuleRef?.current?.getDataForSubmit();
    // openEndPolicy 场景下不需要校验expiryDateRuleValues
    if (!openEndPolicy && !expiryDateRuleValues.validated) {
      return;
    }
    let vehicleRelatedValues;
    if (this.vehicleRelatedRef.current) {
      if (this.vehicleRelatedRef.current.hasChanged()) {
        vehicleRelatedValues = await this.vehicleRelatedRef.current.getDataForSubmit();
        if (!vehicleRelatedValues.validated) {
          return;
        }
      }
    }

    const policyEffectiveWithoutPayValues =
      await this.PolicyEffectiveWithoutCollection_NBRef.current.getDataForSubmit();
    if (!policyEffectiveWithoutPayValues.validated) {
      return;
    }
    let renewalAgreementValues;
    if (!openEndPolicy) {
      renewalAgreementValues = await this.RenewalRef.current.getDataForSubmit();
      if (!renewalAgreementValues.validated) {
        return;
      }
    }

    const premiumFrequencyAndInstallmentValues = await this.payFrequencyRef.current.getDataForSubmit();
    if (!premiumFrequencyAndInstallmentValues.validated) {
      return;
    }

    if (rules.some(item => item.effectiveDateRule?.toString() === ProductEffectiveDateRule.UserInput)) {
      if (values.forward_dating) {
        if (forwarddatingEarlistValidation.validateStatus === 'error') {
          message.warning(this.props.t(forwarddatingEarlistValidation.errorMsg));
          return;
        }
        if (forwarddatingLatestValidation.validateStatus === 'error') {
          message.warning(this.props.t(forwarddatingLatestValidation.errorMsg));
          return;
        }
      }
      if (values.backdating) {
        if (backdatingEarlistValidation.validateStatus === 'error') {
          message.warning(this.props.t(backdatingEarlistValidation.errorMsg));
          return;
        }
        if (backdatingLatestValidation.validateStatus === 'error') {
          message.warning(this.props.t(backdatingLatestValidation.errorMsg));
          return;
        }
      }
    }
    if (
      // openEndPolicy 场景下不需要校验expiryDateRuleValues
      !openEndPolicy &&
      // 当 expiry date rule配置 calculated based on coverage period 时，coverage period 才有必填校验
      (!coveragePeriodData.coveragePeriods || !coveragePeriodData.coveragePeriods.filter(i => i.isAdded).length) &&
      expiryDateRuleValues.data.expiryDateRule.expiryDateRule === ProductExpiryDateRule.CalculatedBasedOnCoveragePeriod
    ) {
      message.warning(this.props.t('Please choose at least one coverage period agreement'));
      return;
    }
    // openEndPolicy 场景下不需要校验 paymentPeriods
    // 选中 multiMainProduct, Payment Period为必填
    const paymentPeriodsData = paymentsData?.paymentPeriods?.filter(i => i.isAdded);
    if ((!openEndPolicy || multiMainProduct) && (!paymentsData.paymentPeriods || !paymentPeriodsData.length)) {
      message.warning(this.props.t('Please choose at least one premium period agreement'));
      return;
    }

    // 选中 multiMainProduct, Installment Premium Frequency为必填
    if (
      multiMainProduct &&
      isEmpty(packageInitialValue?.premiumFrequencyAndInstallment?.premiumFrequencyAndInstallmentList)
    ) {
      message.warning(this.props.t('Please choose at least one installment premium frequency agreement'));
      return;
    }

    // 页面输入框中删除数字，提交到后端需要设置空字符串，这样才会删除数据库里之前存的值
    if (!values.forward_dating) {
      values.forwarddatingEarlist = '';
      values.forwarddatingLatest = '';
    }
    if (!values.backdating) {
      values.backdatingEarlist = '';
      values.backdatingLatest = '';
    }

    // 删掉前端用的字段
    delete values.forward_dating;
    delete values.backdating;

    //  riderPremiumPaymentMethod配置保存信息
    let riderPremiumPaymentMethodValues = [];
    if (this.riderPremiumPaymentMethodRef.current && riderList.length > 0 && isShowRiderPremiumPaymentMethod) {
      riderPremiumPaymentMethodValues = this.riderPremiumPaymentMethodRef.current.getTableChangedData();
      // 为了触发表格中的字段Rider Premium Payment Method必填校验
      const isRequire = this.riderPremiumPaymentMethodRef.current.getFormChangedData();
      if (riderPremiumPaymentMethodValues.find(item => !item.paymentMethod)) {
        message.error(this.props.t('Please complete the configuration'));
        return false;
      }
    }

    const effectiveDateRuleParams = {
      effectiveDateRule,
      condition,
      // 空对象不提交
      rules: rules.filter(item => Object.values(item).filter(value => Boolean(value)).length !== 0),
    };

    const param = {
      ...values,
      ...unitMap,
      packageId: this.packageId,
      packageProductPaymentMethodRequestList: riderPremiumPaymentMethodValues,
      ...effectiveDateRuleParams,
      // openEndPolicy为true的情况，有值的话，也是需要传给后端的，需要清空这块配置
      expiryDateRule:
        openEndPolicy && !expiryDateRuleValues?.data ? undefined : expiryDateRuleValues.data.expiryDateRule,
      vehicleMarketValueAgreement: vehicleRelatedValues?.data?.vehicleMarketValueAgreement,
      policyEffectiveWithoutPay: policyEffectiveWithoutPayValues.data.policyEffectiveWithoutPay,
      // openEndPolicy 和 renewal互斥，不会同时存在
      renewalAgreement: !openEndPolicy ? renewalAgreementValues.data.renewalAgreement : undefined,
      premiumFrequencyAndInstallment: premiumFrequencyAndInstallmentValues.data.premiumFrequencyAndInstallment,
    };
    const saveArr = [NewMarketService.PackageAgreementConfigMgmtService.savePackageAgreement(param)];
    if (this.state.showParticipating || this.showAnnuity() || this.showSurvivalBenefit()) {
      saveArr.push(this.saveParticipatingProduct(values));
    }
    if (this.state.showInvestment) {
      saveArr.push(this.saveInvestmentProduct());
    }
    Promise.all(saveArr)
      .then(() => {
        this.setState({
          packageInitialValue: {
            ...packageInitialValue,
            rules,
            condition,
          },
        });
        message.success(this.props.t('Save successfully'));
        if (!isSave) {
          this.gotoNextPage();
        }
      })
      .catch(err => {
        console.log('err', err);
      });
  };

  gotoNextPage = async () => {
    this.props.navigate(`/market/package/application-elements?packageId=${this.packageId}`, {
      state: {
        mode: this.state.mode || 'edit',
      },
    });
  };

  backToSearch = () => {
    this.props.navigate('/market/package/search');
  };

  async componentDidMount() {
    this.packageId = urlQuery('packageId');
    if (!this.packageId) {
      this.backToSearch();
      return;
    }

    const { state } = this.props.location;
    const mode = state && state.mode;
    this.setState({
      mode: mode || 'edit',
    });
    await this.queryPackageAgreementDetail(mode);
    this.setState({
      initLoading: true,
    });
    await this.judgeShowEdit(mode, this.packageId);
    await Promise.all([this.queryCoveragePeriods(), this.queryPayments(), this.queryPackageInvestment()]);
    await this.queryAgreementPartial();
    this.setState({
      initLoading: false,
      skeletonVisible: false,
    });
  }

  queryPackageAgreementDetail = async mode => {
    NewMarketService.PackageAgreementConfigMgmtService.queryPackageAgreement({
      packageId: this.packageId,
      status: mode || 'edit',
    }).then(res => {
      if (res.success && res.value) {
        res.msg && message.warning(res.msg);

        const rules = res.value.rules ?? [
          {
            effectiveDateRule: res.value.effectiveDateRule,
            defaultTime: res.value.defaultTime,
            enableDefaultTime: res.value.enableDefaultTime,
            defaultTimeEditable: res.value.defaultTimeEditable,
            effectiveDateDefinedFactor: res.value.effectiveDateDefinedFactor,
          },
        ];

        this.setState({
          backdatingChecked: res.value && !isNaN(res.value.backdatingEarlist) && res.value.backdatingEarlist !== '',
          forward_datingChecked:
            res.value && !isNaN(res.value.forwarddatingEarlist) && res.value.forwarddatingEarlist !== '',
          packageInitialValue: Object.assign(this.state.packageInitialValue, {
            ...res.value,
            rules,
          }),
          watchedRules: rules,
          unitMap: {
            backdatingEarlistUnit: res.value.backdatingEarlistUnit || 'D',
            backdatingLatestUnit: res.value.backdatingLatestUnit || 'D',
            forwarddatingLatestUnit: res.value.forwarddatingLatestUnit || 'D',
            forwarddatingEarlistUnit: res.value.forwarddatingEarlistUnit || 'D',
          },
          reviewRecurrenceUnit: res.value.reviewRecurrenceMonths ? 'MONTHS' : 'YEARS',
        });
      }
    });
  };

  queryCoveragePeriods = () => {
    return NewMarketService.PackageAgreementConfigMgmtService.queryCoveragePeriods({ packageId: this.packageId }).then(
      res => {
        if (res.success && res.value && res.value.coveragePeriods) {
          res.value.coveragePeriods.map((item, index) => {
            item.index = index;
          });
          this.setState({ coveragePeriodData: res.value });
        }
      }
    );
  };

  queryPayments = () => {
    return NewMarketService.PackageAgreementConfigMgmtService.queryPayments({
      packageId: this.packageId,
    }).then(res => {
      if (res.success && res.value && res.value.paymentPeriods) {
        res.value.paymentPeriods.map((item, index) => {
          item.index = index;
        });
        this.setState({ paymentsData: res.value });
      }
    });
  };

  queryParticipatingProduct = () => {
    return NewMarketService.PackageAgreementParticipatingMgmtService.queryPackageAgreementParticipating(
      this.packageId
    ).then(res => {
      const results = res ? { ...res } : {};
      this.setState({
        participatingProductData: results,
      });
    });
  };

  /**
   * GIS-39332
   * 将 Participating 配置和Survival、Annuity配置分开
   * Survival、Annuity的数据从form中获取，
   * Participating数据从 子组件表单中收集
   */
  saveParticipatingProduct = async formData => {
    const { participatingProductData } = this.state;
    const { current } = this.ref;
    let participatingValues = {};
    let survivalValues = {};
    let annuityValues = {};
    if (current) {
      participatingValues = await current?.form.validateFields();
    }
    if (this.showSurvivalBenefit()) {
      survivalValues = {
        survivalBenefitRule: formData.survivalBenefitRule,
        survivalBenefitPaymentOption: formData.survivalBenefitPaymentOption,
        survivalBenefitPaymentFrequency: formData.survivalBenefitPaymentFrequency,
      };
    }
    if (this.showAnnuity()) {
      annuityValues = {
        annuityRule: formData.annuityRule,
        annuityPaymentOption: formData.annuityPaymentOption,
        annuityPaymentFrequency: formData.annuityPaymentFrequency,
        annuityPaymentDeferPeriodType: formData.annuityPaymentDeferPeriodType,
        annuityPaymentDeferPeriod: formData.annuityPaymentDeferPeriod,
        annuityPaymentPeriodType: formData.annuityPaymentPeriodType,
        annuityPaymentPeriod: formData.annuityPaymentPeriod,
        annuityWithGuaranteePeriodType: formData.annuityWithGuaranteePeriodType,
        annuityWithGuaranteePeriod: formData.annuityWithGuaranteePeriod,
      };
    }
    const params = {
      packageId: this.packageId,
      ...participatingValues,
      ...survivalValues,
      ...annuityValues,
    };

    if (participatingProductData.id) {
      params.id = participatingProductData.id;
    }
    return NewMarketService.PackageAgreementParticipatingMgmtService.savePackageAgreementParticipating(params).then(
      res => {
        if (res.success === false) {
          message.error(res.msg);
        }
      }
    );
  };

  saveInvestmentData = tableData => {
    const { investmentPremiumAllocationData } = this.state;
    const investTemp = { ...investmentPremiumAllocationData };
    investTemp.investmentPremiumAllocationList = tableData;
    this.setState({
      investmentPremiumAllocationData: investTemp,
    });
  };

  saveInvestmentProduct = () => {
    const { investmentPremiumAllocationData } = this.state;
    const { t, enums } = this.props;
    const { current } = this.investRef;
    const premiumTypeEnums = (enums.premiumType || []).filter(
      item => item.itemExtend1 !== '1' && item.itemExtend1 !== '2'
    );
    return new Promise((resolve, investmentReject) => {
      const tableData = investmentPremiumAllocationData.investmentPremiumAllocationList;
      let errorFlag = false;
      // allocation table premiumType 为 All的话不允许再添加其他type
      if (
        tableData &&
        tableData.length > 1 &&
        tableData.find(allocation => `${allocation.premiumType}` !== '5') &&
        tableData.find(allocation => `${allocation.premiumType}` === '5')
      ) {
        message.warning(
          t('Premium allocation section: Premium type “All” should not combined with other premium types.')
        );
        errorFlag = true;
      }
      // 判断 investmentPremiumAllocationList 里面是否有同一个premiumType下fund的allocation相加不等于100
      if (tableData) {
        const premiumTypeObj = {};
        tableData.forEach(item => {
          if (!premiumTypeObj[item.premiumType]) {
            premiumTypeObj[item.premiumType] = [];
          }
          premiumTypeObj[item.premiumType].push(+item.premiumAllocationByPercent);
        });
        Object.keys(premiumTypeObj).forEach(premium => {
          const fundSum = premiumTypeObj[premium].reduce((prev, curr) => prev + curr, 0);
          if (fundSum !== 100) {
            const name = premiumTypeEnums.find(item => item.itemExtend1 === premium).itemName;
            message.error(
              t(
                'Premium allocation section: For premium type {{premiumName}}, the total allocation percentage is not 100%.',
                { premiumName: name }
              )
            );
            investmentReject();
            errorFlag = true;
          }
        });
      }
      if (errorFlag) return;
      // 获取并校验investment里planned premium

      current?.form
        ?.validateFields()
        .then(values => {
          const params = {
            packageId: this.packageId,
            ...values,
            investmentPremiumAllocationList: tableData
              ? tableData.map(data => {
                  const obj = { ...data };
                  delete obj.key;
                  delete obj.id;
                  return obj;
                })
              : undefined,
          };
          if (investmentPremiumAllocationData.id) {
            params.id = investmentPremiumAllocationData.id;
          }
          NewMarketService.PackageAgreementInvestmentMgmtService.savePackageAgreementInvestment(params)
            .then(res => {
              if (res.success === false) {
                message.error(res.msg);
                investmentReject();
              } else {
                resolve();
              }
            })
            .catch(() => {
              investmentReject();
            });
        })
        .catch(err => {
          investmentReject();
        });
    });
  };

  queryAgreementPartial = async () => {
    const { productCategoryId } = this.state;
    if (!productCategoryId) {
      return;
    }
    const { enums } = this.props;
    if (!enums.productCategory) {
      setTimeout(() => {
        this.queryAgreementPartial();
      }, 1000);
      return;
    }
    const productCategoryCode = enums.productCategory?.find(item => {
      return item.itemExtend1 === productCategoryId;
    })?.dictValue;

    NewProductService.ProductConfigMgmtService.queryAgreementPartition(productCategoryCode).then(res => {
      let showParticipating = false;
      let showInvestment = false;
      if (res && res.length > 0) {
        showParticipating = !!res.find(agreement => agreement.agreementDefCode === 'participating_product_agreement');
        showInvestment = !!res.find(agreement => agreement.agreementDefCode === 'investmentProductAgreement');
      }
      if (showParticipating) {
        this.queryParticipatingProduct();
      }
      this.setState({
        showParticipating,
        showInvestment,
      });
    });
  };

  queryPackageInvestment = () => {
    NewMarketService.PackageAgreementInvestmentMgmtService.queryPackageAgreementInvestment(this.packageId).then(res => {
      const results = res ? { ...res } : {};
      this.setState({
        investmentPremiumAllocationData: results,
      });
    });
  };

  judgeShowEdit = async (mode, packageId) => {
    const { userInfo, hasEditAuth, isSuperUser } = this.props;

    const res2 = await NewMarketService.PackageProductMgmtService.queryConfigProducts({
      packageId,
    });
    if (res2 && !res2.message) {
      const mainProductInfo = res2.products.find(productItem => productItem.productTypeCode === ProductTypeEnum.MAIN);
      this.setState({
        isShowEditButton: (res2.creator === `${userInfo.userId}` && hasEditAuth) || isSuperUser,
        productCategoryId: mainProductInfo ? mainProductInfo.productCategoryId : '',
        packageCategory: res2.packageCategory,
        openEndPolicy: res2.openEndPolicy,
        isShowRiderPremiumPaymentMethod: mainProductInfo
          ? [ProductCategoryItemExtend1.UnitLinked, ProductCategoryItemExtend1.Universal].includes(
              mainProductInfo.productCategoryId
            )
          : false,
        riderList:
          res2.products.length > 0
            ? res2.products.filter(
                productItem =>
                  productItem.productTypeCode === ProductTypeEnum.RIDER &&
                  ![ProductCategoryItemExtend1.UnitLinked, ProductCategoryItemExtend1.Universal].includes(
                    productItem.productCategoryId
                  )
              )
            : [],
        packageProductPaymentMethodResponseList: res2.packageProductPaymentMethodResponseList || [],
        multiMainProduct: res2.multiMainProduct,
      });
    }
  };

  changeUnit = (key, value) => {
    const { unitMap } = this.state;
    unitMap[key] = value;
    this.setState({ unitMap });
  };

  renderForwardBackdatingSelect = key => {
    const { enums } = this.props;
    const { unitMap, mode, isShowEditButton } = this.state;
    const readOnly = mode === 'view' || !isShowEditButton;
    const datingStyle = 'select-after';
    const array = enums.dateUnit
      ? enums.dateUnit
          .filter(item => {
            if (item.itemExtend1 === 'D') {
              return true;
            }
            // if (item.itemExtend1 === 'H') {
            //   return true;
            // }
            // if (item.itemExtend1 === 'M') {
            //   return true;
            // }
            if (item.itemExtend1 === unitMap[key]) {
              return true;
            }
          })
          .map(item => {
            if (item.itemExtend1 === 'Y') {
              item.disabled = true;
            }
            return item;
          })
      : [];

    return (
      <GeneralSelect
        style={{ width: '100px' }}
        className={datingStyle}
        disabled={readOnly}
        allowClear={false}
        value={unitMap[key].toString()}
        onChange={value => this.changeUnit(key, value)}
        option={array.map((item, index) => ({
          key: index,
          value: item.itemExtend1,
          label: item.itemName,
          disabled: item.disabled,
        }))}
        showSearch={false}
      />
    );
  };

  renderAddonAfterForSelect = (key, onlyShowYear) => {
    const { enums } = this.props;
    const { unitMap, mode, isShowEditButton } = this.state;
    const readOnly = mode === 'view' || !isShowEditButton;
    const datingStyle =
      key === 'backdatingLatestUnit' ||
      key === 'backdatingEarlistUnit' ||
      key === 'forwarddatingLatestUnit' ||
      key === 'forwarddatingEarlistUnit'
        ? 'select-after'
        : '';
    let array = enums.dateUnit ? enums.dateUnit : [];
    if (onlyShowYear) {
      array = array.filter(i => i.itemExtend1 === 'Y');
    }
    return (
      <GeneralSelect
        style={{ width: '80px' }}
        className={datingStyle}
        disabled={readOnly}
        allowClear={false}
        value={unitMap[key].toString()}
        onChange={value => this.changeUnit(key, value)}
        option={array.map((item, index) => ({
          key: index,
          value: item.itemExtend1,
          label: item.itemName,
        }))}
      />
    );
  };

  deletePeriod = (func, record) => {
    NewMarketService.PackageAgreementConfigMgmtService.deleteSinglePeriod({
      periodId: record.id,
      packageId: this.packageId,
    }).then(res => {
      if (res.success) {
        message.success(this.props.t('Deleted successfully'));
        if (func) {
          this[func]();
        }
      }
    });
  };

  renderCoverageTable = () => {
    const { coveragePeriodData, mode, isShowEditButton, selectedExpiryDateRule } = this.state;
    return (
      <CoveragePeriod
        coveragePeriodData={coveragePeriodData}
        mode={mode}
        isShowEditButton={isShowEditButton}
        deletePeriod={this.deletePeriod}
        selectedExpiryDateRule={selectedExpiryDateRule}
        queryCoveragePeriods={this.queryCoveragePeriods}
        packageCategory={this.state.packageCategory}
      />
    );
  };

  renderInsurancePaymentTable = () => {
    const { paymentsData, mode, isShowEditButton, multiMainProduct } = this.state;
    const { enums, t } = this.props;
    if (!paymentsData.paymentPeriods) {
      paymentsData.paymentPeriods = [];
    }
    const showTableData = paymentsData.paymentPeriods.filter(i => i.isAdded);
    const columns = [
      {
        title: t('No.'),
        dataIndex: 'index',
        width: 60,
        render: (text, record, index) => index + 1,
      },
      {
        title: t('Premium Period Type'),
        dataIndex: 'agreementType',
        render: (text, record) => {
          const coveragePeroidAgreementTypeObj = text
            ? enums.premiumPeriodType.filter(i => i.itemExtend1 === text.toString())
            : [];
          let renderText = text;
          if (coveragePeroidAgreementTypeObj.length > 0) {
            renderText = coveragePeroidAgreementTypeObj[0].itemName;
          }
          return <span>{renderText}</span>;
        },
        width: 200,
        key: 'agreementType',
      },
      {
        title: t('Premium Period Value Type'),
        dataIndex: 'valueType',
        key: 'valueType',
        width: 250,
        render: text => {
          const coveragePeriodValueTypeObj = enums.coveragePeriodValueType
            ? enums.coveragePeriodValueType.find(i => i.itemExtend1 === `${text}`)
            : undefined;
          const renderText = coveragePeriodValueTypeObj ? coveragePeriodValueTypeObj.itemName : '- -';
          return <span>{renderText}</span>;
        },
      },
      {
        title: t('Premium Period'),
        dataIndex: 'elementValue',
        key: 'elementValue',
        width: 200,
        render: (text, record) => {
          const { minPeriod, maxPeriod, agreementType, elementValue } = record;

          const suffix = +agreementType === 2 ? t('Age') : renderEnumName(agreementType, enums.premiumPeriodType);
          if (minPeriod && maxPeriod) {
            return `${minPeriod} - ${maxPeriod} ${suffix}`;
          }

          if (elementValue) {
            return `${elementValue} ${suffix}`;
          }

          return t('- -');
        },
      },
      {
        title: t('Premium End Date Calculation Rule'),
        dataIndex: 'premiumEndDateCalculationRule',
        key: 'premiumEndDateCalculationRule',
        width: 300,
        render: text => {
          const itemName = renderEnumName(text, enums.premiumEndDateCalculationRule);
          return itemName || t('- -');
        },
      },
    ];
    const readOnly = mode === 'view' || !isShowEditButton;
    const tableColumn = columns.concat([
      {
        title: this.props.t('Actions'),
        dataIndex: 'action',
        align: 'right',
        fixed: 'right',
        width: 100,
        render: (text, record) => {
          return (
            <DeleteAction
              disabled={readOnly}
              doubleConfirmType="popconfirm"
              onClick={() => this.deletePeriod('queryPayments', record)}
              deleteConfirmContent={this.props.t('Are you sure to delete this record?')}
            />
          );
        },
      },
    ]);
    return (
      <Col span={24}>
        <FormItem
          label={t('Premium Period')}
          colon={false}
          className="mb-2 [&_.market-ant4-form-item-control]:h-0"
          required={multiMainProduct}
        />
        <Button
          disabled={readOnly}
          onClick={() => {
            InsurancePaymentPeriodDrawer.open();
          }}
          className="long-add-btn"
          icon={<Icon type="add" />}
        >
          {this.props.t('Add')}
        </Button>
        <InsurancePaymentPeriodDrawer
          data={paymentsData}
          columns={columns}
          onRefresh={this.queryPayments}
          packageCategory={this.state.packageCategory}
          openEndPolicy={this.state.openEndPolicy}
        />
        <Table
          dataSource={showTableData}
          rowKey="index"
          style={{ marginBottom: 24 }}
          columns={tableColumn}
          scroll={{ x: 'max-content', y: 540 }}
          emptyType="text"
          pagination={false}
        />
      </Col>
    );
  };

  getDateEnumI18n = value => {
    return (this.props.enums.dateUnit || []).find(item => item.itemExtend1 === value).itemName;
  };

  changeAllowBackDating = e => {
    const { setFieldsValue, validateFields } = this.formRef.current;
    setFieldsValue({ allowBackdating: e.target.value }, () => {
      validateFields(['backdatingLatest']);
    });
  };

  backToLastHistory = async () => {
    this.props.navigate(`/market/package/benefit-configuration?packageId=${this.packageId}`, {
      state: {
        mode: this.state.mode || 'edit',
      },
    });
  };

  onEdit = () => {
    if (this.state.mode !== 'view') {
      return;
    }
    this.setState({ mode: 'edit' });
  };

  changeState = (e, type) => {
    if (type === 'backdating') {
      this.setState({ backdatingChecked: e.target.checked });
    }
    if (type === 'forward_dating') {
      this.setState({ forward_datingChecked: e.target.checked });
    }
  };

  dealWithErrMsg = (key, status, msg) => {
    const msgInfo = {};
    msgInfo[`${key}Validation`] = {
      validateStatus: status,
      errorMsg: msg,
    };
    this.setState({
      ...msgInfo,
    });
  };

  handleDatingChange = (earlistVal, latestVal, earlistKey, latestKey, curKey) => {
    if (!earlistVal || !latestVal) {
      if (!earlistVal && earlistVal !== 0) {
        this.dealWithErrMsg(earlistKey, 'error', this.props.t('Please input'));
      } else {
        this.dealWithErrMsg(earlistKey, 'success', null);
      }
      if (!latestVal && latestVal !== 0) {
        this.dealWithErrMsg(latestKey, 'error', this.props.t('Please input'));
      } else {
        this.dealWithErrMsg(latestKey, 'success', null);
      }
    } else {
      this.dealWithErrMsg(earlistKey, 'success', null);
      this.dealWithErrMsg(latestKey, 'success', null);
    }
  };

  getAgreementHideList = productCategoryId => {
    if (productCategoryId === ProductCategoryItemExtend1.GroupEmployeeBenefit) {
      return [
        'Customer_end_Interface_Display_Rules',
        'Investment_Product',
        'Participation_Product',
        'Insurance_Rules_onfiguration',
      ];
    }
    return [];
  };

  isAgreementHide = agreementCode => {
    const { packageCategory } = this.state;
    const hideAgreementArr = this.getAgreementHideList(packageCategory);
    if (hideAgreementArr.includes(agreementCode)) {
      return false;
    }
    return true;
  };

  renderEditSaveBtn() {
    const { isShowEditButton, mode } = this.state;
    if (!isShowEditButton || this.props.envConfig.env === 'prd') {
      return null;
    }

    if (mode === 'view') {
      return (
        <Button size="large" onClick={() => this.onEdit()}>
          {this.props.t('Edit')}
        </Button>
      );
    }

    return (
      <Button disabled={this.state.initLoading} size="large" onClick={() => this.onGotoNext(true)}>
        {this.props.t('Save')}
      </Button>
    );
  }

  renderForm() {
    const form = this.formRef.current;
    if (!form) {
      return null;
    }
    const { enums, t } = this.props;
    const {
      packageInitialValue,
      mode,
      riderList,
      isShowEditButton,
      participatingProductData,
      investmentPremiumAllocationData,
      isShowRiderPremiumPaymentMethod,
      packageProductPaymentMethodResponseList,
      watchedRules,
      coveragePeriodData,
      selectedExpiryDateRule,
    } = this.state;
    const readOnly = mode === 'view' || !isShowEditButton || this.props.envConfig.env === 'prd';

    const effectiveDateRulesNew = packageInitialValue.rules;

    return (
      <div className="right-content-wrapper">
        <FormItem noStyle shouldUpdate>
          {({ getFieldValue }) => (
            <div className="mt-6 mx-6">
              <AgreementContainerLayout title="Coverage Period" agreementCode="coveragePeriod" hideDivider>
                <EffectiveDateRule
                  rules={effectiveDateRulesNew}
                  condition={packageInitialValue.condition}
                  disabled={readOnly}
                  refInstance={this.effectiveDateRuleRef}
                  isTravel={this.state.packageCategory === ProductCategoryItemExtend1.Travel}
                  onEffectiveDateRulesChange={rules => {
                    this.setState({
                      watchedRules: rules,
                    });
                  }}
                />
                {/* effectiveDateRule 有且只有一条userInput的数据时显示backdating和Forward-dating */}
                {watchedRules?.length === 1 &&
                watchedRules[0].effectiveDateRule?.toString() === ProductEffectiveDateRule.UserInput ? (
                  <div className="market-ant4-radio-wrapper effective-date-user-input mb-4">
                    <div className="mt-4 flex flex-col gap-2">
                      <div>
                        <div>
                          <FormItem
                            name="backdating"
                            valuePropName="checked"
                            initialValue={this.state.backdatingChecked}
                            noStyle
                          >
                            <Checkbox
                              disabled={readOnly}
                              onChange={e => {
                                this.changeState(e, 'backdating');
                                setTimeout(() => {
                                  this.forceUpdate();
                                }, 200);
                              }}
                            >
                              {this.props.t('Backdating')}
                            </Checkbox>
                          </FormItem>

                          <Tooltip
                            title={t(
                              '​Allowed backdating range compared to Policy Insure Date. E.g. Insure Date is 15th Dec, if backdating range is 3 days ~ 6 days, the allowed range of Effective Date is 9th Dec ~ 12th Dec.'
                            )}
                          >
                            <Icon type="info-circle" className="ml-1" />
                          </Tooltip>
                        </div>
                        {getFieldValue('backdating') && (
                          <div
                            className={styles.backdatingFormGroup}
                            style={{
                              marginTop: 8,
                              display: 'flex',
                            }}
                          >
                            <FormItem
                              required
                              validateStatus={this.state.backdatingLatestValidation.validateStatus}
                              help={this.state.backdatingLatestValidation.errorMsg}
                              style={{ height: 32 }}
                              name="backdatingLatest"
                              initialValue={packageInitialValue.backdatingLatest}
                            >
                              <InputNumber
                                className="input-group-value"
                                disabled={readOnly}
                                min={0}
                                max={999}
                                parser={value => value.replace(/[^\d]/g, '')}
                                onChange={value => {
                                  this.handleDatingChange(
                                    value,
                                    getFieldValue('backdatingEarlist'),
                                    'backdatingLatest',
                                    'backdatingEarlist',
                                    'backdatingLatest'
                                  );
                                }}
                                addonAfter={this.renderForwardBackdatingSelect('backdatingLatestUnit')}
                              />
                            </FormItem>
                            <span
                              style={{
                                margin: '0 8px',
                                position: 'relative',
                                top: '5px',
                              }}
                            >
                              -
                            </span>
                            <FormItem
                              required
                              validateStatus={this.state.backdatingEarlistValidation.validateStatus}
                              help={this.state.backdatingEarlistValidation.errorMsg}
                              style={{ height: 32 }}
                              name="backdatingEarlist"
                              initialValue={
                                packageInitialValue.backdatingEarlist &&
                                parseInt(packageInitialValue.backdatingEarlist, 10)
                              }
                            >
                              <InputNumber
                                className="input-group-value"
                                disabled={readOnly}
                                min={0}
                                max={999}
                                parser={value => value.replace(/[^\d]/g, '')}
                                onChange={value => {
                                  this.handleDatingChange(
                                    getFieldValue('backdatingLatest'),
                                    value,
                                    'backdatingLatest',
                                    'backdatingEarlist',
                                    'backdatingEarlist'
                                  );
                                }}
                                addonAfter={this.renderForwardBackdatingSelect('backdatingEarlistUnit')}
                              />
                            </FormItem>
                          </div>
                        )}
                      </div>
                      <div>
                        <div style={{ marginTop: 10 }}>
                          <FormItem
                            name="forward_dating"
                            valuePropName="checked"
                            initialValue={this.state.forward_datingChecked}
                            noStyle
                          >
                            <Checkbox
                              disabled={readOnly}
                              onChange={e => {
                                this.changeState(e, 'forward_dating');
                                setTimeout(() => {
                                  this.forceUpdate();
                                }, 200);
                              }}
                            >
                              {this.props.t('Forward-dating')}
                            </Checkbox>
                          </FormItem>

                          <Tooltip
                            title={t(
                              '​Allowed forward-dating range compared to Policy Insure Date. E.g. Insure Date is 15th Dec, if forward-dating range is 3 days ~ 6 days, the allowed range of Effective Date is 18th Dec ~ 21th Dec.'
                            )}
                          >
                            <Icon type="info-circle" className="ml-1" />
                          </Tooltip>
                        </div>
                        {getFieldValue('forward_dating') && (
                          <div
                            className={styles.backdatingFormGroup}
                            style={{
                              marginTop: 8,
                              display: 'flex',
                            }}
                          >
                            <FormItem
                              disabled={readOnly}
                              required
                              validateStatus={this.state.forwarddatingEarlistValidation.validateStatus}
                              help={this.state.forwarddatingEarlistValidation.errorMsg}
                              name="forwarddatingEarlist"
                              initialValue={packageInitialValue.forwarddatingEarlist}
                            >
                              <InputNumber
                                className="input-group-value"
                                min={0}
                                max={999}
                                parser={value => value.replace(/[^\d]/g, '')}
                                disabled={readOnly}
                                onChange={value => {
                                  this.forceUpdate();
                                  this.handleDatingChange(
                                    value,
                                    getFieldValue('forwarddatingLatest'),
                                    'forwarddatingEarlist',
                                    'forwarddatingLatest',
                                    'forwarddatingEarlist'
                                  );
                                }}
                                addonAfter={this.renderForwardBackdatingSelect('forwarddatingEarlistUnit')}
                              />
                            </FormItem>
                            <span className="mx-2 relative top-[5px]">-</span>
                            <FormItem
                              required
                              validateStatus={this.state.forwarddatingLatestValidation.validateStatus}
                              help={this.state.forwarddatingLatestValidation.errorMsg}
                              name="forwarddatingLatest"
                              initialValue={
                                packageInitialValue.forwarddatingLatest &&
                                parseInt(packageInitialValue.forwarddatingLatest, 10)
                              }
                            >
                              <InputNumber
                                className="input-group-value"
                                disabled={readOnly}
                                max={999}
                                parser={value => value.replace(/[^\d]/g, '')}
                                onChange={value => {
                                  this.forceUpdate();
                                  this.handleDatingChange(
                                    getFieldValue('forwarddatingEarlist'),
                                    value,
                                    'forwarddatingEarlist',
                                    'forwarddatingLatest',
                                    'forwarddatingLatest'
                                  );
                                }}
                                addonAfter={this.renderForwardBackdatingSelect('forwarddatingLatestUnit')}
                              />
                            </FormItem>
                          </div>
                        )}
                      </div>
                      {(this.state.backdatingChecked || this.state.forward_datingChecked) && (
                        <FormItem
                          label={this.props.t('Backdating or Forward-dating period calculation basis')}
                          colon={false}
                          className="effective-date-rule-enhancement mb-0"
                          required
                          name="effectiveDateRuleEnhancement"
                          initialValue={
                            packageInitialValue?.effectiveDateRuleEnhancement?.toString() ||
                            PackageEffectiveDateRuleEnhancementType.Basedon00_00_00ofPolicyInsureDate
                          }
                          rules={[
                            {
                              required: true,
                              message: this.props.t('Please select'),
                            },
                          ]}
                        >
                          <Radio.Group disabled={readOnly}>
                            {enums.packageEffectiveDateRuleEnhancement &&
                              enums.packageEffectiveDateRuleEnhancement.map((item, index) => {
                                return (
                                  <Radio
                                    key={index}
                                    value={item.dictValue}
                                    style={{
                                      display: 'block',
                                    }}
                                  >
                                    {item.dictValueName}
                                  </Radio>
                                );
                              })}
                          </Radio.Group>
                        </FormItem>
                      )}
                    </div>
                  </div>
                ) : null}

                {this.state.openEndPolicy && !packageInitialValue.expiryDateRule?.expiryDateRule ? null : (
                  <ExpiryDateRule
                    disabled={readOnly}
                    initialValues={packageInitialValue.expiryDateRule}
                    refInstance={this.expiryDateRuleRef}
                    isTravel={this.state.packageCategory === ProductCategoryItemExtend1.Travel}
                    openEndPolicy={this.state.openEndPolicy}
                    changeExpiryDateRule={params => {
                      const { form, setCurrVal, preVal } = params;
                      const setSelectedExpiryDateRule = value => {
                        this.setState({ selectedExpiryDateRule: value });
                      };

                      if (coveragePeriodData.coveragePeriods.filter(i => i.isAdded)?.length) {
                        Modal.confirm({
                          title: t('The coverage period agreement will be deleted'),
                          onOk: () => {
                            NewMarketService.PackageAgreementConfigMgmtService.saveCoveragePeriod({
                              packageId: coveragePeriodData.packageId,
                              customizeCode: coveragePeriodData.customizeCode,
                              coveragePeriods: [],
                            }).then(res => {
                              if (res.success) {
                                this.queryCoveragePeriods();
                              }
                            });
                            setCurrVal(setSelectedExpiryDateRule);
                          },
                          onCancel: () => {
                            form.setFieldsValue({
                              expiryDateRule: preVal,
                            });
                          },
                          okText: t('yes'),
                          cancelText: t('no'),
                        });
                      } else {
                        setCurrVal(setSelectedExpiryDateRule);
                      }
                    }}
                  />
                )}
                {(this.state.openEndPolicy &&
                  (coveragePeriodData?.coveragePeriods?.filter(i => i.isAdded) ?? []).length === 0) ||
                selectedExpiryDateRule === ProductExpiryDateRule.LatestExpiryDateOfPolicyCoverages ? null : (
                  <React.Fragment>
                    <FormItem
                      label={t('Coverage Period')}
                      colon={false}
                      required={selectedExpiryDateRule === ProductExpiryDateRule.CalculatedBasedOnCoveragePeriod}
                      className="mb-2 [&_.market-ant4-form-item-control]:h-0"
                    />
                    {this.renderCoverageTable()}
                  </React.Fragment>
                )}
                <MoreConfigurationContainer>
                  <Col style={{ marginTop: 24 }} span={24}>
                    <Form.Item
                      colon={false}
                      label={t('Re-Define Effective Date Rule')}
                      name="reDefineEffectiveDateRule"
                      valuePropName="checked"
                      getValueFromEvent={({ target }) => (target.checked ? 1 : 0)}
                      initialValue={packageInitialValue.reDefineEffectiveDateRule}
                    >
                      <Checkbox disabled={readOnly}>{enums.reDefineEffectiveDateRule?.[0]?.dictValueName}</Checkbox>
                    </Form.Item>
                  </Col>

                  <Divider className="mb-4" />

                  <PolicyEffectiveWithoutCollection_NB
                    disabled={readOnly}
                    refInstance={this.PolicyEffectiveWithoutCollection_NBRef}
                    initialValues={packageInitialValue.policyEffectiveWithoutPay}
                  />
                </MoreConfigurationContainer>
              </AgreementContainerLayout>

              <PayFrequencyAgreement
                disabled={this.state.mode === 'view' || !this.state.isShowEditButton}
                initialValues={this.state.packageInitialValue}
                packageInfo={{
                  packageId: this.packageId,
                  openEndPolicy: this.state.openEndPolicy,
                }}
                reload={this.queryPackageAgreementDetail}
                payFrequencyRefInstance={this.payFrequencyRef}
                insurancePaymentTable={this.renderInsurancePaymentTable()}
                multiMainProduct={this.state.multiMainProduct}
              />

              <AgreementContainerLayout title={t('Issuance Agreement')} agreementCode="issuanceAgreement">
                <Col span={24}>
                  <FormItem
                    label={this.props.t('Issuing Model of Policy')}
                    colon={false}
                    required
                    name="issuingModel"
                    initialValue={packageInitialValue.issuingModel?.toString()}
                    rules={[
                      {
                        required: true,
                        message: this.props.t('Please select'),
                      },
                    ]}
                  >
                    <Radio.Group
                      disabled={readOnly}
                      allowClear={false}
                      options={
                        Array.isArray(enums.issuingModel)
                          ? enums.issuingModel.map((item, index) => ({
                              key: index,
                              value: item.itemExtend1,
                              label: item.itemName,
                            }))
                          : []
                      }
                    />
                  </FormItem>
                </Col>

                <Col span={24}>
                  <FormItem
                    label={this.props.t('Same Premium Calculation for Each Insured')}
                    colon={false}
                    required
                    name="samePremiumCalculationForEachInsured"
                    initialValue={packageInitialValue.samePremiumCalculationForEachInsured?.toString() || YesNoType.No}
                    rules={[
                      {
                        required: true,
                        message: this.props.t('Please select'),
                      },
                    ]}
                  >
                    <Radio.Group
                      disabled={readOnly}
                      allowClear={false}
                      options={(enums.yesNo || []).map((item, index) => ({
                        key: index,
                        value: item.dictValue,
                        label: item.dictValueName,
                      }))}
                    />
                  </FormItem>
                </Col>
              </AgreementContainerLayout>

              <AgreementContainerLayout title={t('Tax Setting')} agreementCode="taxSetting">
                <Col span={24}>
                  <TaxSetting disabled={readOnly} packageId={this.packageId} />
                </Col>
              </AgreementContainerLayout>

              {this.state.packageCategory === ProductCategoryItemExtend1.Auto && (
                <VehicleRelatedConfiguration
                  disabled={readOnly}
                  refInstance={this.vehicleRelatedRef}
                  initialValues={packageInitialValue.vehicleMarketValueAgreement}
                />
              )}

              <AgreementContainerLayout title={t('Age Validation')} agreementCode="ageValidation">
                <Row>
                  <Col span={24}>
                    <FormItem
                      label={this.props.t('Age Calculation Basis')}
                      colon={false}
                      required
                      name="ageCalBase"
                      initialValue={packageInitialValue.ageCalBase?.toString()}
                      rules={[
                        {
                          required: true,
                          message: this.props.t('Please select'),
                        },
                      ]}
                    >
                      <GeneralSelect
                        disabled
                        allowClear={false}
                        option={
                          (enums?.ageCalcBasic || []).map((item, index) => ({
                            key: index,
                            value: item.itemExtend1,
                            label: item.itemName,
                          })) || []
                        }
                        className="!w-[580px]"
                      />
                    </FormItem>
                  </Col>

                  {/* https://jira.zaouter.com/browse/GIS-67071 前端隐藏，改成非必填，老数据已配置的进package 编辑可以看见该字段，新创建的package 隐藏 */}
                  {packageInitialValue.maxInsured ? (
                    <Col span={24}>
                      <FormItem
                        label={this.props.t('Max Number of Inputted Insured')}
                        colon={false}
                        name="maxInsured"
                        initialValue={packageInitialValue.maxInsured}
                      >
                        <InputNumber style={{ width: 240 }} maxLength={8} min={1} precision={0} disabled={readOnly} />
                      </FormItem>
                    </Col>
                  ) : null}

                  {this.state.showParticipating && this.isAgreementHide('Participation_Product') ? (
                    <ParticipatingProduct
                      refInstance={this.ref}
                      disabled={readOnly}
                      editStatus={readOnly}
                      participatingProductData={participatingProductData}
                    />
                  ) : null}
                  {this.showSurvivalBenefit() ? (
                    <React.Fragment>
                      <Divider style={{ margin: '0 0 16px' }} dashed />
                      <SurvivalBenefitConfiguration
                        disabled={readOnly}
                        participatingProductData={participatingProductData}
                        form={form}
                      />
                    </React.Fragment>
                  ) : null}
                  {this.showAnnuity() ? (
                    <React.Fragment>
                      <Divider style={{ margin: '0 0 16px' }} dashed />
                      <AnnuityConfiguration
                        form={form}
                        disabled={readOnly}
                        participatingProductData={participatingProductData}
                      />
                    </React.Fragment>
                  ) : null}

                  {this.state.showInvestment && this.isAgreementHide('Investment_Product') ? (
                    <InvestmentProduct
                      refInstance={this.investRef}
                      disabled={readOnly}
                      editStatus={readOnly}
                      packageId={this.packageId}
                      investmentPremiumAllocationData={investmentPremiumAllocationData}
                      saveInvestmentData={this.saveInvestmentData}
                    />
                  ) : null}

                  {isShowRiderPremiumPaymentMethod ? (
                    <RiderPremiumPaymentMethod
                      packageId={this.packageId}
                      riderList={riderList}
                      disabled={readOnly}
                      refInstance={this.riderPremiumPaymentMethodRef}
                      packageProductPaymentMethodResponseList={packageProductPaymentMethodResponseList}
                    />
                  ) : null}
                </Row>
              </AgreementContainerLayout>

              {/* openEndPolicy打开，并且renewal没有数据则隐藏该区块 */}
              {this.state.openEndPolicy &&
              packageInitialValue.renewalAgreement?.renewableType?.toString() !== RenewableType.Renewable ? null : (
                <Col span={24}>
                  <RenewalConfiguration
                    disabled={readOnly}
                    refInstance={this.RenewalRef}
                    initialValues={packageInitialValue.renewalAgreement}
                  />
                </Col>
              )}

              {this.state.openEndPolicy && (
                <Col span={24}>
                  <AgreementContainerLayout title={t('Open End Policy')} agreementCode="openEndPolicy">
                    <div className="flex gap-[80px]">
                      <Form.Item
                        colon={false}
                        label={
                          <React.Fragment>
                            {t('Review Recurrence')}
                            <Tooltip
                              title={t(
                                "System will automatically review the policy on each Review Recurrence starting from the effective date. When review recurred on monthly basis, 'Premium Period Type' in Premium Period & Installment must be set up as 'Monthly' as well."
                              )}
                            >
                              <Icon type="info-circle" style={{ marginLeft: 8 }} />
                            </Tooltip>
                          </React.Fragment>
                        }
                        name={
                          this.state.reviewRecurrenceUnit === 'YEARS'
                            ? 'reviewRecurrenceYears'
                            : 'reviewRecurrenceMonths'
                        }
                        initialValue={
                          this.state.reviewRecurrenceUnit === 'YEARS'
                            ? packageInitialValue.reviewRecurrenceYears
                            : packageInitialValue.reviewRecurrenceMonths
                        }
                        rules={[
                          {
                            required: true,
                            message: t(
                              'Policy will be auto reviewed on each Review Recurrency from the Effective Date.'
                            ),
                          },
                        ]}
                      >
                        <InputNumber
                          min={1}
                          addonBefore="per"
                          addonAfter={
                            <Select
                              value={this.state.reviewRecurrenceUnit}
                              onChange={unit => {
                                this.setState({
                                  reviewRecurrenceUnit: unit,
                                });
                              }}
                              options={[
                                {
                                  label: t('Year(s)'),
                                  value: 'YEARS',
                                },
                                {
                                  label: t('Month(s)'),
                                  value: 'MONTHS',
                                },
                              ]}
                              style={{ width: 100 }}
                              disabled={readOnly}
                            />
                          }
                          placeholder={t('Number')}
                          disabled={readOnly}
                        />
                      </Form.Item>

                      <Form.Item
                        colon={false}
                        label={
                          <React.Fragment>
                            {t('Review Extraction Date')}
                            <Tooltip
                              title={t(
                                'Retrieve the policy to be reviewed and generate the bill X days prior to the Review Date.'
                              )}
                            >
                              <Icon type="info-circle" style={{ marginLeft: 8 }} />
                            </Tooltip>
                          </React.Fragment>
                        }
                        name="reviewExtractionDays"
                        initialValue={packageInitialValue.reviewExtractionDays}
                        rules={[
                          {
                            required: true,
                            message: t(
                              'Extract review policy and generate bill from X days prior to Effective Date + .total Review Recurrencies.'
                            ),
                          },
                        ]}
                      >
                        <InputNumber disabled={readOnly} min={1} addonAfter={t('Day(s)')} placeholder={t('Number')} />
                      </Form.Item>
                    </div>
                  </AgreementContainerLayout>
                </Col>
              )}
            </div>
          )}
        </FormItem>
      </div>
    );
  }

  render() {
    const { packageInitialValue, mode, isShowEditButton } = this.state;
    const readOnly = mode === 'view' || !isShowEditButton || this.props.envConfig.env === 'prd';

    this.navList = [
      {
        href: 'coveragePeriod',
        title: this.props.t('Coverage Period'),
        visible: true,
      },
      {
        href: 'premiumPeriod',
        title: this.props.t('Premium Period & Installment'),
        visible: true,
      },
      {
        href: 'issuanceAgreement',
        title: this.props.t('Issuance Agreement'),
        visible: true,
      },
      {
        href: 'taxSetting',
        title: this.props.t('Tax Setting'),
        visible: true,
      },
      {
        href: 'vehicleRelatedConfiguration',
        title: this.props.t('Vehicle Related Configuration'),
        visible: this.state.packageCategory === ProductCategoryItemExtend1.Auto,
      },
      {
        href: 'ageValidation',
        title: this.props.t('Age Validation'),
        visible: true,
      },
      {
        href: 'investmentProduct',
        title: this.props.t('Investment Product'),
        visible: true,
      },
      {
        href: 'participatingProduct',
        title: this.props.t('Participating Product'),
        visible: this.state.showParticipating && this.isAgreementHide('Participation_Product'),
      },
      {
        href: 'renewal',
        title: this.props.t('Renewal'),
        visible: true,
      },
      {
        href: 'openEndPolicy',
        title: this.props.t('Open End Policy'),
        visible: Boolean(packageInitialValue?.allowOpenEndPolicy),
      },
    ];

    return (
      <Layout className="market-layout package-agreement-container">
        <FMarketHeader backPath="/market/package/search" subMenu="Package" />
        <div className="flex" style={{ height: 'calc(100% - 50px)' }}>
          <Sider width={208} className="market-sider">
            <FMarketMenu
              page="PACKAGE"
              defaultSelectedKeys={['3']}
              category={1}
              type={mode}
              packageId={this.packageId}
              navigate={this.props.navigate}
            />
          </Sider>
          <Content className="market-content">
            <Skeleton active loading={this.state.skeletonVisible} className="p-6">
              <Form layout="vertical" ref={this.formRef}>
                {this.renderForm()}
              </Form>
            </Skeleton>
          </Content>

          <ProductAnchor navList={this.navList.filter(item => item.visible)} />

          <div className="bottom-action-bar">
            {readOnly && (
              <Button disabled={this.state.initLoading} size="large" onClick={this.gotoNextPage} type="primary">
                {this.props.t('Next')}
              </Button>
            )}
            {this.renderEditSaveBtn()}
            {!readOnly && (
              <Button disabled={this.state.initLoading} size="large" onClick={() => this.onGotoNext()} type="default">
                {this.props.t('Next')}
              </Button>
            )}
          </div>
        </div>
      </Layout>
    );
  }
}

export default withTranslation(['market', 'common'])(
  connect(
    state => {
      const enums = state.enums;
      // 国际化后期再加
      const permissionMap = selectPermissionCheckMap(state);
      return {
        enums,
        envConfig: state.envConfig,
        hasEditAuth: !!permissionMap['market.edit'],
        isSuperUser: !!permissionMap['market.edit-all'],
        userInfo: state.userInfo,
        tenantTimeFormatByZeus: state.tenantTimeFormatByZeus,
      };
    },
    {
      saveEnum,
    }
  )(PackageAgreementContainer)
);
