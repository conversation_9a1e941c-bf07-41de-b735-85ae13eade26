/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { FormInstance, Rule } from 'antd/es/form';
import type { ColumnProps } from 'antd/es/table';
import { FieldType } from '@zhongan/nagrand-ui';

import { t } from '@market/i18n';
import { AllocationTable, BizDict, PremiumTypeEnum } from '@market/request/interface';
import { isEmpty } from 'lodash-es';

interface Fund {
  itemExtend1: string;
  itemName: string;
  pureName: string;
}

interface ControllerProps extends Partial<Rule[]> {
  style?: Record<string, any>;
  placeholder?: string;
  onChange?: (value: string) => void;
  getPopupContainer?: () => HTMLElement | HTMLAllCollection;
}
interface Validate {
  trigger?: string;
  rules?: Rule[];
}
interface InvestColumn extends ColumnProps<AllocationTable> {
  inputType?: string;
  selectOptions?: BizDict[] | Fund[];
  controllerprops?: ControllerProps;
  validate?: Validate[];
  editable?: boolean;
  fieldProps?: Record<string, any>;
  formItemProps?: Record<string, any>;
}
interface InvestParam {
  premiumTypeEnums: BizDict[];
  allocationTable: AllocationTable[];
  fundEnums: Fund[];
  editKey: string;
  tableForm: FormInstance;
}
export const InvestColumns = ({
  premiumTypeEnums,
  allocationTable,
  fundEnums,
  editKey,
  tableForm,
}: InvestParam): InvestColumn[] => [
    {
      title: t('Premium Type'),
      dataIndex: 'premiumType',
      editable: true,
      fieldProps: {
        type: FieldType.Select,
        extraProps: {
          options: premiumTypeEnums.filter((item: BizDict) => item.itemExtend1 !== PremiumTypeEnum.All).map(item => {
            return {
              label: item.itemName,
              value: item.itemExtend1.toString(),
            }
          }),
          placeholder: t('Please select'),
          getPopupContainer: () => document.body,
        }
      },
      formItemProps: {
        rules: [{ required: true, message: t('Please select') }]
      },
    },
    {
      title: t('Fund'),
      dataIndex: 'fundCode',
      fieldProps: {
        type: FieldType.Select,
        extraProps: {
          options: fundEnums.map(item => {
            return {
              label: item.itemName,
              value: item.itemExtend1.toString(),
            }
          }),
          placeholder: t('Please select'),
          getPopupContainer: () => document.body,
        }
      },
      render: (text: string, record: AllocationTable): string => {
        const name = fundEnums?.find(item => item.itemExtend1 === text)?.pureName;
        if (typeof name === 'string') {
          return `${text} _ ${name}`;
        }
        return text;
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: t('Please select'),
          },
          {
            validator: (rule: any, inputValue: string, callback: (msg?: string) => void) => {
              const otherData = allocationTable?.filter(data => data.key !== editKey);

              if (!inputValue || isEmpty(otherData) || !tableForm.getFieldValue('premiumType')) {
                callback();
                return;
              }
              // premiumType & fundCode 不可重复
              if (tableForm &&
                otherData.find(
                  data =>
                    data.fundCode?.toString() === inputValue &&
                    data.premiumType?.toString() === (tableForm.getFieldValue('premiumType') as string).toString()
                )) {
                callback(t('Please do not select the same fund for the same Premium Type'));
              } else {
                callback()
              }
            },
          },
        ]
      },
      editable: true,
    },
    {
      title: t('Premium Allocation(%)'),
      dataIndex: 'premiumAllocationByPercent',
      fieldProps: {
        type: FieldType.InputNumber,
        extraProps: {
          placeholder: t('Please input'),
          style: { width: '240px' },
          min: 0.00000001,
          max: 100,
        }
      },
      formItemProps: {
        rules: [{ required: true, message: t('Please input') }]
      },
      editable: true,
    },
  ];
