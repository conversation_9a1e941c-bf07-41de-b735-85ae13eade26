import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Radio, Row } from 'antd';
import { FormInstance } from 'antd/lib';

import { PackagePeriodRule } from '@market/common/enums';
import { SelectOption } from '@market/common/interface';
import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { PaymentFrequencyBaseDict } from '@market/request/interface';

interface Props {
  disabled: boolean;
  participatingProductData: Record<string, number>;
  form: FormInstance;
}

export const SurvivalBenefitConfiguration = ({ form, disabled, participatingProductData }: Props) => {
  /* ============== 枚举使用start ============== */
  const packagePeriodRuleOptions = useBizDictAsOptions('packagePeriodRule');
  const survivalPaymentDrawTypeOptions = useBizDictAsOptions('survivalPaymentDrawType');
  const paymentFrequencyBaseOptions = useBizDictAsOptions('paymentFrequencyBase');
  /* ============== 枚举使用end ============== */
  const [t] = useTranslation(['market', 'common']);

  useEffect(() => {
    const { survivalBenefitRule, survivalBenefitPaymentOption, survivalBenefitPaymentFrequency } =
      participatingProductData;
    form.setFieldsValue({
      survivalBenefitRule: survivalBenefitRule || PackagePeriodRule.FollowEachProduct,
      survivalBenefitPaymentOption: survivalBenefitPaymentOption?.toString(),
      survivalBenefitPaymentFrequency: survivalBenefitPaymentFrequency?.toString(),
    });
  }, [participatingProductData]);

  return (
    <Row>
      <Col span={24}>
        <Form.Item
          label={t('Survival Benefit Configuration')}
          name="survivalBenefitRule"
          rules={[
            {
              required: true,
              message: t('Please select'),
            },
          ]}
        >
          <Radio.Group disabled={disabled}>
            {packagePeriodRuleOptions.map(j => (
              <Radio key={j.value} value={+j.value}>
                {j.label}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
      </Col>
      <Form.Item noStyle shouldUpdate>
        {({ getFieldValue }) =>
          getFieldValue('survivalBenefitRule') === PackagePeriodRule.UnifiedRules ? (
            <React.Fragment>
              <Col span={8}>
                <Form.Item
                  label={t('Payment Option')}
                  name="survivalBenefitPaymentOption"
                  rules={[
                    {
                      required: true,
                      message: t('Please select'),
                    },
                  ]}
                >
                  <GeneralSelect
                    style={{ width: '240px' }}
                    option={survivalPaymentDrawTypeOptions}
                    disabled={disabled}
                  />
                </Form.Item>
              </Col>

              <Col span={8}>
                <Form.Item
                  label={t('Payment Frequency')}
                  name="survivalBenefitPaymentFrequency"
                  rules={[
                    {
                      required: true,
                      message: t('Please select'),
                    },
                  ]}
                >
                  <GeneralSelect
                    style={{ width: '240px' }}
                    option={paymentFrequencyBaseOptions.filter((bizDict: SelectOption) =>
                      [
                        PaymentFrequencyBaseDict.Yearly,
                        PaymentFrequencyBaseDict.SemiYearly,
                        PaymentFrequencyBaseDict.Quarterly,
                        PaymentFrequencyBaseDict.Monthly,
                      ].includes(bizDict.value as PaymentFrequencyBaseDict)
                    )}
                    disabled={disabled}
                  />
                </Form.Item>
              </Col>
            </React.Fragment>
          ) : null
        }
      </Form.Item>
    </Row>
  );
};

export default SurvivalBenefitConfiguration;
