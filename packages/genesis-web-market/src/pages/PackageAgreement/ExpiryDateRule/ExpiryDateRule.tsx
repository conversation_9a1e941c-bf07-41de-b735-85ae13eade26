import { Ref, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Checkbox, Col, Form, type FormInstance, Input, Space } from 'antd';

import { Drawer } from '@zhongan/nagrand-ui';

import { TimeRangeFormatEnum } from 'genesis-web-service/lib/system/system.interface';

import GeneralSelect from '@market/components/GeneralSelect';
import MoreButton from '@market/components/MoreButton/MoreButton';
import { useBizDict, useBizDictAsOptions } from '@market/hook/bizDict';
import { selectTenantTimeFormatByZeus } from '@market/redux/selector';
import { formErrorHandler } from '@market/utils/formUtils';

import { convertDefaultTimeForDisplay, convertDefaultTimeForSubmit } from '../EffectiveDateRule/defaultTime';
import styles from './styles.module.scss';

export const enum ProductExpiryDateRule {
  UserInput = '1',
  CalculatedBasedOnCoveragePeriod = '2',
  FollowMasterPolicy = '3',
  BasedOnDefinedApplicationElements = '4',
  LatestExpiryDateOfPolicyCoverages = '5'
}

export interface ExpiryDateRuleFormValues {
  expiryDateRule: string | number;
  expiryDateDefinedFactor: string | number;
  enableFixedTime?: boolean;
  defaultExpireTime?: string;
}
type InitialValues = ExpiryDateRuleFormValues;
interface Props {
  disabled: boolean;
  initialValues: InitialValues;
  refInstance: Ref<any>;
  changeExpiryDateRule: (params: {
    form: FormInstance;
    preVal: string;
    setCurrVal: (cb: (selectedExpiryDateRule: number) => void) => void;
  }) => void;
  isTravel?: boolean;
  openEndPolicy?: boolean;
}

export const ExpiryDateRule = ({
  disabled,
  initialValues,
  refInstance,
  changeExpiryDateRule,
  isTravel = false,
  openEndPolicy,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();
  const [drawerForm] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [expiryDateRuleValue, setExpiryDateRuleValue] = useState(initialValues?.expiryDateRule?.toString());
  /* ============== 枚举使用start ============== */
  const tenantTimeFormatByZeus: {
    timeRangeFormat: TimeRangeFormatEnum;
  } = useSelector(selectTenantTimeFormatByZeus);
  const expiryDateRuleBizDictOptions = useBizDict('techProductExpiryDateRule');
  const coveragePeriodBasedOnApplicationElementsBizDictOptions = useBizDictAsOptions(
    'coveragePeriodBasedOnApplicationElements'
  );
  const [hasChangedStatus, setHasChangedStatus] = useState(false);

  /* ============== 枚举使用end ============== */

  useEffect(() => {
    form.setFieldValue('enableFixedTime', initialValues?.enableFixedTime);
    form.setFieldValue('defaultExpireTime', initialValues?.defaultExpireTime);
  }, [form, initialValues?.defaultExpireTime, initialValues?.enableFixedTime]);

  useEffect(() => {
    if (!expiryDateRuleValue) {
      setExpiryDateRuleValue(initialValues?.expiryDateRule?.toString());
    }
  }, [initialValues?.expiryDateRule]);

  useImperativeHandle(refInstance, () => ({
    getDataForSubmit: async () => {
      // eslint-disable-next-line @typescript-eslint/await-thenable
      const validFormValues: ExpiryDateRuleFormValues & {
        validated?: boolean;
        values: ExpiryDateRuleFormValues;
      } = await form.validateFields().catch(formErrorHandler(form));
      const formValues: ExpiryDateRuleFormValues = validFormValues.values ?? validFormValues;
      return {
        validated: validFormValues.validated !== false,
        data: {
          expiryDateRule: {
            ...formValues,
            defaultExpireTime: formValues.defaultExpireTime
              ? convertDefaultTimeForSubmit(formValues.defaultExpireTime, tenantTimeFormatByZeus?.timeRangeFormat)
              : undefined,
          },
        },
      };
    },
    hasChanged: () => true,
  }));

  const expiryDateRuleOptions = useMemo(
    () =>
      expiryDateRuleBizDictOptions
        .filter(option =>
          isTravel ? true : option.dictValue !== ProductExpiryDateRule.BasedOnDefinedApplicationElements
        )
        .sort((a, b) => (a?.orderNo ?? 0) - (b?.orderNo ?? 0))
        .map(optionItem => ({
          value: optionItem.dictValue,
          label: optionItem.dictValueName,
        })),
    [expiryDateRuleBizDictOptions, isTravel]
  );

  const resetDrawer = () => {
    setOpen(false);
    drawerForm.resetFields();
  };

  return (
    <Form
      layout="vertical"
      form={form}
      onValuesChange={() => {
        setHasChangedStatus(true);
      }}
    >
      <Col span={16} className="inline-block mr-[8px]">
        <Form.Item name="enableFixedTime" noStyle />
        <Form.Item name="defaultExpireTime" noStyle />
        <Form.Item noStyle shouldUpdate>
          {({ getFieldValue }) => {
            const defaultExpiryTimeValue = getFieldValue('defaultExpireTime');
            return (
              <Space>
                <Space.Compact style={{ alignItems: 'end' }}>
                  <Form.Item
                    colon={false}
                    name="expiryDateRule"
                    label={t('Expiry Date Rule')}
                    initialValue={initialValues?.expiryDateRule?.toString()}
                    // openEndPolicy场景下需要清除这块配置，不能校验必填
                    rules={openEndPolicy ? [] : [{ required: true, message: t('Please select') }]}
                  >
                    <GeneralSelect
                      disabled={disabled}
                      style={{ width: 580 }}
                      value={expiryDateRuleValue}
                      option={expiryDateRuleOptions}
                      onChange={val => {
                        changeExpiryDateRule({
                          form,
                          preVal: expiryDateRuleValue,
                          setCurrVal: callback => {
                            setExpiryDateRuleValue(val);
                            callback?.(val);
                            form.resetFields(['enableFixedTime', 'defaultExpireTime']);
                          },
                        });
                      }}
                    />
                  </Form.Item>
                  {String(getFieldValue('expiryDateRule')) ===
                    ProductExpiryDateRule.CalculatedBasedOnCoveragePeriod && (
                    <MoreButton
                      style={{ marginBottom: 24 }}
                      disabled={disabled}
                      onClick={() => {
                        setOpen(true);
                        drawerForm.setFieldsValue({
                          enableFixedTime: form.getFieldValue('enableFixedTime'),
                          defaultExpireTime: convertDefaultTimeForDisplay(
                            form.getFieldValue('defaultExpireTime'),
                            tenantTimeFormatByZeus?.timeRangeFormat
                          ),
                        });
                      }}
                    />
                  )}
                </Space.Compact>
                {defaultExpiryTimeValue && (
                  <span>{`${convertDefaultTimeForDisplay(defaultExpiryTimeValue, tenantTimeFormatByZeus?.timeRangeFormat)!}`}</span>
                )}
              </Space>
            );
          }}
        </Form.Item>
      </Col>
      <Form.Item
        noStyle
        shouldUpdate={(pre: ExpiryDateRuleFormValues, curr) => pre.expiryDateRule !== curr.expiryDateRule}
      >
        {({ getFieldValue }) =>
          (getFieldValue('expiryDateRule') ?? initialValues?.expiryDateRule?.toString()) ===
          ProductExpiryDateRule.BasedOnDefinedApplicationElements ? (
            <Col span={8} className="inline-block">
              <Form.Item
                label={t('Defined Application Elements')}
                colon={false}
                required
                name="expiryDateDefinedFactor"
                initialValue={initialValues?.expiryDateDefinedFactor?.toString()}
                rules={[
                  {
                    required: true,
                    message: t('Please select'),
                  },
                ]}
              >
                <GeneralSelect
                  disabled={disabled}
                  option={coveragePeriodBasedOnApplicationElementsBizDictOptions}
                  style={{ width: 240 }}
                />
              </Form.Item>
            </Col>
          ) : null
        }
      </Form.Item>

      <Drawer
        open={open}
        maskClosable
        title={t('Expiry Date Rule')}
        onClose={resetDrawer}
        destroyOnClose={false}
        className={styles.drawer}
        submitBtnShow={!disabled}
        onSubmit={() => {
          drawerForm
            .validateFields(['enableFixedTime', 'defaultExpireTime'])
            .then((values: { enableFixedTime: string; defaultExpireTime: string }) => {
              form.setFieldValue('enableFixedTime', values.enableFixedTime);
              form.setFieldValue('defaultExpireTime', values.defaultExpireTime);
            })
            .then(() => {
              resetDrawer();
            });
        }}
      >
        <Form form={drawerForm} layout="vertical">
          <Form.Item name="enableFixedTime" valuePropName="checked">
            <Checkbox
              disabled={disabled}
              style={{ marginTop: 26 }}
              onChange={() => {
                drawerForm.resetFields(['defaultExpireTime']);
              }}
            >
              {t('Set Fixed Time')}
            </Checkbox>
          </Form.Item>

          <div style={{ display: 'flex' }}>
            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) =>
                (getFieldValue('enableFixedTime') as string) && (
                  <Form.Item
                    name="defaultExpireTime"
                    label={t('Fixed Time')}
                    validateTrigger="onBlur"
                    rules={[
                      {
                        // ? /^(00:0[1-9]|00:[1-5][0-9]|1[0-9]:[0-5][0-9]|2[0-3]:[0-5][0-9]|24:00)$/ // 00:01~24.00
                        pattern:
                          tenantTimeFormatByZeus?.timeRangeFormat === 'zeroToTwentyFour'
                            ? /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$|^24:00$/ // 00:00~24.00
                            : /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, // 00:00~23.59
                        message: t("Please input in this format: 'HH:mm'. e.g. 12:00"),
                      },
                      { required: true, message: t('Please input') },
                    ]}
                  >
                    <Input placeholder="HH:mm" disabled={disabled} style={{ width: 240, marginRight: 120 }} />
                  </Form.Item>
                )
              }
            </Form.Item>
          </div>
        </Form>
      </Drawer>
    </Form>
  );
};

export default ExpiryDateRule;
