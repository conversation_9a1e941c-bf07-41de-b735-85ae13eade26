.drawer {
  :global {
    .market-ant4-drawer-body {
      padding: 0;
    }
    .nagrand-drawer-header {
      display: flex;
      justify-content: space-between;
      font-weight: 700;
      font-size: 16px;
      padding: var(--gap-md) var(--gap-big);
      box-shadow: 0 -0.5px 0 0 var(--border-default) inset;
    }
    .nagrand-drawer-content {
      padding: 0 var(--gap-big) var(--gap-md) var(--gap-big);
    }
    .nagrand-drawer-footer {
      position: absolute;
      width: 100%;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: end;
      height: 72px;
      padding: 0 var(--gap-big);
      margin-top: var(--gap-md);
      background: var(--white);
      box-shadow: 0 -0.5px 0 0 var(--border-default);
      button:nth-child(1) {
        margin-right: 16px;
      }
    }
  }
}
