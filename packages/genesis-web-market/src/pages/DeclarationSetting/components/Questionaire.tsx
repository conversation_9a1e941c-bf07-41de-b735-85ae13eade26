/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { forwardRef, useCallback, useImperativeHandle, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RouteComponentProps } from 'react-router-dom';

import { message } from 'antd';
import type { ColumnProps } from 'antd/es/table';

import { useAsyncEffect } from 'ahooks';
import { cloneDeep, intersection, isEmpty, isString } from 'lodash-es';

import { EditableTable, FieldType } from '@zhongan/nagrand-ui';

import { QS, QuestionaireMapData, QuestionnaireCommonService } from 'genesis-web-service';

import { EnumType } from '@client/shared/model/common';
import { EMPTY_PLACEHOLDER, NAME_CONNECTOR_STR } from '@market/common/constant';
import { selectEnums } from '@market/redux/selector';
import { OptEnum } from '@market/request/interface';
import { NewMarketService } from '@market/services/market/market.service.new';

import { getKeyFromRow } from './handle';

export interface QuestionnaireTable extends QuestionaireMapData {
  key: string;
}

interface IProps extends RouteComponentProps {
  packageId: string;
  disabled: boolean;
}

interface IRefProps {
  getTableData: () => QuestionnaireTable[];
}

const Questionnaire = forwardRef<IRefProps, IProps>((props, ref) => {
  const enums = useSelector<unknown, Record<string, EnumType[]>>(selectEnums);
  const [t] = useTranslation(['market', 'common']);
  const [questionnaireTableData, setQuestionnaireTableData] = useState<QuestionnaireTable[]>([]);
  const [queryNameOptions, setQueryNameOptions] = useState<QS.QuestionnaireEntity[]>([]);

  useImperativeHandle(ref, () => ({
    getTableData() {
      return questionnaireTableData;
    },
  }));

  useAsyncEffect(async () => {
    // 回显已配置过的问卷
    const res = await NewMarketService.PackageUwQuestionnaireService.query({
      packageId: +props.packageId
    });
    const tempData = (cloneDeep(res) || []).map(item => ({
      ...item,
      questionnairePurposeCode: item.questionnairePurposeCode?.toString(),
      questionnaireVersion: item.questionnaireVersion?.toString(),
      key: getKeyFromRow(item),
    }));
    tempData.forEach(tempItem => {
      if (tempItem.questionnaireTrigger) {
        tempItem.questionnaireTrigger = tempItem.questionnaireTrigger.split(',');
      }
    });
    setQuestionnaireTableData(tempData);

    // 获取 "Questionnaire Name" 字段的 dropdown 列表
    const questionnaires = await QuestionnaireCommonService.queryQuestionnaires();
    setQueryNameOptions(questionnaires);
  }, [props.packageId]);

  const columns: ColumnProps<QuestionnaireTable>[] = useMemo(
    () => [
      {
        title: t('number'),
        dataIndex: 'index',
        width: 100,
        render: (text: string, record: QuestionnaireTable, index: number) => index + 1,
        editable: false,
      },
      {
        title: t('Questionnaire Name'),
        dataIndex: 'questionnaireCode',
        key: 'questionnaireCode',
        inputType: 'select',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: (queryNameOptions || []).map(i => ({
              ...i,
              label: `${i.questionnaireCode} _ ${i.questionnaireVersion} _ ${i.questionnaireName}`,
              value: i.questionnaireCode,
            })),
            placeholder: t('Please select'),
            getPopupContainer: () => document.body,
          },
        },
        render: (_, record: QuestionnaireTable) => {
          const { questionnaireCode = '', questionnaireVersion = '', questionnaireName = '' } = record || {};
          const value = [questionnaireCode, questionnaireVersion, questionnaireName].filter(item => !!item);
          return isEmpty(value) ? EMPTY_PLACEHOLDER : value.join('_');
        },
        editable: true,
      },
      {
        title: t('Questionnaire Purpose'),
        dataIndex: 'questionnairePurposeCode',
        key: 'questionnairePurposeCode',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            options: (enums.questionnairePurpose || []).map(item => {
              return {
                ...item,
                label: item.itemName,
                value: item.itemExtend1.toString(),
              };
            }),
            placeholder: t('Please select'),
            getPopupContainer: () => document.body,
          },
        },
        render: (text: string) =>
          (enums.questionnairePurpose || []).find(
            item => item.itemExtend1.toString() === (text ? text.toString() : text)
          )?.itemName,
        editable: true,
      },
      {
        title: t('Trigger'),
        dataIndex: 'questionnaireTrigger',
        key: 'questionnaireTrigger',
        fieldProps: {
          type: FieldType.Select,
          extraProps: {
            mode: 'multiple',
            options: (enums.questionnaireTrigger || []).map(item => {
              return {
                ...item,
                label: item.itemName,
                value: item.itemExtend1.toString(),
              };
            }),
            placeholder: t('Please select'),
            getPopupContainer: () => document.body,
          },
        },
        render: (triggerArr: string[] | string) => {
          const triggerNameArr: string[] = [];
          if (Array.isArray(triggerArr)) {
            triggerArr.forEach((triggerItem: string) => {
              const triggerName = (enums.questionnaireTrigger || []).find(item => item.dictValue === triggerItem);
              triggerNameArr.push(triggerName?.dictValueName);
            });
            return triggerNameArr.join(NAME_CONNECTOR_STR);
          }
        },
        editable: true,
      },
    ],
    [t, queryNameOptions, enums.questionnairePurpose, enums.questionnaireTrigger]
  );

  const checkTrigger = useCallback((tempTableData: QuestionnaireTable[], valueTemp: QuestionnaireTable) => {
    let selectedValues: string[] = [];
    let result = false;
    tempTableData.forEach((item: QuestionnaireTable) => {
      if (item.questionnairePurposeCode === valueTemp.questionnairePurposeCode) {
        selectedValues = selectedValues.concat(item.questionnaireTrigger);
      }
    });
    if (intersection(selectedValues, valueTemp.questionnaireTrigger).length > 0) {
      result = true;
    }
    return result;
  }, []);

  const handleGetQuestionnaireInfo = (questionnaireCode: string) =>
    queryNameOptions.find(item => item.questionnaireCode === questionnaireCode) || {
      questionnaireCode: '',
      questionnaireName: '',
      questionnaireVersion: 0,
    };

  /**
   * Edit finished
   */
  const handleOnSubmit = useCallback(
    (value?: QuestionnaireTable, key?: string) =>
      new Promise((resolve, reject) => {
        const { questionnairePurposeCode = '', questionnaireCode = '' } = value || {};
        const tempTableData = cloneDeep(questionnaireTableData);

        const questionnaireNameFromTableData =
          tempTableData.find(item => item.questionnaireCode === questionnaireCode)?.questionnaireName || '';

        const questionnaireNameFromOptions =
          queryNameOptions.find(item => item.questionnaireCode === questionnaireCode)?.questionnaireName || '';

        const valueTemp = {
          ...value,
          ...handleGetQuestionnaireInfo(questionnaireCode),
          questionnaireName: questionnaireNameFromTableData || questionnaireNameFromOptions,
          questionnairePurposeCode: questionnairePurposeCode.toString() || '',
          packageId: props.packageId,
        } as QuestionnaireTable;

        valueTemp.key = getKeyFromRow(valueTemp);
        const editIndex = tempTableData.findIndex((row: QuestionnaireTable) => row.key === key);
        if (key !== OptEnum.Add) {
          tempTableData[editIndex].questionnaireTrigger = [];
        }
        const hasDupPurposeFlag = checkTrigger(tempTableData, valueTemp);
        if (hasDupPurposeFlag) {
          message.warning(
            t(
              'Only one record is allowed for each questionnaire purpose under the same trigger. No duplicated allowed. Please check.'
            )
          );
          reject();
          return;
        }
        if (key === OptEnum.Add) {
          const index = tempTableData.findIndex(item => item.key === 'add');
          tempTableData[index] = valueTemp;
        } else {
          tempTableData[editIndex] = valueTemp;
        }

        setQuestionnaireTableData(tempTableData);
        resolve(true);
      }),
    [questionnaireTableData, handleGetQuestionnaireInfo, props.packageId, checkTrigger, t]
  );

  const _data = questionnaireTableData.map(item => {
    const trigger = item.questionnaireTrigger;
    return { ...item, questionnaireTrigger: isString(trigger) ? trigger.split(',') : trigger };
  });

  return (
    <EditableTable
      columns={columns}
      rowKey={getKeyFromRow}
      dataSource={_data}
      setDataSource={setQuestionnaireTableData}
      pagination={false}
      scroll={{ x: 'max-content' }}
      addBtnProps={{
        type: 'dashed',
        addTitle: t('Add'),
        disabled: props.disabled,
      }}
      editBtnProps={{
        disabled: () => props.disabled,
      }}
      deleteBtnProps={{
        disabled: () => props.disabled,
      }}
      handleConfirm={handleOnSubmit}
    />
  );
});

export default Questionnaire;
