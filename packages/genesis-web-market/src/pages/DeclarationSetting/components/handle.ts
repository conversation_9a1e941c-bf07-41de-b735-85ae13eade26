import { isString } from 'lodash-es';

interface IParams {
  questionnairePurposeCode: string;
  questionnaireTrigger: string | string[];
}

export function getKeyFromRow({ questionnairePurposeCode, questionnaireTrigger }: IParams) {
  let _trigger = questionnaireTrigger || [];
  if (isString(_trigger)) {
    _trigger = _trigger.split(',');
  }
  return `${questionnairePurposeCode}${_trigger?.[0]}`;
}
