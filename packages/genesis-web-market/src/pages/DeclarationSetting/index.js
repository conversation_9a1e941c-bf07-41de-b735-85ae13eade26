import { Component, Suspense, createRef } from 'react';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';

import { Button, Layout, Skeleton, message } from 'antd';

import Section from '@market/components/Section/section';
import { selectPermissionCheckMap } from '@market/redux/selector';
import { NewMarketService } from '@market/services/market/market.service.new';

import EditableTable from '../../components/EditableTable';
import { FMarketHeader } from '../../components/F-Market-Header';
import FMarketMenu from '../../components/F-Market-Menu';
import { generalConfirm, urlQuery } from '../../util';
import DeclarationEditDrawer from './DeclarationEditDrawer';
import Questionnaire from './components/Questionaire';
import './index.scss';

const { Sider, Content } = Layout;
class DeclarationSetting extends Component {
  constructor(props) {
    super(props);
    this.questionnaireRef = createRef();
    this.state = {
      drawerVisible: false,
      currentRecord: {},
      mode: 'edit',
      isShowEditButton: false,
      tableStateList: [],
      tableLoading: [],
      categoryData: [],
      declarationData: [],
      skeletonVisible: true,
      sortingTypeList: [],
    };
  }

  componentDidMount() {
    this.packageId = urlQuery('packageId');
    if (!this.packageId) {
      this.backToSearch();
      return;
    }
    this.auditInfo = {
      menu: 'Package',
      page: 'Declaration Configuration',
    };

    const { state } = this.props.location;
    const mode = state && state.mode;
    this.setState({
      mode: mode || 'edit',
    });
    // 动态配置当前告知大类
    this.queryCategories();
    this.judgeShowEdit(mode, this.packageId);
  }

  judgeShowEdit = (mode, packageId) => {
    const { userInfo, editableAuth, isSuperUser } = this.props;

    NewMarketService.PackageProductMgmtService.queryConfigProducts({
      packageId,
    })
      .then(
        res => {
          if (res && !res.message) {
            this.setState({
              isShowEditButton: (res.creator === `${userInfo.userId}` && editableAuth) || isSuperUser,
            });
          }
        },
        error => {
          return message.error(error.message);
        }
      )
      .finally(() => {
        this.setState({
          skeletonVisible: false,
        });
      });
  };

  getColumns = () => {
    const { enums } = this.props;
    return [
      {
        title: this.props.t('number'),
        dataIndex: 'index',
        width: 80,
        render: (text, record) => {
          return <span>{record.index}</span>;
        },
      },
      {
        title: this.props.t('Declarant'),
        dataIndex: 'userType',
        width: 150,
        render: text => {
          if (text) {
            const destObj = enums.informGroupType.filter(i => i.itemExtend1 === text.toString());
            text = destObj && destObj.length > 0 && destObj[0].itemName;
          }
          return <span>{text}</span>;
        },
      },
      {
        title: this.props.t('Content Category'),
        width: 200,
        dataIndex: 'informCategoryName',
      },
      {
        title: this.props.t('Declaration Type'),
        width: 200,
        dataIndex: 'answerType',
        render: text => {
          if (text) {
            const destObj = enums.answerType.filter(i => i.itemExtend1 === text.toString());
            text = destObj && destObj.length > 0 && destObj[0].itemName;
          }
          return <span>{text}</span>;
        },
      },
      {
        title: this.props.t('Question Declaration'),
        width: 300,
        dataIndex: 'question',
      },
      {
        title: this.props.t('Declaration Question Code'),
        width: 220,
        dataIndex: 'questionCode',
      },
    ];
  };

  async queryCategories() {
    const { enums } = this.props;
    const categoryList = enums.declarationCategory || [];
    this.setState({ categoryData: categoryList });
    categoryList.forEach(category => {
      this.queryDeclaration(category.dictValue);
    });
  }

  async queryDeclaration(categoryType, isDelete) {
    const { tableLoading } = this.state;
    tableLoading[categoryType] = true;
    this.setState({
      tableLoading,
    });
    const { declarationData = [] } = this.state;
    if (isDelete === 'Y') {
      declarationData[categoryType] = [];
    }
    const decRes = await NewMarketService.PropertyPackageInformMgmtService.query({
      packageId: this.packageId,
      informCategoryParentId: categoryType,
    });
    if (decRes && decRes.value) {
      const list = decRes.value.packageInformQueryDTOList || [];
      list.forEach((item, index) => {
        item.index = index + 1;
        item.key = item.id;
      });
      declarationData[categoryType] = [...list];
    }
    this.setState({ declarationData });
    tableLoading[categoryType] = false;
    this.setState({
      tableLoading,
    });
  }

  deleteRow = (id, type) => {
    NewMarketService.PropertyPackageInformMgmtService.delete({
      ids: [id],
      auditInfo: {
        ...this.auditInfo,
        button: 'Declaration Delete',
      },
    }).then(res => {
      if (res.success) {
        message.success(this.props.t('Deleted successfully'));
        this.queryDeclaration(type, 'Y');
      }
    });
  };

  addNewDeclaration = tableType => {
    const { declarationData = [] } = this.state;
    const dataList = declarationData[tableType];
    let orderNo = 1;
    if (dataList && dataList.length > 0) {
      orderNo = dataList[dataList.length - 1].orderNo;
    }
    this.setState({
      drawerVisible: true,
      currentRecord: {
        type: tableType,
        orderNo,
      },
    });
  };

  onEditRow = (record, tableType) => {
    this.setState({
      drawerVisible: true,
      currentRecord: {
        type: tableType,
        ...record,
      },
    });
  };

  confirmDeclaration = type => {
    this.setState({
      drawerVisible: false,
    });
    this.queryDeclaration(type);
  };

  backToSearch = () => {
    this.props.navigate('/market/package/search');
  };

  doneStep = () => {
    NewMarketService.PackageGoodsStepInfoMgmtService.doneStep({
      // 记录步骤
      refId: this.packageId,
      type: 1,
      stepNo: 5,
      isNew: false,
      isSubmit: true,
      auditInfo: {
        ...this.auditInfo,
        button: 'Submit',
      },
    }).then(res1 => {
      if (res1.success) {
        message.success(this.props.t('Publish successfully'));
        this.backToSearch();
      }
    });
  };

  onSubmit = async isSave => {
    const { current } = this.questionnaireRef;
    const { sortingTypeList } = this.state;
    const tableData = current.getTableData();
    const data = {
      packageId: this.packageId,
    };
    if (sortingTypeList.length > 0) {
      sortingTypeList?.map(item =>
        message.error(
          this.props.t('There is unfinished editing in <{{title}}>. Please complete before proceeding.', {
            title: item,
          })
        )
      );
      return;
    }
    if (tableData.length > 0) {
      tableData.forEach(item => {
        if (Array.isArray(item.questionnaireTrigger) && item.questionnaireTrigger.length > 0) {
          item.questionnaireTrigger = item.questionnaireTrigger.join(',');
        }
      });
      data.packageQuestionnaireBaseList = tableData;
    }
    await NewMarketService.PackageUwQuestionnaireService.saveUwQuestionnaire(data).then(res => {
      if (res.msg) {
        message.warning(res.msg);
      } else if (isSave) {
        message.success(this.props.t('Save successfully'));
      }
    });
    if (isSave) {
      return;
    }
    const packageValidation = await NewMarketService.GoodsPackageValidateService.validatePackage(this.packageId);
    if (packageValidation.errors?.length) {
      generalConfirm({
        title: this.props.t('Validation Response'),
        content: (
          <ul>
            {packageValidation.errors.map(message => (
              <li>{message}</li>
            ))}
          </ul>
        ),
      });
      return;
    }
    if (packageValidation.warning?.length) {
      generalConfirm({
        title: this.props.t('Validation Response'),
        content: (
          <ul>
            {packageValidation.warning.map(message => (
              <li>{message}</li>
            ))}
          </ul>
        ),
        okText: this.props.t('Confirm'),
        onOk: () => {
          this.doneStep();
        },
      });
      return;
    }
    this.doneStep();
  };

  sortChange = (record, type) => {
    const { declarationData = [] } = this.state;
    declarationData[type] = record;
    this.setState({ declarationData });
  };

  saveSort = (item, name) => {
    const { declarationData = [], sortingTypeList } = this.state;
    const sortingList = [...sortingTypeList];
    const index = sortingTypeList.findIndex(item => item === name);
    sortingList?.splice(index, 1);
    this.setState({
      sortingTypeList: sortingList,
    });
    const existData = declarationData[item] || [];
    const sortArr = [];
    existData.forEach((i, index) => {
      sortArr.push({
        id: i.id,
        orderNo: index + 1,
      });
    });
    sortArr.auditInfo = {
      ...this.auditInfo,
      button: 'Save Sort',
    };
    NewMarketService.PropertyPackageInformMgmtService.update(sortArr).then(res => {
      if (res.success) {
        message.success(this.props.t('Save Successfully'));
        this.queryDeclaration(item);
      }
    });
  };

  backToLastHistory = async () => {
    this.props.navigate(`/market/package/application-elements?packageId=${this.packageId}`, {
      state: {
        mode: this.state.mode || 'edit',
      },
    });
  };

  onEdit = () => {
    if (this.state.mode !== 'view') {
      return;
    }
    this.setState({ mode: 'edit' });
  };

  sortHealthTable = category => {
    const { sortingTypeList } = this.state;
    const sortingList = [...sortingTypeList].concat(category.dictValueName);
    this.setState({
      sortingTypeList: sortingList,
    });
  };

  editableTableHtml = readOnly => {
    const { categoryData, declarationData } = this.state;
    const decHtml = [];
    categoryData.forEach((category, i) => {
      decHtml.push(
        <div className={`sub-block ${i === 0 ? 'first' : ''}`}>
          <Section subTitle={category.dictValueName}>
            <EditableTable
              loading={this.state.tableLoading[category.dictValue]}
              sortable
              data={declarationData[category.dictValue]}
              columns={this.getColumns()}
              onAddNew={() => this.addNewDeclaration(category.dictValue)}
              onDelete={key => this.deleteRow(key, category.dictValue)}
              onEditRow={record => this.onEditRow(record, category.dictValue)}
              onStartSort={() => this.sortHealthTable(category)}
              onSortChange={record => this.sortChange(record, category.dictValue)}
              onSaveSort={() => this.saveSort(category.dictValue, category.dictValueName)}
              disabled={readOnly}
              onStateChange={tableState => {
                const tableStateList = [...this.state.tableStateList];
                tableStateList[0] = tableState;
                this.setState({
                  tableStateList,
                });
              }}
            />
          </Section>
        </div>
      );
    });
    return decHtml;
  };

  renderEditSaveBtn() {
    const { isShowEditButton, mode } = this.state;
    if (!isShowEditButton || this.props.envConfig.env === 'prd') {
      return null;
    }

    if (mode === 'view') {
      return (
        <Button size="large" onClick={() => this.onEdit()}>
          {this.props.t('Edit')}
        </Button>
      );
    }

    return (
      <Button size="large" onClick={() => this.onSubmit(true)}>
        {this.props.t('Save')}
      </Button>
    );
  }

  render() {
    const { drawerVisible = false, currentRecord, mode, isShowEditButton, categoryData } = this.state;
    const readOnly = mode === 'view' || !isShowEditButton || this.props.envConfig.env === 'prd';
    return (
      <Layout className="market-layout package-declaration">
        <FMarketHeader backPath="/market/package/search" subMenu="Package" />
        <div className="flex" style={{ height: 'calc(100% - 50px)' }}>
          <Sider width={208} className="market-sider">
            <FMarketMenu
              page="PACKAGE"
              defaultSelectedKeys={['5']}
              type={mode}
              category={1}
              packageId={this.packageId}
              navigate={this.props.navigate}
            />
          </Sider>
          <Content className="market-content">
            <Skeleton active loading={this.state.skeletonVisible}>
              <div className="right-content-wrapper mt-6 mx-6">
                {this.editableTableHtml(readOnly)}
                <Suspense fallback={<h1>...</h1>}>
                  <div className="sub-block">
                    <Section subTitle={this.props.t('Questionnaire')}>
                      <Questionnaire ref={this.questionnaireRef} packageId={this.packageId} disabled={readOnly} />
                    </Section>
                  </div>
                </Suspense>
              </div>
              {/* 告知编辑的抽屉弹出层 */}
              <div className="edit-part">
                {drawerVisible && (
                  <DeclarationEditDrawer
                    packageId={this.packageId}
                    record={currentRecord}
                    onCancel={() => this.setState({ drawerVisible: false, currentRecord: {} })}
                    onConfirm={this.confirmDeclaration}
                    visible={drawerVisible}
                    categoryData={categoryData}
                  />
                )}
              </div>
            </Skeleton>
          </Content>
          <div className="bottom-action-bar">
            {readOnly ? null : (
              <Button size="large" onClick={() => this.onSubmit()} type="primary">
                {this.props.t('Publish')}
              </Button>
            )}
            {this.renderEditSaveBtn()}
            {/* <Button
              size="large"
              onClick={() =>
                this.props.navigate(`/market/package/policy-change?packageId=${this.packageId}`, {
                  state: {
                    mode: this.state.mode || 'edit',
                  },
                })
              }
            >
              {this.props.t('Next')}
            </Button> */}
          </div>
        </div>
      </Layout>
    );
  }
}

const mapStateToProps = state => {
  const enums = state.enums;
  const permissionMap = selectPermissionCheckMap(state);
  return {
    enums,
    envConfig: state.envConfig,
    editableAuth: !!permissionMap['market.edit'],
    isSuperUser: !!permissionMap['market.edit-all'],
    userInfo: state.userInfo,
  };
};

export default withTranslation(['market', 'common'])(connect(mapStateToProps)(DeclarationSetting));
