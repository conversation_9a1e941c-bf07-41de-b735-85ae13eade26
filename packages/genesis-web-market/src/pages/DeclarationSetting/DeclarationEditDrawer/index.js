import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';

import { Col, Form, Row, message } from 'antd';

import PropTypes from 'prop-types';

import { Drawer } from '@zhongan/nagrand-ui';

import GeneralSelect from '@market/components/GeneralSelect';
import { t } from '@market/i18n';
import { NewMarketService } from '@market/services/market/market.service.new';

import './index.scss';

const DeclarationEditDrawer = ({ onCancel, visible, onConfirm, packageId, categoryData, enums, record = {} }) => {
  const [questionList, setQuestionList] = useState([]);
  const [condition, setCondition] = useState({}); // 搜索问题的条件
  const formItemSelectList = [
    {
      label: t('Declarant'),
      value: 'userType',
      options: [],
    },
    {
      label: t('Content Category'),
      value: 'contentCategoryId',
      options: [],
    },
    {
      label: t('Declaration Type'),
      value: 'answerType',
      options: [],
    },
  ];
  const [contentListAll, setContentListAll] = useState([]);

  const [form] = Form.useForm();

  useEffect(() => {
    if (categoryData) {
      const newContentListAll = [];
      categoryData.forEach(category => {
        const content = (category.childList || []).map(item => {
          item.itemExtend1 = item.dictValue;
          return item;
        });
        newContentListAll[category.dictValue] = [...content];
      });
      setContentListAll(newContentListAll);
    }
  }, [categoryData]);

  useEffect(() => {
    if (visible) {
      const newCondition = {
        informCategoryId: record.type,
      };
      if (record.answerType || record.informCategoryId) {
        newCondition.answerType = record.answerType;
        newCondition.contentCategoryId = record.informCategoryId;
      }
      setCondition(newCondition);
      if (newCondition.answerType || newCondition.contentCategoryId) {
        queryQuestions(newCondition);
      }
    }
  }, [visible, record]);

  const onSelect = (key, value) => {
    if (key === 'userType') return;
    const newCondition = { ...condition };
    newCondition[key] = value;
    setCondition(newCondition);
    form.setFieldsValue({ question: undefined });
    queryQuestions(newCondition);
  };

  const queryQuestions = condition => {
    const data = {
      page: {
        pageIndex: 1,
        limit: 1000,
        condition,
      },
    };
    /* 处理健康告知和财务告知的内容list */
    NewMarketService.InformQuestionMgmtService.query(data).then(res => {
      if (res.success && res.value && res.value.questionList) {
        setQuestionList(res.value.questionList.results || []);
      } else {
        setQuestionList([]);
      }
    });
  };

  const getNameById = (list, dictValue) => {
    const target = list.find(item => item.dictValue === dictValue);
    return (target && target.dictValueName) || '';
  };

  const submitDeclaration = packageId => {
    form
      .validateFields()
      .then(values => {
        const contentList = contentListAll[record.type];
        let data = {
          packageId,
          userType: values.userType,
          questionCode: values.question,
          orderNo: record.orderNo,
          informCategoryId: values.contentCategoryId,
          informCategoryName: getNameById(contentList, values.contentCategoryId),
          informCategoryParentId: record.type,
          informCategoryParentName: getNameById(categoryData, record.type),
        };
        if (!record.id) {
          // 新增
          data.auditInfo = {
            menu: 'Package',
            page: 'Declaration Configuration',
            button: 'Drawer Confirm',
          };
          NewMarketService.PropertyPackageInformMgmtService.save(data).then(res => {
            if (res.success) {
              message.success(t('Save successfully'));
              onConfirm(record.type);
            }
          });
        } else {
          data = [
            {
              id: record.id,
              userType: values.userType,
              questionCode: values.question,
              informCategoryId: values.contentCategoryId,
              informCategoryName: getNameById(contentList, values.contentCategoryId),
              informCategoryParentId: record.type,
              informCategoryParentName: getNameById(categoryData, record.type),
            },
          ];
          data.auditInfo = {
            menu: 'Package',
            page: 'Declaration Configuration',
            button: 'Drawer Confirm',
          };
          NewMarketService.PropertyPackageInformMgmtService.update(data).then(res => {
            if (res.success) {
              message.success(t('edit success'));
              onConfirm(record.type);
            }
          });
        }
      })
      .catch(errorInfo => {
        console.log('Validation Failed:', errorInfo);
      });
  };

  const getTitle = record => {
    const category = categoryData.filter(item => item.dictValue === record.type);
    const title = `${record.id ? t('Edit') : t('Add')} ${(category[0] && category[0].dictValueName) || ''}`;
    return title;
  };

  const fillSelectList = record => {
    let list = [...formItemSelectList];
    let declarationTypeList = [];
    let informGroupTypeList = [];
    const contentList = contentListAll[record.type] || []; // 告知内容List
    if (enums.answerType) {
      // 告知类型，单选、多选
      declarationTypeList = enums.answerType;
    }
    if (enums.informGroupType) {
      // 被保人、受益人
      informGroupTypeList = enums.informGroupType;
    }
    list = list.map(item => {
      if (item.value === 'userType') {
        item.options = informGroupTypeList;
        item.initialValue = record.userType && record.userType.toString();
      }
      if (item.value === 'answerType') {
        item.options = declarationTypeList;
        item.initialValue = record.answerType && record.answerType.toString();
      }
      if (item.value === 'contentCategoryId') {
        item.options = contentList;
        item.initialValue = record.informCategoryId && record.informCategoryId.toString();
      }
      return item;
    });
    return list;
  };

  const onCancelHandler = () => {
    form.resetFields();
    onCancel();
  };

  const formattedSelectList = fillSelectList(record);

  return (
    <Drawer
      open={visible}
      title={getTitle(record)}
      onClose={onCancelHandler}
      onSubmit={() => submitDeclaration(packageId)}
    >
      <Form layout="vertical" className="declaration-selet-list" form={form}>
        <Row>
          {formattedSelectList.map(item => (
            <Col span={8} key={item.value}>
              <Form.Item
                label={item.label}
                key={item.value}
                name={item.value}
                rules={[
                  {
                    required: true,
                    message: t('Please select'),
                  },
                ]}
                initialValue={item.initialValue}
              >
                <GeneralSelect
                  onSelect={e => onSelect(item.value, e)}
                  allowClear={false}
                  placeholder={t('Please select')}
                  className="select-item"
                  option={((item && item.options) || []).map(sub => ({
                    value: sub.itemExtend1,
                    label: sub.dictValueName,
                  }))}
                />
              </Form.Item>
            </Col>
          ))}
          <Col span={8}>
            <Form.Item
              required
              label={t('Code & Question Declaration')}
              name="question"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
              initialValue={record.questionCode}
            >
              <GeneralSelect
                placeholder={t('Please select')}
                allowClear={false}
                className="select-item"
                option={questionList.map(sub => ({
                  key: sub.informQuestionId,
                  value: sub.questionCode,
                  label: `${sub.questionCode} - ${sub.question}`,
                }))}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Drawer>
  );
};

DeclarationEditDrawer.propTypes = {
  onCancel: PropTypes.func.isRequired,
  visible: PropTypes.bool.isRequired,
  onConfirm: PropTypes.func.isRequired,
  packageId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

const mapStateToProps = state => {
  const enums = state.enums;
  return {
    enums,
  };
};

export default connect(mapStateToProps)(DeclarationEditDrawer);
