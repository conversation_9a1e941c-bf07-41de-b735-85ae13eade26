.package-declaration.market-ant4-layout {
  height: 100%;
  overflow: hidden;
  .right-content-wrapper {
    // 解决由于屏幕太大导致的底部灰色留白太多的问题
    min-height: calc(100% - 104px);
  }
  .detail-sider {
    background: var(--white);
    height: calc(100% - 72px);
    overflow: auto;
  }
  .market-content.market-ant4-layout-content {
    .right-content-wrapper {
      .block-title {
        padding-bottom: 16px;
        font-weight: bold;
        color: var(--text-color);
        line-height: 24px;
        font-size: 16px;
        border-bottom: 1px dashed var(--border-light);
      }
      .sub-block {
        padding: 16px 0px 24px 0px;
        border-bottom: 1px solid var(--border-light);
        &.first {
          padding-top: 0;
        }
        &:last-child {
          border-bottom: none;
        }
        .sub-block-title {
          margin-bottom: 8px;
          font-weight: bold;
          color: var(--text-color);
        }
      }
      .sub-block:last-child {
        padding-bottom: 0;
      }
    }
  }
}
