import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Switch } from 'antd';
import { ColumnProps } from 'antd/es/table';

import { useRequest } from 'ahooks';

import { EditAction, Table, TextEllipsisDetect } from '@zhongan/nagrand-ui';

import {
  ClaimStackInstanceRelationType,
  LiabilityLevelClaimStackTemplateRelationResponse,
} from 'genesis-web-service/service-types/market-types/package';

import { DetailPageMode } from '@market/common/interface';
import { useUpdateInstanceRelation } from '@market/hook/claim-stack/useStackTemplateService';
import { useDrawerState } from '@market/hook/useDrawerState';
import ClaimStackTemplateDetailDrawer from '@market/pages/BenefitConfiguration/SMEStackValueSetting/components/ClaimStackTemplateDetailDrawer/ClaimStackTemplateDetailDrawer';
import { StackTemplateValues } from '@market/pages/BenefitConfiguration/SMEStackValueSetting/interface';
import { NewMarketService } from '@market/services/market/market.service.new';
import { urlQuery } from '@market/util';

interface Props {
  disabled: boolean;
  goodsPlanId: number;
  packageId: number;
  reoload: () => Promise<void>;
  liabilityStacks: LiabilityLevelClaimStackTemplateRelationResponse[];
  loading: boolean;
}

export const LiabilityClaimStack = ({
  disabled,
  goodsPlanId,
  packageId,
  reoload,
  liabilityStacks,
  loading,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [drawerState, setDrawerState] = useDrawerState<StackTemplateValues>();

  const { updateInstanceRelation } = useUpdateInstanceRelation();
  const { runAsync: updateClaimStackRelationNeedUseValue, loading: updateLoading } = useRequest(
    param => NewMarketService.PackageProductInsuranceMgmtService.updateClaimStackRelationNeedUseValue(param),
    {
      manual: true,
    }
  );

  const columns = useMemo(() => {
    const tempColumns: ColumnProps<LiabilityLevelClaimStackTemplateRelationResponse>[] = [
      {
        title: t('No.'),
        width: 60,
        render: (text: string, record: any, index: number) => index + 1,
      },
      {
        title: t('Product Name'),
        width: 150,
        dataIndex: 'productName',
      },
      {
        title: t('Liability Name'),
        width: 250,
        dataIndex: 'liabilityNames',
      },
      {
        title: t('Stack Content'),
        render: (_, record) => {
          if (!record.template?.content) {
            return t('--');
          }
          return <TextEllipsisDetect text={record.template?.content} width={600} line={2} />;
        },
      },
      {
        title: t('Use or not'),
        width: 120,
        render: (_, record) => (
          <Switch
            checked={record.needUse}
            onChange={checked => {
              updateClaimStackRelationNeedUseValue({
                goodsId: +urlQuery('goodsId')!,
                packageId,
                liabilityIdSet: record.liabilityIdSet,
                planId: goodsPlanId,
                productId: record.productId,
                templateCode: record.template?.code,
                relationType: record.relationType,
                needUse: checked,
              }).then(() => {
                reoload();
              });
            }}
          />
        ),
      },
    ];

    if (!disabled) {
      tempColumns.push({
        fixed: 'right',
        width: 80,
        title: t('Actions'),
        render: (text, record) => (
          <EditAction
            onClick={() => {
              setDrawerState({
                record,
                visible: true,
                mode: DetailPageMode.edit,
              });
            }}
          />
        ),
      });
    }

    return tempColumns;
  }, [disabled, goodsPlanId, packageId, reoload, setDrawerState, t, updateClaimStackRelationNeedUseValue]);

  return (
    <div>
      <div className="mb-2">{t('Liability Claim Stack')}</div>
      <Table
        loading={updateLoading || loading}
        columns={columns}
        dataSource={liabilityStacks}
        scroll={{ x: 'max-content' }}
        pagination={false}
      />
      <ClaimStackTemplateDetailDrawer
        {...drawerState}
        relationType={ClaimStackInstanceRelationType.PLAN_PRODUCT_LIABILITY}
        onClose={() => {
          setDrawerState({ visible: false });
        }}
        onSubmitSuccess={newRecord => {
          updateInstanceRelation({
            planId: goodsPlanId,
            packageId: +packageId,
            relationType: ClaimStackInstanceRelationType.PLAN_PRODUCT_LIABILITY,
            stackTemplate: newRecord,
            liabilityIdSet: drawerState.record.liabilityIdSet,
            productId: drawerState.record.productId,
          }).then(() => {
            reoload();
            setDrawerState({ visible: false });
          });
        }}
      />
    </div>
  );
};

export default LiabilityClaimStack;
