import { useTranslation } from 'react-i18next';

import { Divider } from 'genesis-web-component/lib/components';
import { GoodsPlanResponseDTO, PlanPackageDTO } from 'genesis-web-service/service-types/market-types/package';

import Section from '@market/components/Section/section';
import { usePlanClaimStackService } from '@market/hook/claim-stack/usePlanClaimStackService';

import { PlanPackages } from '../index';
import LiabilityClaimStack from './LiabilityClaimStack/LiabilityClaimStack';
import PolicyClaimStack from './PolicyClaimStack/PolicyClaimStack';
import ProductClaimStack from './ProductClaimStack/ProductClaimStack';

interface Props {
  plan: GoodsPlanResponseDTO;
  disabled: boolean;
  packageItem: PlanPackageDTO & PlanPackages;
}

export const PlanClaimStackSetting = ({ plan, disabled, packageItem }: Props): JSX.Element | null => {
  const [t] = useTranslation(['market', 'common']);

  const { planClaimStack, queryPlanClaimStack, loading } = usePlanClaimStackService(
    plan.goodsPlanId!,
    packageItem.packageId!
  );

  if (
    planClaimStack?.liabilityStacks?.length === 0 &&
    planClaimStack?.productStacks?.length === 0 &&
    planClaimStack?.packageStacks?.length === 0
  ) {
    return null;
  }

  return (
    <div>
      <Divider className="!my-3" />
      <Section thirdLevelTitle={t('Claim Stack Setting')}>
        <div className="mb-6">
          <LiabilityClaimStack
            goodsPlanId={plan.goodsPlanId!}
            disabled={disabled}
            packageId={packageItem.packageId!}
            reoload={queryPlanClaimStack}
            liabilityStacks={planClaimStack?.liabilityStacks || []}
          />
        </div>
        <div className="mb-6">
          <ProductClaimStack
            goodsPlanId={plan.goodsPlanId!}
            disabled={disabled}
            packageId={packageItem.packageId!}
            reoload={queryPlanClaimStack}
            productStacks={planClaimStack?.productStacks || []}
          />
        </div>
        <PolicyClaimStack
          goodsPlanId={plan.goodsPlanId!}
          disabled={disabled}
          packageId={packageItem.packageId!}
          reoload={queryPlanClaimStack}
          packageStacks={planClaimStack?.packageStacks || []}
        />
      </Section>
    </div>
  );
};

export default PlanClaimStackSetting;
