import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { ColumnProps } from 'antd/es/table';

import { EditAction, Table, TextEllipsisDetect } from '@zhongan/nagrand-ui';

import {
  ClaimStackInstanceRelationType,
  PackageLevelClaimStackTemplateRelationResponse,
} from 'genesis-web-service/service-types/market-types/package';

import { DetailPageMode } from '@market/common/interface';
import { useUpdateInstanceRelation } from '@market/hook/claim-stack/useStackTemplateService';
import { useDrawerState } from '@market/hook/useDrawerState';
import ClaimStackTemplateDetailDrawer from '@market/pages/BenefitConfiguration/SMEStackValueSetting/components/ClaimStackTemplateDetailDrawer/ClaimStackTemplateDetailDrawer';
import { StackTemplateValues } from '@market/pages/BenefitConfiguration/SMEStackValueSetting/interface';

interface Props {
  disabled: boolean;
  goodsPlanId: number;
  packageId: number;
  reoload: () => Promise<void>;
  packageStacks: PackageLevelClaimStackTemplateRelationResponse[];
}

export const ProductClaimStack = ({ disabled, goodsPlanId, packageId, reoload, packageStacks }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [drawerState, setDrawerState] = useDrawerState<StackTemplateValues>();

  const { updateInstanceRelation } = useUpdateInstanceRelation();

  const columns = useMemo(() => {
    const tempColumns: ColumnProps<PackageLevelClaimStackTemplateRelationResponse>[] = [
      {
        title: t('No.'),
        width: 60,
        render: (text: string, record: any, index: number) => index + 1,
      },
      {
        title: t('Stack Content'),
        render: (_, record) => {
          if (!record.template?.content) {
            return t('--');
          }
          return <TextEllipsisDetect text={record.template?.content} width={1000} line={2} />;
        },
      },
    ];

    if (!disabled) {
      tempColumns.push({
        fixed: 'right',
        width: 80,
        title: t('Actions'),
        render: (text, record) => (
          <EditAction
            onClick={() => {
              setDrawerState({
                record,
                visible: true,
                mode: DetailPageMode.edit,
              });
            }}
          />
        ),
      });
    }

    return tempColumns;
  }, [disabled, t]);

  return (
    <div>
      <div className="mb-2">{t('Policy Claim Stack')}</div>
      <Table columns={columns} dataSource={packageStacks} pagination={false} />
      <ClaimStackTemplateDetailDrawer
        {...drawerState}
        relationType={ClaimStackInstanceRelationType.PLAN_PACKAGE}
        onClose={() => {
          setDrawerState({ visible: false });
        }}
        onSubmitSuccess={newRecord => {
          updateInstanceRelation({
            planId: goodsPlanId,
            packageId: +packageId,
            relationType: ClaimStackInstanceRelationType.PLAN_PACKAGE,
            stackTemplate: newRecord,
          }).then(() => {
            reoload();
            setDrawerState({ visible: false });
          });
        }}
      />
    </div>
  );
};

export default ProductClaimStack;
