import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { Form, Input, InputNumber, Radio } from 'antd';
import { ColumnProps } from 'antd/es/table';
import { FormInstance } from 'antd/lib';

import cls from 'classnames';
import { debounce, set } from 'lodash-es';

import { ColumnsType, Icon, Popconfirm, Table } from '@zhongan/nagrand-ui';

import { IsVirtualType, ProductTypeEnum } from 'genesis-web-service';
import {
  PlanLiabilityQueryResponseDTO,
  PlanProductInfoResponseDTO,
  base_SchemaFactorCodePreDefineValueBase,
} from 'genesis-web-service/service-types/market-types/package';

import Collapse from '@market/components/Collapse';
import { ProductTagLabel } from '@market/components/ProductTagLabel/ProductTagLabel';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { useObjectCategory } from '@market/hook/tenant-config/useObjectCategory';
import { ProductSASetting } from '@market/pages/BenefitConfiguration/ProductSASetting/ProductSASetting';
import { EffectivePremiumCalculationMethodList, PreDefineValues } from '@market/pages/BenefitConfiguration/interface';
import { getLiabilityValues, getProductValues } from '@market/pages/BenefitConfiguration/sercice';

import { PremiumCalculationMethod } from '..';
import LiabilityObject from '../LiabilityObject/LiabilityObject';

interface ProductCollapseProps {
  planIndex: number;
  packageIndex: number;
  productIndex: number;
  product: PlanProductInfoResponseDTO;
  disabled: boolean;
  form: FormInstance<any>;
  visible: boolean;
  setVisible: (index: number, visible: boolean) => void;
}

export const ProductCollapse = (props: ProductCollapseProps) => {
  const { planIndex, packageIndex, productIndex, product, disabled, form, visible, setVisible } = props;
  const dataSource = product.planLiabilities || [];
  const [t] = useTranslation(['market', 'common']);
  const isVirtualProduct = product.isVirtual === IsVirtualType.Yes;
  const { categories, hasAddressCategoryArr } = useObjectCategory();
  const [productSASettingVisible, setProductSASettingVisible] = useState<boolean>(false);

  const [productSASettingInfo, setProductSASettingInfo] = useState<{
    type: 'product' | 'liability';
    unitType: PremiumCalculationMethod;
    selfInsuranceItem: PlanLiabilityQueryResponseDTO | PlanProductInfoResponseDTO;
  } | null>();

  const productValue = form.getFieldValue([
    'coveragePlanList',
    planIndex,
    'planPackages',
    packageIndex,
    'productInsuranceList',
    productIndex,
  ]) as PlanProductInfoResponseDTO;
  const envConfig = useSelector<{ envConfig: { env: string } }, { env: string }>(state => state.envConfig);

  const initialUnitType = productValue.unitTypeInPackage as PremiumCalculationMethod;

  const [incrementalId, setIncrementalId] = useState(0);

  const [unitType, setUnitType] = useState(productValue.unitType);
  const unitTypeOptions = useBizDictAsOptions('agreementBasis')?.filter(option => {
    option.value = Number(option.value);
    if (envConfig.env === 'cfg') {
      return true;
    }
    return EffectivePremiumCalculationMethodList.includes(option.value?.toString());
  });

  const getColumns = useMemo(() => {
    const columns: ColumnProps<PlanLiabilityQueryResponseDTO>[] = [
      {
        title: t('dutyText'),
        dataIndex: 'liabilityName',
        width: 240,
      },
      {
        title: () => (
          <span>
            {unitType === PremiumCalculationMethod.CalculatePremium ||
            unitType === PremiumCalculationMethod.SpecifiedSAPremium
              ? t('Liability SA')
              : unitType === PremiumCalculationMethod.CalculateSA
                ? t('liabilityPremium')
                : t('Liability Coverage / Premium')}
          </span>
        ),
        dataIndex: 'liabilityAmount',
        width: 160,
        editable: true,
        render: (text: string, record: PlanLiabilityQueryResponseDTO, liabilityIndex: number) => {
          const liabilityValue = (form.getFieldValue([
            'coveragePlanList',
            planIndex,
            'planPackages',
            packageIndex,
            'productInsuranceList',
            productIndex,
            'planLiabilities',
            liabilityIndex,
          ]) ?? {}) as PlanLiabilityQueryResponseDTO;
          if (!liabilityValue.enableSumInsured && unitType !== PremiumCalculationMethod.CalculateSA) {
            return t('- -');
          }
          const liabilitySA = getLiabilityValues(
            {
              liabilitySchemaCodePreDefineValues:
                liabilityValue.liabilitySchemaCodePreDefineValues as PreDefineValues[],
              isVirtualProduct: product.isVirtual === IsVirtualType.Yes,
              liabilityAmount: liabilityValue.liabilityAmount ?? '',
            },
            unitType === PremiumCalculationMethod.CalculateSA ? 'periodStandardPremium' : 'sumInsured'
          );
          return (
            <div>
              <span className="font-normal text-@label ml-[10px]">{liabilitySA}</span>
              <Icon
                type="edit"
                onClick={() => {
                  if (!disabled) {
                    setProductSASettingInfo({
                      unitType,
                      type: 'liability',
                      selfInsuranceItem: liabilityValue,
                    });
                    setProductSASettingVisible(true);
                  }
                }}
                className={disabled ? 'cursor-not-allowed ml-4' : 'cursor-pointer ml-4'}
              />
              {liabilitySA !== t('Unset') ? (
                <span className="ml-4">
                  <Popconfirm
                    title={t('Are you sure to delete this record?')}
                    disabled={disabled}
                    onConfirm={() => {
                      delete liabilityValue.liabilityAmount;
                      delete liabilityValue.liabilitySchemaCodePreDefineValues;
                      setProductSASettingInfo(undefined);
                      setIncrementalId(incrementalId + 1);
                    }}
                    okText={t('yes')}
                    cancelText={t('no')}
                  >
                    <Icon type="delete" className={disabled ? 'cursor-not-allowed' : 'cursor-pointer'} />
                  </Popconfirm>
                </span>
              ) : null}
            </div>
          );
        },
        onCell: (record: PlanLiabilityQueryResponseDTO, index: number) => ({
          record,
          dataIndex: 'liabilityAmount',
          editing: unitType === PremiumCalculationMethod.SpecifiedSAPremium,
          planIndex,
          productIndex,
          index,
          packageIndex,
        }),
      },
      {
        title: () => (
          <span>
            {[
              PremiumCalculationMethod.CalculatePremium,
              PremiumCalculationMethod.CalculateSA,
              PremiumCalculationMethod.SpecifiedSAPremium,
            ].includes(unitType)
              ? t('Liability Limit')
              : t('Amount Limit')}
          </span>
        ),
        dataIndex: 'liabilityLimitAmount',
        width: 160,
        onCell: (record: PlanLiabilityQueryResponseDTO, index: number) => ({
          record,
          dataIndex: 'liabilityLimitAmount',
          editing: true,
          planIndex,
          productIndex,
          index,
          packageIndex,
        }),
      },
    ];

    if ((product.productObjects || [])?.length > 0) {
      columns.push({
        title: t('Object'),
        width: 344,
        render: (text: string, record: PlanLiabilityQueryResponseDTO, liabilityIndex: number) => (
          <LiabilityObject
            disabled={disabled}
            planLiability={record}
            categories={categories}
            hasAddressCategoryArr={hasAddressCategoryArr}
            namePath={[
              'coveragePlanList',
              planIndex,
              'planPackages',
              packageIndex,
              'productInsuranceList',
              productIndex,
              'planLiabilities',
              liabilityIndex,
            ]}
          />
        ),
      });
    }
    if (unitType === PremiumCalculationMethod.SpecifiedSAPremium) {
      columns.splice(3, 0, {
        title: () => t('liabilityPremium'),
        dataIndex: 'liabilityPremium',
        width: 160,
        onCell: (record: PlanLiabilityQueryResponseDTO, index) => ({
          record,
          dataIndex: 'liabilityPremium',
          editing: true,
          onChange: debounce(() => {
            form.setFieldValue(
              [
                'coveragePlanList',
                planIndex,
                'planPackages',
                packageIndex,
                'productInsuranceList',
                productIndex,
                'sumPremium',
              ],
              product.planLiabilities?.reduce((acc, item) => acc + Number(item.liabilityPremium), 0) || undefined
            );
          }, 300),
          planIndex,
          productIndex,
          index,
          packageIndex,
        }),
      });
    }
    return columns;
  }, [unitType, incrementalId, disabled, categories]);

  const EditableCell = ({
    editing,
    record,
    children,
    dataIndex,
    onChange,
    index,
  }: {
    editing: boolean;
    record: PlanLiabilityQueryResponseDTO;
    children: React.FC;
    dataIndex: keyof PlanLiabilityQueryResponseDTO;
    onChange: () => void;
    index: number;
  }) => (
    <td>
      {editing ? (
        <Form.Item
          noStyle
          preserve
          rules={[
            {
              message: t('Please input'),
            },
            {
              validator: (rule, inputValue, callback) => {
                const regx = /^(0|[1-9][0-9]*)(\.\d+)?$/g;
                if (inputValue && !regx.test(inputValue)) {
                  callback('Please enter numeric value');
                }
                callback();
              },
            },
          ]}
        >
          <InputNumber
            disabled={disabled}
            defaultValue={record?.[dataIndex] as number}
            placeholder={t('Please input')}
            onChange={val => {
              form.setFieldValue(
                [
                  'coveragePlanList',
                  planIndex,
                  'planPackages',
                  packageIndex,
                  'productInsuranceList',
                  productIndex,
                  'planLiabilities',
                  index,
                  dataIndex,
                ],
                val
              );
              set(record, dataIndex, val);
              onChange?.();
            }}
            className="!w-full"
          />
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );

  const onSubmitProductSASettingInfo = (preDefineValues: base_SchemaFactorCodePreDefineValueBase) => {
    if (productSASettingInfo?.type === 'product') {
      (productSASettingInfo.selfInsuranceItem as PlanProductInfoResponseDTO).productSchemaCodePreDefineValues = [
        preDefineValues,
      ];
    } else if (productSASettingInfo?.type === 'liability') {
      (productSASettingInfo.selfInsuranceItem as PlanLiabilityQueryResponseDTO).liabilitySchemaCodePreDefineValues = [
        preDefineValues,
      ];
    }
    setProductSASettingInfo(null);
  };

  const BenefitConfigForm = useMemo(() => {
    const productSA = getProductValues(
      {
        productSchemaCodePreDefineValues: productValue.productSchemaCodePreDefineValues as PreDefineValues[],
        isVirtualProduct,
        elementValue: productValue.securityAmount ?? '',
      },
      unitType === PremiumCalculationMethod.CalculateSA ? 'periodStandardPremium' : 'sumInsured'
    );
    return (
      <div className="benefit-config-form">
        <Form.Item
          className={
            unitType &&
            [PremiumCalculationMethod.CalculatePremium, PremiumCalculationMethod.CalculateSA].includes(initialUnitType)
              ? 'visible'
              : 'hidden'
          }
          name={[productIndex, 'unitType']}
        >
          <Radio.Group
            options={unitTypeOptions
              .filter(option => option.value === initialUnitType)
              .concat({
                label: t('Specified SA & Premium'),
                value: PremiumCalculationMethod.SpecifiedSAPremium,
              })}
            onChange={el => setUnitType(el.target.value)}
          />
        </Form.Item>
        {unitType === PremiumCalculationMethod.SpecifiedSAPremium ? (
          <div className="market-field-wrapper">
            <Form.Item name={[productIndex, 'securityAmount']} label={t('Product SA')}>
              <Input placeholder={t('Please input')} />
            </Form.Item>
            <Form.Item label={t('Product Premium')} name={[productIndex, 'sumPremium']}>
              <Input placeholder={t('Please input')} disabled />
            </Form.Item>
          </div>
        ) : (
          <Form.Item noStyle colon={false} name={[productIndex, 'productSchemaCodePreDefineValues']} preserve>
            <span className="flex mt-6 mb-4">
              <span className="font-bold">
                {unitType === PremiumCalculationMethod.CalculateSA ? t('Product Premium') : t('Product SA')}
              </span>
              <span className="flex items-center">
                <span className="font-normal text-@label ml-[10px]">{productSA}</span>
                <span className="mx-4 font-normal text-@label inline-block w-[1px] h-4 bg-@label" />
                <Icon
                  type="edit"
                  onClick={() => {
                    if (!disabled) {
                      setProductSASettingInfo({
                        unitType,
                        type: 'product',
                        selfInsuranceItem: productValue,
                      });
                      setProductSASettingVisible(true);
                    }
                  }}
                  className={disabled ? 'cursor-not-allowed' : 'cursor-pointer'}
                />
                {productSA !== t('Unset') ? (
                  <span className="ml-4">
                    <Popconfirm
                      title={t('Are you sure to delete this record?')}
                      disabled={disabled}
                      onConfirm={() => {
                        delete productValue.securityAmount;
                        delete productValue.productSchemaCodePreDefineValues;
                        setProductSASettingInfo(undefined);
                        setIncrementalId(incrementalId + 1);
                      }}
                      okText={t('yes')}
                      cancelText={t('no')}
                    >
                      <Icon type="delete" className={disabled ? 'cursor-not-allowed' : 'cursor-pointer'} />
                    </Popconfirm>
                  </span>
                ) : null}
              </span>
            </span>
          </Form.Item>
        )}
      </div>
    );
  }, [unitType, incrementalId, disabled]);

  const isUserInput = initialUnitType === PremiumCalculationMethod.UserInput;

  return (
    <div>
      <Collapse
        key={product.productId}
        visible={isUserInput ? false : visible}
        destroyOnClose
        isVisibleIcon={!isUserInput}
        title={
          <div>
            <span className="mr-4 font-bold text-root">{`${product?.productName ?? ''} - ${product?.planLiabilities?.[0]?.productCode ?? ''}`}</span>
            <ProductTagLabel
              text={product.productTypeCode === ProductTypeEnum.MAIN ? t('Main') : t('Rider')}
              className={cls(
                visible && !isUserInput ? 'bg-white' : 'bg-@label-bg',
                product.productTypeCode === ProductTypeEnum.MAIN
                  ? '!text-@success-color'
                  : '!text-@info-color-text-dark'
              )}
            />
            <ProductTagLabel
              text={unitTypeOptions?.find(option => option.value === unitType)?.label}
              className={cls(
                visible && !isUserInput ? 'bg-white' : 'bg-@label-bg',
                'ml-2',
                product.productTypeCode === ProductTypeEnum.MAIN
                  ? '!text-@success-color'
                  : '!text-@info-color-text-dark'
              )}
            />
          </div>
        }
        setVisible={(open: boolean) => setVisible?.(productIndex, open)}
      >
        {BenefitConfigForm}
        <Table
          dataSource={dataSource}
          pagination={false}
          emptyType="text"
          rowKey="planLiabilityId"
          scroll={{ x: 'max-content' }}
          columns={getColumns as ColumnsType<PlanLiabilityQueryResponseDTO>}
          components={{
            body: { cell: EditableCell },
          }}
        />
      </Collapse>
      {productSASettingVisible ? (
        <ProductSASetting
          currentType={productSASettingInfo?.type ?? 'product'}
          currentCode={
            productSASettingInfo?.unitType === PremiumCalculationMethod.CalculateSA
              ? 'periodStandardPremium'
              : 'sumInsured'
          }
          visible
          onSubmit={(preDefineValues: PreDefineValues) => {
            onSubmitProductSASettingInfo(preDefineValues as base_SchemaFactorCodePreDefineValueBase);
            setIncrementalId(incrementalId + 1);
          }}
          onCancelChange={() => {
            setProductSASettingVisible(false);
          }}
          initialValue={
            ((productSASettingInfo?.selfInsuranceItem as PlanProductInfoResponseDTO)
              .productSchemaCodePreDefineValues?.[0] ??
              (productSASettingInfo?.selfInsuranceItem as PlanLiabilityQueryResponseDTO)
                .liabilitySchemaCodePreDefineValues?.[0]) as PreDefineValues
          }
          type="modal"
        />
      ) : null}
    </div>
  );
};
