import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Location, useLocation, useNavigate } from 'react-router-dom';

import { Button, Form, Layout, Skeleton, message } from 'antd';

import { useRequest } from 'ahooks';
import cls from 'classnames';
import { cloneDeep, isNil, pick, set } from 'lodash-es';

import { Icon, TextEllipsisDetect } from '@zhongan/nagrand-ui';

import { IsOptional, PackageProduct, ProductTypeEnum } from 'genesis-web-service';
import { ProductInfoValue } from 'genesis-web-service/lib/market';
import {
  GoodsInfoResponseDTO,
  GoodsPlanResponseDTO,
  PackageProductInfo,
  PackageProductInfoResponseDTO,
  PackageProductSaveResponseDTO,
  PlanPackageDTO,
  PlanProductInfoResponseDTO,
} from 'genesis-web-service/service-types/market-types/package';

import { FMarketHeader } from '@market/components/F-Market-Header';
import { usePermission } from '@market/hook/permission';
import { IPlanInfo } from '@market/pages/CoveragePlan/PlanDrawer/type';
import { defaultMaxForsale, planStatus } from '@market/pages/CoveragePlan/PlanDrawer/utils/constants';
import { DrawerRefProps, Mode } from '@market/request/interface';
import { NewMarketService } from '@market/services/market/market.service.new';
import { urlQuery } from '@market/util';

import FMarketMenu from '../../components/F-Market-Menu';
import PlanConfigurationDrawer from '../PackageBasicConfig/components/PlanConfigurationDrawer';
import PackageSection from './PackageSection/PackageSection';
import { PlanDrawer } from './PlanDrawer';
import { PlanStatus } from './PlanDrawer/type';

const { Content, Sider } = Layout;

export interface PlanPackages {
  products?: (PackageProductInfo & PackageProductInfoResponseDTO)[];
  packageLiabilityList?: PackageProductSaveResponseDTO[];
  productInsuranceList?: PlanProductInfoResponseDTO[];
}

export type PlanProduct = GoodsPlanResponseDTO & {
  planPackages?: PlanPackages[];
  [planStatus]: PlanStatus;
};

export type EditPackage = PlanPackages & PlanPackageDTO & { planId: number };

export enum PremiumCalculationMethod {
  CalculatePremium = 1,
  CalculateSA = 2,
  Unit = 3,
  BenefitLevel = 4,
  SpecifiedSAPremium = 5,
  UserInput = 6,
}

const CoveragePlan = (): JSX.Element => {
  const navigate = useNavigate();
  const location = useLocation() as unknown as Location<{ mode: string; queryMode: string }>;
  const [form] = Form.useForm();
  const [t] = useTranslation(['market', 'common']);
  const goodsId = urlQuery('goodsId');
  const layoutId = urlQuery('layoutId');
  const [mode, setMode] = useState<Mode>(location.state?.mode as Mode);
  const [disabled, setDisable] = useState<boolean>(mode !== Mode.Edit);
  const [goodsInfo, setGoodsInfo] = useState<GoodsInfoResponseDTO>();
  const [activePlanId, setActivePlanId] = useState<number | undefined>();

  const [planConfigurationDrawerVisible, setPlanConfigurationDrawerVisible] = useState<boolean>(false);

  const [editPackage, setEditPackage] = useState<EditPackage>();
  const hasEditAuth = !!usePermission('market.edit');
  const isSuperUser = !!usePermission('market.edit-all');
  const hasPermission = hasEditAuth || isSuperUser;

  const [firstLoadingFlag, setFirstLoadingFlag] = useState<boolean>(true);
  const [activePlan, setActivePlan] = useState<number>(0);
  const planDrawerRef = useRef<DrawerRefProps>(null);

  const {
    loading: coveragePlanLoading,
    data: coveragePlanList,
    run: queryCoveragePlanList,
  } = useRequest(
    async () => {
      const res = await NewMarketService.GoodsMgmtControllerService.find({
        goodsId: Number(goodsId!),
      });

      const maxSalesCount = res?.value?.salesProp?.maxSalesCount;
      const wholesaleVolumeIsLimited = !isNil(maxSalesCount) && maxSalesCount !== defaultMaxForsale;

      if (res?.value?.plans) {
        await Promise.all(
          res.value.plans.map(async (plan: PlanProduct) => {
            const { suitCrowd, maxForsale, minPrice } = plan;
            let _planStatus = PlanStatus.Done;

            // 新创建的 plan 不会有这三个值, 所以是 Undone 状态, 需要完善信息才能提交
            if (isNil(suitCrowd || maxForsale || minPrice)) {
              _planStatus = PlanStatus.Undone;
            }

            // 如果 sales attributes 页面的 wholesale volume 是 limited, 且 plan.maxForsale 值不存在, 需要将该 plan 状态变更为 Undone
            if (wholesaleVolumeIsLimited && (!maxForsale || maxForsale >= maxSalesCount)) {
              _planStatus = PlanStatus.Undone;
            }
            set(plan, planStatus, _planStatus);

            await Promise.all(
              plan.planPackages?.map(async (packageItem, packageIndex) => {
                const productsRes = await NewMarketService.GoodsPlanMgmtService.queryCoveragePlanProductAndLiability(
                  plan.goodsPlanId!,
                  packageItem.packageId!
                );
                productsRes.packageProductInfoList?.map(product => {
                  set(product, 'productType', product.isMain);
                  set(product, 'packageLiabilityList', product.packageProductLiabilityInfoList);
                  set(product, 'canEdit', product.optionalInPackage === IsOptional.Optional);
                  set(product, 'isSelect', product.isSelected === 1);

                  product.packageProductLiabilityInfoList?.map(liability => {
                    set(liability, 'canEdit', liability.optionalInPackage === IsOptional.Optional);
                    set(liability, 'isSelect', liability.isSelected === 1);
                  });
                });

                set(plan.planPackages![packageIndex]!, 'products', productsRes.packageProductInfoList);

                const productInsuranceList = await NewMarketService.GoodsPlanMgmtService.getProductInsuranceList({
                  planId: plan.goodsPlanId,
                  packageId: packageItem.packageId,
                });

                productInsuranceList.map(product => {
                  set(
                    product,
                    'sumPremium',
                    product.planLiabilities?.reduce((acc, item) => acc + Number(item.liabilityPremium), 0) || undefined
                  );
                });
                set(plan.planPackages![packageIndex]!, 'productInsuranceList', productInsuranceList);

                set(plan.planPackages![packageIndex]!, 'policyScenario', packageItem?.policyScenario);
              }) ?? []
            );
          })
        );
      }

      const result = cloneDeep(res.value?.plans) as PlanProduct[];
      setGoodsInfo({ ...res.value, plans: result });
      form.setFieldsValue({ coveragePlanList: result });
      return result;
    },
    {
      refreshDeps: [goodsId],
    }
  );

  useEffect(() => {
    if (firstLoadingFlag && !coveragePlanLoading) {
      setFirstLoadingFlag(false);
    }
  }, [coveragePlanLoading]);

  /**
   * 验证并更新 goodsPlan
   * @returns 是否验证失败
   */
  const validAndUpdateGoodsPlan = async () => {
    // 目前没有项目使用，先去掉校验
    // A group of plan validations
    // const isInvalidPlans = validatePlans({
    //   t,
    //   plans: goodsInfo?.plans,
    //   salesProp: goodsInfo?.salesProp,
    // });

    // if (isInvalidPlans) return true;

    // 更新 goodsPlan
    await NewMarketService.GoodsPlanMgmtService.save({
      goodsId: Number(goodsId!),
      plans: goodsInfo?.plans?.map((item, index) => ({
        ...item,
        key: undefined,
        id: undefined,
        index: undefined,
        orderNo: index + 1,
      })),
    });

    return false;
  };

  /**
   * Submit
   */
  const onSubmit = async () => {
    await form.validateFields();
    const formValues = form.getFieldsValue() as unknown as {
      coveragePlanList: PlanProduct[];
    };

    const isInvalid = await validAndUpdateGoodsPlan();
    if (isInvalid) return true;

    // 更新 planPackages
    await Promise.all(
      formValues.coveragePlanList.map(async plan => {
        await NewMarketService.GoodsPlanMgmtService.updatePackageProductInsuranceList({
          planId: plan.goodsPlanId,
          planProductsDTO: ([] as PlanProductInfoResponseDTO[]).concat(
            ...(plan?.planPackages?.map(packageObj => (packageObj as PlanPackages)?.productInsuranceList ?? []) ?? [])
          ),
          packageInfos: plan?.planPackages?.map(packageObj => pick(packageObj, ['packageId', 'planPremiumModel'])),
        });
      })
    );

    message.success(t('Save Successfully'));
  };

  const gotoNextPage = async () => {
    if (mode === Mode.Edit) {
      const isInvalid = await onSubmit();
      if (isInvalid) return;
    }

    navigate(`/market/goods/doc-display${location.search}`, {
      state: {
        mode,
        queryModel: location.state.queryMode,
      },
    });
  };

  const onEditPlan = (goodsPlanId?: number) => {
    if (!goodsPlanId) return;
    setActivePlanId(goodsPlanId);
    planDrawerRef.current?.setOpen();
  };

  const calPlanStatus = (plan: PlanProduct) => {
    const targetPlan = goodsInfo?.plans?.find(item => item.goodsPlanId === plan.goodsPlanId) as IPlanInfo;
    if (targetPlan?.[planStatus]) return targetPlan?.[planStatus];
    if (plan[planStatus]) return plan[planStatus];

    return PlanStatus.Undone;
  };

  return (
    <Layout className="goods-basic-config market-layout">
      <FMarketHeader backPath="/market/goods/search" subMenu="Marketing_Goods" />
      <div className="flex" style={{ height: 'calc(100% - 50px)' }}>
        <Sider width={208} className="market-sider">
          <FMarketMenu
            page="GOODS"
            type={mode}
            category={2}
            layoutId={layoutId}
            goodsId={goodsId}
            defaultSelectedKeys={['3']}
            navigate={navigate}
          />
        </Sider>
        <Content className="market-content !bg-inherit !overflow-hidden">
          <Skeleton active loading={firstLoadingFlag}>
            <div className="right-content h-full">
              <Form
                initialValues={{ coveragePlanList }}
                form={form}
                disabled={disabled}
                layout="vertical"
                className="flex flex-col h-full"
              >
                <div className="px-4 py-6 overflow-hidden whitespace-nowrap bg-white overflow-x-auto shadow-lg z-10">
                  {coveragePlanList?.map((plan, index) => (
                    <div
                      className={cls(
                        `inline-flex h-[66px] justify-between mr-4 border-solid px-4 py-3 rounded-lg w-[200px] cursor-pointer border-[1px]`,
                        {
                          'bg-@info-color-bg border-@primary-color': activePlan === index,
                          'border-@border-default bg-@disabled-bg': activePlan !== index,
                        }
                      )}
                      onClick={() => setActivePlan(index)}
                    >
                      <div>
                        <div className="font-bold text-@text-color mb-1" style={{ lineHeight: 'normal' }}>
                          <TextEllipsisDetect text={plan.goodsPlanName} maxWidth={140} />
                        </div>

                        <div className="font-medium text-@text-color-secondary text-xs">
                          <TextEllipsisDetect text={plan.planCode} maxWidth={140} />
                        </div>
                      </div>
                      <Icon
                        type={disabled ? 'view' : 'edit'}
                        // Note: 弹窗内使用的是 goodsInfo 数据, 点击弹窗 save 按钮时,会将弹窗表单更新到 goodsInfo 上, 所以这里的 planStatus 应该总是取 goodsInfo 内的 plans 数组
                        // 目前没有项目使用，先去掉校验
                        // style={{ color: calPlanStatus(plan) === PlanStatus.Undone && 'var(--error-color)' }}
                        onClick={() => onEditPlan(plan.goodsPlanId)}
                      />
                    </div>
                  ))}
                </div>
                <Form.List name="coveragePlanList">
                  {planFields =>
                    planFields.map(planField => {
                      const plan: PlanProduct = coveragePlanList?.[planField.key] ?? {};

                      return (
                        <div
                          className={`${activePlan === planField.key ? 'visible' : 'hidden'} overflow-auto flex-1`}
                          key={planField.key}
                        >
                          <Form.Item>
                            <Form.List name={[planField.name, 'planPackages']}>
                              {packageFields =>
                                packageFields.map(packageField => {
                                  const packageItem = plan?.planPackages?.[packageField.key];
                                  return packageItem ? (
                                    <PackageSection
                                      packageItem={packageItem}
                                      packageField={packageField}
                                      plan={plan}
                                      disabled={disabled}
                                      planField={planField}
                                      setPlanConfigurationDrawerVisible={setPlanConfigurationDrawerVisible}
                                      setEditPackage={setEditPackage}
                                    />
                                  ) : null;
                                })
                              }
                            </Form.List>
                          </Form.Item>
                        </div>
                      );
                    })
                  }
                </Form.List>
                {planConfigurationDrawerVisible ? (
                  <PlanConfigurationDrawer
                    disabled={disabled}
                    isCoveragePlan
                    onChoseMainProduct={() => {}}
                    mainProductList={[]}
                    addedMainProduct={
                      (editPackage?.products?.find(product => product.isMain === ProductTypeEnum.MAIN) ??
                        {}) as ProductInfoValue
                    }
                    visible
                    packageCode={editPackage?.packageCode ?? ''}
                    packageId={editPackage?.packageId ?? 0}
                    planId={editPackage?.planId ?? 0}
                    isEditingLiability
                    editMode="edit"
                    productLiabilityData={editPackage?.products as unknown as PackageProduct[]}
                    originConfiguredProduct={editPackage?.products as unknown as PackageProduct[]}
                    ridersWithoutWaiver={[]}
                    queryConfigProducts={() => {}}
                    onClose={() => {
                      setPlanConfigurationDrawerVisible(false);
                    }}
                    onSubmit={async () => {
                      const isInvalid = await validAndUpdateGoodsPlan();
                      if (isInvalid) return;

                      queryCoveragePlanList();
                      setPlanConfigurationDrawerVisible(false);
                      message.success(t('Save Successfully'));
                    }}
                  />
                ) : null}
              </Form>
            </div>
          </Skeleton>
        </Content>

        <PlanDrawer
          ref={planDrawerRef}
          activePlanId={activePlanId}
          readonly={disabled}
          goodsInfo={cloneDeep(goodsInfo)}
          setGoodsInfo={setGoodsInfo}
        />

        <div className="bottom-action-bar">
          {disabled ? (
            <React.Fragment>
              <Button size="large" onClick={gotoNextPage} type="primary">
                {t('Next')}
              </Button>
              {hasPermission && (
                <Button
                  size="large"
                  onClick={() => {
                    setDisable(false);
                    setMode(Mode.Edit);
                  }}
                >
                  {t('Edit')}
                </Button>
              )}
            </React.Fragment>
          ) : (
            <React.Fragment>
              <Button size="large" onClick={onSubmit}>
                {t('Save')}
              </Button>
              <Button size="large" onClick={gotoNextPage}>
                {t('Next')}
              </Button>
            </React.Fragment>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default CoveragePlan;
