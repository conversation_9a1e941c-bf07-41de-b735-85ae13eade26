import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Cascader, Form } from 'antd';

import { PlanLiabilityQueryResponseDTO } from 'genesis-web-service/service-types/market-types/package';

import {
  ObjectCategoryNode,
  hideAddressAndCollectCategories,
  useObjectCategory,
} from '@market/hook/tenant-config/useObjectCategory';

import { filterCategories, generateObjectsBySelectedCodes, generateSelectedCodesByObjects } from './util';

interface Props {
  disabled: boolean;
  planLiability: PlanLiabilityQueryResponseDTO;
  namePath: (string | number)[];
  categories: ObjectCategoryNode[];
  hasAddressCategoryArr: string[][];
}

export const LiabilityObject = ({
  disabled,
  planLiability,
  namePath,
  categories,
  hasAddressCategoryArr,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const form = Form.useFormInstance();
  // const { categories, hasAddressCategoryArr } = useObjectCategory();

  if (planLiability.selectedObjectCodes === undefined) {
    planLiability.selectedObjectCodes = generateSelectedCodesByObjects(
      hideAddressAndCollectCategories(planLiability?.objects || [], () => {})
    );
  }

  // 根据product上面的配置，收敛租户的配置，作为liability的可选项
  const baseProductObjectCategoryOptions = useMemo(() => {
    const codes = generateSelectedCodesByObjects(
      hideAddressAndCollectCategories(planLiability?.baseProductLiabilityObjects || [], () => {})
    );

    return filterCategories(categories, codes);
  }, [planLiability?.baseProductLiabilityObjects, categories]);

  if (baseProductObjectCategoryOptions.length === 0) {
    return t('--');
  }

  return (
    <Cascader
      style={{ width: 312 }}
      options={baseProductObjectCategoryOptions}
      fieldNames={{ label: 'name', value: 'code' }}
      multiple
      placeholder={t('Please select')}
      displayRender={(labels: string[]) => labels.join('|')}
      disabled={disabled}
      defaultValue={planLiability.selectedObjectCodes || []}
      onChange={val => {
        if (!val || val.length === 0) {
          form.setFieldValue([...namePath, 'objects'], []);
          return;
        }
        console.log('hasAddressCategoryArr', val, hasAddressCategoryArr);
        const objects = generateObjectsBySelectedCodes(val as string[][], {
          hasAddressCategoryArr,
        });

        form.setFieldValue([...namePath, 'objects'], objects);
      }}
    />
  );
};

export default LiabilityObject;
