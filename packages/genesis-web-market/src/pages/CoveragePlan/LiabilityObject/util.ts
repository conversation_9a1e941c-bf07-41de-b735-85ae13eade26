import { cloneDeep, size } from 'lodash-es';

import { ObjectCategoryNode } from '@market/hook/tenant-config/useObjectCategory';

const collectCodeAntTypeRecur = (list: ObjectCategoryNode[], parentCodes: string[], result: string[][]) => {
  if (Array.isArray(list)) {
    list.forEach(item => {
      if (item.objectTypes) {
        item.objectTypes.forEach(type => {
          result.push([...parentCodes, item.code, type]);
        });
      }

      collectCodeAntTypeRecur(item.children || [], [...parentCodes, item.code], result);
    });
  }
};

// 根据之前记录的哪些category下面有address，并且有同层级的object被选中，提交的时候带上address
export const fillAddress = (selectedCodes: string[][], hasAddressCategoryArr: string[][]): string[][] => {
  const finalCodes: string[][] = [...selectedCodes];

  selectedCodes.forEach(codeArr => {
    const str = codeArr.slice(0, codeArr.length - 1).join('-');
    const existIndex = hasAddressCategoryArr.findIndex(codeArr2 => codeArr2.join('-') === str);
    if (existIndex > 0) {
      finalCodes.push([...codeArr.slice(0, codeArr.length - 1), 'ADDRESS']);
      // 一个category只需要收集一次，避免重复收集
      hasAddressCategoryArr.splice(existIndex, 1);
    }
  });

  return finalCodes;
};

const collectObjRecur = (parentObj: ObjectCategoryNode, codes: string[], level: number) => {
  if (codes.length === level) {
    return;
  }
  let currentLevelObj = parentObj.children.find(item => item.code === codes[level]);

  if (!currentLevelObj) {
    currentLevelObj = {
      code: codes[level],
      objectTypes: [],
      children: [],
    };
    parentObj.children.push(currentLevelObj);
  }

  if (codes.length === level + 2) {
    currentLevelObj.objectTypes.push(codes[level + 1]);
  } else {
    collectObjRecur(currentLevelObj, codes, level + 1);
  }
};

// 将接口入参转成表单数据
export const generateSelectedCodesByObjects = (objects: ObjectCategoryNode[]) => {
  const result: string[][] = [];

  collectCodeAntTypeRecur(objects, [], result);

  return result;
};

/**
 * 将表单数据转成接口入参
 */
export const generateObjectsBySelectedCodes = (
  selectedCodes: string[][],
  options: {
    hasAddressCategoryArr: string[][];
  }
) => {
  const result: ObjectCategoryNode[] = [];
  const { hasAddressCategoryArr } = cloneDeep(options);
  const finalCodes = fillAddress(selectedCodes, hasAddressCategoryArr);
  // 把层级低的排在前面
  const sortedCodes = finalCodes.sort((a, b) => a.length - b.length);

  sortedCodes.forEach((codes: string[]) => {
    let firstLevelObj = result.find(item => item.code === codes[0]);

    if (!firstLevelObj) {
      firstLevelObj = {
        code: codes[0],
        objectTypes: [],
        children: [],
      };
      result.push(firstLevelObj);
    }
    if (codes.length === 2) {
      firstLevelObj.objectTypes.push(codes[1]);
    } else {
      collectObjRecur(firstLevelObj, codes, 1);
    }
  });

  return result;
};

/**
 * 根据paths过滤出选中的树节点
 */
export const filterCategories = (categories: ObjectCategoryNode[], paths: string[][]) => {
  if (!size(categories)) {
    return [];
  }

  const pathMap = new Map();

  paths.forEach(path => {
    let current = pathMap;
    path.forEach((p, index) => {
      if (!current.has(p)) {
        current.set(p, index === path.length - 1 ? new Set() : new Map());
      }
      current = current.get(p);
    });
  });

  // 递归过滤
  const recursiveFilter = (items: ObjectCategoryNode[], pathLevel: string[]) =>
    items.reduce((acc, item) => {
      const currentPath = pathLevel.concat(item.code);
      let current = pathMap;
      let isValidPath = true;

      // 验证当前路径是否存在于 pathMap
      currentPath.forEach(p => {
        if (current.has(p)) {
          current = current.get(p);
        } else {
          isValidPath = false;
        }
      });

      if (isValidPath) {
        const newItem = { ...item }; // 克隆当前项
        if (newItem.children) {
          newItem.children = recursiveFilter(newItem.children, currentPath);
        }
        // 只添加有子节点的项
        if (newItem.children && newItem.children.length > 0) {
          acc.push(newItem);
        }
        if (!newItem.children) {
          acc.push(newItem);
        }
      }
      return acc;
    }, [] as ObjectCategoryNode[]);

  const result = recursiveFilter(categories, []);

  // 合并相同父级
  const mergedResult: ObjectCategoryNode[] = [];

  result.forEach(item => {
    const existing = mergedResult.find(r => r.code === item.code);
    if (existing) {
      if (item.children) {
        existing.children.push(...item.children);
      }
    } else {
      mergedResult.push(item);
    }
  });

  return mergedResult;
};
