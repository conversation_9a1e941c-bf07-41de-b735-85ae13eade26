import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, FormListFieldData, Radio } from 'antd';

import cls from 'classnames';

import { Icon } from '@zhongan/nagrand-ui';

import { Divider } from 'genesis-web-component/lib/components';
import { IsVirtualType } from 'genesis-web-service/lib/product';
import { PlanPackageDTO, PolicyScenarioEnum } from 'genesis-web-service/service-types/market-types/package';

import Section from '@market/components/Section/section';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { PremiumCalculationMethod } from '@market/pages/CoveragePlan';

import PlanClaimStackSetting from '../PlanClaimStackSetting/PlanClaimStackSetting';
import { ProductCollapse } from '../ProductCollapse/ProductCollapse';
import { EditPackage, PlanPackages, PlanProduct } from '../index';

interface Props {
  packageItem: PlanPackageDTO & PlanPackages;
  packageField: FormListFieldData;
  plan: PlanProduct;
  planField: FormListFieldData;
  setPlanConfigurationDrawerVisible: (value: boolean) => void;
  setEditPackage: (packageData: EditPackage) => void;
  disabled: boolean;
}

export const PackageSection = ({
  plan,
  packageItem,
  packageField,
  planField,
  disabled,
  setPlanConfigurationDrawerVisible,
  setEditPackage,
}: Props): JSX.Element => {
  const form = Form.useFormInstance();
  const [t] = useTranslation(['market', 'common']);
  const planPremiumModelOptions = useBizDictAsOptions('planPremiumModel');
  const [productCollapseVisibleList, setProductCollapseVisibleList] = useState<number[]>();

  const isUserInput = packageItem?.productInsuranceList?.every(
    item => item.unitTypeInPackage === PremiumCalculationMethod.UserInput
  );

  return (
    <div className="m-2">
      <Section
        subTitle={
          <div className="flex gap-4 items-center flex-1">
            <span className="font-medium text-xl text-@text-color]">
              {`${packageItem.packageCode ?? ''} - ${packageItem.packageName ?? ''}`}
            </span>
            <div
              onClick={() => {
                setPlanConfigurationDrawerVisible(true);
                setEditPackage({
                  ...packageItem,
                  planId: plan.goodsPlanId!,
                });
              }}
              className={cls(
                'px-2 rounded py-1 border border-solid border-@primary-color text-@primary-color cursor-pointer'
              )}
            >
              <Icon type="x-man-setting" />
              <span className="ml-[6px] font-medium text-xs">{t('Product & Liability')}</span>
            </div>
          </div>
        }
        className="p-4 rounded-lg"
      >
        {packageItem.policyScenario === PolicyScenarioEnum.GROUP_POLICY ? (
          <React.Fragment>
            <Section thirdLevelTitle={t('Basic Info')}>
              <div className="my-4">
                <Form.Item
                  name={[packageField.key, 'planPremiumModel']}
                  label={t('Plan Premium Model')}
                  initialValue={packageItem.planPremiumModel}
                  rules={[
                    {
                      required: true,
                      message: t('Please select'),
                    },
                  ]}
                >
                  <Radio.Group options={planPremiumModelOptions} />
                </Form.Item>
              </div>
            </Section>
            <Divider className="!my-3" />
          </React.Fragment>
        ) : null}

        {packageItem?.productInsuranceList?.length ? (
          <div>
            <div className="flex justify-between items-center">
              <span className="flex items-center font-bold text-textSecondary">
                <span className={cls('w-1 h-[21px] rounded-xl mr-1 inline-block bg-@border-default')} />
                {t('Premium Agreement')}
              </span>
              {/* user input 不允许展开卡片 */}
              {!isUserInput && (
                <div
                  onClick={() => {
                    productCollapseVisibleList?.length
                      ? setProductCollapseVisibleList(undefined)
                      : setProductCollapseVisibleList(
                          Array(packageItem?.productInsuranceList?.length)
                            .fill(0)
                            .map((_, i) => i)
                        );
                  }}
                  className="cursor-pointer"
                >
                  <span className="text-@primary-color">
                    <span className="mr-2 font-normal">
                      {productCollapseVisibleList?.length ? t('Collapse all') : t('Expand all')}
                    </span>
                    {productCollapseVisibleList?.length ? <Icon type="up" /> : <Icon type="down" />}
                  </span>
                </div>
              )}
            </div>

            <Form.List name={[packageField.name, 'productInsuranceList']}>
              {productFields => (
                <div>
                  {productFields.map(productField => {
                    const product = plan?.planPackages?.[packageField.key]?.productInsuranceList?.[productField.key];
                    return product ? (
                      <div>
                        {product.isVirtual === IsVirtualType.No ? (
                          <ProductCollapse
                            form={form}
                            dataSource={product.planLiabilities!}
                            planIndex={planField.key}
                            packageIndex={packageField.key}
                            productIndex={productField.key}
                            product={product}
                            disabled={disabled}
                            visible={!!productCollapseVisibleList?.includes(productField.key)}
                            setVisible={(index, visible) => {
                              visible
                                ? setProductCollapseVisibleList([...(productCollapseVisibleList ?? []), index])
                                : setProductCollapseVisibleList([
                                    ...(productCollapseVisibleList?.filter(item => item !== index) ?? []),
                                  ]);
                            }}
                          />
                        ) : null}
                      </div>
                    ) : null;
                  })}
                </div>
              )}
            </Form.List>
          </div>
        ) : null}
        <PlanClaimStackSetting plan={plan} packageItem={packageItem} disabled={disabled} />
      </Section>
    </div>
  );
};

export default PackageSection;
