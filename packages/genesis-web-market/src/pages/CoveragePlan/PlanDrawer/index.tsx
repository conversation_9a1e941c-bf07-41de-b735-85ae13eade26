import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { FormLayout } from '@formily/antd-v5';
import { Field, createForm, onFieldReact, onFieldValueChange } from '@formily/core';
import { FormProvider } from '@formily/react';

import { useDeepCompareEffect } from 'ahooks';
import cls from 'classnames';
import { isEmpty } from 'lodash-es';

import { Drawer } from '@zhongan/nagrand-ui';

import { GoodsInfoResponseDTO } from 'genesis-web-service/service-types/market-types/package';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { IPlanInfo, PlanStatus } from '@market/pages/CoveragePlan/PlanDrawer/type';
import { validateDurationDate } from '@market/pages/CoveragePlan/PlanDrawer/utils/validations';
import { DrawerRefProps } from '@market/request/interface';

import { SchemaField } from './SchemaField';
import { planSchema } from './schema';
import { IForm, SalesVolume } from './type';
import {
  defaultMaxForsale,
  durationField,
  limitSalesVolumeField,
  maxForSaleField,
  planStatus,
  recommendPlanField,
} from './utils/constants';
import { isPlanDisplayField } from './utils/constants';
import { handlePlanInitialData } from './utils/handle';

interface IProps {
  activePlanId: number | undefined;
  readonly: boolean;
  goodsInfo: GoodsInfoResponseDTO | undefined;
  setGoodsInfo: (goods: GoodsInfoResponseDTO) => void;
}

export const PlanDrawer = forwardRef<DrawerRefProps, IProps>((props, ref) => {
  const [t] = useTranslation(['market', 'common']);
  const [open, setOpen] = useState(false);
  const { l10n } = useL10n();
  const newPlans = useRef<IPlanInfo[]>([]);
  const cachedRecommendPlan = useRef<number>();
  const { activePlanId, readonly, goodsInfo, setGoodsInfo } = props;

  useEffect(() => {
    if (goodsInfo?.plans) {
      newPlans.current = goodsInfo.plans as IPlanInfo[];
    }
  }, [goodsInfo]);

  useImperativeHandle(ref, () => ({
    setOpen: () => {
      setOpen(true);
    },
  }));

  const form = useMemo(() => {
    return createForm<IForm>({
      validateFirst: true,
      readPretty: readonly,
      effects() {
        // Plan Display 是 false 时, 需要隐藏时间控件
        onFieldValueChange(isPlanDisplayField, field => {
          field.query(durationField).take(target => {
            target.display = field.value ? 'visible' : 'none';
          });
        });
        onFieldReact(durationField, field => {
          const isPlanDisplay = field.query(isPlanDisplayField).get('value');
          field.display = isPlanDisplay ? 'visible' : 'none';
        });

        // Maximum Sales Units 字段默认隐藏, 只有当 limitSalesVolumeField: limited 时才显示
        onFieldReact(limitSalesVolumeField, (field, innerForm) => {
          innerForm.setFieldState(maxForSaleField, state => {
            state.visible = (field as Field).value === SalesVolume.Limited;
          });
        });
      },
    });
    // Note: open 勿删
  }, [readonly, open]);

  /**
   * 切换 Plan Recommend
   */
  const onRecommendPlanChange = async (selectedPlanId: number) => {
    try {
      const formVal = await form.submit<IForm>();
      formVal[planStatus] = PlanStatus.Done;
      cachedRecommendPlan.current = selectedPlanId;

      // 将上一个 plan 的表单数据更新到 newPlans 内
      const _newPlans = newPlans.current?.map(plan => {
        if (plan.goodsPlanId === formVal.goodsPlanId) {
          return formVal;
        }
        return plan;
      });
      newPlans.current = _newPlans as IPlanInfo[];

      // 初始化选中的 plan 的表单数据
      const planData = handlePlanInitialData({
        plans: _newPlans,
        salesProp: goodsInfo?.salesProp,
        activePlanId: selectedPlanId,
        form,
        l10n,
      });
      form.setValues(planData);
    } catch (error) {
      // 需求: 在切换 plan 时会校验上个 form 表单是否合法, 如果不合法, 不能切换到下一个 plan 去
      // 现状: 在 select 组件 onChange 时校验表单, 虽然可以成功校验住, 但是该组件默认行为依然会把下一个 plan 的名字显示出来, 实际上当前表单还是不合法 plan 对应的表单数据,
      //      所以需要一个变量来记录上一个选择的 plan, 然后在 onChange 校验失败时, 将上一个 plan 的名字正确显示
      form.setFieldState(recommendPlanField, field => {
        field.value = cachedRecommendPlan.current;
      });
    }
  };

  /**
   * 初始化回显数据
   */
  useDeepCompareEffect(() => {
    if (!form) return;
    if (!goodsInfo) return;

    cachedRecommendPlan.current = activePlanId;

    const planData = handlePlanInitialData({
      plans: goodsInfo?.plans,
      salesProp: goodsInfo?.salesProp,
      activePlanId: activePlanId!,
      form,
      l10n,
    });
    form.setValues(planData);
  }, [form, open, activePlanId]);

  const onCloseDrawer = () => {
    setOpen(false);
  };

  /**
   * Save button
   */
  const handleSave = async () => {
    const currentPlanVal = await form.submit<IForm>();
    // 更改当前 plan 的 status 为 done
    currentPlanVal[planStatus] = PlanStatus.Done;

    // 校验当前 plan 的 Duration 时间是否在 sales attributes 页面的 Launch Date 范围内
    const isTheDisplayTimeWithinLaunchDate = validateDurationDate({
      plans: [currentPlanVal], // 只校验当前 plan, 在plan configuration页的提交按钮会再次校验全部的plan
      salesProp: goodsInfo?.salesProp,
      t,
    });

    if (isTheDisplayTimeWithinLaunchDate) return;

    const _newPlans = newPlans.current
      .map(plan => {
        // 当切换 recommend plan 时, 会将上一个plan 的表单数据更新到 newPlans 数组中, 但是如果填写完表单直接点击 save 按钮, 当前 plan 的表单数据无法更新到 newPlans 中
        // 所以在点击save 按钮时, 需要将当前 plan 数据更新到 newPlans 中
        if (plan.goodsPlanId === currentPlanVal.goodsPlanId) {
          return currentPlanVal;
        }
        return plan;
      })
      .map(plan => {
        let _maxForSale = plan.maxForsale;
        if (!_maxForSale) {
          _maxForSale = defaultMaxForsale;
        }
        return {
          ...plan,
          [maxForSaleField]: _maxForSale,
        };
      })
      .map(plan => {
        if (isEmpty(plan[durationField])) return plan;

        const [showStartTime, showEndTime] = plan[durationField];
        return {
          ...plan,
          showStartTime: l10n.dateFormat.formatTz(showStartTime, plan.zoneId),
          showEndTime: l10n.dateFormat.formatTz(showEndTime, plan.zoneId),
        };
      });

    setGoodsInfo({ ...goodsInfo, plans: _newPlans });
    onCloseDrawer();
  };

  return (
    <Drawer
      readonly={readonly}
      open={open}
      title={t('Edit Plan')}
      size="small"
      onClose={onCloseDrawer}
      onSubmit={handleSave}
      rootClassName={cls('nagrand-drawer', 'small')}
      destroyOnClose
    >
      <FormProvider form={form}>
        <FormLayout layout="vertical" colon={false}>
          <SchemaField
            schema={planSchema}
            scope={{
              t,
              onRecommendPlanChange,
              recommendPlanOptions: goodsInfo?.plans?.map(item => {
                return {
                  value: item.goodsPlanId,
                  label: `${item.goodsPlanName} - ${item.planCode}`,
                };
              }),
            }}
          />
        </FormLayout>
      </FormProvider>
    </Drawer>
  );
});
