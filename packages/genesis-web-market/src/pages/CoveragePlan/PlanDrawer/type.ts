import { Moment } from 'moment';

import { GoodsPlanResponseDTO } from 'genesis-web-service/service-types/market-types/package';

export enum PlanStatus {
  'Done' = 1,
  'Undone' = 2,
}

export enum SalesVolume {
  'Limited' = 'limited',
  'Unlimited' = 'unlimited',
}

export type IPlanInfo = GoodsPlanResponseDTO & {
  F_isPlanDisplay: boolean;
  F_duration: Moment[];
  F_limitSalesVolume: SalesVolume;
  F_recommendPlan: number;
  F_LaunchDate: string[];
  F_status: PlanStatus;
};

export type IForm = IPlanInfo;
