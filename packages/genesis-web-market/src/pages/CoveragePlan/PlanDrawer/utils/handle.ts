import { Form } from '@formily/core';

import { Moment } from 'moment';

import { GoodSalesPropResponseDTO } from 'genesis-web-service/service-types/market-types/package';
import { L10n } from 'genesis-web-shared/lib/l10n';

import { SalesVolume } from '@market/pages/CoveragePlan/PlanDrawer/type';

import { IPlanInfo } from '../type';
import {
  defaultMaxForsale,
  durationField,
  isPlanDisplayField,
  launchDateField,
  limitSalesVolumeField,
  maxForSaleField,
  minPriceField,
  recommendPlanField,
  suitCrowdField,
} from './constants';

interface IParams {
  plans?: IPlanInfo[];
  salesProp?: GoodSalesPropResponseDTO;
  activePlanId: number;
  form: Form;
  l10n: L10n;
}

export function handlePlanInitialData({ plans, salesProp, activePlanId, form, l10n }: IParams) {
  const targetPlan = plans?.find(item => item.goodsPlanId === activePlanId) ?? ({} as IPlanInfo);
  const zoneId = targetPlan.zoneId ?? salesProp?.zoneId;
  let { showStartTime = '', showEndTime = '', suitCrowd = '', minPrice = '' } = targetPlan;
  let initialDuration = targetPlan[durationField] ?? ([] as Moment[]);
  let initialIsPlanDisplay = targetPlan[isPlanDisplayField] ?? true;
  let _maxForsale = targetPlan.maxForsale;

  // Limit Sales Volume 字段处理
  const initialLimitSalesVolume = () => {
    // 如果 sales attributes 页面的 wholesale volume 字段配置的是 limited 场景, 则所有的 plan 都需要是 limited 且不能修改(禁用Limit Sales Volume 字段)
    const isLimitedCase = salesProp?.maxSalesCount !== defaultMaxForsale;
    if (isLimitedCase) {
      if (_maxForsale === defaultMaxForsale) {
        _maxForsale = undefined;
      }

      form.setFieldState(limitSalesVolumeField, state => {
        state.disabled = true;
      });
      form.setFieldState(maxForSaleField, state => {
        if (state.componentProps) {
          // 最大值需要取 sales attributes 页面配置的值
          state.componentProps.max = salesProp?.maxSalesCount;
        }
      });
      return SalesVolume.Limited;
    }

    if (!targetPlan?.maxForsale) return SalesVolume.Unlimited;
    if (targetPlan?.maxForsale === defaultMaxForsale) return SalesVolume.Unlimited;
    return SalesVolume.Limited;
  };

  // 时间格式处理
  if (showStartTime && showEndTime && initialIsPlanDisplay) {
    initialIsPlanDisplay = true;
    const _showStartTime = l10n.dateFormat.l10nMoment(showStartTime, zoneId);
    const _showEndTime = l10n.dateFormat.l10nMoment(showEndTime, zoneId);
    initialDuration = [_showStartTime, _showEndTime];
  } else {
    initialIsPlanDisplay = false;
  }

  const data = {
    ...targetPlan,
    [isPlanDisplayField]: initialIsPlanDisplay,
    [recommendPlanField]: activePlanId,
    [limitSalesVolumeField]: initialLimitSalesVolume(),
    [durationField]: initialDuration,
    [maxForSaleField]: _maxForsale,
    [suitCrowdField]: suitCrowd,
    [minPriceField]: minPrice,
    [launchDateField]: [salesProp?.beginShelvesDate, salesProp?.endUnderShelvesDate],
  };

  // 处理字段的显示隐藏逻辑
  form?.setFieldState(maxForSaleField, state => {
    state.visible = data[limitSalesVolumeField] === SalesVolume.Limited;
  });
  form?.setFieldState(durationField, state => {
    state.display = data[isPlanDisplayField] ? 'visible' : 'none';
  });

  return data as IPlanInfo;
}
