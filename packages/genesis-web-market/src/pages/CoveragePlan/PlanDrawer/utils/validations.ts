import { message } from 'antd';

import type { TFunction } from 'i18next';

import { GoodSalesPropResponseDTO } from 'genesis-web-service/service-types/market-types/package/index';

import { IPlanInfo, PlanStatus } from '@market/pages/CoveragePlan/PlanDrawer/type';
import { defaultMaxForsale, planStatus } from '@market/pages/CoveragePlan/PlanDrawer/utils/constants';

interface IParams {
  plans: IPlanInfo[];
  maxSalesCount?: number;
  salesProp?: GoodSalesPropResponseDTO;
  t: TFunction;
}

/**
 * 如果有 undone 状态的 plan, 禁止提交
 */
export function validatePlanStatus({ plans, t }: IParams) {
  const isInvalidStatus = plans?.some(plan => plan[planStatus] === PlanStatus.Undone);
  if (isInvalidStatus) {
    message.warning(t('Here has undone plans, please complete first'));
    return true;
  }
}

/**
 * 累加所有 plan 的 maxForsale 字段, 该结果应该小于 sales attributes 页面配置的 Wholesale Volume 的 maxCount 值
 */
export function validateSumMaxForSale({ plans, maxSalesCount, t }: IParams) {
  const sumMaxForSale = plans?.reduce((prev, cur) => {
    return prev + Number(cur?.maxForsale ?? 0);
  }, 0);
  // Wholesale Volume 是 unlimited 时, maxCount的值是 2147483647
  if (maxSalesCount && maxSalesCount !== defaultMaxForsale && sumMaxForSale > maxSalesCount) {
    message.error(t('The sum of Maximum Sales Units of each plan is greater than Goods Wholesale Volume!'));
    return true;
  }
}

/**
 * Duration 时间应该在 sales attributes 页面的 Launch Date 范围内, 否则禁止提交
 */
export function validateDurationDate({ plans, salesProp = {}, t }: IParams) {
  const { beginShelvesDate = '', endUnderShelvesDate = '' } = salesProp;
  const beginShelvesDateNum = Date.parse(beginShelvesDate);
  const endUnderShelvesDateNum = Date.parse(endUnderShelvesDate);

  const isTheDisplayTimeWithinLaunchDate = plans?.some(({ showStartTime, showEndTime }) => {
    const showStartTimeNum = Date.parse(showStartTime ?? '');
    const showEndTimeNum = Date.parse(showEndTime ?? '');

    if (
      showStartTime &&
      showEndTime &&
      (beginShelvesDateNum > showStartTimeNum ||
        showEndTimeNum > endUnderShelvesDateNum ||
        showStartTimeNum > endUnderShelvesDateNum ||
        showEndTimeNum < beginShelvesDateNum)
    ) {
      return true;
    }
  });

  if (isTheDisplayTimeWithinLaunchDate) {
    message.error(t('The Display Time should be within the Launch Date'));
    return true;
  }
}

export function validatePlans({ t, plans, salesProp }: IParams) {
  // 如果有 undone 状态的 plan, 禁止提交
  const isInvalidStatus = validatePlanStatus({ plans, t });
  if (isInvalidStatus) return true;

  // 累加所有 plan 的 maxForsale 字段, 该结果应该小于 sales attributes 页面配置的 Wholesale Volume 的 maxCount 值
  const isInvalidateSumMaxForSale = validateSumMaxForSale({
    plans,
    maxSalesCount: salesProp?.maxSalesCount,
    t,
  });
  if (isInvalidateSumMaxForSale) return true;

  // Duration 时间应该在 sales attributes 页面的 Launch Date 范围内, 否则禁止提交
  const isTheDisplayTimeWithinLaunchDate = validateDurationDate({
    plans,
    salesProp,
    t,
  });

  if (isTheDisplayTimeWithinLaunchDate) return true;
}
