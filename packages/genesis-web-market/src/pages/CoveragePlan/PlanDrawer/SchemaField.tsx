import { FormGrid, FormItem, Grid<PERSON>olumn, NumberPicker, Switch } from '@formily/antd-v5';
import { createSchemaField } from '@formily/react';

import { Input, PreviewText, Select } from '@zhongan/nagrand-ui';

import { Duration } from './Duration';

export const SchemaField = createSchemaField({
  components: {
    FormItem,
    Duration,
    Select,
    Input,
    Switch,
    PreviewText,
    FormGrid,
    NumberPicker,
    GridColumn,
  },
});
