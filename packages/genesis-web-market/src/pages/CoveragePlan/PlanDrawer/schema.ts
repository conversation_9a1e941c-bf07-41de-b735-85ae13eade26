import { ISchema } from '@formily/react';

import { SalesVolume } from './type';
import {
  durationField,
  isPlanDisplayField,
  launchDateField,
  limitSalesVolumeField,
  maxForSaleField,
  minPriceField,
  recommendPlanField,
  suitCrowdField,
  zoneIdField,
} from './utils/constants';

const selectRequiredMsg = [
  {
    required: true,
    message: "{{ t('Please select') }}",
  },
];

const uiHiddenFields = {
  [zoneIdField]: {
    type: 'string',
  },
  [launchDateField]: {
    type: 'array',
  },
};

export const planSchema: ISchema = {
  type: 'object',
  properties: {
    // UI hidden, only collect value
    ...uiHiddenFields,

    PLAN_SELECT: {
      type: 'void',
      'x-component': 'FormGrid',
      'x-component-props': {
        columnGap: 24,
        minColumns: 2,
        maxColumns: 2,
      },
      properties: {
        // Plan Recommend
        [recommendPlanField]: {
          type: 'string',
          title: '{{ t("Plan Recommend") }}',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: `{{ t('Please select') }}`,
            options: '{{ recommendPlanOptions }}',
            onChange: '{{ onRecommendPlanChange }}',
          },
          // 'x-validator': selectRequiredMsg,
        },
      },
    },
    // Plan Display
    [isPlanDisplayField]: {
      type: 'boolean',
      title: '{{ t("Plan Display") }}',
      'x-decorator': 'FormItem',
      'x-decorator-props': {
        layout: 'inline',
      },
      'x-component': 'Switch',
    },
    // Duration to Display
    [durationField]: {
      type: 'array',
      'x-decorator': 'FormItem',
      'x-component': 'Duration',
      // 'x-validator': selectRequiredMsg,
    },
    LINE_4: {
      type: 'void',
      'x-component': 'FormGrid',
      'x-component-props': {
        columnGap: 24,
      },
      properties: {
        // Targeted Customer
        [suitCrowdField]: {
          type: 'string',
          title: '{{ t("Targeted Customer") }}',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: `{{ t('Please input') }}`,
          },
        },
        // Limit Sales Volume
        [limitSalesVolumeField]: {
          type: 'string',
          title: '{{ t("Limit Sales Volume") }}',
          default: SalesVolume.Unlimited,
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: `{{ t('Please select') }}`,
            options: [
              {
                value: SalesVolume.Unlimited,
                label: "{{ t('Unlimited') }}",
              },
              {
                value: SalesVolume.Limited,
                label: "{{ t('Limited') }}",
              },
            ],
          },
          // 'x-validator': selectRequiredMsg,
        },
      },
    },
    END_LINE: {
      type: 'void',
      'x-component': 'FormGrid',
      'x-component-props': {
        columnGap: 24,
        minColumns: 2,
        maxColumns: 2,
      },
      properties: {
        // Maximum Sales Units
        // 默认隐藏, 只有当 limitSalesVolumeField 值是 limited 时才显示
        [maxForSaleField]: {
          type: 'number',
          title: '{{ t("Maximum Sales Units") }}',
          'x-decorator': 'FormItem',
          'x-component': 'NumberPicker',
          'x-component-props': {
            placeholder: `{{ t('Please input') }}`,
            min: 1,
            max: 2147483646,
            maxLength: 10,
            precision: 0,
          },
          // 'x-validator': [
          //   {
          //     required: true,
          //     message: "{{ t('Please input') }}",
          //   },
          // ],
        },
        column: {
          type: 'void',
          'x-component': 'GridColumn',
          'x-component-props': {
            gridSpan: 1,
          },
          properties: {
            // Minimum Price to Show
            [minPriceField]: {
              type: 'number',
              title: '{{ t("Minimum Price to Show") }}',
              'x-decorator': 'FormItem',
              'x-component': 'NumberPicker',
              'x-component-props': {
                placeholder: `{{ t('Please input') }}`,
                min: 0.0001,
                max: 9999999999.9999,
                step: 0.0001,
                precision: 4,
              },
            },
          },
        },
      },
    },
  },
};
