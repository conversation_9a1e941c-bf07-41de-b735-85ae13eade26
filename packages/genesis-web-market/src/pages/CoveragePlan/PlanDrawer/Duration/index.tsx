import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Checkbox, CheckboxProps } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker/index.js';

import { Field } from '@formily/core';
import { useField } from '@formily/react';

import { Moment } from 'moment';

import { DatePicker, Select } from '@zhongan/nagrand-ui';

import { TimeFormatEnum } from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { recommendPlanField } from '@market/pages/CoveragePlan/PlanDrawer/utils/constants';

import { launchDateField, zoneIdField } from '../utils/constants';
import styles from './style.module.scss';

const { RangePicker } = DatePicker;

interface IProps {
  onChange: (values: Moment[]) => void;
  value: Moment[];
  disabled: boolean;
}

type PlanGoodsId = number;

export const Duration = ({ onChange, disabled, value }: IProps) => {
  const [disableCheckbox, setDisableCheckbox] = useState(disabled);
  const { l10n } = useL10n();
  const [t] = useTranslation(['market', 'common']);
  const field = useField<Field>();
  const zoneId = field.query(zoneIdField).value();
  const launchDate = field.query(launchDateField).value();
  const launchBeginDate = l10n.dateFormat.l10nMoment(launchDate[0], zoneId);
  const launchEndDate = l10n.dateFormat.l10nMoment(launchDate[1], zoneId);
  const cachedRecommendPlan = useRef<Record<PlanGoodsId, boolean>>({});

  // zone options
  const zoneInfoList = l10n?.dateFormat?.zoneInfoList as unknown as Record<string, string>[];
  const options = zoneInfoList?.map?.((item, index) => ({
    key: index,
    value: item.value,
    label: item.name,
  }));

  /**
   * 使用 Launch period 的值
   */
  const onCheckboxChange: CheckboxProps['onChange'] = e => {
    const selectedRecommendPlan = field.query(recommendPlanField).value();
    onChange([launchBeginDate, launchEndDate]);
    if (!cachedRecommendPlan.current[selectedRecommendPlan]) {
      cachedRecommendPlan.current[selectedRecommendPlan] = true;
      setDisableCheckbox(true);
    }
  };

  const onDateChange: RangePickerProps['onChange'] = dates => {
    onChange(dates);
  };

  return (
    <div className={styles.duration}>
      <div className="mb-2 flex justify-between">
        <div className="leading-[22px]">{t('Duration to Display')}</div>
        {!disabled && (
          <Checkbox disabled={disableCheckbox} onChange={onCheckboxChange}>
            {t('Same as Launch Period')}
          </Checkbox>
        )}
      </div>

      <div className="flex">
        <Select disabled style={{ width: 230 }} value={zoneId} options={options} />
        <RangePicker
          showTime
          allowClear
          disabled={disabled}
          value={value}
          onChange={onDateChange}
          placeholder={[t('Start Date'), t('End Date')]}
          maxDate={launchEndDate}
          minDate={launchBeginDate}
          format={
            l10n?.dateFormat?.timeFormatByZeus === TimeFormatEnum.NoSecond
              ? l10n?.dateFormat?.dateTimeFormatNoSecond
              : l10n?.dateFormat?.dateTimeFormat
          }
        />
      </div>
    </div>
  );
};
