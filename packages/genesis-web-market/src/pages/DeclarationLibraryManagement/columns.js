import { t } from '@market/i18n';

const columns = (enums = {}) => {
  return [
    {
      title: t('number'),
      dataIndex: 'informQuestionId',
      width: 100,
      render: (text, record) => {
        return Number(text) + 1;
      },
    },
    {
      title: t('Question Code'),
      dataIndex: 'questionCode',
      // width: 100,
    },
    {
      title: t('Declaration Category'),
      dataIndex: 'informCategoryId',
      renderKey: 'informCategoryId',
      // width: 140,
    },
    {
      title: t('Content Category'),
      dataIndex: 'contentCategoryId',
      renderKey: 'contentCategoryId',
      // width: 120,
    },
    {
      title: t('Declaration Type'),
      dataIndex: 'answerType',
      // width: 120,
      render: text => {
        const final = (enums.answerType || []).find(item => {
          if (item.dictValue === `${text}`) {
            return item;
          }
        });
        return final ? final.dictValueName : '';
      },
    },
    {
      title: t('Question Description'),
      dataIndex: 'question',
      ellipsis: true,
      width: 180,
    },
    {
      title: t('status'),
      dataIndex: 'status',
      width: 140,
      renderKey: 'status',
    },
    {
      title: t('Actions'),
      width: 120,
      renderKey: 'action',
      fixed: 'right',
      align: 'right',
    },
  ];
};

export default columns;
