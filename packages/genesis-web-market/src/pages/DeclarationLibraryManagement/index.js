import { Component } from 'react';
import { connect } from 'react-redux';

import { Skeleton } from 'antd';

import {
  AddNewButton,
  DeleteAction,
  EditAction,
  OperationContainer,
  QueryResultContainer,
  StatusTag,
  Table,
  TableActionsContainer,
  ViewAction,
} from '@zhongan/nagrand-ui';

import { saveEnum } from '@market/redux/action';
import { selectPermissionCheckMap } from '@market/redux/selector';
import { NewMarketService } from '@market/services/market/market.service.new';
import { combineColumns } from '@market/util';

import { t } from '../../i18n';
import QueryForm from './QueryForm';
import columnsGenerator from './columns';
import DetailDrawer from './detailDrawer';
import './index.scss';

const xss = require('xss');

const categoryMap = {};
const contentCategoryMap = {};

class DeclarationLibraryManagement extends Component {
  state = {
    questionList: [],
    total: 0,
    current: 1,
    pageSize: 10,
    categoryList: [],
    condition: {},
    drawerVisible: false,
    status: null,
    selectedRecord: null,
    answerList: [],
    queryForm: null,
    tableLoading: false,
    skeletonVisible: true,
  };

  componentDidMount() {
    this.query();
    this.queryCategories();
  }

  async queryCategories() {
    /* 处理健康告知和财务告知的内容list */
    const { enums } = this.props;
    let categoryList = enums.declarationCategory || [];
    if (categoryList && Array.isArray(categoryList)) {
      const contentCategory = {};
      categoryList = categoryList.map(category => {
        category.itemExtend1 = category.dictValue;
        category.itemName = category.dictValueName;
        contentCategory[category.dictValue] = category.childList.map(content => {
          content.itemExtend1 = content.dictValue;
          content.itemName = content.dictValueName;
          categoryMap[content.dictValue] = category.dictValueName;
          contentCategoryMap[content.dictValue] = content.dictValueName;
          return content;
        });
        return category;
      });
      this.setState({ categoryList, contentCategory });
    }
  }

  query = (condition = {}, current = 1, pageSize = 10) => {
    this.setState({ tableLoading: true });
    const data = {
      page: {
        pageIndex: current,
        limit: pageSize,
        condition,
      },
    };
    NewMarketService.InformQuestionMgmtService.query(data)
      .then(res => {
        if (res.success && res.value && res.value.questionList) {
          this.setState({
            questionList: res.value.questionList.results || [],
            total: res.value.questionList.total,
          });
        }
      })
      .finally(() => {
        this.setState({ tableLoading: false, skeletonVisible: false });
      });
  };

  onSubmit = values => {
    this.setState({
      condition: values,
      current: 1,
    });
    this.query(values);
  };

  enhanceColumn = () => {
    const { editableAuth, isSuperUser, userInfo } = this.props;
    return {
      status: (_text, record) => {
        const effective = record.status === 1;
        const statusI18n = effective ? t('PolicyStatusEnum.POLICY_EFFECT') : t('have_disabled');
        const type = effective ? 'success' : 'no-status';
        return <StatusTag statusI18n={statusI18n} type={type} needDot />;
      },
      informCategoryId: (_text, record) => {
        return categoryMap[record.contentCategoryId] || '--';
      },
      contentCategoryId: (_text, record) => {
        return contentCategoryMap[record.contentCategoryId] || '--';
      },
      action: (_text, record) => {
        const currentUserIsCreator = `${userInfo.userId}` === record.creator;
        const canEdit = (currentUserIsCreator && editableAuth) || isSuperUser;
        return (
          <TableActionsContainer>
            <ViewAction onClick={() => this.handleEdit(record, 'view')} />
            <EditAction onClick={() => this.handleEdit(record, 'edit')} disabled={!canEdit} />
            <DeleteAction
              doubleConfirmType="popconfirm"
              deleteConfirmContent={t('Are you sure to delete this record?')}
              onClick={() => this.deleteConfirm(record)}
              disabled={!canEdit}
            />
          </TableActionsContainer>
        );
      },
    };
  };

  onTableChange = pagination => {
    const { current, pageSize } = pagination;
    const { condition } = this.state;
    this.setState({
      current,
      pageSize,
    });
    this.query(condition, current, pageSize);
  };

  addNew = () => {
    this.setState({
      status: 1,
      drawerVisible: true,
      selectedRecord: null,
      answerList: [],
    });
  };

  handleEdit = (record, action) => {
    NewMarketService.InformQuestionMgmtService.queryAnswers({
      questionId: record.informQuestionId,
    }).then(res => {
      if (res.success) {
        let { answerList, question } = res.value;
        if (record && record.answerType === 1) {
          answerList = (answerList || []).map(item => {
            const newItem = { ...item };
            newItem.disabled = true;
            if (newItem.isDefault === 1) {
              newItem.disabled = false;
            }
            return newItem;
          });
        }
        if (question.questionDetail) {
          question.questionDetail = xss(question.questionDetail);
        }
        this.setState({
          selectedRecord: { ...record, ...question },
          status: action === 'edit' ? 2 : 3,
          drawerVisible: true,
          answerList,
        });
      }
    });
  };

  deleteConfirm = record => {
    const { condition, current } = this.state;
    NewMarketService.InformQuestionMgmtService.delete({
      informQuestionId: record.informQuestionId,
    }).then(res => {
      if (res.success) {
        // message.success('Delete success');
        this.query(condition, current);
      } else {
        // message.error(msg || 'Delete Fail');
      }
    });
  };

  closeDetailDrawer = drawer => {
    this.setState(
      {
        drawerVisible: false,
      },
      () => {
        drawer?.resetFields?.();
      }
    );
  };

  render() {
    const { columns, enums } = this.props;
    const {
      condition,
      status,
      questionList,
      current,
      categoryList,
      contentCategory,
      drawerVisible,
      selectedRecord,
      answerList,
    } = this.state;

    combineColumns(columns, this.enhanceColumn());
    return (
      <Skeleton active loading={this.state.skeletonVisible}>
        <div>
          <QueryForm
            declarationCategoryEnums={categoryList}
            declarationTypeEnums={enums.answerType}
            search={this.onSubmit}
            loading={this.state.tableLoading}
          />

          <QueryResultContainer>
            <OperationContainer>
              <OperationContainer.Left>
                <AddNewButton
                  type="primary"
                  ghost
                  onClick={this.addNew}
                  disabled={!this.props.hasEditAuth && !this.props.isSuperUser}
                >
                  {t('Add New')}
                </AddNewButton>
              </OperationContainer.Left>
            </OperationContainer>

            <Table
              loading={this.state.tableLoading}
              columns={columns}
              dataSource={questionList}
              rowKey="informQuestionId"
              onChange={this.onTableChange}
              scroll={{ x: 'max-content' }}
              pagination={{
                total: this.state.total,
              }}
              emptyType="icon"
            />
          </QueryResultContainer>
        </div>

        {drawerVisible && (
          <DetailDrawer
            status={status}
            record={selectedRecord}
            callback={() => this.query(condition, current)}
            categoryList={categoryList}
            contentCategory={contentCategory}
            onClose={drawer => {
              this.closeDetailDrawer(drawer);
            }}
            answerList={answerList}
            {...this.props}
          />
        )}
      </Skeleton>
    );
  }
}

export default connect(
  state => {
    const enums = state.enums;
    const userInfo = state.userInfo;
    const permissionMap = selectPermissionCheckMap(state);

    return {
      enums,
      userInfo,
      editableAuth: !!permissionMap['market.edit'],
      isSuperUser: !!permissionMap['market.edit-all'],
      columns: columnsGenerator(enums),
    };
  },
  { saveEnum }
)(DeclarationLibraryManagement);
