import { FC, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';

import { FieldType, QueryForm as NagrandQueryForm } from '@zhongan/nagrand-ui';

import { BizDict } from '@market/common/interface';

interface Option {
  value: string;
  label: string;
  data: any;
}

interface Props {
  declarationTypeEnums: BizDict[];
  declarationCategoryEnums: BizDict[];
  loading: boolean;
  search: (arg: Record<string, unknown>) => void;
}

const QueryForm: FC<Props> = ({ declarationTypeEnums, declarationCategoryEnums, loading, search }) => {
  const [form] = Form.useForm();
  const [t] = useTranslation(['market', 'common']);
  const [contentCategoryEnums, setContentCategoryEnums] = useState<BizDict[]>([]);

  return (
    <NagrandQueryForm
      needSearchAfterClear
      title={t('Declaration Library')}
      formProps={{ form }}
      loading={loading}
      queryFields={[
        {
          col: 8,
          key: 'informCategoryId',
          label: t('Declaration Category'),
          type: FieldType.Select,
          extraProps: {
            onClear: () => {
              setContentCategoryEnums([]);
              form.resetFields(['contentCategoryId']);
            },
            onSelect: (_: string, option: Option) => {
              form.resetFields(['contentCategoryId']);
              setContentCategoryEnums(option.data);
            },
            options:
              declarationCategoryEnums?.map(item => ({
                value: item.itemExtend1,
                label: item.itemName,
                data: item.childList,
              })) ?? [],
          },
        },
        {
          col: 8,
          key: 'contentCategoryId',
          label: t('Declaration Content Category'),
          type: FieldType.Select,
          extraProps: {
            options:
              contentCategoryEnums?.map(item => ({
                value: item.itemExtend1,
                label: item.itemName,
              })) ?? [],
          },
        },
        {
          col: 8,
          key: 'answerType',
          label: t('Declaration Type'),
          type: FieldType.Select,
          extraProps: {
            options:
              declarationTypeEnums?.map(item => ({
                value: item.itemExtend1,
                label: item.itemName,
              })) ?? [],
          },
        },
        {
          col: 8,
          key: 'questionCode',
          label: t('Declaration Question Code'),
          type: FieldType.Input,
        },
        {
          col: 8,
          key: 'question',
          label: t('Question Description'),
          type: FieldType.Input,
        },
      ]}
      onSearch={values => {
        search(values);
      }}
    />
  );
};

export default QueryForm;
