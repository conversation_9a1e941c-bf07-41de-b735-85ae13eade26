import { Component } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { connect } from 'react-redux';

import { Button, Checkbox, Col, Form, Input, Radio, Row, Tooltip, message } from 'antd';

import { cloneDeep, isNil } from 'lodash-es';

import { Drawer, Icon, Table } from '@zhongan/nagrand-ui';

import GeneralSelect from '@market/components/GeneralSelect';
import { saveEnum } from '@market/redux/action';
import { NewMarketService } from '@market/services/market/market.service.new';

import { t } from '../../i18n';

const xss = require('xss');

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const CheckboxGroup = Checkbox.Group;
const { TextArea } = Input;
class DeclarationDrawer extends Component {
  formRef = React.createRef();

  constructor(props) {
    super(props);
    this.state = {
      answerType: '1',
      answerList: props.answerList,
    };
    this.OtherSettingEnum = [
      {
        value: 'effectPremium',
        label: t('Premium Related'),
      },
      {
        value: 'effectProcess',
        label: t('Process Related'),
      },
      {
        value: 'forUnderwriting',
        label: t('Underwriting Related'),
      },
    ];
  }

  onSubmit = recentRecord => {
    const { status, callback } = this.props;
    const { answerList = [] } = this.state;

    this.formRef.current?.validateFields().then(values => {
      const {
        informCategoryId,
        answerType,
        questionCode,
        contentCategoryId,
        otherSetting,
        question,
        answer,
        questionDetail,
      } = values;
      const settings = {
        effectPremium: 2,
        effectProcess: 2,
        forUnderwriting: 2,
      };
      let list = [];
      // Q&A
      if (answerType === '3') {
        list = [
          {
            answer,
            isDefault: 1,
          },
        ];
      } else {
        list = answerList.map((item, index) => {
          return {
            answer: this.formRef.current?.getFieldValue?.(`answer-${index}`),
            isDefault: this.formRef.current?.getFieldValue?.(`isDefault-${index}`),
          };
        });
      }
      otherSetting.forEach(item => {
        settings[item] = 1;
      });
      const param = {
        informCategoryId,
        contentCategoryId,
        answerType,
        questionCode,
        question,
        questionDetail,
        answerList: list,
        ...settings,
      };
      param.questionDetail = xss(param.questionDetail);

      // Multiple Choice validate
      if (answerType === '2') {
        // at least one default answer 'Yes'
        let defaultAnswerNum = 0;
        list.forEach(item => {
          if (item.isDefault === '1') {
            defaultAnswerNum += 1;
          }
        });
        if (defaultAnswerNum < 2) {
          message.error(t('Please select at least two default answer.'));
          return;
        }
      }
      param.auditInfo = {
        menu: 'Declaration Library Management',
        page: 'Declaration Library Management',
        button: 'Drawer Submit',
      };
      (status === 1
        ? NewMarketService.InformQuestionMgmtService.save(param)
        : NewMarketService.InformQuestionMgmtService.update({
            ...param,
            questionId: recentRecord.informQuestionId,
          })
      ).then(res => {
        if (res.success) {
          message.success(t('Save successfully'));
          callback(res);
          this.handleClose();
        }
      });
    });
  };

  getTitle = () => {
    const { status } = this.props;
    switch (status) {
      case 1:
        return t('Add Declaration Library');
      case 2:
        return t('Edit Declaration Library');
      case 3:
        return t('View');
      default:
        return t('Add Declaration Library');
    }
  };

  renderOption = (enums = [], type) => {
    // type:1/2(option/radio)
    return (
      enums &&
      enums.map((item, index) => {
        if (type === 1) {
          return {
            value: item.dictValue,
            key: item.dictValueName + index,
            label: item.dictValueName,
          };
        }
        if (type === 2) {
          return (
            <Radio value={item.dictValue} key={item.dictValueName + index}>
              {item.dictValueName}
            </Radio>
          );
        }
      })
    );
  };

  genID(length) {
    return Number(Math.random().toString().substr(3, length) + Date.now()).toString(36);
  }

  addAnswer = () => {
    let { answerList = [] } = this.state;
    const answerType = this.formRef.current?.getFieldValue?.('answerType');
    let hasDefaultAnswer = false;
    if (answerList.length > 0) {
      answerList.forEach((item, idx) => {
        const getVal = this.formRef.current?.getFieldValue?.(`isDefault-${idx}`);
        if (`${getVal}` === '1') {
          hasDefaultAnswer = true;
        }
      });
    }
    answerList = [
      ...answerList,
      {
        answerId: this.genID(5),
        isDefault: '0',
        disabled: answerType === '1' && hasDefaultAnswer,
      },
    ];
    this.setState({ answerList });
  };

  removeAnswer = index => {
    const { answerList = [] } = this.state;
    const answerType = this.formRef.current?.getFieldValue?.('answerType');
    if (answerType === '1') {
      const isDefaultVal = this.formRef.current?.getFieldValue?.(`isDefault-${index}`);
      if (isDefaultVal === '1') {
        answerList.forEach((item, idx) => {
          if (idx !== index) {
            item.disabled = false;
          }
        });
      }
    }
    answerList.splice(index, 1);
    this.setState({ answerList });
  };

  handleDefaultAnswerChange(answerType, val, index) {
    const { answerList = [] } = this.state;
    let cloneList = cloneDeep(answerList);
    if (answerType === '1') {
      if (val === '1') {
        cloneList = answerList.map((item, idx) => {
          if (idx !== index) {
            item.isDefault = '0';
            item.disabled = true;
          } else {
            item.isDefault = val;
          }
          return item;
        });
      } else {
        cloneList = answerList.map((item, idx) => {
          item.isDefault = '0';
          item.disabled = false;
          return item;
        });
      }
    } else {
      cloneList = answerList.map((item, idx) => {
        item.disabled = false;
        if (idx === index) {
          item.isDefault = val;
        }
        return item;
      });
    }

    this.setState({ answerList: cloneList });
  }

  getCheckboxInitialVal(recentRecord) {
    const checkedArr = [];
    this.OtherSettingEnum.forEach(item => {
      if (recentRecord[item.value] === 1) {
        checkedArr.push(item.value);
      }
    });
    return checkedArr;
  }

  handleClose = () => {
    const { onClose } = this.props;
    if (onClose) onClose(this.formRef.current);
  };

  onRadioGroupChange = () => {
    const { answerList = [] } = this.state;
    answerList.forEach((item, index) => {
      item.disabled = false;
      this.formRef.current.setFieldsValue({ [`isDefault-${index}`]: '0' });
    });
    this.setState({ answerList });
  };

  renderAnswerList = (answerType, recentRecord) => {
    const { status } = this.props;
    const { answerList = [] } = this.state;
    const disabled = status === 3;
    const yesNo = [
      {
        dictValue: '1',
        dictValueName: t('yes'),
      },
      {
        dictValue: '0',
        dictValueName: t('no'),
      },
    ];
    const columns = [
      {
        title: t('number'),
        width: 150,
        render: (text, record, index) => {
          return index + 1;
        },
      },
      {
        title: (
          <div>
            <span style={{ color: 'red' }}>* </span>
            {t('Answer')}
          </div>
        ),
        dataIndex: 'answer',
        render: (text, record, index) => {
          return (
            <FormItem
              noStyle
              name={`answer-${index}`}
              initialValue={record.answer}
              rules={[{ required: true, message: ' ' }]}
            >
              <Input
                disabled={disabled}
                placeholder={t('Please input')}
                onChange={event => {
                  const newList = cloneDeep(answerList).map(item => {
                    if (item.answerId === record.answerId) {
                      item.answer = event.target.value;
                    }
                    return item;
                  });
                  this.setState({ answerList: newList });
                }}
              />
            </FormItem>
          );
        },
      },
      {
        title: (
          <div>
            <span style={{ color: 'red' }}>* </span>
            {t('Default Answer')}
          </div>
        ),
        key: 'isDefault',
        render: (text, record, index) => {
          return (
            <Form.Item
              name={`isDefault-${index}`}
              noStyle
              initialValue={(record.isDefault && `${record.isDefault}`) || '0'}
              rules={[{ required: true }]}
              getValueProps={value => {
                return { value: !isNil(value) && `${value}` };
              }}
              normalize={value => {
                return !isNil(value) && `${value}`;
              }}
            >
              <GeneralSelect
                allowClear={false}
                disabled={disabled || (this.formRef.current?.getFieldValue?.('answerType') === '1' && record.disabled)}
                onChange={val => this.handleDefaultAnswerChange(answerType, val, index)}
                option={this.renderOption(yesNo, 1)}
              />
            </Form.Item>
          );
        },
      },
      {
        title: t('Actions'),
        width: 92,
        align: 'right',
        renderKey: 'action',
        render: (text, record, index) => {
          return (
            (!disabled && (
              <Tooltip title={t('Delete')}>
                <Icon type="delete" style={{ cursor: 'pointer' }} onClick={() => this.removeAnswer(index)} />
              </Tooltip>
            )) ||
            null
          );
        },
      },
    ];
    switch (answerType) {
      // Single Choice
      case '1':
      // Multiple Choice
      case '2':
        return (
          <FormItem noStyle>
            {!disabled && (
              <Button className="mb-2" type="dashed" block icon={<Icon type="add" />} onClick={this.addAnswer}>
                {t('Add')}
              </Button>
            )}
            <Table rowKey="answerId" columns={columns} dataSource={answerList} pagination={false} />
          </FormItem>
        );
      // Q&A
      case '3':
        return (
          <FormItem
            name="answer"
            label={t('Answer')}
            initialValue={answerList?.[0]?.answer}
            rules={[{ required: true, message: t('Please input') }]}
          >
            <TextArea
              disabled={disabled}
              autoSize={false}
              style={{ width: 560, height: 88 }}
              placeholder={t('Please input')}
            />
          </FormItem>
        );
    }
  };

  getInformCategoryIdValue = () => {
    const { categoryList, record } = this.props;
    let informCategoryIdValue;
    if (record) {
      categoryList.forEach(categoryItem => {
        const currentCategoryItem = categoryItem.childList.find(
          childItem => record.contentCategoryId === +childItem.dictValue
        );
        if (currentCategoryItem) {
          informCategoryIdValue = currentCategoryItem.parentValue;
        }
      });
    }
    return informCategoryIdValue;
  };

  render() {
    const { status, enums, record, categoryList, contentCategory } = this.props;
    const { answerList = [] } = this.state;
    const disabled = status === 3;
    const editDisabled = status === 2;
    const buttonShown = status === 3;
    const recentRecord = status === 1 ? {} : { ...record };
    const informCategoryIdValue =
      this.getInformCategoryIdValue() || this.formRef.current?.getFieldValue?.('informCategoryId');
    const modules = {
      toolbar: [
        [{ size: ['small', false, 'large', 'huge'] }],
        [{ indent: '+1' }, { indent: '-1' }, { align: [] }, 'bold', 'italic', 'underline', 'strike', { color: [] }],
        [{ list: 'ordered' }, { list: 'bullet' }],
        ['link', 'clean'],
      ],
    };

    // 设置 table 表格内的初始值
    answerList.forEach((item, idx) => {
      this.formRef.current?.setFieldsValue?.({
        [`answer-${idx}`]: item.answer,
        [`isDefault-${idx}`]: item.isDefault,
      });
    });

    return (
      <Drawer
        open={true}
        title={this.getTitle()}
        onClose={this.handleClose}
        submitBtnShow={!buttonShown}
        onSubmit={() => this.onSubmit(recentRecord)}
      >
        <Form
          layout="vertical"
          colon={false}
          ref={this.formRef}
          className="declaration-form"
          initialValues={{
            questionCode: recentRecord.questionCode,
            informCategoryId: informCategoryIdValue,
            contentCategoryId: recentRecord?.contentCategoryId ? `${recentRecord?.contentCategoryId}` : undefined,
            otherSetting: this.getCheckboxInitialVal(recentRecord),
            question: recentRecord.question || undefined,
            answerType: (recentRecord.answerType && `${recentRecord.answerType}`) || '1',
            questionDetail: recentRecord.questionDetail?.replace(/<\/p><p>(?!<br>)(?<!<br><p>)/g, '<p><br>') ?? '',
          }}
        >
          <Row className="multi-col">
            <Col>
              <FormItem
                name="questionCode"
                label={t('Declaration Question Code')}
                rules={[{ required: true, message: t('Please input') }]}
              >
                <Input disabled={disabled || editDisabled} placeholder={t('Please input')} />
              </FormItem>
            </Col>
            <Col>
              <FormItem
                name="informCategoryId"
                label={t('Declaration Category')}
                rules={[{ required: true, message: t('Please select') }]}
              >
                <GeneralSelect
                  disabled={disabled || editDisabled}
                  allowClear={false}
                  onChange={() => this.formRef.current?.resetFields?.(['contentCategoryId'])}
                  placeholder={t('Please select')}
                  option={this.renderOption(categoryList, 1)}
                />
              </FormItem>
            </Col>
            <Col>
              <FormItem
                noStyle
                shouldUpdate={(prevValues, curValues) => prevValues.informCategoryId !== curValues.informCategoryId}
              >
                {({ getFieldValue }) => {
                  const selectedInformCategoryId = getFieldValue('informCategoryId');

                  return (
                    <FormItem
                      name="contentCategoryId"
                      label={t('Declaration Content Category')}
                      rules={[{ required: true, message: t('Please select') }]}
                    >
                      <GeneralSelect
                        disabled={disabled || editDisabled}
                        allowClear={false}
                        placeholder={t('Please select')}
                        option={this.renderOption(
                          selectedInformCategoryId ? contentCategory[selectedInformCategoryId] : [],
                          1
                        )}
                      />
                    </FormItem>
                  );
                }}
              </FormItem>
            </Col>
          </Row>
          <Row>
            <FormItem name="otherSetting" label={t('Other Setting')}>
              <CheckboxGroup disabled={disabled} options={this.OtherSettingEnum} />
            </FormItem>
          </Row>
          <Row>
            <FormItem
              name="question"
              label={t('Question Description')}
              rules={[{ required: true, message: t('Please input') }]}
            >
              <TextArea
                disabled={disabled}
                autoSize={false}
                style={{ width: 560, height: 88 }}
                placeholder={t('Please input')}
              />
            </FormItem>
          </Row>
          <Row>
            <FormItem
              name="answerType"
              label={t('Declaration Type')}
              rules={[{ required: true, message: t('Please select') }]}
            >
              <RadioGroup disabled={disabled || editDisabled} onChange={this.onRadioGroupChange}>
                {this.renderOption(enums.answerType, 2)}
              </RadioGroup>
            </FormItem>
          </Row>
          <Row>
            <FormItem
              style={{ width: '100%' }}
              shouldUpdate={(prevValues, curValues) => prevValues.answerType !== curValues.answerType}
            >
              {({ getFieldValue }) => {
                return this.renderAnswerList(getFieldValue('answerType'), recentRecord);
              }}
            </FormItem>
          </Row>
          <Row>
            <FormItem name="questionDetail" style={{ width: '100%' }} label={t('Declaration Detail')}>
              <ReactQuill modules={modules} placeholder={t('Please input')} readOnly={disabled} />
            </FormItem>
          </Row>
        </Form>
      </Drawer>
    );
  }
}

export default connect(
  state => {
    const enums = state.enums;

    return {
      enums,
    };
  },
  {
    saveEnum,
  }
)(DeclarationDrawer);
