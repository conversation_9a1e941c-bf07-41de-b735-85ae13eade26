import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';

import { Button, Divider, Form, Layout, message } from 'antd';

import { useRequest } from 'ahooks';

import { DeleteAction, SimpleSectionHeader } from '@zhongan/nagrand-ui';

import { PosItemResponse, PosResponse } from 'genesis-web-service/service-types/market-types/package';

import { CustomerServiceDictValue } from '@market/common/enums';
import { DetailPageMode } from '@market/common/interface';
import { FMarketHeader } from '@market/components/F-Market-Header';
import FMarketMenu from '@market/components/F-Market-Menu';
import ProductAnchor from '@market/components/ProductAnchor';
import { useBizDict } from '@market/hook/bizDict';
import { POSItemsSelectDrawer } from '@market/pages/PolicyChange/components/POSItemsSelectDrawer/index';
import { NewMarketService } from '@market/services/market/market.service.new';
import { generalConfirm, urlQuery } from '@market/util';
import { renderEnumName } from '@market/utils/enum';

import { CommissionClawbackField } from './components/CommissionClawbackField';
import { FreelookFields } from './components/FreelookFields';
import { PolicyCancellationOrSurrenderFields } from './components/PolicyCancellationOrSurrenderFields';
import { PolicyCoverageDateChangeFields } from './components/PolicyCoverageDateChangeFields';

const { Sider, Content } = Layout;
const PolicyChange = () => {
  const navigate = useNavigate();
  const packageId = urlQuery('packageId');
  const [t] = useTranslation(['market', 'common']);

  const [selectedPosItems, setSelectedPosItems] = useState<CustomerServiceDictValue[]>([]);
  const posItemOptions = useBizDict('customerServiceItem');

  const [form] = Form.useForm();
  const location = useLocation();
  const [mode, setMode] = useState<DetailPageMode>(
    (location?.state as { mode: DetailPageMode })?.mode || DetailPageMode.view
  );
  const readOnly = mode === 'view';

  const gotoNextPage = useCallback(() => {
    navigate(`/market/package/declaration?packageId=${packageId!}`, {
      state: { mode: mode || 'edit' },
    });
  }, [navigate, mode, packageId]);

  const getFieldsDisplay = (item: CustomerServiceDictValue, itemName: number) => {
    switch (item) {
      case CustomerServiceDictValue.Freelook:
        return <FreelookFields fieldsNamePrefix={itemName} />;
      case CustomerServiceDictValue.PolicyCancellationorSurrender:
        return <PolicyCancellationOrSurrenderFields fieldsNamePrefix={itemName} />;
      case CustomerServiceDictValue.PolicyCoverageDateChange:
        return <PolicyCoverageDateChangeFields fieldsNamePrefix={itemName} />;
      default:
        return <CommissionClawbackField fieldsNamePrefix={itemName} />;
    }
  };

  const { runAsync: onSearch } = useRequest(
    async () => {
      const res = await NewMarketService.PackageAgreementConfigMgmtService.queryPackageAgreement({
        packageId: Number(packageId),
      });
      const posItemList = res?.value?.pos?.posItemList?.map(item => {
        const transformValues = (obj: PosItemResponse): PosItemResponse => {
          if (typeof obj !== 'object' || obj === null) {
            return obj;
          }

          if (Array.isArray(obj)) {
            return obj.map(transformValues);
          }

          return Object.fromEntries(
            Object.entries(obj).map(([key, value]) => {
              // number类型的转为string
              if (typeof value === 'number') {
                return [key, value?.toString()];
              }
              // number数组的转为string数组
              if (Array.isArray(value) && value.every(child => typeof child === 'number')) {
                return [key, value?.map(child => (child as number)?.toString())];
              }
              return [key, transformValues(value)];
            })
          );
        };

        return transformValues(item);
      });
      form.setFieldsValue({
        posItemList,
      });
      setSelectedPosItems(posItemList?.map(item => item.posItem as unknown as CustomerServiceDictValue) || []);
    },
    {
      refreshDeps: [packageId],
    }
  );

  const onSubmit = useCallback(async () => {
    const values = await form.validateFields();

    await NewMarketService.PackageAgreementConfigMgmtService.savePackageAgreement({
      packageId: Number(packageId),
      pos: values as PosResponse,
    });

    await onSearch();
    message.success(t('Save success'));
  }, [gotoNextPage, form, packageId]);

  useEffect(() => {
    const posItemList = form.getFieldValue('posItemList') as PosItemResponse[];

    form.setFieldsValue({
      posItemList: selectedPosItems?.map(
        item =>
          posItemList?.find(i => (i.posItem as unknown as CustomerServiceDictValue) === item) ?? {
            posItem: item,
          }
      ),
    });
  }, [selectedPosItems]);

  const onPublish = useCallback(async () => {
    await onSubmit();

    const packageValidation = await NewMarketService.GoodsPackageValidateService.validatePackage(Number(packageId));
    if (packageValidation.errors?.length) {
      generalConfirm({
        title: t('Validation Response'),
        content: (
          <ul>
            {packageValidation.errors.map(msg => (
              <li>{msg}</li>
            ))}
          </ul>
        ),
        onOk: () => {},
      });
      return;
    }
    if (packageValidation.warning?.length) {
      generalConfirm({
        title: t('Validation Response'),
        content: (
          <ul>
            {packageValidation.warning.map(msg => (
              <li>{msg}</li>
            ))}
          </ul>
        ),
        okText: t('Confirm'),
        onOk: () => {
          NewMarketService.PackageGoodsStepInfoMgmtService.doneStep({
            refId: Number(packageId),
            type: 1,
            stepNo: 6,
            isSubmit: true,
          }).then(res => {
            if (res.success) {
              message.success(t('Publish successfully'));
              navigate('/market/package/search');
            }
          });
        },
      });
    }
  }, [packageId]);

  return (
    <Layout className="market-layout">
      <FMarketHeader backPath="/market/package/search" subMenu="Package" />
      <div className="flex" style={{ height: 'calc(100% - 50px)' }}>
        <Sider width={208} className="market-sider">
          <FMarketMenu
            page="PACKAGE"
            defaultSelectedKeys={['6']}
            category={1}
            packageId={packageId}
            navigate={navigate}
          />
        </Sider>
        <Content className="market-content">
          <div className="right-content-wrapper my-6 mx-6">
            <Form form={form} disabled={readOnly}>
              <Form.List name="posItemList">
                {posItems =>
                  posItems?.map(item => {
                    const selectedPosItem = selectedPosItems[item.key];
                    return (
                      <div key={selectedPosItem} className="[&_.market-ant4-form-item]:mb-2">
                        <div className="flex gap-2 mb-4" id={selectedPosItem}>
                          <SimpleSectionHeader weight="bold">
                            {renderEnumName(selectedPosItem, posItemOptions)}
                          </SimpleSectionHeader>
                          <DeleteAction
                            doubleConfirmType="popconfirm"
                            deleteConfirmContent={t('Are you sure to delete this POS Item ?')}
                            onClick={() => setSelectedPosItems(selectedPosItems.filter(i => i !== selectedPosItem))}
                            className="h-[22px]"
                          />
                        </div>
                        {getFieldsDisplay(selectedPosItem, item.name)}
                        <Divider />
                      </div>
                    );
                  })
                }
              </Form.List>
            </Form>

            <POSItemsSelectDrawer
              selectedPosItems={selectedPosItems}
              setSelectedPosItems={setSelectedPosItems}
              disabled={readOnly}
            />
          </div>
          <ProductAnchor
            navList={selectedPosItems.map(item => ({
              href: item,
              title: renderEnumName(item, posItemOptions),
            }))}
          />
        </Content>
        <div className="bottom-action-bar">
          {readOnly ? (
            <Button size="large" onClick={() => setMode(DetailPageMode.edit)}>
              {t('Edit')}
            </Button>
          ) : (
            <React.Fragment>
              <Button size="large" type="primary" onClick={onPublish}>
                {t('Publish')}
              </Button>
              <Button size="large" onClick={onSubmit} type="default">
                {t('Save')}
              </Button>
            </React.Fragment>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default PolicyChange;
