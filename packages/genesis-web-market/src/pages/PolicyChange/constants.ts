import { CustomerServiceDictValue } from '@market/common/enums';

export const OPTIONAL_POS_ITEMS = [
  CustomerServiceDictValue.AddorDeleteRider,
  CustomerServiceDictValue.AutoRenewalSwitchChange,
  CustomerServiceDictValue.BeneficiaryChange,
  CustomerServiceDictValue.Freelook,
  CustomerServiceDictValue.InsuredBasicInformationChange,
  CustomerServiceDictValue.InsuredIdentificationDetailChange,
  CustomerServiceDictValue.InsureObject,
  CustomerServiceDictValue.MasterPolicyAddOrDeleteInsuredObject,
  CustomerServiceDictValue.MasterPolicyBasicInfoChange,
  CustomerServiceDictValue.MasterPolicyCancellation,
  CustomerServiceDictValue.OpenEndPolicyAutoReview,
  CustomerServiceDictValue.PlanChange,
  CustomerServiceDictValue.PolicyBasicInformationChange,
  CustomerServiceDictValue.PolicyCancellationorSurrender,
  CustomerServiceDictValue.PolicyCoverageDateChange,
  CustomerServiceDictValue.PolicyReprint,
  CustomerServiceDictValue.PolicyInformationChange,
  CustomerServiceDictValue.PolicyPaymentInformationChange,
  CustomerServiceDictValue.PolicyholderBasicInformationChange,
  CustomerServiceDictValue.PolicyholderChange,
  CustomerServiceDictValue.PremiumFrequencyChange,
  CustomerServiceDictValue.Reinstatement,
];

export enum PosCalculationMethod {
  ByFormula = 'BY_FORMULA',
  DeltaPremiumTax = 'DELTA_PREMIUM_TAX',
  CalculateUnearnedPremiumOnDailyBasis = 'CALCULATE_BY_DAILY_BASIS',
}
