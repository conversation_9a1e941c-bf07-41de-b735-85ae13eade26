import React from 'react';
import { useTranslation } from 'react-i18next';

import { Checkbox, Form } from 'antd';

import { CoverageDateChangeType } from '@market/common/enums';
import { useBizDictAsOptions } from '@market/hook/bizDict';

import { CommissionClawbackField } from '../CommissionClawbackField';

export const PolicyCoverageDateChangeFields = ({ fieldsNamePrefix }: { fieldsNamePrefix: number }) => {
  const coverageDateChangeTypeOptions = useBizDictAsOptions('coverageDateChangeType');
  const coveragePeriodFieldsToReviseOptions = useBizDictAsOptions('coveragePeriodRevision');

  const [t] = useTranslation(['market', 'common']);

  return (
    <React.Fragment>
      <CommissionClawbackField fieldsNamePrefix={fieldsNamePrefix} />
      <Form.Item shouldUpdate noStyle>
        {({ getFieldValue, setFieldValue }) => (
          <React.Fragment>
            <Form.Item
              label={t('Coverage Date Change Type')}
              name={[fieldsNamePrefix, 'policyCoverageDateChangeItem', 'coverageDateChangeTypeList']}
              rules={[{ required: true, message: t('Please select') }]}
              initialValue={['1']}
            >
              <Checkbox.Group
                options={coverageDateChangeTypeOptions}
                onChange={() => {
                  setFieldValue(
                    [
                      'posItemList',
                      fieldsNamePrefix,
                      'policyCoverageDateChangeItem',
                      'coveragePeriodFieldsToReviseList',
                    ],
                    undefined
                  );
                }}
              />
            </Form.Item>
            {(
              getFieldValue([
                'posItemList',
                fieldsNamePrefix,
                'policyCoverageDateChangeItem',
                'coverageDateChangeTypeList',
              ]) as CoverageDateChangeType
            )?.includes(CoverageDateChangeType.CoveragePeriodRevision) ? (
              <Form.Item
                label={t('Coverage Period Fields to Revise')}
                name={[fieldsNamePrefix, 'policyCoverageDateChangeItem', 'coveragePeriodFieldsToReviseList']}
                rules={[{ required: true, message: t('Please select') }]}
              >
                <Checkbox.Group options={coveragePeriodFieldsToReviseOptions} />
              </Form.Item>
            ) : null}
          </React.Fragment>
        )}
      </Form.Item>
    </React.Fragment>
  );
};
