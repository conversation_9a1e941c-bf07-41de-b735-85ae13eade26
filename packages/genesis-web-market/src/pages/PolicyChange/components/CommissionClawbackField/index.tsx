import React from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Radio } from 'antd';

import { useBizDictAsOptions } from '@market/hook/bizDict';

export const CommissionClawbackField = ({ fieldsNamePrefix }: { fieldsNamePrefix: number }) => {
  const yesNoOptions = useBizDictAsOptions('yesNo', {
    labelKey: 'dictValueName',
    valueKey: 'enumItemName',
  });
  const [t] = useTranslation(['market', 'common']);
  return (
    <React.Fragment>
      <Form.Item name={[fieldsNamePrefix, 'allowCommissionClawback']} label={t('Commission Clawback')}>
        <Radio.Group options={yesNoOptions} />
      </Form.Item>
    </React.Fragment>
  );
};
