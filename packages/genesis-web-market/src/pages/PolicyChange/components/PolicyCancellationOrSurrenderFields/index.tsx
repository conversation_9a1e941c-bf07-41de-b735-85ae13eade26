import React from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Radio } from 'antd';

import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';

import { CommissionClawbackField } from '../CommissionClawbackField';

export const PolicyCancellationOrSurrenderFields = ({ fieldsNamePrefix }: { fieldsNamePrefix: number }) => {
  const yesNoOptions = useBizDictAsOptions('yesNo', {
    labelKey: 'dictValueName',
    valueKey: 'enumItemName',
  });
  const cancellationTypeOptions = useBizDictAsOptions('cancellationType');

  const [t] = useTranslation(['market', 'common']);
  return (
    <React.Fragment>
      <Col span={16}>
        <Form.Item
          label={t('Cancellation Type')}
          name={[fieldsNamePrefix, 'cancellationItem', 'cancellationTypeList']}
          rules={[{ required: true, message: t('Please select') }]}
          layout="vertical"
        >
          <GeneralSelect mode="multiple" option={cancellationTypeOptions} className="!w-full" />
        </Form.Item>
      </Col>
      <CommissionClawbackField fieldsNamePrefix={fieldsNamePrefix} />
      <Form.Item
        label={t('Service Fee Clawback')}
        name={[fieldsNamePrefix, 'cancellationItem', 'allowServiceFeeClawback']}
      >
        <Radio.Group options={yesNoOptions} />
      </Form.Item>
    </React.Fragment>
  );
};
