import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Checkbox, Form } from 'antd';

import { sortBy } from 'lodash-es';

import { Drawer, Icon } from '@zhongan/nagrand-ui';

import { CustomerServiceDictValue } from '@market/common/enums';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { OPTIONAL_POS_ITEMS } from '@market/pages/PolicyChange/constants';

export const POSItemsSelectDrawer = ({
  disabled,
  selectedPosItems,
  setSelectedPosItems,
}: {
  disabled: boolean;
  selectedPosItems: CustomerServiceDictValue[];
  setSelectedPosItems: (value: CustomerServiceDictValue[]) => void;
}) => {
  const [visible, setVisible] = useState(false);
  const [t] = useTranslation(['market', 'common']);
  const posItemOptions = useBizDictAsOptions('customerServiceItem');
  const [form] = Form.useForm();
  const sortedFilteredPosItemOptions = sortBy(
    posItemOptions?.filter(option => OPTIONAL_POS_ITEMS.includes(option.value as CustomerServiceDictValue)),
    'label'
  );

  useEffect(() => {
    form.setFieldValue('selectedPosItems', selectedPosItems);
  }, [selectedPosItems]);

  return (
    <div>
      <Button icon={<Icon type="add" />} onClick={() => setVisible(true)} disabled={disabled}>
        {t('Add POS Items')}
      </Button>
      <Drawer
        title={t('Add POS Items')}
        open={visible}
        onClose={() => setVisible(false)}
        width={600}
        onSubmit={() => {
          setSelectedPosItems(Array.from(new Set([...selectedPosItems, ...form.getFieldValue('selectedPosItems')])));
          setVisible(false);
          document.getElementsByClassName('market-ant4-layout-content market-content')[0].scrollTo({
            top: 0,
            behavior: 'smooth',
          });
        }}
      >
        <Form form={form}>
          <Form.Item noStyle name="selectedPosItems">
            <Checkbox.Group
              options={sortedFilteredPosItemOptions?.map(option => ({
                ...option,
                disabled: selectedPosItems?.includes(option.value as CustomerServiceDictValue),
              }))}
              className="[&_.market-ant4-checkbox-wrapper]:flex [&_.market-ant4-checkbox-wrapper]:mb-4 block"
            />
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
};
