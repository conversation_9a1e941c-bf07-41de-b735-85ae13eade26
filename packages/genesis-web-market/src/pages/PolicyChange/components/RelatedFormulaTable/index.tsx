import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Input, Select, message } from 'antd';
import { ColumnProps } from 'antd/es/table';

import { useRequest } from 'ahooks';

import { AddNewButton, DeleteAction, Drawer, EditAction, Table } from '@zhongan/nagrand-ui';

import { FormulaSubCategory } from 'genesis-web-service';
import {
  PosCalculationLevelEnum,
  PosFormulaItemResponse,
} from 'genesis-web-service/service-types/market-types/package';

import { BizTopic, CustomerServiceDictValue } from '@market/common/enums';
import { useBizDict, useBizDictAsOptions } from '@market/hook/bizDict';
import { useFormulaList } from '@market/hook/product.service';
import { NewMarketService } from '@market/services/market/market.service.new';
import { urlQuery } from '@market/util';
import { renderOptionName } from '@market/utils/enum';

import { PosCalculationMethod } from '../../constants';

export interface RelatedFormulaTableProps {
  posItem: CustomerServiceDictValue;
  fieldsNamePrefix: number;
}

export const RelatedFormulaTable = ({ posItem, fieldsNamePrefix }: RelatedFormulaTableProps) => {
  const packageId = urlQuery('packageId');
  const [t] = useTranslation();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [drawerKey, setDrawerKey] = useState<number | 'add'>();
  const [form] = Form.useForm();
  const calculationLevelOptions = useBizDictAsOptions('posCalculationLevel');
  const bizTopicOptions = useBizDict('bizTopic');
  const posCalculationMethodOptions = useBizDictAsOptions('posCalculationMethod');

  const outForm = Form.useFormInstance();
  const posFormulaCategoryItemsNamePath = ['posItemList', fieldsNamePrefix, 'posFormulaCategoryItems'];

  const isCancellationPosItem = [
    CustomerServiceDictValue.PolicyCancellationorSurrender,
    CustomerServiceDictValue.Freelook,
  ].includes(posItem);

  const editingCalculationLevel = Form.useWatch('posCalculationLevel', form);
  const editingCalculationMethod = Form.useWatch('posCalculationMethod', form);
  const editingFormulaCode = Form.useWatch(['posFormula', 'formulaCode'], form);
  const editingCoverageList = Form.useWatch('coverageList', form) as number[];
  const dataSource = (Form.useWatch(posFormulaCategoryItemsNamePath, outForm) ?? []) as PosFormulaItemResponse[];

  const formulaList = useFormulaList({
    formulaCategoryCode: +BizTopic.POS,
  });

  const formulaOptions = useMemo(
    () =>
      formulaList?.map(option => ({
        ...option,
        label: option.formulaName!,
        value: option.formulaCode!,
      })),
    [formulaList]
  );

  const handleEdit = useCallback((record: PosFormulaItemResponse, index: number) => {
    setDrawerOpen(true);
    setDrawerKey(index);
    form.setFieldsValue(record);
  }, []);

  const handleAdd = useCallback(() => {
    setDrawerOpen(true);
    setDrawerKey('add');
    form.resetFields();
  }, []);

  const handleDelete = useCallback(
    (index: number) => {
      outForm.setFieldValue(posFormulaCategoryItemsNamePath, [
        ...dataSource.slice(0, index),
        ...dataSource.slice(index + 1),
      ]);
    },
    [dataSource]
  );

  const { data: coverageListOptions } = useRequest(async () => {
    const { products } = await NewMarketService.PackageProductMgmtService.queryConfigProducts({
      packageId: +packageId!,
    });
    return products?.map(product => ({
      label: product.productName!,
      value: product.insuranceProductCode!,
    }));
  });

  const standardPremiumOption = useMemo(() => {
    const target = (bizTopicOptions.find(enumItem => enumItem.dictValue === BizTopic.POS)?.childList || [])?.find(
      item => item.dictValue === FormulaSubCategory.StandardPremium
    );
    return target
      ? [
          {
            label: target.dictValueName,
            value: target.dictValue as string,
          },
        ]
      : [];
  }, [bizTopicOptions]);

  useEffect(() => {
    form.setFieldValue(
      'posFormulaDescription',
      editingCalculationMethod === PosCalculationMethod.CalculateUnearnedPremiumOnDailyBasis
        ? t(
            'POS additional/refund net premium = (product net premium after POS - product net premium before) * unearned period / coverage period'
          )
        : formulaOptions.find(formula => formula.value === editingFormulaCode)?.remark
    );
  }, [editingFormulaCode, formulaOptions, editingCalculationMethod]);

  const onSubmit = useCallback(async () => {
    const values = (await form.validateFields()) as PosFormulaItemResponse;
    const newDataSource =
      drawerKey === 'add'
        ? [values, ...dataSource]
        : typeof drawerKey === 'number'
          ? [...dataSource.slice(0, drawerKey), values, ...dataSource.slice(drawerKey + 1)]
          : [];

    const calculationLevelList = newDataSource?.map(item => item.calculationLevel);
    // 同一个 sub category 下 不允许同时存在 by coverage level 和 by policy level 的记录；by policy level 最多一条
    if (
      new Set(calculationLevelList).size === 2 ||
      calculationLevelList.filter(item => item === PosCalculationLevelEnum.BY_POLICY_LEVEL).length > 1
    ) {
      message.error(
        "Within the same Formula Sub Category, you can only have either one 'By Policy Level' record or multiple 'By Coverage Level' records, but not both."
      );
    } else {
      outForm.setFieldValue(posFormulaCategoryItemsNamePath, newDataSource);
      setDrawerOpen(false);
    }
  }, [dataSource, drawerKey]);

  const columns: ColumnProps<PosFormulaItemResponse>[] = useMemo(
    () => [
      {
        title: t('Calculation Level'),
        dataIndex: 'calculationLevel',
        render: value => renderOptionName(value, calculationLevelOptions),
      },
      {
        title: t('Product Code - Name'),
        dataIndex: 'coverageList',
        render: (list: string[]) => list?.map(item => renderOptionName(item, coverageListOptions!))?.join(','),
      },
      {
        title: t('Calculation Method'),
        dataIndex: 'posCalculationMethod',
        render: value => renderOptionName(value, posCalculationMethodOptions),
      },
      {
        title: t('Formula Sub Category'),
        dataIndex: 'posFormulaSubCategory',
        render: value => renderOptionName(value, standardPremiumOption),
      },
      {
        title: t('Formula Code'),
        dataIndex: ['posFormula', 'formulaCode'],
        render: value => renderOptionName(value, formulaOptions),
      },
      {
        title: t('Formula Description'),
        dataIndex: 'posFormulaDescription',
      },
      {
        title: t('Actions'),
        render: (text, record, index) => (
          <div>
            <EditAction onClick={() => handleEdit(record, index)} />
            <DeleteAction onClick={() => handleDelete(index)} />
          </div>
        ),
      },
    ],
    [formulaOptions, standardPremiumOption, posCalculationMethodOptions, coverageListOptions, calculationLevelOptions]
  );

  return (
    <div>
      <AddNewButton onClick={handleAdd} className="my-3" />

      <Form.Item name={[fieldsNamePrefix, 'posFormulaCategoryItems']}>
        <Table columns={columns} dataSource={dataSource ?? []} pagination={false} scroll={{ x: 'max-content' }} />
      </Form.Item>
      <Drawer
        open={drawerOpen}
        title={drawerKey === 'add' ? t('Add Related Formula') : t('Edit Related Formula')}
        onSubmit={onSubmit}
        onClose={() => setDrawerOpen(false)}
      >
        <Form form={form} layout="vertical" className="market-field-wrapper">
          <Form.Item
            label={t('Calculation Level')}
            name="calculationLevel"
            rules={[
              {
                required: true,
                message: t('Please select'),
              },
            ]}
          >
            <Select options={calculationLevelOptions} placeholder={t('Please select')} />
          </Form.Item>
          <Form.Item
            label={t('Product Code - Name')}
            name="coverageList"
            rules={[
              {
                required: editingCalculationLevel === PosCalculationLevelEnum.BY_COVERAGE_LEVEL,
                message: t('Please select'),
              },
            ]}
          >
            <Select
              options={coverageListOptions?.filter(option => {
                const value = option.value as unknown as number;
                return (
                  !dataSource?.some(item => item.coverageList?.includes(value)) || editingCoverageList?.includes(value)
                );
              })}
              mode="multiple"
              placeholder={t('Please select')}
            />
          </Form.Item>
          <Form.Item
            label={t('Calculation Method')}
            name="posCalculationMethod"
            rules={[{ required: true, message: t('Please select') }]}
            initialValue={isCancellationPosItem ? PosCalculationMethod.ByFormula : undefined}
          >
            <Select
              options={posCalculationMethodOptions?.filter(options =>
                [
                  PosCalculationMethod.ByFormula,
                  !isCancellationPosItem && PosCalculationMethod.CalculateUnearnedPremiumOnDailyBasis,
                ].includes(options.value as PosCalculationMethod)
              )}
              placeholder={t('Please select')}
              disabled={isCancellationPosItem}
              onChange={val => {
                if (val === PosCalculationMethod.CalculateUnearnedPremiumOnDailyBasis) {
                  form.setFieldValue(['posFormula', 'formulaCode'], undefined);
                }
              }}
            />
          </Form.Item>
          <Form.Item
            label={t('Formula Sub Category')}
            name="posFormulaSubCategory"
            initialValue={FormulaSubCategory.StandardPremium}
          >
            <Select disabled placeholder={t('Please select')} options={standardPremiumOption} />
          </Form.Item>
          <Form.Item
            label={t('Formula Code')}
            name={['posFormula', 'formulaCode']}
            rules={[
              { required: editingCalculationMethod === PosCalculationMethod.ByFormula, message: t('Please select') },
            ]}
          >
            <Select
              disabled={editingCalculationMethod === PosCalculationMethod.CalculateUnearnedPremiumOnDailyBasis}
              options={formulaOptions}
              placeholder={t('Please select')}
              showSearch
              optionFilterProp="label"
            />
          </Form.Item>
          <Form.Item label={t('Formula Description')} name="posFormulaDescription">
            <Input placeholder={t('Please input')} disabled />
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
};
