import React from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Input, InputNumber, Radio } from 'antd';

import { FreelookPeriodUnitType } from 'genesis-web-service';

import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';

import { CommissionClawbackField } from '../CommissionClawbackField';

export const FreelookFields = ({ fieldsNamePrefix }: { fieldsNamePrefix: number }) => {
  const freelookPeriodUnitOptions = useBizDictAsOptions('freelookPeriodUnit');
  const yesNoOptions = useBizDictAsOptions('yesNo', {
    labelKey: 'dictValueName',
    valueKey: 'enumItemName',
  });
  const [t] = useTranslation(['market', 'common']);

  return (
    <React.Fragment>
      <Form.Item label={t('Freelook Period')} required layout="vertical">
        <Input.Group compact>
          <Form.Item
            name={[fieldsNamePrefix, 'freeLookItem', 'freeLookPeriod']}
            rules={[{ required: true, message: t('Please input') }]}
            noStyle
          >
            <InputNumber precision={0} min={0} max={999} className="w-[160px]" placeholder={t('Freelook Period')} />
          </Form.Item>
          <Form.Item
            initialValue={FreelookPeriodUnitType.Day}
            name={[fieldsNamePrefix, 'freeLookItem', 'freelookPeriodUnit']}
            noStyle
          >
            <GeneralSelect
              allowClear={false}
              style={{ width: 80 }}
              option={(freelookPeriodUnitOptions || []).filter(
                optionItem => optionItem.value !== FreelookPeriodUnitType.Hour
              )}
            />
          </Form.Item>
        </Input.Group>
      </Form.Item>
      <CommissionClawbackField fieldsNamePrefix={fieldsNamePrefix} />
      <Form.Item label={t('Service Fee Clawback')} name={[fieldsNamePrefix, 'freeLookItem', 'allowServiceFeeClawback']}>
        <Radio.Group options={yesNoOptions} />
      </Form.Item>
    </React.Fragment>
  );
};
