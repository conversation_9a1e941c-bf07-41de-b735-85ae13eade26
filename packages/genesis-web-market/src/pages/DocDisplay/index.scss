.goods-doc-display.market-ant4-layout {
  height: 100%;
  overflow: hidden;
  .market-content.market-ant4-layout-content {
    .right-content-wrapper {
      .block-title {
        padding-bottom: 16px;
        font-weight: bold;
        color: var(--text-color);
        line-height: 24px;
        font-size: 16px;
        border-bottom: 1px dashed var(--border-light);
      }
      .sub-block {
        padding: 16px 0px 24px 0px;
        .sub-block-title {
          margin-bottom: 16px;
          font-weight: bold;
          color: var(--text-color);
        }
      }
      .sub-block#temp {
        padding-bottom: 0;
      }
      .other-container {
        .special-title {
          margin-bottom: 16px;
        }
        .base-editable-table {
          margin-bottom: 24px;
        }
      }
      .sub-block + .sub-block {
        border-top: 1px dashed var(--border-light);
      }
      #other {
        padding-bottom: 0;
      }
    }
  }
  .block-config-table {
    border: 1px solid var(--border-default);

    .block-config-table-header {
      border-bottom: 1px solid var(--border-default);
      padding: 10px 24px;
      height: 52px;
      line-height: 32px;
      font-weight: bold;
    }
    .long-add-btn {
      margin-top: 22px;
    }
  }
  .block-config-table + .block-config-table {
    margin-top: 16px;
  }

  .function-label-block-wrapper {
    padding: 24px 24px 0;

    .function-label-block {
      flex-grow: 1;
      padding-bottom: 24px;
      border-bottom: 1px dashed var(--border-light);
    }
  }
}
.doc-upload-drawer {
  .market-ant4-drawer-wrapper-body {
    min-height: 100%;
    position: relative;
    .market-ant4-drawer-body {
      padding: 24px 0px;
      height: 100%;
    }
    .drawer-content {
      padding: 0px 32px 80px 32px;
      .drawer-title {
        margin-bottom: 32px;
        font-weight: bold;
        padding-bottom: 20px;
        border-bottom: 1px dashed var(--border-light);
      }
      .detail-info {
        // padding: 20px 0 24px 0;
        border-bottom: 1px dashed var(--border-light);
        margin-bottom: 24px;
        > .market-ant4-col + .market-ant4-col {
          margin-left: 80px;
        }
      }
      .drawer-foot {
        position: fixed;
        right: 0;
        bottom: 0;
        width: 1024px;
        border-top: 1px solid var(--border-default);
        background: var(--white);
        text-align: right;
        height: 72px;
        padding: 16px 32px 16px;
        z-index: 2;
      }
    }
  }
}
