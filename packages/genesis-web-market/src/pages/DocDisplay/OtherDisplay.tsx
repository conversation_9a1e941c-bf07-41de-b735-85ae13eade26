import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, Input } from 'antd';

import { cloneDeep, pick } from 'lodash-es';

import { EditableTable, FieldType, Icon, NewPosition, message } from '@zhongan/nagrand-ui';

import { ProductCategoryItemExtend1, YesNoType } from 'genesis-web-service';

import { NAME_CONNECTOR_STR } from '@market/common/constant';
import { BizDict } from '@market/common/interface';
import { OptEnum } from '@market/request/interface';
import { NewMarketService } from '@market/services/market/market.service.new';
import { renderEnumName } from '@market/utils/enum';

const FormItem = Form.Item;
const Textarea = Input.TextArea;

interface SpecialAgreementProps {
  specialAgreementCode?: string;
  specialAgreement?: string;
  isUpdatable: number | string;
  planIds?: number[] | string[];
  order?: number;
  index?: number;
  key?: string | number;
}

interface StateDataProps {
  isShowEditButton: boolean;
  mode: string;
  planList: BizDict[];
  goodsCategory: ProductCategoryItemExtend1;
  initialValueMap: { specialAgreementList: SpecialAgreementProps[] };
}

interface OtherDisplayProps {
  disabled: boolean;
  stateData?: StateDataProps;
  enums: { yesNo: BizDict[] };
  goodsId: number;
  setSAValue: (value: SpecialAgreementProps[]) => void;
  queryGoodsFile: () => void;
}

const OtherDisplay = ({ disabled, stateData, enums, goodsId, setSAValue, queryGoodsFile }: OtherDisplayProps) => {
  const [t] = useTranslation(['market', 'common']);
  const [loading, setLoading] = useState(false);
  const [sortMode, setSortMode] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const { initialValueMap, goodsCategory, planList, mode, isShowEditButton } = stateData || {};
  const { specialAgreementList = [] } = initialValueMap || {};

  const disabledSA = mode === 'view' || !isShowEditButton || loading || sortMode;

  const saveAgreementList = (agreementList: any[]) => {
    const specialAgreementListValue = agreementList.map((item, index) => ({
      ...pick(item, ['specialAgreementCode', 'specialAgreement', 'isUpdatable', 'planIds']),
      order: index + 1,
    }));
    const params = {
      isUpdateSpecialAgreement: true,
      goodsId,
      isSubmit: false,
      specialAgreementList: specialAgreementListValue,
    };
    return NewMarketService.GoodsFileDisplayMgmtService.save(params);
  };

  const columns = [
    {
      title: t('number'),
      dataIndex: 'index',
      width: 80,
      editable: false,
    },
    {
      title: (
        <span>
          <span className="mr-1 text-[#ff0000]">*</span>
          <span>{t('Agreement Code')}</span>
        </span>
      ),
      dataIndex: 'specialAgreementCode',
      fieldProps: {
        type: FieldType.Input,
      },
      formItemProps: {
        rules: [{ required: true, message: t('Please input') }],
      },
      editable: true,
    },
    {
      title: (
        <span>
          <span className="mr-1 text-[#ff0000]">*</span>
          <span>{t('Special Agreement')}</span>
        </span>
      ),
      dataIndex: 'specialAgreement',
      fieldProps: {
        type: FieldType.Input,
        extraProps: {
          maxLength: 400,
        },
      },
      formItemProps: {
        rules: [{ required: true, message: t('Please input') }],
      },
      editable: true,
    },
    {
      title: (
        <span>
          <span className="mr-1 text-[#ff0000]">*</span>
          <span>{t('Plan Code')}</span>
        </span>
      ),
      dataIndex: 'planIds',
      fieldProps: {
        type: FieldType.Select,
        extraProps: {
          mode: 'multiple',
          options: planList?.map((item: BizDict) => ({
            value: item.itemExtend1,
            label: item.itemName,
          })),
          getPopupContainer: () => document.querySelector('#other .ant-table') || document.body,
        },
      },
      formItemProps: {
        rules: [{ required: true, message: t('Please select') }],
      },
      editable: true,
      render: (text: string | string[]) => {
        if (!Array.isArray(text)) return '';
        return text
          .map(item => {
            const { itemName } = planList?.find((plan: BizDict) => `${item}` === `${plan.itemExtend1}`) || {};
            return itemName;
          })
          .join(NAME_CONNECTOR_STR);
      },
    },
    {
      title: (
        <span>
          <span className="mr-1 text-[#ff0000]">*</span>
          <span>{t('Updatable or Not')}</span>
        </span>
      ),
      dataIndex: 'isUpdatable',
      fieldProps: {
        type: FieldType.Select,
        extraProps: {
          options: enums.yesNo?.map(item => ({ value: item.itemExtend1, label: item.itemName })) || [],
          getPopupContainer: () => document.querySelector('#other .ant-table') || document.body,
        },
      },
      formItemProps: {
        rules: [{ required: true, message: t('Please select') }],
      },
      editable: true,
      render: (text = YesNoType.No) => renderEnumName(text, enums.yesNo),
    },
  ];

  const handleConfirm = (value: SpecialAgreementProps, key: number | string) => {
    const newData = cloneDeep(specialAgreementList);
    return new Promise<void>((resolve, reject) => {
      if (key === OptEnum.Add) {
        const index = newData.findIndex(item => item.key === 'add');
        newData[index] = {
          ...value,
          index: index + 1,
        };
      } else {
        newData[(key as number) - 1] = {
          index: key as number,
          ...value,
        };
      }
      setLoading(true);
      saveAgreementList(newData)
        .then(() => {
          setSAValue(newData);
          setEditMode(false);
          resolve();
        })
        .catch(() => {
          setLoading(false);
          reject();
        });
    });
  };

  const handleDelete = async (index: number) => {
    let newValue: SpecialAgreementProps[] = cloneDeep(specialAgreementList) || [];
    newValue.splice(index, 1);
    newValue = newValue?.map((item, valueIndex) => ({
      ...item,
      index: valueIndex + 1,
    }));
    setLoading(true);
    try {
      await saveAgreementList(newValue);
      message.success(t('text_delete_success'));
      setSAValue(newValue);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const saveSort = async () => {
    setSortMode(true);
    setLoading(true);
    try {
      await saveAgreementList(specialAgreementList);
      message.success(t('sort successfully'));
      setSAValue(specialAgreementList);
      setSortMode(false);
      queryGoodsFile();
      setLoading(false);
    } catch {
      setLoading(false);
    }
  };

  const rightSection = useMemo(() => {
    if (specialAgreementList.length <= 1) return undefined;

    return sortMode ? (
      <Button onClick={saveSort}>
        <Icon type="sort" /> {t('Save Sort')}
      </Button>
    ) : (
      <Button onClick={() => setSortMode(true)} disabled={editMode}>
        <Icon type="sort" /> {t('Sort')}
      </Button>
    );
  }, [specialAgreementList, sortMode, editMode]);

  return (
    <div className="other-container">
      <div className="special-title">{t('Special Agreement')}</div>
      <EditableTable
        rowKey="index"
        columns={columns}
        dataSource={specialAgreementList}
        setDataSource={value =>
          setSAValue(
            value?.map((item, index) => ({
              ...item,
              index: index + 1,
            }))
          )
        }
        pagination={false}
        scroll={{ x: 'max-content' }}
        loading={loading}
        addBtnProps={{
          type: 'default',
          addTitle: t('Add'),
          disabled: disabledSA,
          handleAdd: () => setEditMode(true),
        }}
        newPosition={NewPosition.Bottom}
        initializeAddData={{
          specialAgreementCode: undefined,
          specialAgreement: undefined,
          index: specialAgreementList?.length + 1,
          planIds: goodsCategory === ProductCategoryItemExtend1.GroupEmployeeBenefit ? ['1'] : ['0'],
          isUpdatable: '2',
        }}
        editBtnProps={{ disabled: () => disabledSA, handleEdit: () => setEditMode(true) }}
        deleteBtnProps={{ disabled: () => disabledSA, handleDelete }}
        draggable={sortMode}
        handleConfirm={handleConfirm}
        handleCancel={() => setEditMode(false)}
        rightSection={rightSection}
      />
      <FormItem
        layout="vertical"
        name="disputeSettlement"
        label={t('Dispute Settlement')}
        colon={false}
        className="mt-4"
      >
        <Textarea
          placeholder={t('Please input')}
          style={{ width: 560, height: 88 }}
          maxLength={64}
          disabled={disabled}
        />
      </FormItem>
    </div>
  );
};

export default OtherDisplay;
