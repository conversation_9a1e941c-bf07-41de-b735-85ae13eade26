import React, { Component } from 'react';

import { Button, Checkbox, Form, Layout, Skeleton, message } from 'antd';

import { Icon } from '@zhongan/nagrand-ui';

import { ProductCategoryItemExtend1 } from 'genesis-web-service';

import Section from '@market/components/Section/section';
import { t } from '@market/i18n';
import { isShowNotification } from '@market/marketService/goodsTesting.service';
import { NewMarketService } from '@market/services/market/market.service.new';

import EditableTable from '../../components/EditableTable';
import { FMarketHeader } from '../../components/F-Market-Header';
import FMarketMenu from '../../components/F-Market-Menu';
import { downloadFile, generalConfirm, urlQuery } from '../../util';
import FileManagement from './FileManagement';
import OtherDisplay from './OtherDisplay';
import './index.scss';

const FormItem = Form.Item;
const { Sider, Content } = Layout;

class DocDisplay extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [
        {
          href: 'declaration',
          title: t('Declaration'),
        },
        {
          href: 'insured-notice',
          title: t('Insured Notice'),
          value: 10,
        },
        {
          href: 'insurance-case',
          title: t('Insurance Case'),
          value: 6,
        },
        {
          href: 'policy-manual',
          title: t('Policy Manual'),
          value: 8,
        },
        {
          href: 'regulation-of-products',
          title: t('Regulation of Products'),
          value: 9,
        },
        {
          href: 'customized-document',
          title: t('Customized Document'),
          value: 7,
        },
      ],
      packageCode: '',
      initialValueMap: {},
      currentRecord: {},
      file: undefined,
      fileLoading: false,
      drawerTableList: [],
      disableConfirm: false,
      zoneId: null,
      isShowEditButton: false,
      planList: [],
      batchDeleteCustomDocList: new Set(),
      customDocDeleteMode: false,
      customDocList: [],
      startCheckCode: false,
      startCheckName: false,
      jumpValidteCheck: false,
      tableLoading1: false,
      tableLoading2: false,
      goodsCategory: undefined,
      showNotification: false,
      skeletonVisible: true,
      sortState: 'Normal',
    };
  }

  async componentDidMount() {
    // const self = this;
    this.goodsId = urlQuery('goodsId');
    this.layoutId = urlQuery('layoutId');
    if (!this.goodsId || !this.layoutId) {
      this.props.navigate('/market/goods/search');
      return;
    }
    const showNotification = await isShowNotification(this.goodsId);
    const { hasEditAuth, isSuperUser, userInfo } = this.props;

    const { state } = this.props.location;
    const mode = state && state.mode;
    const queryMode = state && state.queryMode;
    this.setState({
      mode: mode || 'edit',
      queryMode,
      showNotification,
    });
    NewMarketService.GoodMgmtService.find({
      goodsId: this.goodsId,
      queryModel: '',
    }).then(res => {
      if (res.success) {
        const goodsCategory = res.value.basicProp.goodsCategory || undefined;
        let tempObj = { itemExtend1: '0', itemName: t('ALL') };
        if (goodsCategory === ProductCategoryItemExtend1.GroupEmployeeBenefit) {
          tempObj = {
            itemExtend1: '1',
            itemName: t('Group Policy Level'),
          };
        }
        const list = [
          tempObj,
          ...res.value.plans.map(i => {
            return {
              itemExtend1: i.goodsPlanId,
              itemName: i.planCode,
            };
          }),
        ];
        this.setState({
          planList: list,
          goodsCategory,
          salesProp: res.value.salesProp,
          isShowEditButton:
            (res.value.basicProp && res.value.basicProp.creator === `${userInfo.userId}` && hasEditAuth) || isSuperUser,
          skeletonVisible: false,
        });
      }
    });
    this.queryGoodsFile();
    this.queryTimeZone(this.goodsId, queryMode);
  }

  queryGoodsFile = () => {
    this.setState({
      tableLoading1: true,
    });
    return NewMarketService.GoodsFileDisplayMgmtService.query({
      goodsId: this.goodsId,
    })
      .then(res => {
        if (res) {
          if (res.specialAgreementList && res.specialAgreementList.length > 0) {
            res.specialAgreementList.forEach(item => {
              item.index = item.order;
              item.key = item.index;
              // 下拉枚举中是string，在此转换一下
              if (Array.isArray(item.planIds)) {
                item.planIds = item.planIds.map(j => `${j}`);
              } else {
                item.planIds = ['0'];
              }
              item.isUpdatable = `${item.isUpdatable}`;
            });
          }
          const customDocList = [];
          if (res.goodsMediaMap && res.goodsMediaMap['7'] && res.goodsMediaMap['7'].length > 0) {
            // 插入默认对象
            res.goodsMediaMap['7'].forEach(i => {
              if (!customDocList.find(j => j.labelCode === i.labelCode)) {
                customDocList.push({
                  id: i.labelCode,
                  labelCode: i.labelCode,
                  labelName: i.labelName,
                  list: [],
                });
              }
            });
            // 根据labelCode分组
            res.goodsMediaMap['7'].forEach(i => {
              customDocList.forEach(j => {
                if (j.labelCode === i.labelCode) {
                  j.list.push(i);
                }
              });
            });
            // 给每个数组补上序号、key
            customDocList.forEach(i => {
              i.list.forEach((j, index) => {
                j.index = index + 1;
                j.key = j.id;
              });
            });
          }

          if (res.goodsMediaMap) {
            // 过滤掉新数据
            const goodsMediaMap = res.goodsMediaMap;
            Object.keys(goodsMediaMap).forEach(mediaCategory => {
              // 有docType是新版本的数据，不在旧版本中展示
              const list = goodsMediaMap[mediaCategory].filter(item => !item.docType);
              if (list.length === 0) {
                goodsMediaMap[mediaCategory] = undefined;
              } else {
                goodsMediaMap[mediaCategory] = list;
              }
            });
            res.goodsMediaMap = goodsMediaMap;
          }

          this.setState({
            initialValueMap: res,
            customDocList,
            customDocAddMode: false,
          });
          this.props.form.setFieldValue('disputeSettlement', res.disputeSettlement);
        }
      })
      .finally(() => {
        this.setState({
          tableLoading1: false,
        });
      });
  };

  queryTimeZone = (goodsId, queryModel) => {
    NewMarketService.GoodMgmtService.find({
      goodsId,
      queryModel,
    }).then(res => {
      if (res.success && res.value && res.value.salesProp && res.value.salesProp.zoneId) {
        this.setState({
          zoneId: res.value.salesProp.zoneId,
        });
      }
    });
  };

  onConfirmSubmit = async type => {
    if (this.state.sortState === 'Editing') {
      message.error(
        t('There is unfinished editing in <{{title}}>. Please complete before proceeding.', {
          title: t('Special Agreement'),
        })
      );

      return;
    }

    const confrimArr = ['declaration', 'disputeSettlement'];
    this.props.form.validateFields(confrimArr).then(async values => {
      const params = {
        disputeSettlement: values.disputeSettlement,
        goodsId: this.goodsId,
        isUpdateSpecialAgreement: false,
        isSubmit: false,
        isShownFinance: values.declaration?.indexOf('showFinancial') > -1 ? 1 : 0,
        isShownHealth: values.declaration?.indexOf('showHealth') > -1 ? 1 : 0,
      };
      await NewMarketService.GoodsFileDisplayMgmtService.save(params);
      if (type === 'save') {
        message.success(t('Save successfully'));
        return;
      }

      if (type === 'next') {
        message.success(t('Save successfully'));
        this.goToNextPage();
        return;
      }
      // 后续是submit流程

      NewMarketService.GoodsPackageValidateService.validateGoods(this.goodsId).then(goodsValidation => {
        if (goodsValidation.errors?.length) {
          generalConfirm({
            title: t('Validation Response'),
            content: (
              <ul>
                {goodsValidation.errors.map(message => (
                  <li>{message}</li>
                ))}
              </ul>
            ),
          });
          return;
        }

        NewMarketService.PackageGoodsStepInfoMgmtService.doneStep({
          // 记录步骤
          refId: this.goodsId, // 如果是产品组合就传对应的packageId
          type: 2, // 商品 2， 产品组合 1
          stepNo: 3, // 步骤，参见menu里面的数字
          isNew: false, // 传一个是否是add状态，只有在每一个的第一步才会有这个参数
          isSubmit: type === 'submit', // 是否是submit操作，当同时存在next和submit的时候，都要调这个方法，用这个区分是否是submit
        }).then(res1 => {
          if (res1.success) {
            message.success(t('Publish successfully'));
            this.props.navigate('/market/goods/search');
          }
        });
      });
    });
  };

  goToNextPage = () => {
    this.props.navigate(`/market/goods/notification-config?goodsId=${this.goodsId}&layoutId=${this.layoutId}`, {
      state: {
        mode: this.state.mode || 'edit',
        queryMode: this.state.queryMode,
      },
    });
  };

  getBlockContent = item => {
    switch (item.href) {
      case 'declaration':
        return this.getDeclaration();
      case 'insured-notice':
      case 'insurance-case':
      case 'policy-manual':
      case 'regulation-of-products':
        return <React.Fragment>{this.getTable(item)}</React.Fragment>;
    }
  };

  getDeclaration = () => {
    const { initialValueMap, isShowEditButton } = this.state;
    const options = [
      {
        label: t('Show Health Declaration'),
        value: 'showHealth',
      },
      {
        label: t('Show Financial Declaration'),
        value: 'showFinancial',
      },
    ];
    const tempArr = [];
    if (initialValueMap.isShownHealth && initialValueMap.isShownHealth === 1) {
      tempArr.push('showHealth');
    }
    if (initialValueMap.isShownFinance && initialValueMap.isShownFinance === 1) {
      tempArr.push('showFinancial');
    }
    return (
      <FormItem name="declaration" initialValue={tempArr} label="">
        <Checkbox.Group disabled={this.state.mode === 'view' || !isShowEditButton} options={options} />
      </FormItem>
    );
  };

  saveSort = item => {
    const { initialValueMap } = this.state;
    const existData = initialValueMap.goodsMediaMap[item.value];
    const sortArr = [];
    if (existData) {
      if (item.value === 7) {
        // Customized Document
        for (let i = 0; i < this.state.customDocList.length; i++) {
          const element = this.state.customDocList[i];
          for (let j = 0; j < element.list.length; j++) {
            sortArr.push({
              id: element.list[j].id,
              orderNo: i + j + 1,
            });
          }
        }
      } else {
        existData.forEach((i, index) => {
          sortArr.push({
            id: i.id,
            orderNo: index + 1,
          });
        });
      }
      NewMarketService.GoodsFileDisplayMgmtService.updateOrderNum(sortArr).then(res => {
        if (res.success) {
          message.success(t('sort successfully'));
          this.queryGoodsFile();
        }
      });
    }
  };

  sortChange = (record, array) => {
    const { initialValueMap } = this.state;
    if (initialValueMap.goodsMediaMap) {
      initialValueMap.goodsMediaMap[record.mediaCategory] = array;
      this.setState({
        initialValueMap,
      });
    }
  };

  downloadMediaFile = record => {
    const formData = new FormData();
    formData.append('id', record.id);
    formData.append('url', record.url);
    downloadFile('/api/market/file/downloadMediaFile', formData, 'post', (res, errorMsg) => {
      if (res === 'error' && !errorMsg) {
        message.error(t('System error'));
      }
      if (res === 'success') {
        message.success(t('Download Successfully'));
      }
    });
  };

  renderNewTable = () => {
    return (
      <div className="sub-block" key="goods-file" id="goods-file">
        <div className="sub-block-title">{t('Goods File Management')}</div>
        <FileManagement
          mode={this.state.mode}
          goodsId={this.goodsId}
          layoutId={this.layoutId}
          zoneId={this.state.zoneId}
        />
      </div>
    );
  };

  getTable = item => {
    const { initialValueMap, isShowEditButton } = this.state;
    const { enums } = this.props;
    let data = [];
    if (initialValueMap.goodsMediaMap && initialValueMap.goodsMediaMap[item.value]) {
      data = initialValueMap.goodsMediaMap[item.value];
      data.map((i, index) => {
        i.index = index + 1;
        i.key = i.id;
      });
    }
    if (data.length === 0) {
      return null;
    }
    const columns = [
      {
        title: t('number'),
        dataIndex: 'index',
        width: 80,
        render: (text, record) => {
          return <span>{record.index}</span>;
        },
      },
      {
        title: t('Display Name'),
        dataIndex: 'displayName',
        width: 140,
      },
      {
        title: t('File'),
        dataIndex: 'fileName',
        width: 200,
        render: (text, record) => {
          if (!text) {
            return <span>--</span>;
          }
          return (
            <span>
              <Icon type="paper-clip" style={{ marginRight: 4 }} />
              <a onClick={() => this.downloadMediaFile(record)}>{text}</a>
            </span>
          );
        },
      },
      {
        title: t('File Declaration'),
        dataIndex: 'fileDeclaration',
        width: 180,
        render: (text, record) => {
          return <span>{text || '--'}</span>;
        },
      },
      {
        title: t('Data Address'),
        dataIndex: 'dataAccess',
        width: 180,
        render: (text, record) => {
          return <span>{text || '--'}</span>;
        },
      },
      {
        title: t('language'),
        dataIndex: 'language',
        width: 100,
        render: (text, record) => {
          const obj = (enums.language || []).filter(i => i.dictValue === text);
          if (obj && obj.length > 0) {
            text = obj[0].itemName;
          }
          return <span>{text || '--'}</span>;
        },
      },
    ];
    return (
      <EditableTable
        hideAdd
        disabled
        loading={this.state.tableLoading1}
        scroll={{ x: 1008 }}
        columns={columns}
        data={data}
        sortable
        onSortUp={(record, index, array) => this.sortChange(record, array)}
        onSortDown={(record, index, array) => this.sortChange(record, array)}
        onSaveSort={() => this.saveSort(item)}
        onSortTop={(record, index, array) => this.sortChange(record, array)}
        onDelete={this.deleteFile}
      />
    );
  };

  deleteFile = key => {
    NewMarketService.GoodsFileDisplayMgmtService.deleteGoodsMedia({
      id: key,
      confirmAndSkipWarning: true,
    }).then(res => {
      if (res.success) {
        message.success(t('Deleted successfully'));
        this.queryGoodsFile();
      }
    });
  };

  // 处理销售渠道值的变化
  handleCoveragePlanCodeChange = (value, option) => {
    setTimeout(() => {
      this.setState({
        renderRandom: Math.random(),
      });
    }, 500);

    // 选择All的时候清空其余选项
    if (option.length) {
      if (option[option.length - 1].key === '0') {
        return ['0'];
      }

      if (option[0].key === '0' && option.length > 1) {
        value = value.slice(1);
      }
    }

    return value;
  };

  // ----------

  onBack = () => {
    this.props.navigate(`/market/goods/salesAttributes?goodsId=${this.goodsId}&layoutId=${this.layoutId}`, {
      state: {
        mode: this.state.mode || 'edit',
        queryModel: this.state.queryModel,
      },
    });
  };

  onEdit = () => {
    if (this.state.mode !== 'view') {
      return;
    }
    this.setState({ mode: 'edit' });
  };

  setLoadingStatus = status => {
    this.setState({
      fileLoading: status,
    });
  };

  renderEditSaveBtn() {
    const { isShowEditButton, mode } = this.state;
    if (!isShowEditButton || this.props.envConfig.env === 'prd') {
      return null;
    }

    if (mode === 'view') {
      return (
        <Button size="large" onClick={() => this.onEdit()}>
          {t('Edit')}
        </Button>
      );
    }

    return (
      <Button size="large" onClick={() => this.onConfirmSubmit('save')}>
        {t('Save')}
      </Button>
    );
  }

  render() {
    const { data, mode, queryMode, isShowEditButton, initialValueMap, showNotification } = this.state;

    // 如果老数据没有配置的话，隐藏这个区块
    const avaiableData = data.filter(item => {
      if (!item.value) {
        return initialValueMap.isShownHealth === 1 || initialValueMap.isShownFinance === 1;
      }
      let dataList = [];
      if (initialValueMap?.goodsMediaMap?.[item.value]) {
        dataList = initialValueMap.goodsMediaMap[item.value];
      }

      if (dataList.length === 0) {
        return false;
      }
      return true;
    });

    const readOnly = mode === 'view' || !isShowEditButton || this.props.envConfig.env === 'prd';

    return (
      <>
        <FMarketHeader backPath="/market/goods/search" subMenu="Marketing_Goods" />
        <div className="flex" style={{ height: 'calc(100% - 50px)' }}>
          <Sider width={208} className="market-sider">
            <FMarketMenu
              page="GOODS"
              type={mode}
              queryMode={queryMode}
              defaultSelectedKeys={['4']}
              category={2}
              goodsId={this.goodsId}
              layoutId={this.layoutId}
              navigate={this.props.navigate}
            />
          </Sider>
          <Content className="market-content">
            <Skeleton active loading={this.state.skeletonVisible}>
              <div className="right-content-wrapper">
                <div className="right-content">
                  <Section title={t('Documents Display')}>
                    {/* <div style={{ paddingTop: 32 }} className="block-title">Documents Display</div> */}
                    {this.renderNewTable()}
                    {avaiableData
                      .filter(i => i.value !== 7)
                      .map((item, index) => {
                        return (
                          <div className="sub-block" key={index} id={item.href}>
                            <div className="sub-block-title">{item.title}</div>
                            {this.getBlockContent(item)}
                          </div>
                        );
                      })}
                    <div className="sub-block" key="other" id="other">
                      <div className="sub-block-title">{t('Other')}</div>
                      <OtherDisplay
                        disabled={readOnly}
                        enums={this.props.enums}
                        stateData={this.state}
                        setSAValue={value =>
                          this.setState({
                            initialValueMap: { ...this.state.initialValueMap, specialAgreementList: value },
                          })
                        }
                        goodsId={this.goodsId}
                        queryGoodsFile={this.queryGoodsFile}
                      />
                    </div>
                  </Section>
                </div>
              </div>
            </Skeleton>
          </Content>
          <div className="bottom-action-bar">
            {readOnly ? null : (
              <Button size="large" onClick={() => this.onConfirmSubmit('submit')} type="primary">
                {t('Publish')}
              </Button>
            )}
            {this.props.envConfig.marketNotificationConfigEnable && showNotification && readOnly && (
              <Button size="large" onClick={this.goToNextPage} type="primary">
                {t('Next')}
              </Button>
            )}
            {this.renderEditSaveBtn()}
            {this.props.envConfig.marketNotificationConfigEnable && showNotification && !readOnly ? (
              <Button size="large" onClick={() => this.onConfirmSubmit('next')} type="default">
                {t('Next')}
              </Button>
            ) : null}
          </div>
        </div>
      </>
    );
  }
}

export default DocDisplay;
