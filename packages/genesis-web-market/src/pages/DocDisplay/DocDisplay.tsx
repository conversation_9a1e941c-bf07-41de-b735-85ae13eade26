import { Form, Layout } from 'antd';
import { useTranslation } from 'react-i18next';
import DocDisplayContainer from './index';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import { useSelector } from 'react-redux';
import { selectEnums, selectEnvConfig, selectPermissionCheckMap, selectUserState } from '@market/redux/selector';
import { useLocation, useNavigate } from 'react-router-dom';

interface Props {

}

export const DocDisplay = ({

}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const location = useLocation();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const {l10n} = useL10n();
  const permissionMap = useSelector(selectPermissionCheckMap);
  const enums = useSelector(selectEnums);
  const userInfo = useSelector(selectUserState);
  const envConfig = useSelector(selectEnvConfig);

  console.log('location', location);

  return (
    <Layout className="market-layout goods-doc-display">
      <Form className='h-full' form={form} layout='vertical'>
        <DocDisplayContainer
          form={form}
          l10n={l10n}
          enums={enums}
          envConfig={envConfig}
          userInfo={userInfo}
          location={location}
          navigate={navigate}
          isSuperUser={!!permissionMap['market.edit-all']}
          hasEditAuth={!!permissionMap['market.edit']}
        />
      </Form>
    </Layout>
  );
};

export default DocDisplay;