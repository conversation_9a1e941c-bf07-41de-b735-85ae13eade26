import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Divider, Form, Modal, Pagination, Radio, UploadProps, message } from 'antd';

import classNames from 'classnames/bind';
import { isEmpty, keyBy } from 'lodash-es';

import { Drawer, Icon, UploadFileItem } from '@zhongan/nagrand-ui';

import { GoodsMediaDTO } from 'genesis-web-service/service-types/market-types/package';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import { isImgFile } from 'genesis-web-shared/lib/util/fileType';

import { NAME_CONNECTOR_STR } from '@market/common/constant';
import { BizDict, DetailPageMode } from '@market/common/interface';
import UploadModal from '@market/pages/DocDisplay/FileManagement/components/UploadModal/UploadModal';
import { NewMarketService } from '@market/services/market/market.service.new';
import { downloadFile } from '@market/util';
import { copyText } from '@market/utils/util';

import { FileGroup, FileInfo, FileType, UploadType } from '../../interface';
import FileTrigger from '../FileTrigger';
import UploadDragger from '../UploadDragger/UploadDragger';
import styles from './FileManagementDrawer.module.scss';

const PAGE_SIZE = 10;

const cx = classNames.bind(styles);

interface Props {
  visible: boolean;
  mode: DetailPageMode;
  layoutId: string;
  goodsId: string;
  zoneId: string;
  fileGroup?: FileGroup;
  uploadProps: UploadProps;
  uploadType: UploadType;
  languageBizDicts: BizDict[];
  documentTypeBizDicts: BizDict[];
  onClose: () => void;
  onUploadUrl: () => void;
  setUploadType: (type: UploadType) => void;
  deleteFile: (file: FileInfo) => void;
}

export const FileManagementDrawer = ({
  visible,
  mode,
  layoutId,
  zoneId,
  goodsId,
  fileGroup,
  uploadProps,
  uploadType,
  languageBizDicts,
  documentTypeBizDicts,
  onClose,
  onUploadUrl,
  setUploadType,
  deleteFile,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [page, setPage] = useState(1);
  const { dateFormat, l10n } = useL10n();

  const languageBizDictMap = useMemo(() => keyBy(languageBizDicts, 'dictValue'), [languageBizDicts]);
  const documentTypeBizDictMap = useMemo(() => keyBy(documentTypeBizDicts, 'dictValue'), [documentTypeBizDicts]);

  const readOnly = mode === DetailPageMode.view;

  const fileList = fileGroup?.files || [];
  const [uploadedFile, setUploadedFile] = useState<FileType & FileInfo>();

  const closeDrawer = useCallback(() => {
    setPage(1);
    onClose();
  }, [onClose]);

  const downloadMediaFile = (file: FileInfo) => {
    downloadFile(`/api/market/file/${file.unicodeAddress!}`, {}, 'get', (res: string, errorMsg: string) => {
      if (res === 'error' && !errorMsg) {
        message.error(t('System error'));
      }
      if (res === 'success') {
        message.success(t('Download Successfully'));
      }
    });
  };

  const renderIconsByType = useCallback(
    (file: FileInfo) => {
      if (file.uploadType === UploadType.file) {
        if (readOnly) {
          return [
            {
              key: 'download',
              icon: (
                <span onClick={() => downloadMediaFile(file)}>
                  <Icon type="download" />
                </span>
              ),
            },
          ];
        }
        return [
          {
            key: 'edit',
            icon: (
              <span
                onClick={() => {
                  const { fileName, unicodeAddress, fileAddress } = file;
                  setUploadedFile({
                    ...file,
                    name: fileName ?? '',
                    fileUniqueCode: unicodeAddress,
                    localUrl: fileAddress,
                    uid: unicodeAddress ?? '',
                  });
                }}
              >
                <Icon type="edit" />
              </span>
            ),
          },
          {
            key: 'delete',
            icon: (
              <span onClick={() => deleteFile(file)}>
                <Icon type="delete" />
              </span>
            ),
          },
          {
            key: 'download',
            icon: (
              <span onClick={() => downloadMediaFile(file)}>
                <Icon type="download" />
              </span>
            ),
          },
        ];
      }

      if (readOnly) {
        return [
          {
            key: 'copy',
            icon: (
              <span
                onClick={() =>
                  copyText(file.fileName, () => {
                    message.success(t('Copy Successfully'));
                  })
                }
              >
                <Icon type="copy" />
              </span>
            ),
          },
        ];
      }

      return [
        {
          key: 'delete',
          icon: (
            <span onClick={() => deleteFile(file)}>
              <Icon type="delete" />
            </span>
          ),
        },
        {
          key: 'copy',
          icon: (
            <span
              onClick={() =>
                copyText(file.fileName, () => {
                  message.success(t('Copy Successfully'));
                })
              }
            >
              <Icon type="copy" />
            </span>
          ),
        },
      ];
    },
    [deleteFile, downloadMediaFile, readOnly, t]
  );

  const renderEffectivePeriod = useCallback(
    (file: FileInfo) => dateFormat?.getDateTimeRangeString(file.effectiveDate, file.expiryDate, zoneId) || '',
    [dateFormat, zoneId]
  );

  const renderUploadSection = useCallback(() => {
    if (readOnly) {
      return null;
    }
    return (
      <React.Fragment>
        <Form>
          <Form.Item label="Upload by">
            <Radio.Group
              value={uploadType}
              onChange={evt => {
                setUploadType(evt.target.value);
              }}
            >
              <Radio value={UploadType.file}>{t('File')}</Radio>
              <Radio value={UploadType.url}>{t('URL')}</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
        {uploadType === UploadType.url ? (
          <FileTrigger onClick={onUploadUrl} />
        ) : (
          <UploadDragger uploadProps={uploadProps} />
        )}
      </React.Fragment>
    );
  }, [onUploadUrl, readOnly, setUploadType, t, uploadProps, uploadType]);

  const renderFileListSection = useCallback(() => {
    if (fileList.length === 0) {
      return null;
    }
    return (
      <React.Fragment>
        <Divider type="horizontal" style={{ margin: '40px 0 16px 0' }} />
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <div style={{ fontWeight: 700, marginBottom: 8 }}>{t('File Management')}</div>
            {fileList.length > 10 ? (
              <Pagination
                current={page}
                pageSize={PAGE_SIZE}
                total={fileList.length}
                onChange={_page => {
                  setPage(_page);
                }}
                size="small"
              />
            ) : null}
          </div>
          {fileList.slice((page - 1) * PAGE_SIZE, page * PAGE_SIZE).map(file => (
            <UploadFileItem
              style={{ marginBottom: 16 }}
              fileName={file.fileName}
              fileUrl={`/api/market/file/${file.unicodeAddress!}`}
              needPreview={isImgFile(file.fileName)}
              isShowHover
              hoverInfoList={renderIconsByType(file)}
              customerDom={
                <div style={{ width: 200 }} className={cx('file-custom-dom')}>
                  <div>{t('Effective Period')}</div>
                  <div style={{ marginTop: 4 }}>{renderEffectivePeriod(file)}</div>
                </div>
              }
              toolTip={
                file.fileDeclaration || file.version || !isEmpty(file.tags) ? (
                  <div className="text-@text-color-secondary gap-y-3">
                    {file.version && (
                      <div>
                        {t('File Version')}: {file.version}
                      </div>
                    )}
                    {file.fileDeclaration && (
                      <div>
                        {t('File Declaration')}: {file.fileDeclaration}
                      </div>
                    )}
                    {!isEmpty(file.tags) && (
                      <div>
                        {t('Tag')}: {file.tags?.join(NAME_CONNECTOR_STR)}
                      </div>
                    )}
                  </div>
                ) : undefined
              }
            />
          ))}
        </div>
      </React.Fragment>
    );
  }, [fileList, page, renderEffectivePeriod, renderIconsByType, t]);

  const saveGoodsMedia = useCallback(
    (param: GoodsMediaDTO) =>
      NewMarketService.GoodsFileDisplayMgmtService.saveGoodsMedia({
        ...param,
        confirmAndSkipWarning: 1,
      }).then(() => {
        message.success(t('Submit successfully'));
        closeDrawer();
      }),
    [closeDrawer, t]
  );

  const saveGoodsMediaValidate = useCallback(
    (param: GoodsMediaDTO) => {
      NewMarketService.GoodsFileDisplayMgmtService.saveGoodsMediaValidate(param).then(res => {
        if (res.success) {
          saveGoodsMedia(param);
        } else if (res.errors && res.errors?.length > 0) {
          message.error(res.errors.join('; '));
        } else if (res.warns && res.warns?.length > 0) {
          Modal.confirm({
            title: res.warns.join(';'),
            onOk: () => saveGoodsMedia(param),
            onCancel: () => Promise.resolve(),
          });
        }
      });
    },
    [saveGoodsMedia]
  );

  const onSubmit = useCallback(() => {
    if (!fileGroup) {
      return;
    }

    const param: GoodsMediaDTO = {
      docType: fileGroup.fileType,
      layoutId: +layoutId,
      goodsId,
      language: fileGroup.language,
      id: fileGroup?.id,
      orderNo: 1,
      goodsMediaSubList:
        fileGroup.files.map(file => ({
          fileUniqueCode: file.unicodeAddress,
          url: file.fileAddress,
          fileName: file.fileName,
          effectiveTime: file.effectiveDate,
          expireTime: file.expiryDate,
          fileDeclaration: file.fileDeclaration,
          version: file.version,
          tags: file.tags,
          dataAccess: file.uploadType === UploadType.url ? file.fileName : undefined,
          id: file?.id,
        })) || [],
    };

    saveGoodsMediaValidate(param);
  }, [fileGroup, goodsId, layoutId, saveGoodsMediaValidate]);

  return (
    <Drawer
      open={visible}
      title={t('Goods File Management')}
      width="small"
      onClose={closeDrawer}
      zIndex={100}
      onSubmit={onSubmit}
      submitBtnShow={!readOnly}
      readonly={readOnly}
    >
      <div className={cx('container')}>
        <div className={cx('basic-info')}>
          <div className={cx('info-item')}>
            <div className={cx('title')}>{t('File Type')}</div>
            <div className={cx('value')}>
              {fileGroup?.fileType ? documentTypeBizDictMap[fileGroup.fileType]?.dictValueName : ''}
            </div>
          </div>
          <div className={cx('info-item')}>
            <div className={cx('title')}>{t('Language')}</div>
            <div className={cx('value')}>
              {fileGroup?.language ? languageBizDictMap[fileGroup.language]?.dictValueName : ''}
            </div>
          </div>
        </div>
        {renderUploadSection()}
        {renderFileListSection()}
      </div>
      <UploadModal
        fileGroup={fileGroup}
        visible={uploadedFile !== undefined}
        uploadedFile={uploadedFile}
        uploadProps={uploadProps}
        onClose={editFile => {
          if (editFile && fileGroup) {
            const index = fileGroup?.files?.findIndex(file => file.unicodeAddress === editFile.unicodeAddress);
            fileGroup.files[index] = editFile;
          }
          setUploadedFile(undefined);
        }}
        zoneId={zoneId}
        uploadType={uploadType}
      />
    </Drawer>
  );
};

export default FileManagementDrawer;
