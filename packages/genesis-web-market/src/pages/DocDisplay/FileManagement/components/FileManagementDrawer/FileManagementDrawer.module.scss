.container {
  .basic-info {
    display: flex;
    margin-bottom: 24px;
    background-color: var(--layout-body-background);
    padding: 16px;
    border-radius: 4px;
    justify-content: space-between;

    .info-item {
      width: 280px;
      line-height: 22px;
      font-weight: 700;
      font-size: 14px;

      .title {
        color: $label;
      }

      .value {
        color: var(--text-color);
        margin-top: 2px;
      }
    }
  }

  .file-custom-dom {
    font-size: 12px;
    line-height: 16px;
    font-weight: 500;
    color: $label;
  }
}
