import { useTranslation } from 'react-i18next';

import Icon from '@ant-design/icons';
import { Upload, UploadProps } from 'antd';

import classNames from 'classnames/bind';

import DraggerFileIcon from '@market/asset/svg/dragger_file_icon.svg';

import styles from './UploadDragger.module.scss';

const cx = classNames.bind(styles);

const { Dragger } = Upload;

interface Props {
  uploadProps: UploadProps;
  text?: string;
}

export const UploadDragger = ({ uploadProps, text }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  return (
    <Dragger className={cx('upload-dragger')} {...uploadProps}>
      <p className={cx('upload-dragger-icon')}>
        <Icon component={DraggerFileIcon} style={{ fontSize: 40 }} />
      </p>
      <p className={cx('upload-text')}>{t('Click or drag the file here to upload')}</p>
      <p className={cx('upload-hint')}>{text || t('You can only upload PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG')}</p>
    </Dragger>
  );
};

export default UploadDragger;
