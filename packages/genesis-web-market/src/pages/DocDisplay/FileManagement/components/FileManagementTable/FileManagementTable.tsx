import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import type { ColumnProps } from 'antd/es/table';

import classNames from 'classnames/bind';
import { keyBy } from 'lodash-es';

import { DeleteAction, EditAction, Table, ViewAction } from '@zhongan/nagrand-ui';

import { BizDict } from '@market/common/interface';

import { FileGroup } from '../../interface';
import styles from './FileManagementTable.module.scss';

const cx = classNames.bind(styles);

interface Props {
  disabled?: boolean;
  data: FileGroup[];
  languageBizDicts: BizDict[];
  documentTypeBizDicts: BizDict[];
  onView: (record: FileGroup) => void;
  onEdit: (record: FileGroup) => void;
  onDelete: (record: FileGroup) => void;
}

export const FileManagementTable = ({
  disabled = false,
  data,
  languageBizDicts,
  documentTypeBizDicts,
  onView,
  onEdit,
  onDelete,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  const languageBizDictMap = useMemo(() => keyBy(languageBizDicts, 'dictValue'), [languageBizDicts]);
  const documentTypeBizDictMap = useMemo(() => keyBy(documentTypeBizDicts, 'dictValue'), [documentTypeBizDicts]);

  const columns = useMemo(() => {
    const tempColumns: ColumnProps<FileGroup>[] = [
      {
        title: t('File Type'),
        dataIndex: 'fileType',
        render(text: string, record, index) {
          if (record.rowSpan) {
            return {
              children: documentTypeBizDictMap[text]?.dictValueName || text,
              props: {
                rowSpan: record.rowSpan,
              },
            };
          }
          return {
            children: '',
            props: {
              rowSpan: 0,
            },
          };
        },
      },
      {
        title: t('Language'),
        dataIndex: 'language',
        render(text: string, record, index) {
          return languageBizDictMap[text]?.dictValueName || text;
        },
      },
      {
        title: t('Number of Files/URLs'),
        width: 200,
        render: (text, record) => <span className={cx('url-number')}>{record.goodsMediaSubCount}</span>,
      },
    ];

    if (!disabled) {
      tempColumns.push({
        width: 130,
        title: t('Actions'),
        render(text, record, index) {
          return (
            <span className={cx('action-icons')}>
              <ViewAction onClick={() => onView(record)} />
              <EditAction onClick={() => onEdit(record)} />
              <DeleteAction
                doubleConfirmType="popconfirm"
                onClick={() => onDelete(record)}
                deleteConfirmContent={t('Are you sure to delete this record?')}
              />
            </span>
          );
        },
      });
    } else {
      tempColumns.push({
        width: 120,
        title: t('Actions'),
        render(text, record, index) {
          return (
            <span className={cx('action-icons')}>
              <ViewAction onClick={() => onView(record)} />
            </span>
          );
        },
      });
    }

    return tempColumns;
  }, [disabled, languageBizDictMap, onDelete, onEdit, onView, t, documentTypeBizDictMap]);

  return <Table columns={columns} dataSource={data} bordered scroll={{ y: 600 }} pagination={false} />;
};

export default FileManagementTable;
