import { useTranslation } from 'react-i18next';

import Icon from '@ant-design/icons';

import classNames from 'classnames/bind';

import DraggerFileIcon from '@market/asset/svg/dragger_file_icon.svg';

import styles from './FileTrigger.module.scss';

const cx = classNames.bind(styles);

interface Props {
  onClick: () => void;
}

export const FileTrigger = ({ onClick }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  return (
    <div key="file-trigger" onClick={onClick} className={cx('file-trigger')}>
      <p className={cx('file-trigger-icon')}>
        <Icon component={DraggerFileIcon} style={{ fontSize: 40 }} />
      </p>
      <p className={cx('text')}>{t('Click here to upload')}</p>
      <p className={cx('hint')}>{t('Please copy your File URL first.')}</p>
    </div>
  );
};

export default FileTrigger;
