import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { DatePicker, Form, Input, Modal, Upload, UploadProps } from 'antd';

import classNames from 'classnames/bind';
import { Moment } from 'moment';

import { Icon, UploadFileItem } from '@zhongan/nagrand-ui';

import { TimeFormatEnum } from 'genesis-web-service/lib/system/system.interface';
import { useL10n } from 'genesis-web-shared/lib/l10n';
import { isImgFile } from 'genesis-web-shared/lib/util/fileType';

import InputTags from '@market/components/InputTags';

import { FileGroup, FileType, UploadType } from '../../interface';
import styles from './UploadModal.module.scss';

const cx = classNames.bind(styles);

interface Props {
  visible: boolean;
  onClose: () => void;
  uploadedFile?: FileType;
  uploadProps: UploadProps;
  uploadType: UploadType;
  fileGroup?: FileGroup;
  zoneId: string;
}

export const UploadModal = ({
  visible,
  onClose,
  uploadedFile,
  uploadProps,
  uploadType,
  zoneId,
  fileGroup,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();
  const { dateFormat } = useL10n();

  const effectivePeriod = Form.useWatch('effectivePeriod', form);

  const closeModal = useCallback(() => {
    form.resetFields();
    onClose();
  }, [form, onClose]);

  const onSubmit = useCallback(() => {
    if (!fileGroup) {
      return;
    }
    form
      .validateFields()
      .then(
        (values: {
          effectivePeriod: [Moment, Moment];
          fileDeclaration?: string;
          version?: string;
          customizeUrl?: string;
          tags?: string[];
        }) => {
          fileGroup.files.push({
            effectiveDate: dateFormat!.formatTz(values.effectivePeriod[0], zoneId)!,
            expiryDate: dateFormat!.formatTz(values.effectivePeriod[1], zoneId)!,
            fileType: fileGroup.fileType,
            language: fileGroup.language,
            fileName: (values.customizeUrl || uploadedFile?.name)!,
            fileDeclaration: values.fileDeclaration,
            version: values.version,
            tags: values.tags,
            unicodeAddress: uploadedFile?.fileUniqueCode,
            fileAddress: uploadedFile?.localUrl,
            uploadType,
            orderNo: fileGroup.files.length + 1,
          });

          onClose();
          form.resetFields();
        }
      );
  }, [fileGroup, form, dateFormat, zoneId, uploadedFile, uploadType, onClose]);

  return (
    <Modal
      zIndex={101}
      title={t('Upload')}
      open={visible}
      width={480}
      className={cx('upload-modal')}
      okText={t('Submit')}
      okButtonProps={{
        disabled: !effectivePeriod,
      }}
      onCancel={closeModal}
      onOk={onSubmit}
      closable={false}
      destroyOnClose
    >
      {uploadType === UploadType.file ? (
        <UploadFileItem
          style={{ marginBottom: 24 }}
          fileName={uploadedFile?.name}
          fileUrl={`/api/market/file/${uploadedFile?.fileUniqueCode}`}
          needPreview={isImgFile(uploadedFile?.name as string)}
          isShowHover
          hoverInfoList={[
            {
              key: 're-upload',
              icon: (
                <Upload {...uploadProps}>
                  <span>
                    <Icon type="reload" />
                  </span>
                </Upload>
              ),
            },
          ]}
        />
      ) : null}
      <Form layout="vertical" form={form}>
        {uploadType === UploadType.url ? (
          <Form.Item
            required
            label={t('File')}
            name="customizeUrl"
            rules={[
              {
                required: true,
                message: t('Please input'),
              },
            ]}
          >
            <Input addonBefore="URL" placeholder={t('Please input or paste the URL here')} />
          </Form.Item>
        ) : null}
        <Form.Item
          required
          label={t('Effective Period')}
          name="effectivePeriod"
          rules={[
            {
              required: true,
              message: t('Please select'),
            },
          ]}
        >
          <DatePicker.RangePicker
            showTime
            placeholder={[t('Start date'), t('End date')]}
            style={{ width: '100%' }}
            format={
              dateFormat?.timeFormatByZeus === TimeFormatEnum.NoSecond
                ? dateFormat?.dateTimeFormatNoSecond
                : dateFormat?.dateTimeFormat
            }
          />
        </Form.Item>
        <Form.Item label={t('File Version')} name="version">
          <Input style={{ width: '100%' }} placeholder={t('Please input')} />
        </Form.Item>
        <Form.Item label={t('File Declaration')} name="fileDeclaration">
          <Input.TextArea placeholder={t('Please input')} />
        </Form.Item>
        <Form.Item label={t('Tag')} name="tags">
          <InputTags />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UploadModal;
