import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Cascader, Modal, UploadProps, message } from 'antd';
import { RcFile, UploadChangeParam, UploadFile } from 'antd/lib/upload';

import classNames from 'classnames/bind';
import { groupBy } from 'lodash-es';
import moment from 'moment';

import { Icon } from '@zhongan/nagrand-ui';

import { DocumentTypeSubCategoryEnum } from 'genesis-web-service';
import { security } from 'genesis-web-shared';

import { DetailPageMode } from '@market/common/interface';
import { useBizDict } from '@market/hook/bizDict';
import { NewMarketService } from '@market/services/market/market.service.new';

import styles from './FileManagement.module.scss';
import FileManagementDrawer from './components/FileManagementDrawer';
import FileManagementTable from './components/FileManagementTable';
import UploadModal from './components/UploadModal/UploadModal';
import { useGoodsFile } from './hook';
import { FileGroup, FileInfo, FileType, UploadType } from './interface';

interface CascaderOption {
  label: string;
  value: string;
  disabled?: boolean;
  children?: CascaderOption[];
}

const cx = classNames.bind(styles);

interface Props {
  mode: DetailPageMode;
  goodsId: string;
  layoutId: string;
  zoneId: string;
}

export const FileManagement = ({ mode, goodsId, layoutId, zoneId }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [drawerMode, setDrawerMode] = useState<DetailPageMode>(DetailPageMode.view);
  const [drawerRecord, setDrawerRecord] = useState<FileGroup>();
  const [modalVisible, setModalVisible] = useState(false);
  /* ============== 枚举使用start ============== */
  const documentTypeBizDicts = useBizDict('goodsDocType');
  const languageBizDicts = useBizDict('language');
  /* ============== 枚举使用end ============== */
  const [uploadType, setUploadType] = useState<UploadType>(UploadType.file);
  const [uploadedFile, setUploadedFile] = useState<FileType>();

  const { fileGroupList, queryFileList } = useGoodsFile(goodsId);
  const readOnly = mode === 'view';

  const combinedOptions = useMemo(() => {
    const _fileGroupMap = groupBy(fileGroupList, item => item.fileType);
    const validBizDictOptions: CascaderOption[] = documentTypeBizDicts.map(item => ({
      label: item.dictValueName,
      value: item.dictValue,
      children: [],
    }));

    validBizDictOptions.forEach(option => {
      const languageOptions: CascaderOption[] = languageBizDicts.map(languageBizDict => ({
        label: languageBizDict.dictValueName,
        value: languageBizDict.dictValue,
        children: [],
        disabled: !!_fileGroupMap[option.value]?.find(_item => `${languageBizDict.dictValue}` === `${_item.language}`),
      }));

      languageOptions.unshift({
        label: t('LANGUAGE'),
        value: 'LANGUAGE',
        disabled: true,
      });

      option.children = languageOptions;
    });

    // TODO 已经配置过的需要设置disable
    validBizDictOptions.unshift({
      label: t('FILE TYPE'),
      value: 'FILE TYPE',
      disabled: true,
    });

    return validBizDictOptions;
  }, [documentTypeBizDicts, languageBizDicts, t, fileGroupList]);

  const beforeUpload = useCallback(
    (file: RcFile) => {
      const supported = ['.xls', '.xlsx', '.doc', '.docx', '.pdf', '.jpg', '.png'];
      const index = file.name.lastIndexOf('.');
      const fileType = file.name.slice(index);
      if (supported.indexOf(fileType) < 0) {
        message.error(t('You can only upload xls, xlsx, doc, docx, pdf, jpg or png.'));
        return false;
      }
    },
    [t]
  );

  const uploadProps: UploadProps = {
    multiple: false,
    action: '/api/market/file/uploadPublicFile',
    headers: {
      ...security.csrf(),
    },
    data: {
      fileAdditionalInfoSubCategory: DocumentTypeSubCategoryEnum.GoodsDocument,
      additionalInfoId: goodsId,
    },
    accept: '.pdf, .xls, .xlsx, .png, .jpg, .jpeg, .doc, .docx',
    maxCount: 1,
    showUploadList: false,
    beforeUpload,
    onChange: (
      info: UploadChangeParam<
        UploadFile<{
          value: {
            fileUrl: string;
            fileUniqueCode: string;
          };
          success: boolean;
        }>
      >
    ) => {
      if (info.file.status === 'done') {
        if (info.file?.response?.success) {
          info.file.localUrl = info.file.response.value.fileUrl;
          info.file.fileUniqueCode = info.file.response.value.fileUniqueCode;
          message.success(t('Upload Successfully'));
          setUploadedFile(info.file);
          setModalVisible(true);
        } else {
          message.error(t('Upload Failed'));
        }
      } else if (info.file.status === 'error') {
        message.error(t('Upload Failed'));
      }
    },
  };

  const queryFiles = useCallback(
    (record: FileGroup) =>
      NewMarketService.GoodsFileDisplayMgmtService.queryGoodsMediaSub({
        goodsId: +goodsId,
        id: record.id!,
      }).then(res =>
        res.map(subMedia => ({
          effectiveDate: subMedia.effectiveTime,
          expiryDate: subMedia.expireTime,
          fileName: subMedia.fileName!,
          fileDeclaration: subMedia.fileDeclaration,
          version: subMedia.version,
          tags: subMedia.tags,
          id: subMedia.id,
          fileAddress: subMedia.url!,
          unicodeAddress: subMedia.fileUniqueCode,
          language: record.language,
          fileType: record.fileType,
          uploadType: subMedia.dataAccess ? UploadType.url : UploadType.file,
        }))
      ),
    [goodsId]
  );

  const onView = useCallback(
    (record: FileGroup) => {
      if (!record.id) {
        return;
      }
      queryFiles(record).then(files => {
        record.files = files;
        setDrawerMode(DetailPageMode.view);
        setDrawerRecord(record);
        setDrawerVisible(true);
      });
    },
    [queryFiles]
  );

  const onEdit = useCallback(
    (record: FileGroup) => {
      if (!record.id) {
        return;
      }
      queryFiles(record).then(files => {
        record.files = files;
        setDrawerMode(DetailPageMode.edit);
        setDrawerRecord(record);
        setDrawerVisible(true);
      });
    },
    [queryFiles]
  );

  const deleteFile = useCallback(
    (fileId: number) => {
      if (!drawerRecord) {
        return;
      }
      const deleteRecordIndex = drawerRecord.files.findIndex(item => item.id === fileId);

      drawerRecord.files.splice(deleteRecordIndex, 1);
      setDrawerRecord({
        ...drawerRecord,
      });
    },
    [drawerRecord]
  );

  const validateBeforeDelete = useCallback(
    (file: FileInfo) => {
      const fileId = file.id;
      // 只有删除未生效的不需要给提示语
      if (moment().isAfter(moment(file.expiryDate))) {
        Modal.confirm({
          title: t('This file was effective in the past, are you sure to delete it?'),
          onOk: () => deleteFile(fileId!),
        });
      } else {
        deleteFile(fileId!);
      }
    },
    [deleteFile, t]
  );

  const onDelete = useCallback(
    (record: FileGroup, confirmAndSkipWarning?: boolean) => {
      if (!record?.id) {
        return;
      }
      NewMarketService.GoodsFileDisplayMgmtService.deleteGoodsMedia({
        id: record.id,
        confirmAndSkipWarning,
      }).then(res => {
        if (!res.success) {
          if (res.errors && res.errors.length > 0) {
            message.error(res.errors.join('; '));
          }
          if (res.warns && res.warns.length > 0) {
            Modal.confirm({
              title: res.warns.join('; '),
              onOk: () => {
                onDelete(record, true);
              },
              onCancel: () => Promise.resolve(),
            });
            return;
          }

          return;
        }
        queryFileList();
        message.success(t('Delete Successfully'));
      });
    },
    [queryFileList, t]
  );

  return (
    <div>
      {readOnly ? null : (
        <div
          style={{
            marginBottom: 8,
          }}
        >
          <Cascader
            popupClassName={cx('file-manage-cascader')}
            options={combinedOptions}
            onChange={(value, selectedOptions) => {
              setDrawerRecord({
                fileType: value[0],
                language: value[1],
                goodsMediaSubCount: 0,
                files: [],
              });
              setDrawerMode(DetailPageMode.add);
              setDrawerVisible(true);
            }}
          >
            <Button type="dashed" icon={<Icon type="add" />}>
              {t('Add')}
            </Button>
          </Cascader>
        </div>
      )}
      <FileManagementTable
        disabled={readOnly}
        data={fileGroupList}
        languageBizDicts={languageBizDicts}
        documentTypeBizDicts={documentTypeBizDicts}
        onView={onView}
        onEdit={onEdit}
        onDelete={onDelete}
      />
      <UploadModal
        fileGroup={drawerRecord}
        visible={modalVisible}
        uploadedFile={uploadedFile}
        uploadProps={uploadProps}
        onClose={() => {
          queryFileList();
          setModalVisible(false);
        }}
        zoneId={zoneId}
        uploadType={uploadType}
      />
      <FileManagementDrawer
        layoutId={layoutId}
        goodsId={goodsId}
        zoneId={zoneId}
        visible={drawerVisible}
        mode={drawerMode}
        fileGroup={drawerRecord}
        uploadProps={uploadProps}
        uploadType={uploadType}
        languageBizDicts={languageBizDicts}
        documentTypeBizDicts={documentTypeBizDicts}
        onClose={() => {
          queryFileList();
          setDrawerVisible(false);
        }}
        onUploadUrl={() => {
          setModalVisible(true);
        }}
        setUploadType={setUploadType}
        deleteFile={validateBeforeDelete}
      />
    </div>
  );
};

export default FileManagement;
