import { useCallback, useEffect, useState } from 'react';

import { groupBy, keyBy, orderBy, uniqBy } from 'lodash-es';

import { NewMarketService } from '@market/services/market/market.service.new';

import { FileGroup, FileInfo, UploadType } from './interface';

export const useGoodsFile = (goodsId: number | string) => {
  const [fileGroupList, setFileGroupList] = useState<FileGroup[]>([]);

  const queryFileList = useCallback(() => {
    NewMarketService.GoodsFileDisplayMgmtService.query({ goodsId: +goodsId }).then(res => {
      const fileInfolist: FileInfo[] = (res.goodsMediaList || [])
        .filter(fileConfig => fileConfig.docType) // 没有docType是老数据，这边只展示新版本数据
        .map(fileConfig => ({
          id: fileConfig.id,
          fileType: fileConfig.docType!.toString(),
          language: fileConfig.language!.toString(),
          // effectiveDate: fileConfig.effectiveDate,
          // expiryDate: fileConfig.expiryDate,
          // 编辑的时候单独查一次subMedia
          effectiveDate: '',
          expiryDate: '',
          fileName: (fileConfig.dataAccess || fileConfig.fileName)!,
          unicodeAddress: fileConfig.fileUniqueCode,
          uploadType: fileConfig.fileName ? UploadType.file : UploadType.url,
          fileAddress: (fileConfig.url || fileConfig.dataAccess)!,
          files: [],
          orderNo: fileConfig.orderNo,
          goodsMediaSubCount: fileConfig.goodsMediaSubCount,
        }));

      const fileMapByFileType = groupBy(fileInfolist, 'fileType');
      const tempFileGroupMap: FileGroup[] = [];

      Object.keys(fileMapByFileType).forEach(fileType => {
        const fileInfolistByMediaCategory = fileMapByFileType[fileType];
        const orderedFileConfigList = orderBy(fileInfolistByMediaCategory, ['fileType', 'orderNo'], ['asc', 'asc']);

        const groupList: FileGroup[] = uniqBy(
          orderedFileConfigList.map(item => ({
            fileType: item.fileType,
            language: item.language,
            goodsMediaSubCount: item.goodsMediaSubCount,
            id: item.id,
            files: [],
          })),
          item => `${item.fileType}_${item.language}`
        );

        groupList.forEach((item, index) => {
          if (index === 0 || groupList[index - 1].fileType !== item.fileType) {
            item.rowSpan = groupList.filter(fileConfig => `${fileConfig.fileType}` === item.fileType).length;
          }
          item.files = orderedFileConfigList.filter(
            fileConfig => `${fileConfig.language}` === item.language && `${fileConfig.fileType}` === item.fileType
          );
        });

        tempFileGroupMap.push(...groupList);
      });

      setFileGroupList(tempFileGroupMap);
    });
  }, [goodsId]);

  useEffect(() => {
    queryFileList();
  }, []);

  return {
    fileGroupList,
    queryFileList,
  };
};
