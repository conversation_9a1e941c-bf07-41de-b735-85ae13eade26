import { UploadFile } from 'antd/lib/upload';

export interface FileType extends UploadFile {
  localUrl?: string;
  fileUniqueCode?: string;
}

export enum UploadType {
  file = 'file',
  url = 'url',
}

export interface FileInfo {
  fileType: string;
  language: string;
  goodsMediaSubCount?: number;
  effectiveDate: string;
  expiryDate: string;
  fileDeclaration?: string;
  version?: string;
  id?: number;
  fileName: string;
  unicodeAddress?: string;
  uploadType: UploadType;
  fileAddress?: string;
  orderNo?: number;
  tags?: string[];
}

export interface FileGroup {
  id?: number;
  fileType: string;
  language: string;
  rowSpan?: number;
  goodsMediaSubCount: number;
  files: FileInfo[];
}
