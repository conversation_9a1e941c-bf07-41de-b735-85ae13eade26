import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Button, Form } from 'antd';

import { useRequest } from 'ahooks';
import { isEmpty, uniqBy } from 'lodash-es';

import {
  AddNewButton,
  CommonIconAction,
  DeleteAction,
  EditAction,
  FieldType,
  Icon,
  Input,
  Modal,
  OperationContainer,
  QueryForm,
  QueryResultContainer,
  Table,
  TableActionsContainer,
  ViewAction,
  message,
} from '@zhongan/nagrand-ui';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import { ApplicationElementsGroupQueryResult } from 'genesis-web-service';

import { ApplicationScopeEnum, DetailPageMode } from '@market/common/interface';
import { usePackageList } from '@market/hook/package.service';
import { usePermission } from '@market/hook/permission';
import { NewMarketService } from '@market/services/market/market.service.new';
import { renderPackagesNameInTable } from '@market/utils/tableUtils';

import EditReminderModal from '../components/EditReminderModal';

export const ApplicationElementsGroup = (): JSX.Element => {
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [t] = useTranslation(['market', 'common']);
  const packageList = uniqBy(usePackageList(), 'packageCode')?.filter(item => item.packageCode) ?? [];
  const containerEl = useRef<HTMLDivElement>(null);
  const [reminderModalVisible, setReminderModalVisible] = useState(false);
  const [editRecord, setEditRecord] = useState<ApplicationElementsGroupQueryResult>();
  const hasEditAuth = !!usePermission('market.edit');
  const isSuperUser = !!usePermission('market.edit-all');

  const [modalOpen, setModalOpen] = useState(false);
  const [copiedRecord, setCopiedRecord] = useState<{ id?: number }>({});

  const readOnly = !hasEditAuth && !isSuperUser;
  const navigate = useNavigate();

  const [searchQuery, setSearchQuery] = useRouterState<{
    [key: string]: any;
    current: number;
    pageSize: number;
  }>({
    current: 0,
    pageSize: 10,
  });

  useEffect(() => {
    searchForm.setFieldsValue(searchQuery);
  }, []);

  const {
    data: searchResult,
    run: searchResultRun,
    loading,
  } = useRequest(
    () => {
      const { current, pageSize, ...condition } = searchQuery;
      return NewMarketService.ApplicationElementsPackService.search(condition, {
        page: searchQuery.current,
        size: searchQuery.pageSize,
      }).then(res => ({
        list: res.content!,
        total: res.totalElements!,
      }));
    },
    {
      refreshDeps: [searchQuery],
      debounceWait: 300,
      onError: error => {
        message.error(error.message);
      },
    }
  );

  const goToDetailPage = useCallback(
    (id: number, mode: DetailPageMode, scope?: string) => {
      if (mode === DetailPageMode.copy) {
        form.validateFields().then(values => {
          NewMarketService.ApplicationElementsPackService.copy(id, { ...values }).then(newId => {
            if (newId) {
              navigate(`/market/application-elements-group-detail?id=${newId}`, {
                state: {
                  mode: DetailPageMode.edit,
                  scope,
                  ...values,
                },
              });
            }
          });
        });
      } else {
        navigate(`/market/application-elements-group-detail?id=${id}`, {
          state: {
            mode,
            scope,
          },
        });
      }
    },
    [form, navigate]
  );

  const onEdit = useCallback(
    (record: ApplicationElementsGroupQueryResult) => {
      if (record.packages.length > 0) {
        setEditRecord(record);
        setReminderModalVisible(true);
      } else {
        goToDetailPage(record.id, DetailPageMode.edit);
      }
    },
    [goToDetailPage]
  );

  const onView = useCallback(
    (record: { id: number; scope: string }) => {
      const { id, scope } = record || {};

      goToDetailPage(id, DetailPageMode.view, scope);
    },
    [goToDetailPage]
  );

  const resetCopyModal = () => {
    setCopiedRecord({});
    setModalOpen(false);
    form.resetFields();
  };

  const onCopy = useCallback((record: { id: number }) => {
    setCopiedRecord(record);
    setModalOpen(true);
  }, []);

  const doubleConfirmDeletePackage = async (applicationElementsPackCode?: string) => {
    Modal.destroyAll();
    if (!applicationElementsPackCode) return;
    const packageListData =
      await NewMarketService.PackageApplicationElementShareMgmtService.queryPackageByPackCode(
        applicationElementsPackCode
      );
    if (isEmpty(packageListData)) return true;

    // application elements 有被 package 引用, 不能删除
    Modal.error({
      title: t('Delete Failed'),
      content: (
        <div>
          <div>{t('This application elements group is relied on')}:</div>
          <div className="gap-2 flex flex-wrap p-4 max-h-[256px] mt-2 overflow-y-auto color-textSecondary bg-layoutBg rounded-lg">
            {packageListData?.map(({ code, id }) => (
              <Button
                type="link"
                key={id}
                className="!p-0 block"
                onClick={() => {
                  Modal.destroyAll();
                  if (id) {
                    navigate(`/market/package/basic?packageId=${id}`);
                  }
                }}
              >
                {code}
              </Button>
            ))}
          </div>
        </div>
      ),
    });

    return false;
  };

  const onDelete = async (record: { id: number; applicationElementsPackCode: string }) => {
    const isCanDelete = await doubleConfirmDeletePackage(record?.applicationElementsPackCode);
    if (!isCanDelete) return;

    NewMarketService.ApplicationElementsPackService.delete(record.id).then(() => {
      message.success(t('Delete Success'));
      searchResultRun();
    });
  };

  return (
    <div ref={containerEl}>
      <QueryForm
        needSearchAfterClear
        title={t('Application Elements Group')}
        loading={loading}
        queryFields={[
          {
            col: 8,
            key: 'applicationElementsPackCode',
            label: t('Application Elements Group Code'),
            type: FieldType.Input,
          },
          {
            col: 8,
            key: 'applicationElementsPackName',
            label: t('Application Elements Group Name'),
            type: FieldType.Input,
          },
          {
            col: 8,
            type: FieldType.Select,
            label: t('Package Code'),
            key: 'packageCode',
            extraProps: {
              loading: !packageList.length,
              options:
                packageList?.map(item => ({
                  value: item.packageCode,
                  label: item.packageCode,
                })) ?? [],
            },
          },
          {
            col: 8,
            type: FieldType.Select,
            label: t('Package Name'),
            key: 'packageName',
            extraProps: {
              loading: !packageList.length,
              options:
                packageList?.map(item => ({
                  value: item.packageName,
                  label: item.packageName,
                })) ?? [],
            },
          },
        ]}
        onSearch={values => {
          setSearchQuery({
            ...searchQuery,
            ...values,
            pageSize: 10,
            current: 0,
          });
        }}
        formProps={{
          form: searchForm,
        }}
      />

      <QueryResultContainer>
        <OperationContainer>
          <OperationContainer.Left>
            <AddNewButton
              type="primary"
              ghost
              disabled={readOnly}
              onClick={() => {
                navigate('/market/application-elements-group-detail', {
                  state: { mode: DetailPageMode.add },
                });
              }}
            >
              {t('Add New')}
            </AddNewButton>
          </OperationContainer.Left>
        </OperationContainer>

        <Table
          loading={loading}
          dataSource={searchResult?.list}
          scroll={{ x: 'max-content' }}
          columns={[
            {
              title: t('Application Elements Group Code'),
              dataIndex: 'applicationElementsPackCode',
              width: 320,
            },
            {
              title: t('Application Elements Group Name'),
              dataIndex: 'applicationElementsPackName',
              width: 320,
            },
            {
              title: t('Package Name'),
              render: (text, record, index) => {
                if (record?.packages?.length > 0) {
                  return renderPackagesNameInTable(record.packages, 2, 600);
                }
                return t('- -');
              },
            },
            {
              title: t('Actions'),
              fixed: 'right',
              align: 'right',
              render: (text, record) => (
                <TableActionsContainer>
                  <ViewAction onClick={() => onView(record)} />
                  <EditAction
                    onClick={() => onEdit(record)}
                    disabled={readOnly || record?.scope === ApplicationScopeEnum.Library}
                  />
                  <CommonIconAction
                    disabled={readOnly}
                    tooltipTitle={t('Copy')}
                    icon={<Icon type="copy" />}
                    onClick={() => {
                      if (readOnly) {
                        return;
                      }
                      onCopy(record);
                    }}
                  />
                  <DeleteAction
                    disabled={readOnly || record?.scope === ApplicationScopeEnum.Library}
                    doubleConfirmPlacement="topLeft"
                    doubleConfirmType="popconfirm"
                    onClick={() => onDelete(record)}
                    deleteConfirmContent={t('Are you sure to delete this record?')}
                  />
                </TableActionsContainer>
              ),
            },
          ]}
          pagination={{
            pageSize: searchQuery.pageSize,
            current: searchQuery.current + 1,
            total: searchResult?.total,
            onChange: (page: number, pageSize: number) => {
              setSearchQuery({
                ...searchQuery,
                current: page - 1,
                pageSize,
              });
              searchResultRun();
            },
          }}
          emptyType="icon"
        />
      </QueryResultContainer>

      <Modal
        open={modalOpen}
        title={t('Copy Reminder')}
        onCancel={() => {
          resetCopyModal();
        }}
        onOk={() => {
          goToDetailPage(copiedRecord.id!, DetailPageMode.copy);
        }}
      >
        <Form form={form} name="copyModalForm" layout="vertical">
          <Form.Item
            name="applicationElementsPackName"
            label={t('Application Elements Group Name')}
            rules={[{ required: true, message: 'Please input' }]}
          >
            <Input placeholder={t('Please input')} />
          </Form.Item>
          <Form.Item
            name="applicationElementsPackCode"
            label={t('Application Elements Group Code')}
            rules={[{ required: true, message: 'Please input' }]}
          >
            <Input placeholder={t('Please input')} />
          </Form.Item>
        </Form>
      </Modal>

      <EditReminderModal
        visible={reminderModalVisible}
        onCancel={() => {
          setReminderModalVisible(false);
        }}
        onOk={() => {
          goToDetailPage(editRecord!.id, DetailPageMode.edit);
          setReminderModalVisible(false);
        }}
        packageNameList={editRecord?.packages.map(item => item.packageName) || []}
      />
    </div>
  );
};

export default ApplicationElementsGroup;
