/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useSearchParams } from 'react-router-dom';

import { PushpinOutlined } from '@ant-design/icons';
import { Anchor, Button, Divider, Form, Spin, Typography, message } from 'antd';

import classNames from 'classnames/bind';

import { CommonIconAction, Icon, Input } from '@zhongan/nagrand-ui';

import {
  ApplicationElementTopic,
  ApplicationElementsGroupInfo,
  ApplicationElementsGroupQueryResult,
  FactorInfo,
} from 'genesis-web-service/lib/market';

import { ApplicationScopeEnum, DetailPageMode } from '@market/common/interface';
import { FMarketHeader } from '@market/components/F-Market-Header';
import { usePermission } from '@market/hook/permission';
import { NewMarketService } from '@market/services/market/market.service.new';
import { formErrorHandler } from '@market/utils/formUtils';

import ApplicationElementsSectionList from '../components/ApplicationElementsSectionList';
import { AnchorIdConfigByTopic, ApplicationElementsSectionSequence, TitleConfigByTopic } from '../config';
import { filterApplicationElements } from '../dealServiceData';
import { ApplicationElementsSectionListRef } from '../interface';
import styles from './ApplicationElementsGroupDetail.module.scss';

const cx = classNames.bind(styles);

const { Paragraph } = Typography;

export const ApplicationElementsGroupDetail = (): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const sectionListRef = useRef<ApplicationElementsSectionListRef>(null);

  const [form] = Form.useForm();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const hasEditAuth = !!usePermission('market.edit');
  const isSuperUser = !!usePermission('market.edit-all');

  const readOnly = !hasEditAuth && !isSuperUser;
  const scrollContainer = useRef<HTMLDivElement>(null);
  const [pined, setPined] = useState(false);
  const [mode, setMode] = useState<DetailPageMode>();
  const [applicationElementsGroupInfo, setApplicationElementsGroupInfo] =
    useState<ApplicationElementsGroupQueryResult>();

  const hiddenBottomBar = useMemo(
    () => mode === DetailPageMode.view && location?.state?.scope === ApplicationScopeEnum.Library,
    [mode, location?.state?.scope]
  );

  const queryApplicationElementsGroup = useCallback(
    (id: number) => {
      NewMarketService.ApplicationElementsPackService.query$GET$mgmt_v2_applicationelements_query_applicationElementsId(
        id
      ).then(res => {
        setApplicationElementsGroupInfo(res);
        form.setFieldsValue({ ...res });
      });
    },
    [form]
  );

  useEffect(() => {
    const id = searchParams.get('id');
    if (id) {
      queryApplicationElementsGroup(Number(id));
    }
    setMode(location?.state?.mode || DetailPageMode.view);
  }, []);

  const saveGroupBasicInfo = useCallback(
    (closedTopicList?: ApplicationElementTopic[]) =>
      form
        .validateFields()
        .then(values => {
          const isCloseBeneficiary = closedTopicList?.includes(ApplicationElementTopic.beneficiary);
          // 当关闭 beneficiary 区块的时候，清空该区块内的表单值
          if (isCloseBeneficiary) {
            values.supportBeneficiaryTypes = [];
            form.setFieldValue('supportBeneficiaryTypes', undefined);
          }
          return NewMarketService.ApplicationElementsPackService.save({
            ...(applicationElementsGroupInfo || {}),
            ...values,
            id: applicationElementsGroupInfo?.id,
          }).then((id: number) => {
            if (mode === DetailPageMode.add) {
              setApplicationElementsGroupInfo({
                ...values,
                id,
              });
            }
            setMode(DetailPageMode.edit);
          });
        })
        .catch(formErrorHandler(form)),
    [form, applicationElementsGroupInfo, mode]
  );

  const beforeAdd = useCallback(() => {
    if (mode === DetailPageMode.add) {
      return saveGroupBasicInfo();
    }
    return Promise.resolve(true);
  }, [mode, saveGroupBasicInfo]);

  const renderApplicationElementsSection = useCallback(
    applicationElementsPackCode => (
      <ApplicationElementsSectionList
        beforeAdd={beforeAdd}
        displayPosition="groupDetail"
        applicationElementsPackCode={applicationElementsPackCode}
        mode={mode}
        applicationElementsGroupInfo={applicationElementsGroupInfo}
        refInstance={sectionListRef}
      />
    ),
    [applicationElementsGroupInfo, beforeAdd, mode]
  );

  const onSubmit = useCallback(() => {
    if (mode === DetailPageMode.add) {
      saveGroupBasicInfo().then(res => {
        message.success('Submit Success');
      });
    } else {
      // 编辑时，需要将开关关闭的投保要素删除
      const closedTopicList = ApplicationElementsSectionSequence.filter(
        topic => !sectionListRef.current?.isOpened(topic)
      );
      const configedApplicationElementsInClosed: FactorInfo[] = [];
      closedTopicList.forEach(topic => {
        const applicationElements = sectionListRef.current?.getApplicationElements(topic) || [];
        const configedApplicationElements = filterApplicationElements(applicationElements, true);
        if (configedApplicationElements.length > 0) {
          configedApplicationElementsInClosed.push(...configedApplicationElements);
        }
      });

      Promise.all([
        ...configedApplicationElementsInClosed.map(item =>
          NewMarketService.ApplicationElementsPackService.deleteItems(item.id!)
        ),
        saveGroupBasicInfo(closedTopicList),
      ]).then(() => {
        message.success('Submit Success');
        closedTopicList.forEach(topic => sectionListRef.current?.queryApplicationElements(topic));
      });
    }
  }, [applicationElementsGroupInfo, form, mode, saveGroupBasicInfo]);

  return (
    <Spin style={{ height: '100%' }} spinning={false}>
      <div className={cx('detail-container', { '!pb-0': hiddenBottomBar })}>
        <FMarketHeader backPath="/market/application-elements-group-center" subMenu="Application_Elements_Group" />
        <div ref={scrollContainer} className={cx('content')}>
          <div className={cx('content-left')}>
            <Form form={form} layout="vertical">
              <div id="basic">
                <div
                  style={{
                    fontWeight: 700,
                    marginBottom: 8,
                  }}
                >
                  {t('Basic Information')}
                </div>

                <div style={{ display: 'flex' }}>
                  <Form.Item
                    name="applicationElementsPackName"
                    rules={[
                      {
                        required: true,
                        message: t('Please input'),
                      },
                    ]}
                    required
                    label={t('Application Elements Group Name')}
                  >
                    <Input
                      disabled={mode === DetailPageMode.view}
                      style={{ width: 280 }}
                      placeholder={t('Please input')}
                    />
                  </Form.Item>
                  <Form.Item
                    name="applicationElementsPackCode"
                    rules={[
                      {
                        required: true,
                        message: t('Please input'),
                      },
                    ]}
                    style={{ marginLeft: 80 }}
                    required
                    label={t('Application Elements Group Code')}
                  >
                    <Input
                      disabled={mode === DetailPageMode.edit || mode === DetailPageMode.view}
                      style={{ width: 280 }}
                      placeholder={t('Please input')}
                    />
                  </Form.Item>
                </div>
                <div>
                  <Form.Item name="desc" label={t('Description')}>
                    <Input.TextArea
                      disabled={mode === DetailPageMode.view}
                      style={{ width: 640 }}
                      placeholder={t('Please input')}
                    />
                  </Form.Item>
                </div>
              </div>
              <Divider />
              <Form.Item
                noStyle
                shouldUpdate={(prev: ApplicationElementsGroupInfo, current) =>
                  prev.applicationElementsPackCode !== current.applicationElementsPackCode
                }
              >
                {({ getFieldValue }) => renderApplicationElementsSection(getFieldValue('applicationElementsPackCode'))}
              </Form.Item>
            </Form>
          </div>
          <div className={styles.anchorWrapper}>
            <div className={styles.switchMenuIcon}>
              <Icon type="drag" />
            </div>
            <div className={pined ? styles.pinedContentAbsoluteFloat : styles.contentAbsoluteFloat}>
              <div className={styles.anchorTitle}>
                <div>{t('Quick Menu')}</div>
                <CommonIconAction
                  className={pined ? styles.pinIcon : styles.unPinIcon}
                  icon={<PushpinOutlined />}
                  onClick={() => {
                    setPined(prevState => !prevState);
                  }}
                />
              </div>
              <Anchor
                items={[
                  {
                    key: 'basic',
                    href: '#basic',
                    title: t('Basic Information'),
                  },
                  ...ApplicationElementsSectionSequence.map(topic => ({
                    key: AnchorIdConfigByTopic[topic],
                    href: `#${AnchorIdConfigByTopic[topic]}`,
                    title: (
                      <Paragraph ellipsis={{ rows: 1, expandable: false }} className="!mb-0">
                        {TitleConfigByTopic[topic]}
                      </Paragraph>
                    ),
                  })),
                ]}
                className="pr-4"
              />
            </div>
          </div>
        </div>
        {!hiddenBottomBar && (
          <div className={cx('detail-bottom-bar')}>
            <div className="py-6 pr-6 absolute right-0">
              {mode === DetailPageMode.view ? (
                <Button
                  size="large"
                  onClick={() => {
                    setMode(DetailPageMode.edit);
                  }}
                  disabled={readOnly}
                >
                  {t('Edit')}
                </Button>
              ) : (
                <Button onClick={onSubmit} size="large" type="primary">
                  {t('Submit')}
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </Spin>
  );
};

export default ApplicationElementsGroupDetail;
