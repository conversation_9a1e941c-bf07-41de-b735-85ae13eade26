.detail-container {
  background-color: var(--white);
  position: relative;
  height: 100%;
  padding-bottom: 72px;

  .header {
    position: absolute;
    width: 100%;
    padding: 16px 32px;
    left: 0;
    right: 0;
    top: 0;
    background: var(--white);
    box-shadow: 0px 0.5px 0px var(--disabled-color);
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    color: var(--text-color);
  }

  .content {
    height: 100%;
    overflow: scroll;

    .content-left {
      padding: 24px;
      border-right: 1px solid var(--layout-body-background);
    }

    .anchor-wrapper {
      position: absolute;
      right: 0;
      top: 100px;
      z-index: 99;
      &:hover {
        .content-absolute-float {
          display: block;
        }
      }
    }

    .switch-menu-icon {
      position: absolute;
      right: 0;
      top: 100px;
      padding: 16px 16px 16px 24px;
      border-radius: 100px 0 0 100px;
      box-shadow: 0 4px 24px 0 #102a431f;
      background-color: var(--white);
      border: 1px solid var(--default-color-bg);
      z-index: -1;
    }

    .content-absolute-float {
      display: none;
      width: 257px;
      background-color: var(--white);
      box-shadow: 0 4px 24px 0 #102a431f;
      border-radius: 12px 0 0 12px;
      padding-bottom: 16px;
      padding-left: 16px;
      :global {
        .market-ant4-anchor-link-active > .market-ant4-anchor-link-title {
          color: var(--primary-color);
        }
        .market-ant4-anchor-link-title {
          color: var(--text-color-quaternary);
        }
      }

      .unPin-icon {
        transform: rotate(-45deg);
      }
      .pin-icon {
        color: var(--primary-color);
      }
    }

    .pined-content-absolute-float {
      @extend .content-absolute-float;
      display: block;
    }

    .anchor-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 16px;
      margin-left: -16px;
      font-weight: 500;
      margin-bottom: 16px;
      border-bottom: 0.5px solid var(--divider-color);
    }
  }

  .detail-bottom-bar {
    position: absolute;
    z-index: 21;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid var(--table-header-bg);
    padding: 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 72px;
    background-color: var(--white);
  }
}
