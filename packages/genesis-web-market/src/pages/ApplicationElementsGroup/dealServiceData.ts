import { CustomerSubTypeEnum } from 'genesis-web-component/lib/interface/enum.interface';
import { BizDictItem } from 'genesis-web-service/lib/foundation/foundation.interface';
import {
  ApplicationElementCustomerType,
  ApplicationElementFactorValue,
  ApplicationElementFactorValuesBatchParamsV2,
  ApplicationElementTopic,
  FactorInfo,
} from 'genesis-web-service/lib/market';
import { CountryCodeIsoRule, LinkageWithZipcode, MetadataService } from 'genesis-web-service/lib/metadata';

import {
  AppElementEnum,
  AppElementLinkageEnum,
  ConfigedDataByCustomerType,
  ConfigedDataByObjectSubCategory,
  ConfigedDataByTopicAndCustomerType,
} from '@market/common/applicationElements.interface';
import { BizTopic, ObjectSubCategoryType, UsageScenario } from '@market/common/enums';
import { NewMarketService } from '@market/services/market/market.service.new';

import { SupportOrganizationMatrixTopics } from './config';
import { BATCH_OPERATE_APP_ELEMENT_UNUMS_LIMIT } from './config/batchSaveFactors';
import {
  buildingLinkageMetadataKey,
  buildingTypeChildRelation,
  buildingTypeFirstLevelKeyMapping,
  buildingTypeParentRelation,
  firstLevelBuildingTypeKey,
  getBuildingTypeConfigListByFactorCode,
} from './config/buildingTypeConfig';
import {
  firstLevelIndustryKey,
  getOccupationConfigListByFactorCode,
  industryChildRelation,
  industryFirstLevelKeyMapping,
  industryLinkageMetadataKey,
  industryParentRelation,
} from './config/industryConfig';
import {
  addressLineFacotrsList,
  addressLinkageMetadataKey,
  bankLinkageMetadataKey,
  linkageEnumConfig,
  totalAddressFactorsMap,
  totalBankFactorsMap,
} from './config/linkageEnumConfig';

// 静态因子中不显示国家三字码，显示区号
export const countryDisplayAreaCode = (applicationElement: FactorInfo) => {
  if (applicationElement.bizDictKey !== 'country') {
    return false;
  }
  if (
    applicationElement.customerSubType === CustomerSubTypeEnum.PHONE &&
    applicationElement.factorCode === 'countryCode'
  ) {
    return true;
  }
  if (
    applicationElement.customerSubType === CustomerSubTypeEnum.BASIC_INFO &&
    applicationElement.factorCode === 'registeredPhoneCountryCode'
  ) {
    return true;
  }

  return false;
};

const transBizDictToAppElementEnum =
  (entryBizDictKey: string, applicationElement: FactorInfo, countryCodeIsoRule?: CountryCodeIsoRule) =>
  (item: BizDictItem, index: number): AppElementEnum => {
    // 正常枚举，Value Code展示dictValue，提交dictValue
    // 国家 - 显示区号 - 二字码：Value Code展示itemExtend2， 提交itemExtend1
    // 国家 - 显示区号 - 三字码：Value Code展示itemExtend2， 提交dictValue
    // 国家 - 不显示区号 - 二字码：Value Code展示dictValue， 提交itemExtend1
    // 国家 - 不显示区号 - 三字码：Value Code展示dictValue， 提交dictValue
    let code = item.dictValue;
    let dictValue = item.dictValue;

    // 国家存在二字码和三字码，二字码取itemExtend1属性
    if (entryBizDictKey === 'country') {
      // 显示区号
      if (countryDisplayAreaCode(applicationElement)) {
        code = item.itemExtend2!; // itemExtend2是区号
      }

      if (countryCodeIsoRule === CountryCodeIsoRule.Alpha2) {
        dictValue = item.itemExtend1! as string;
      }
    }

    return {
      code,
      name: item.dictValueName,
      childList: item.childList?.map(
        transBizDictToAppElementEnum(entryBizDictKey, applicationElement, countryCodeIsoRule)
      ),
      key: code,
      dictKey: item.dictKey,
      itemExtend1: item.itemExtend1 as string,
      dictValue, // 显示区号的情况下，提交 or 回显的时候根据code查一下dictValue
      no: index + 1,
    } as AppElementEnum;
  };

/**
 * 一次性全量查出所有枚举，包括有联动的子级
 */
export const getTotalMetadataEnums = async (
  entryBizDictKey: string,
  applicationElement: FactorInfo,
  countryCodeIsoRule?: CountryCodeIsoRule
): Promise<AppElementEnum[]> => {
  let bizDicts: BizDictItem[] = [];

  if (['address1', 'bank', 'industry'].includes(entryBizDictKey)) {
    bizDicts = await MetadataService.queryBizDictBigKey(entryBizDictKey);
  } else {
    bizDicts = await MetadataService.queryBizDict({
      dictKeys: [entryBizDictKey],
    });
  }

  const results = bizDicts.map(transBizDictToAppElementEnum(entryBizDictKey, applicationElement, countryCodeIsoRule));

  return results;
};

export const CONNECTOR_CODE = '$$$$';
export const PARENT_CONNECTOR_CODE = '|';

/**
 * 组装枚举唯一路径
 */
export const generateBizDictUniquePath =
  (bizDictKey: string) =>
  (
    dictValue: string,
    dictValueName?: string,
    itemExtend1?: string,
    parentDictValue?: string,
    parentValueName?: string
  ): string => {
    // 地址zipCode允许重复
    if (addressLinkageMetadataKey.includes(bizDictKey)) {
      const selfValue = `${dictValue}${CONNECTOR_CODE}${itemExtend1 || ''}`;
      return parentDictValue ? `${parentDictValue}${PARENT_CONNECTOR_CODE}${selfValue}` : selfValue;
    }

    // bank Name允许重复
    if (bankLinkageMetadataKey.includes(bizDictKey)) {
      const selfValue = `${dictValue}${CONNECTOR_CODE}${dictValueName || ''}`;
      return parentDictValue && parentValueName
        ? `${parentDictValue}${CONNECTOR_CODE}${parentValueName}${PARENT_CONNECTOR_CODE}${selfValue}`
        : selfValue;
    }
    return parentDictValue ? `${parentDictValue}${PARENT_CONNECTOR_CODE}${dictValue}` : dictValue;
  };

export const parseFullPath = (fullPath: string) => {
  const parentSepIndex = fullPath.lastIndexOf(PARENT_CONNECTOR_CODE);
  if (parentSepIndex < 0) {
    return {
      parentPath: '',
      selfPath: fullPath,
    };
  }

  return {
    selfPath: fullPath.substring(parentSepIndex + 1),
    parentPath: fullPath.substring(0, parentSepIndex),
  };
};

/**
 * 解析枚举唯一路径
 * 后续可能会根据bizDictKey解析出name 或者 extend1字段
 */
export const parseBizDictUniquePath = (bizDictKey: string) => (fullPath: string) => {
  const { parentPath, selfPath } = parseFullPath(fullPath);
  const [parentValue, parentValueName] = parentPath.split(CONNECTOR_CODE);

  return {
    dictValue: selfPath.split(CONNECTOR_CODE)[0],
    parentValue,
    parentValueName,
  };
};

const filterAppElementEnums = (
  rootKey: string,
  totalEnums: AppElementEnum[],
  configedData: Record<string, string[]>,
  factorCode: string,
  childRelation: Record<string, string>
): AppElementEnum[] => {
  let currentCode = rootKey;
  let currentList = totalEnums;

  // 将函数声明从循环中拿出来，避免循环声明
  const filterFunc = (enumItem: AppElementEnum) => {
    // 如果父级没有配置，不过滤返回全部
    if (!configedData?.[currentCode]) {
      return true;
    }
    return configedData?.[currentCode]?.includes(enumItem.code);
  };
  const reduceFunc = (result: AppElementEnum[], enumItem: AppElementEnum) => [...result, ...(enumItem.childList || [])];

  // 防止意外bug导致死循环，影响使用
  const maxCount = 10;
  let times = 1;

  // currentCode从父级一层层往子级递归，直到递归到当前因子
  // currentList从父级的全量枚举，一层层往下过滤
  // 最终返回factorCode对应的可以选择到的全量枚举
  while (currentCode !== factorCode && times < maxCount) {
    const childKey = childRelation[currentCode];
    currentList = currentList.filter(filterFunc).reduce(reduceFunc, []);

    currentCode = childKey;
    times += 1;
  }
  return currentList.map((appElementEnum, index) => ({
    ...appElementEnum,
    no: index + 1,
  }));
};

/**
 * 获取投保要素的可以选择的枚举列表
 *
 * @param applicationElement 投保要素
 * @param options 业务相关数据
 * @param filterOptions 用来处理联动逻辑的数据过滤
 */
export const getEnumsAppElementCanSelect = async (
  applicationElement: FactorInfo,
  options: {
    countryCodeIsoRule: CountryCodeIsoRule;
    topic: ApplicationElementTopic;
  },
  filterOptions?: {
    configedDataMap: {
      industry?: ConfigedDataByCustomerType;
      building?: ConfigedDataByObjectSubCategory;
    };
  }
): Promise<AppElementEnum[]> => {
  const { factorCode, customerType, objectSubCategory } = applicationElement;
  let bizDictKey = applicationElement.bizDictKey;

  if (linkageEnumConfig[factorCode]) {
    bizDictKey = linkageEnumConfig[factorCode].linkageMetadataKey[0];
  }

  // 处理联动数据
  if (filterOptions) {
    const { configedDataMap } = filterOptions;

    let filteredAppElementEnums: AppElementEnum[] = [];

    if (getOccupationConfigListByFactorCode(2, factorCode)) {
      const occupationDataOfTopic = configedDataMap.industry?.[customerType] || {};
      const industryEntryBizDictKey = industryLinkageMetadataKey[0];
      const totalEnums = await getTotalMetadataEnums(
        industryEntryBizDictKey,
        applicationElement,
        options.countryCodeIsoRule
      );

      filteredAppElementEnums = filterAppElementEnums(
        industryFirstLevelKeyMapping[factorCode],
        totalEnums,
        occupationDataOfTopic,
        factorCode,
        industryChildRelation
      );
      return filteredAppElementEnums;
    }

    if (getBuildingTypeConfigListByFactorCode(2, factorCode)) {
      const occupationDataOfTopic = configedDataMap.building?.[objectSubCategory!] || {};
      const buildingEntryBizDictKey = buildingLinkageMetadataKey[0];
      const totalEnums = await getTotalMetadataEnums(
        buildingEntryBizDictKey,
        applicationElement,
        options.countryCodeIsoRule
      );

      filteredAppElementEnums = filterAppElementEnums(
        buildingTypeFirstLevelKeyMapping[factorCode],
        totalEnums,
        occupationDataOfTopic,
        factorCode,
        buildingTypeChildRelation
      );
      return filteredAppElementEnums;
    }
  }

  if (bizDictKey) {
    const totalEnums = await getTotalMetadataEnums(bizDictKey, applicationElement, options.countryCodeIsoRule);

    if (applicationElement.factorCode === 'loanCompany') {
      /* Loan Company 枚举来自于配置中心维护的bank，且分类标记为loan company的bank */
      return totalEnums
        .filter(option => option.itemExtend1 && option.itemExtend1.includes(UsageScenario.LoanCompany))
        .map((option, index) => ({
          ...option,
          no: index + 1,
        }));
    }

    return totalEnums;
  }

  return [];
};

/**
 * 部分特殊的投保要素的枚举，会超过5万条数据，需要单独调接口保存和查询
 * 该方法封装了分批查询枚举的逻辑
 */
export const getAppElementConfigedEnums = async (
  applicationElement: FactorInfo,
  applicationElementsPackCode: string,
  prevList: ApplicationElementFactorValue[] = []
): Promise<ApplicationElementFactorValue[]> => {
  // 如果不是特殊处理的的投保要素，直接返回配置在对象上的枚举列表
  if (!linkageEnumConfig[applicationElement.factorCode] && !applicationElement.cascadeFactorCodes) {
    return Promise.resolve(
      applicationElement?.factorValue?.split(',').map(value => ({
        factorValue: value,
      })) || []
    );
  }

  const enumsByPage = await NewMarketService.ApplicationElementsPackService.queryApplicationElementFactorsPage(
    {
      factorCode: applicationElement.factorCode,
      applicationElementsPackCode,
      topic: applicationElement.topic,
      customerSubType: applicationElement.customerSubType,
      customerType: applicationElement.customerType,
    },
    {
      page: 0 + prevList.length / BATCH_OPERATE_APP_ELEMENT_UNUMS_LIMIT,
      size: BATCH_OPERATE_APP_ELEMENT_UNUMS_LIMIT,
    }
  );

  const factorValues = enumsByPage.factorValues?.results || [];
  const total = enumsByPage.factorValues?.total || 0;

  const totalResults = [...prevList, ...factorValues];

  if (totalResults.length < total) {
    return getAppElementConfigedEnums(applicationElement, applicationElementsPackCode, totalResults);
  }

  return totalResults;
};

/**
 * 部分特殊的投保要素的枚举，会超过5万条数据，需要单独调接口保存和查询
 * 该方法封装了分批保存枚举的逻辑
 */
export const batchSaveAppElementEnums = async (
  applicationElement: FactorInfo,
  totalFactorPaths: string[],
  applicationElementsPackCode: string,
  batchNo = 1,
  uniquePathMap: Record<string, AppElementLinkageEnum>
): Promise<unknown> => {
  if (totalFactorPaths.length === 0) {
    return;
  }
  const start = (batchNo - 1) * BATCH_OPERATE_APP_ELEMENT_UNUMS_LIMIT;
  if (start >= totalFactorPaths.length) {
    // 全部存完了
    return;
  }
  const passedFactorValues = totalFactorPaths.slice(start, start + BATCH_OPERATE_APP_ELEMENT_UNUMS_LIMIT);
  let entryBizDictKey = '';
  if (applicationElement.cascadeFactorCodes) {
    entryBizDictKey = applicationElement.entryBizDictKey;
  } else {
    entryBizDictKey = linkageEnumConfig[applicationElement.factorCode].linkageMetadataKey?.[0];
  }

  const param: ApplicationElementFactorValuesBatchParamsV2 = {
    batchNo,
    applicationElementsPackCode,
    topic: applicationElement.topic,
    factorCode: applicationElement.factorCode,
    bizDictGroupCode: applicationElement.bizDictGroupCode,
    customerSubType: applicationElement.customerSubType,
    customerType: applicationElement.customerType,
    objectCategory: applicationElement.objectCategory,
    objectSubCategory: applicationElement.objectSubCategory,
    entryDictKey: entryBizDictKey,
    bizDictKey: entryBizDictKey,
    factorValues: passedFactorValues.map((fullPath): ApplicationElementFactorValue => {
      const { dictValue, parentValue, parentValueName } = parseBizDictUniquePath(entryBizDictKey)(fullPath);
      return {
        factorValue: dictValue,
        parentCodePath: parentValue,
        // address 枚举需要额外传一下zipCode
        itemExtend1: entryBizDictKey === 'address1' ? uniquePathMap?.[fullPath]?.extraInfo : undefined,
        // bank 枚举需要额外传一下名字
        dictValueName: entryBizDictKey === 'bank' ? uniquePathMap?.[fullPath]?.name : undefined,
        parentValueNamePath: entryBizDictKey === 'bank' ? parentValueName : undefined,
      };
    }),
  };

  await NewMarketService.ApplicationElementsPackService.addToSaveApplicationElementFactorValues(param);
  return batchSaveAppElementEnums(
    applicationElement,
    totalFactorPaths,
    applicationElementsPackCode,
    batchNo + 1,
    uniquePathMap
  );
};

// filter表格里显示的数组
export const filterApplicationElements = (
  factorList: FactorInfo[],
  show = true,
  options?: {
    customerType?: ApplicationElementCustomerType;
    objectCategory?: number;
    objectSubCategory?: number;
  }
) => {
  let list = factorList.filter(item => item && item.configured === show);
  if (options) {
    const { customerType, objectCategory, objectSubCategory } = options;
    if (customerType) {
      list = list.filter(item => item.customerType === customerType);
    }
    if (objectCategory) {
      list = list.filter(item => +item.objectCategory! === +objectCategory);
    }
    if (objectSubCategory) {
      list = list.filter(item => +item.objectSubCategory! === +objectSubCategory);
    }
  }
  return list;
};

export const applicationElementUniqueKey = ({ customerType, factorCode, customerSubType }: FactorInfo) =>
  `${customerType}_${customerSubType}_${factorCode}`;

export const getLinkageFactors = (factorCode: string, depth: number) =>
  linkageEnumConfig[factorCode]?.staticFactorCodeList?.slice(0, depth);

export const getLinkageEmitFactors = (factorCode: string, depth: number) =>
  linkageEnumConfig[factorCode]?.emitDrawerCodeList?.slice(0, depth);

/**
 * 能调到这个方法肯定是属于级联的枚举，所以不需要判断级联关系，直接获取code列表
 */
export const getFactorsForDrawer = (
  applicationElement: FactorInfo,
  options: {
    addressDepth: number;
    bankCascadedLevel: number;
  }
) => {
  const { addressDepth, bankCascadedLevel } = options;
  const { factorCode } = applicationElement;

  if (factorCode === 'loanCompany') {
    // loanCompany因子的可选枚举是bank中配了loanCompany扩展字段的数据
    return ['loanCompany'];
  }

  if (factorCode === 'industryCode' || factorCode === 'occupationCode') {
    // industry occupation 固定两级
    return getLinkageEmitFactors(factorCode, 2);
  }

  if (totalAddressFactorsMap[factorCode] || factorCode === 'zipCode') {
    if (applicationElement.factorCode === 'zipCode' && applicationElement.topic === ApplicationElementTopic.object) {
      return addressLineFacotrsList.slice(0, addressDepth);
    }

    return getLinkageEmitFactors(factorCode, addressDepth);
  }

  if (totalBankFactorsMap) {
    return getLinkageEmitFactors(factorCode, bankCascadedLevel);
  }

  return [];
};

export const isZipCodeLinkage = (linkageWithZipcode: LinkageWithZipcode) =>
  linkageWithZipcode !== LinkageWithZipcode.No;

/**
 * 根据投保要素的code和枚举配置项判断该投保要素code是否有级联关系
 */
export const isLinkageEnum = (
  applicationElement: FactorInfo,
  options: {
    addressModel: string;
    addressDepth: number;
    linkageWithZipcode: LinkageWithZipcode;
    bankModel: string;
    bankCascadedLevel: number;
  }
) => {
  const { addressModel, addressDepth, bankModel, bankCascadedLevel, linkageWithZipcode } = options;
  const { factorCode } = applicationElement;

  if (factorCode === 'loanCompany' && bankModel === 'dropdown') {
    return ['loanCompany'];
  }

  if (factorCode === 'industryCode' || factorCode === 'occupationCode') {
    return linkageEnumConfig[factorCode]?.staticFactorCodeList;
  }

  // 处理zipCode
  if (addressModel === 'dropdown' && factorCode === 'zipCode') {
    if (isZipCodeLinkage(linkageWithZipcode)) {
      if (applicationElement.topic === ApplicationElementTopic.object) {
        return addressLineFacotrsList.slice(0, addressDepth);
      }
      return getLinkageFactors(factorCode, addressDepth);
    }

    return undefined;
  }

  if (addressModel === 'dropdown' && totalAddressFactorsMap[factorCode]) {
    const isLinkedAddressFactor = getLinkageFactors(factorCode, addressDepth).includes(factorCode);

    if (isLinkedAddressFactor) {
      if (applicationElement.topic === ApplicationElementTopic.object) {
        return addressLineFacotrsList.slice(0, addressDepth);
      }
      // address 联动
      return getLinkageFactors(factorCode, addressDepth);
    }
    return undefined;
  }

  if (
    bankModel === 'dropdown' &&
    totalBankFactorsMap[factorCode] &&
    getLinkageFactors(factorCode, bankCascadedLevel).includes(factorCode)
  ) {
    // bank 联动
    const bankLinkageFactors = getLinkageFactors(factorCode, bankCascadedLevel);

    if (bankLinkageFactors.includes('bankCode')) {
      bankLinkageFactors.push('bankName');
    } else if (bankLinkageFactors.includes('bankName')) {
      bankLinkageFactors.push('bankCode');
    }
    if (bankLinkageFactors.includes('bankBranchCode')) {
      bankLinkageFactors.push('bankBranchName');
    } else if (bankLinkageFactors.includes('bankBranchName')) {
      bankLinkageFactors.push('bankBranchCode');
    }
    return bankLinkageFactors;
  }

  if (addressModel === 'freeText' || bankModel === 'freeText') {
    return undefined;
  }

  return linkageEnumConfig[factorCode]?.staticFactorCodeList;
};

export const isLinkedZipCode = (factorCode: string, linkageWithZipcode: LinkageWithZipcode) =>
  (totalAddressFactorsMap[factorCode] || factorCode === 'zipCode') && isZipCodeLinkage(linkageWithZipcode);

/**
 * 将对象数组转成map，方便查找
 */
export function collectUniquePathMap(enumsList: AppElementLinkageEnum[], map: Record<string, AppElementLinkageEnum>) {
  if (enumsList.length === 0) {
    return;
  }
  enumsList.forEach(item => {
    map[item.key] = item;
    collectUniquePathMap(item.childList || [], map);
  });
}

export function getSchemaInfoByTopic(
  topic: ApplicationElementTopic,
  option?: {
    customerType?: ApplicationElementCustomerType;
    objectSubCategory?: ObjectSubCategoryType;
  }
) {
  if (
    SupportOrganizationMatrixTopics.includes(topic) &&
    option?.customerType === ApplicationElementCustomerType.Company
  ) {
    return {
      schemaDefType: 1, // person
      schemaDefRefId: 14, // org
      ratetableCategory: BizTopic.CustomerElementsRelationshipMatrix,
    };
  }

  if (topic === ApplicationElementTopic.object) {
    if (option?.objectSubCategory === ObjectSubCategoryType.Driver) {
      return {
        schemaDefType: 2, // object
        schemaDefRefId: 3, // driver
        ratetableCategory: BizTopic.DriverElementsRelationshipMatrix,
      };
    }

    if (option?.objectSubCategory === ObjectSubCategoryType.Vehicle) {
      return {
        schemaDefType: 2, // object
        schemaDefRefId: 1, // Vehicle
        ratetableCategory: BizTopic.VehicleElementsRelationshipMatrix,
      };
    }

    if (option?.objectSubCategory === ObjectSubCategoryType.OrgRenter) {
      return {
        schemaDefType: 2, // object
        schemaDefRefId: 200035, // org
        ratetableCategory: BizTopic.CustomerElementsRelationshipMatrix,
      };
    }

    return {};
  }
}

export function isEnum(
  record: FactorInfo,
  {
    addressModel,
    linkageWithZipcode,
    addressDepth,
    bankModel,
    bankCascadedLevel,
    hasConfigedIndustry,
    hasConfigedBuildingType,
    positionModel,
  }: {
    hasConfigedIndustry?: ConfigedDataByCustomerType;
    hasConfigedBuildingType?: ConfigedDataByObjectSubCategory;
    addressModel: string;
    addressDepth: number;
    bankModel: string;
    bankCascadedLevel: number;
    linkageWithZipcode: LinkageWithZipcode;
    positionModel: string;
  }
) {
  if (record.factorCode === 'position' && positionModel === 'freeText') {
    // position 系统类型是枚举类型，需要覆盖成freeText类型。它是一级枚举，不需要级联组件
    return false;
  }
  if (record.bizDictKey) {
    // 有bizDictKey，可以编辑
    return true;
  }
  let canConfig = false;

  const linkageFactors = isLinkageEnum(record, {
    addressModel,
    addressDepth,
    linkageWithZipcode,
    bankModel,
    bankCascadedLevel,
  });

  if (linkageFactors) {
    canConfig = true;
  }
  if (getOccupationConfigListByFactorCode(2, record.factorCode)) {
    const parentKey = industryParentRelation[record.factorCode];
    if (firstLevelIndustryKey.includes(record.factorCode)) {
      canConfig = true;
    } else if (
      hasConfigedIndustry &&
      Array.isArray(hasConfigedIndustry?.[record.customerType]?.[parentKey]) &&
      hasConfigedIndustry[record.customerType][parentKey].length > 0
    ) {
      canConfig = true;
    }
  }

  if (getBuildingTypeConfigListByFactorCode(2, record.factorCode)) {
    const parentKey = buildingTypeParentRelation[record.factorCode];
    if (firstLevelBuildingTypeKey.includes(record.factorCode)) {
      canConfig = true;
    } else if (
      hasConfigedBuildingType &&
      Array.isArray(hasConfigedBuildingType?.[record.customerType]?.[parentKey]) &&
      hasConfigedBuildingType[record.customerType]?.[parentKey].length > 0
    ) {
      canConfig = true;
    }
  }

  return canConfig;
}

export const isSameApplicationElement = (factor1: FactorInfo, factor2: FactorInfo): boolean => {
  const isSameCode = factor1.factorCode === factor2.factorCode;

  if (factor1.customerType || factor2.customerType) {
    return (
      isSameCode && factor1.customerType === factor2.customerType && factor1.customerSubType === factor2.customerSubType
    );
  }

  if (factor1.objectCategory || factor2.objectCategory) {
    return (
      isSameCode &&
      factor1.objectCategory === factor2.objectCategory &&
      factor1.objectSubCategory === factor2.objectSubCategory
    );
  }

  return isSameCode;
};

export const getApplicationElementKey = (factor: FactorInfo) =>
  factor.customerSubType ? `${factor.customerSubType}||${factor.factorCode}` : factor.factorCode;

export const isEqualApplicationElement = (key: string, factor: FactorInfo) => key === getApplicationElementKey(factor);
