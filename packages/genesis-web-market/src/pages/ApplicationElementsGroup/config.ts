import { ApplicationElementTopic } from 'genesis-web-service/lib/market';

import { t } from '@market/i18n';

/**
 * 投保要素区块的顺序配置
 */
export const ApplicationElementsSectionSequence = [
  ApplicationElementTopic.policyHolder,
  ApplicationElementTopic.insured,
  ApplicationElementTopic.unnamedInsured,
  ApplicationElementTopic.object,
  ApplicationElementTopic.beneficiary,
  ApplicationElementTopic.payer,
  ApplicationElementTopic.nominee,
  ApplicationElementTopic.secondaryLifeInsured,
  ApplicationElementTopic.consentee,
  ApplicationElementTopic.trustee,
  ApplicationElementTopic.beneficialOwner,
  ApplicationElementTopic.policy,
  ApplicationElementTopic.assignee,
  ApplicationElementTopic.premiumFunder,
  ApplicationElementTopic.contactPerson,
];

/**
 * 投保要素区块的标题配置
 */
export const TitleConfigByTopic = {
  [ApplicationElementTopic.policyHolder]: t('Policyholder'),
  [ApplicationElementTopic.insured]: t('Insured'),
  [ApplicationElementTopic.unnamedInsured]: t('Unnamed Insured'),
  [ApplicationElementTopic.beneficiary]: t('Beneficiary Elements'),
  [ApplicationElementTopic.object]: t('Object Elements'),
  [ApplicationElementTopic.payer]: t('Payer Elements'),
  [ApplicationElementTopic.nominee]: t('Nominee Elements'),
  [ApplicationElementTopic.secondaryLifeInsured]: t('Secondary Life Insured Elements'),
  [ApplicationElementTopic.consentee]: t('Consentee Elements'),
  [ApplicationElementTopic.trustee]: t('Trustee Elements'),
  [ApplicationElementTopic.beneficialOwner]: t('Beneficial Owner Elements'),
  [ApplicationElementTopic.policy]: t('Policy Elements'),
  [ApplicationElementTopic.assignee]: t('Assignee Elements'),
  [ApplicationElementTopic.premiumFunder]: t('Premium Funder Elements'),
  [ApplicationElementTopic.contactPerson]: t('Contact Person Elements'),
};

/**
 * 投保要素区块的锚点配置
 */
export const AnchorIdConfigByTopic = {
  [ApplicationElementTopic.policyHolder]: 'policyHolder',
  [ApplicationElementTopic.insured]: 'insured',
  [ApplicationElementTopic.unnamedInsured]: 'unnamedInsured',
  [ApplicationElementTopic.beneficiary]: 'beneficiary',
  [ApplicationElementTopic.object]: 'object',
  [ApplicationElementTopic.payer]: 'payer',
  [ApplicationElementTopic.nominee]: 'nominee',
  [ApplicationElementTopic.secondaryLifeInsured]: 'secondaryLifeInsured',
  [ApplicationElementTopic.consentee]: 'consentee',
  [ApplicationElementTopic.trustee]: 'trustee',
  [ApplicationElementTopic.beneficialOwner]: 'beneficialOwner',
  [ApplicationElementTopic.policy]: 'policy',
  [ApplicationElementTopic.assignee]: 'assignee',
  [ApplicationElementTopic.premiumFunder]: 'premiumFunder',
  [ApplicationElementTopic.contactPerson]: 'contactPerson',
};

export const SupportOrganizationTopics = [
  ApplicationElementTopic.policyHolder,
  ApplicationElementTopic.insured,
  ApplicationElementTopic.beneficiary,
  ApplicationElementTopic.beneficialOwner,
  ApplicationElementTopic.assignee,
  ApplicationElementTopic.payer,
];

export const SupportOrganizationMatrixTopics = [
  ApplicationElementTopic.policyHolder,
  ApplicationElementTopic.insured,
  ApplicationElementTopic.beneficiary,
  ApplicationElementTopic.payer,
];

/**
 * 投保要素区块是否支持开关功能
 */
export const SupportSwitchTopics = [
  ApplicationElementTopic.unnamedInsured,
  ApplicationElementTopic.object,
  ApplicationElementTopic.beneficiary,
  ApplicationElementTopic.payer,
  ApplicationElementTopic.nominee,
  ApplicationElementTopic.secondaryLifeInsured,
  ApplicationElementTopic.consentee,
  ApplicationElementTopic.trustee,
  ApplicationElementTopic.beneficialOwner,
  ApplicationElementTopic.policy,
  ApplicationElementTopic.assignee,
  ApplicationElementTopic.premiumFunder,
  ApplicationElementTopic.contactPerson,
];

// 其余从insured上面copy
export const CopyFromPolicyHolderTopics = [
  ApplicationElementTopic.insured,
  ApplicationElementTopic.payer,
  ApplicationElementTopic.premiumFunder,
  ApplicationElementTopic.nominee,
  ApplicationElementTopic.beneficialOwner,
];

/**
 * 保单层的投保要素
 */
export const PolicyLevelTopics = [ApplicationElementTopic.policy, ApplicationElementTopic.unnamedInsured];

export const NoCopyTopics = [ApplicationElementTopic.contactPerson];
