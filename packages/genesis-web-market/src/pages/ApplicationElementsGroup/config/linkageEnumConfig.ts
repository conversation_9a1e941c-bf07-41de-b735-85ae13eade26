import { keyBy } from 'lodash-es';

// 第一套车型库在前端的展示名称
export const DEFAULT_SUB_CODE_FONTEND_NAME = 'Default Vehicle Database';

export const addressLinkageMetadataKey = ['address1', 'address2', 'address3', 'address4', 'address5'];

// customer上面的地址factors
const addressFacotrsList = ['address11', 'address12', 'address13', 'address14', 'address15'];

// 标的上面的地址factors
export const addressLineFacotrsList = ['address1', 'address2', 'address3', 'address4', 'address5'];

export const bankLinkageMetadataKey = ['bank', 'bankBranch'];

const bankCodeFacotrsList = ['bankCode', 'bankBranchCode'];

const bankNameFacotrsList = ['bankName', 'bankBranchName'];

export const totalAddressFactorsMap = keyBy([...addressFacotrsList, ...addressLineFacotrsList]);

export const totalBankFactorsMap = keyBy([...bankCodeFacotrsList, ...bankNameFacotrsList]);

const industryFactorsList = ['industryCode', 'occupationCode'];
const industryMetadataKey = ['industry', 'occupation'];

const addressConfig = {
  staticFactorCodeList: addressFacotrsList,
  linkageMetadataKey: addressLinkageMetadataKey,
  emitDrawerCodeList: addressFacotrsList,
};

const addressLineConfig = {
  staticFactorCodeList: addressLineFacotrsList,
  linkageMetadataKey: addressLinkageMetadataKey,
  emitDrawerCodeList: addressLineFacotrsList,
};

export const linkageEnumConfig: Record<
  string,
  {
    staticFactorCodeList: string[]; // 级联关系的投保要素列表
    linkageMetadataKey: string[]; // 投保要素对应的枚举Key列表
    emitDrawerCodeList: string[]; // 用来触发枚举配置的投保要素列表；比如配置bankName的枚举其实是配置bankCode的枚举，然后将枚举name联动到bankName
  }
> = {
  /* address 1~5 start */
  address11: addressConfig,
  address12: addressConfig,
  address13: addressConfig,
  address14: addressConfig,
  address15: addressConfig,
  /* address 1~5 end */

  zipCode: {
    staticFactorCodeList: addressFacotrsList,
    linkageMetadataKey: addressLinkageMetadataKey,
    emitDrawerCodeList: addressFacotrsList,
  },

  /* address line 1~5 start */
  address1: addressLineConfig,
  address2: addressLineConfig,
  address3: addressLineConfig,
  address4: addressLineConfig,
  address5: addressLineConfig,
  /* addressline 1~5 end */

  /* bank start */
  bankCode: {
    staticFactorCodeList: bankCodeFacotrsList,
    linkageMetadataKey: bankLinkageMetadataKey,
    emitDrawerCodeList: bankCodeFacotrsList,
  },
  bankBranchCode: {
    staticFactorCodeList: bankCodeFacotrsList,
    linkageMetadataKey: bankLinkageMetadataKey,
    emitDrawerCodeList: bankCodeFacotrsList,
  },
  bankName: {
    staticFactorCodeList: bankNameFacotrsList,
    linkageMetadataKey: bankLinkageMetadataKey,
    emitDrawerCodeList: bankCodeFacotrsList,
  },
  bankBranchName: {
    staticFactorCodeList: bankNameFacotrsList,
    linkageMetadataKey: bankLinkageMetadataKey,
    emitDrawerCodeList: bankCodeFacotrsList,
  },
  loanCompany: {
    staticFactorCodeList: ['loanCompany'],
    linkageMetadataKey: ['bank'],
    emitDrawerCodeList: ['bankCode'],
  },
  /* bank end */
  industryCode: {
    staticFactorCodeList: industryFactorsList,
    linkageMetadataKey: industryMetadataKey,
    emitDrawerCodeList: industryFactorsList,
  },
  occupationCode: {
    staticFactorCodeList: industryFactorsList,
    linkageMetadataKey: industryMetadataKey,
    emitDrawerCodeList: industryFactorsList,
  },
  // http://localhost:8080/api/metadata/bizDict/v2/tenant/dict/biz-dict/big-key/industry
};
