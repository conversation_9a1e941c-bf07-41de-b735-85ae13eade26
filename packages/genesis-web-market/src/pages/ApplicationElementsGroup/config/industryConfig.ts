/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

export const firstLevelIndustryKey = ['industryCode'];
export const industryParentRelation: Record<string, string> = {
  occupationCode: 'industryCode',
};

export const industryChildRelation: Record<string, string> = {
  industryCode: 'occupationCode',
};

export const industryFirstLevelKeyMapping: Record<string, string> = {
  industryCode: 'industryCode',
  occupationCode: 'industryCode',
};

// 用来判断是否需要走特殊处理的逻辑
export const staticIndustryFactorCodeList = [['industryCode', 'occupationCode']];

export const industryLinkageMetadataKey = ['industry', 'occupation'];

export const getOccupationConfigListByFactorCode = (industryCascadedLevel: number, factorCode: string) => {
  const resultList: string[] | undefined = staticIndustryFactorCodeList.find(list =>
    list.slice(0, industryCascadedLevel).includes(factorCode)
  );
  return resultList;
};
