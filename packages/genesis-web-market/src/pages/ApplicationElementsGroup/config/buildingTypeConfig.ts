/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

export const firstLevelBuildingTypeKey = ['buildingType'];
export const buildingTypeParentRelation: Record<string, string> = {
  subBuildingType: 'buildingType',
};

export const buildingTypeChildRelation: Record<string, string> = {
  buildingType: 'subBuildingType',
};

export const buildingTypeFirstLevelKeyMapping: Record<string, string> = {
  buildingType: 'buildingType',
  subBuildingType: 'buildingType',
};

// 用来判断是否需要走特殊处理的逻辑
export const staticBuildingTypeFactorCodeList = [['buildingType', 'subBuildingType']];

export const buildingLinkageMetadataKey = ['buildingType', 'subBuildingType'];

export const getBuildingTypeConfigListByFactorCode = (buildingTypeCascadedLevel: number, factorCode: string) => {
  const resultList: string[] | undefined = staticBuildingTypeFactorCodeList.find(list =>
    list.slice(0, buildingTypeCascadedLevel).includes(factorCode)
  );
  return resultList;
};
