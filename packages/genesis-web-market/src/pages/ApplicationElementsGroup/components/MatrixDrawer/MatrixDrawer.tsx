import React, { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Row } from 'antd';

import { Drawer } from '@zhongan/nagrand-ui';

import { ApplicationElementCustomerType, ApplicationElementTopic } from 'genesis-web-service';

import { ObjectSubCategoryType } from '@market/common/enums';
import { DetailPageMode } from '@market/common/interface';
import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { useMatrixList } from '@market/hook/calculator.service';

import { getSchemaInfoByTopic } from '../../dealServiceData';

interface Props {
  // category: BizTopic;
  visible: boolean;
  topic?: ApplicationElementTopic;
  customerType?: ApplicationElementCustomerType;
  objectSubCategory?: ObjectSubCategoryType;
  mode: DetailPageMode;
  onClose: () => void;
  onSubmit: (
    values: {
      matrixTableCode?: string;
    },
    schemaInfo: {
      schemaDefType: number;
      schemaDefRefId: number;
    }
  ) => void;
  applicationElementPropertyRequestList: {
    topic: number;
    schemaDefType: number;
    schemaDefRefId: number;
    matrixTableCode: string;
  }[];
}

export const MatrixDrawer = ({
  // category,
  visible,
  topic,
  customerType,
  objectSubCategory,
  mode,
  onClose,
  onSubmit,
  applicationElementPropertyRequestList,
}: Props): JSX.Element | null => {
  if (!topic) {
    return null;
  }
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();

  /* ============== 枚举使用start ============== */
  const bizTopicOptions = useBizDictAsOptions('bizTopic');
  /* ============== 枚举使用end ============== */

  const schemaInfo = useMemo(
    () =>
      getSchemaInfoByTopic(topic, {
        customerType,
        objectSubCategory,
      }),
    [customerType, objectSubCategory, topic]
  );

  const categoryList = useMemo(() => [schemaInfo!.ratetableCategory], [schemaInfo]);
  const matrixList = useMatrixList(categoryList);

  useEffect(() => {
    if (!schemaInfo) {
      form.setFieldsValue({
        matrixTableCode: undefined,
      });
    }

    if (visible) {
      const initCode = applicationElementPropertyRequestList.find(
        item =>
          item.schemaDefType === schemaInfo.schemaDefType &&
          item.schemaDefRefId === schemaInfo.schemaDefRefId &&
          item.topic === topic
      )?.matrixTableCode;

      form.setFieldsValue({
        matrixTableCode: initCode,
      });
    }
  }, [applicationElementPropertyRequestList, form, schemaInfo, topic, visible]);

  const submitDrawer = useCallback(() => {
    if (!schemaInfo) {
      return;
    }
    form.validateFields().then(values => {
      onSubmit(values, {
        schemaDefType: schemaInfo.schemaDefType,
        schemaDefRefId: schemaInfo.schemaDefRefId,
      });
    });
  }, [form, onSubmit, schemaInfo]);

  return (
    <Drawer
      open={visible}
      size="small"
      onClose={() => {
        form.setFieldsValue({
          matrixTableCode: undefined,
        });
        onClose();
      }}
      title={t('Elements Matrix')}
      destroyOnClose
      submitBtnShow={mode === DetailPageMode.edit }
      onSubmit={submitDrawer}
    >
      <Form layout="vertical" form={form}>
        <Row>
          <Col span={12}>
            <Form.Item label={t('Ratetable Category')}>
              <GeneralSelect disabled value={schemaInfo?.ratetableCategory} option={bizTopicOptions} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="matrixTableCode" label={t('Ratetable Code')}>
              <GeneralSelect
                disabled={mode === DetailPageMode.view}
                option={matrixList.map(item => ({
                  label: item.matrixTableName,
                  value: item.matrixTableCode,
                }))}
              />
            </Form.Item>
            {/* <MicroRateTableDrawer />*/}
          </Col>
        </Row>
      </Form>
    </Drawer>
  );
};

export default MatrixDrawer;
