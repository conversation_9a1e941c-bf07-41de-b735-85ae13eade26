import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Switch } from 'antd';

import { useRequest } from 'ahooks';

import { Drawer, Table } from '@zhongan/nagrand-ui';

import { CountryCodeIsoRule, FactorInfo, YesNoType } from 'genesis-web-service';

import {
  AppElementEnum,
  ConfigedDataByCustomerType,
  ConfigedDataByObjectSubCategory,
} from '@market/common/applicationElements.interface';
import { DetailPageMode } from '@market/common/interface';
import { useBizDict } from '@market/hook/bizDict';

import { countryDisplayAreaCode, getAppElementConfigedEnums, getEnumsAppElementCanSelect } from '../../dealServiceData';

interface Props {
  visible: boolean;
  topic: number;
  applicationElementsPackCode: string;
  onDrawerClose: () => void;
  record: FactorInfo;
  onSelectSecondRow: (value: any, record: any) => void;
  configedDataMap: {
    industry?: ConfigedDataByCustomerType;
    building?: ConfigedDataByObjectSubCategory;
  };
  mode: DetailPageMode;
}

export const EnumConfigDrawer = ({
  topic,
  visible,
  applicationElementsPackCode,
  record,
  onDrawerClose,
  onSelectSecondRow,
  configedDataMap,
  mode,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  /* ============== 枚举使用start ============== */
  const countryCodeIsoRuleBizDicts = useBizDict('countryCodeIsoRule');
  /* ============== 枚举使用end ============== */
  const containerEl = useRef<HTMLDivElement>(null);
  const [editTableState, setEditTableState] = useState<AppElementEnum[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [isSyncTenantEnum, setIsSyncTenantEnum] = useState<YesNoType>(YesNoType.No);

  const { loading, runAsync: getAppElementEnumsService } = useRequest(
    () =>
      getEnumsAppElementCanSelect(
        record,
        {
          countryCodeIsoRule: countryCodeIsoRuleBizDicts[0]?.dictValue as CountryCodeIsoRule,
          topic,
        },
        {
          configedDataMap,
        }
      ),
    {
      loadingDelay: 800,
      manual: true,
    }
  );

  const { loading: getConfigedEnumsLoading, runAsync: getAppElementConfigedEnumsService } = useRequest(
    () => getAppElementConfigedEnums(record, applicationElementsPackCode),
    {
      manual: true,
    }
  );

  const queryTableData = useCallback(
    () =>
      getAppElementEnumsService().then(appEnumList => {
        setEditTableState(appEnumList);
        return appEnumList;
      }),
    [getAppElementEnumsService]
  );

  useEffect(() => {
    if (visible) {
      if (record.isSyncTenantEnum) {
        setIsSyncTenantEnum(`${record.isSyncTenantEnum}` as YesNoType);
      }
      const isDisplayAreaCode = countryDisplayAreaCode(record);
      Promise.all([queryTableData(), getAppElementConfigedEnumsService()]).then(([list, initFactorValueList]) => {
        const initSelectedKeys: number[] = [];
        initFactorValueList.forEach(value => {
          const target = list.find(item => {
            if (isDisplayAreaCode) {
              return item.dictValue === value.factorValue;
            }
            return item.code === value.factorValue;
          });
          if (target) {
            initSelectedKeys.push(target.no);
          }
        });

        setSelectedRowKeys(initSelectedKeys);
      });
    }
  }, [visible, record]);

  const columns = useMemo(
    () => [
      {
        title: t('number', { ns: 'market' }),
        dataIndex: 'no',
        width: 70,
      },
      {
        title: t('Value Code', { ns: 'market' }),
        dataIndex: 'code',
        width: 200,
        sorter: (prev: AppElementEnum, next: AppElementEnum) => {
          // 排序的规则：只可能是数字或者字母，当是数字的情况下按照大小排序，如果是字母的情况按照A-Z排序
          if (
            prev.code.charCodeAt(0) > 47 &&
            prev.code.charCodeAt(0) < 58 &&
            next.code.charCodeAt(0) > 47 &&
            next.code.charCodeAt(0) < 58
          ) {
            return Number(prev.code) - Number(next.code);
          }
          return prev.code.charCodeAt(0) - next.code.charCodeAt(0);
        },
      },
      {
        title: t('Display Name', { ns: 'market' }),
        dataIndex: 'name',
      },
    ],
    [t]
  );

  const closeDrawer = useCallback(() => {
    onDrawerClose();
    setEditTableState([]);
    setSelectedRowKeys([]);
  }, [onDrawerClose]);

  const renderTable = useCallback(() => {
    if (!visible) {
      return null;
    }
    const rowSelection = {
      selectedRowKeys,
      onChange: (keys: number[]) => {
        setSelectedRowKeys(keys.sort((a, b) => a - b));
      },
    };

    return (
      <Table
        rowKey="no"
        rowSelection={mode === DetailPageMode.view || isSyncTenantEnum === YesNoType.Yes ? undefined : rowSelection}
        columns={columns}
        dataSource={
          mode === DetailPageMode.view
            ? editTableState.filter(item => isSyncTenantEnum === YesNoType.Yes || selectedRowKeys.includes(item.no))
            : editTableState
        }
        pagination={false}
        loading={loading || getConfigedEnumsLoading}
      />
    );
  }, [visible, selectedRowKeys, columns, editTableState, loading, getConfigedEnumsLoading, mode, isSyncTenantEnum]);

  const onSave = useCallback(() => {
    const selectedFactorValues: string[] = [];
    const isDisplayAreaCode = countryDisplayAreaCode(record);

    selectedRowKeys.map(indexNumber => {
      if (isDisplayAreaCode) {
        // 展示区号的时候，要取dictValue提交
        selectedFactorValues.push(editTableState[indexNumber - 1].dictValue);
      } else {
        selectedFactorValues.push(editTableState[indexNumber - 1].code);
      }
    });

    // 类似 address 这种动态处理枚举的， 配dropdown时需要设置bizDictKey
    if (editTableState?.[0]) {
      record.bizDictKey = editTableState?.[0]?.dictKey;
    }
    if (isSyncTenantEnum === YesNoType.Yes) {
      record.isSyncTenantEnum = +isSyncTenantEnum;
      onSelectSecondRow([], record);
    } else {
      record.isSyncTenantEnum = +YesNoType.No;
      onSelectSecondRow(selectedFactorValues, record);
    }
    closeDrawer();
  }, [closeDrawer, editTableState, onSelectSecondRow, record, selectedRowKeys, isSyncTenantEnum]);

  return (
    <Drawer
      open={visible}
      title={t('Enumerate Configuration')}
      onClose={closeDrawer}
      sendText={t('Save')}
      submitBtnShow={mode !== DetailPageMode.view}
      onSubmit={onSave}
      readonly={mode === DetailPageMode.view}
    >
      <div ref={containerEl}>
        <Form layout="vertical">
          <div className="flex justify-between">
            <Form.Item label={t('Field Name')}>
              <span>{record.factorName}</span>
            </Form.Item>
            <Form.Item label={t('Tenant Enumerated Sync')}>
              <Switch
                onChange={checked => {
                  if (checked) {
                    setIsSyncTenantEnum(YesNoType.Yes);
                  } else {
                    setIsSyncTenantEnum(YesNoType.No);
                  }
                }}
                checked={isSyncTenantEnum === YesNoType.Yes}
                disabled={mode === DetailPageMode.view}
              />
            </Form.Item>
          </div>
        </Form>
        {renderTable()}
      </div>
    </Drawer>
  );
};

export default EnumConfigDrawer;
