import React, { Ref, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Popconfirm, Switch, Tooltip, message } from 'antd';

import { useRequest } from 'ahooks';
import classNames from 'classnames/bind';
import { cloneDeep, keyBy, remove, uniq } from 'lodash-es';

import { Icon } from '@zhongan/nagrand-ui';

import {
  ApplicationElementCustomerType,
  ApplicationElementTopic,
  ApplicationElementsGroupInfo,
  FactorInfo,
} from 'genesis-web-service/lib/market';
import { LinkageWithZipcode } from 'genesis-web-service/lib/metadata';

import { ConfigedDataByObjectSubCategory } from '@market/common/applicationElements.interface';
import { ObjectCategoryType, ObjectSubCategoryType } from '@market/common/enums';
import { DetailPageMode } from '@market/common/interface';
import { useBizDict } from '@market/hook/bizDict';
import { useObjectCategoryTree } from '@market/hook/metadata';

import { AnchorIdConfigByTopic, SupportSwitchTopics, TitleConfigByTopic } from '../../config';
import { getBuildingTypeConfigListByFactorCode } from '../../config/buildingTypeConfig';
import { showMatixObjectSubCategory } from '../../config/matrixConfig';
import {
  filterApplicationElements,
  isLinkageEnum,
  isLinkedZipCode,
  isSameApplicationElement,
} from '../../dealServiceData';
import { ApplicationElementsSectionRef } from '../../interface';
import ApplicationElementsDrawerNew from '../ApplicationElementsDrawerNew';
import ApplicationElementsTable from '../ApplicationElementsTable/ApplicationElementsTable';
import EnumConfigDrawer from '../EnumConfigDrawer';
import ObjectSubCategoryModal from '../ObjectSubCategoryModal';
import SortDrawer from '../SortDrawer';
import styles from './ObjectApplicationElementSection.module.scss';
import { NewMarketService } from '@market/services/market/market.service.new';

const cx = classNames.bind(styles);

export const getObjectKeyByRefId = (item: { schemaDefRefId: number }) => {
  if (item.schemaDefRefId === 3) {
    return `${ObjectCategoryType.Auto}_${ObjectSubCategoryType.Driver}`;
  }
  return `${ObjectCategoryType.Auto}_${ObjectSubCategoryType.Vehicle}`;
};

interface Props {
  refInstance: Ref<ApplicationElementsSectionRef>;
  displayPosition?: 'package' | 'groupDetail';
  mode?: DetailPageMode;
  applicationElementsGroupInfo: ApplicationElementsGroupInfo | undefined;
  applicationElementsPackCode: string;
  tenantDataByKey: {
    addressModel: string;
    addressDepth: number;
    bankModel: string;
    addressCascadedLevel: number;
    bankCascadedLevel: number;
    linkageWithZipcode: LinkageWithZipcode;
  };
  copyFactor?: (
    srcTopic: ApplicationElementTopic,
    targetTopic: ApplicationElementTopic,
    options: {
      customerType: ApplicationElementCustomerType;
      objectCategory?: number;
      objectSubCategory?: number;
    }
  ) => void;
  beforeAdd?: () => Promise<unknown>;
  openMatrixDrawer?: (topic: ApplicationElementTopic, objectSubCategory: ObjectSubCategoryType) => void;
}

export const ObjectApplicationElementSection = ({
  mode,
  refInstance,
  applicationElementsGroupInfo,
  beforeAdd,
  applicationElementsPackCode,
  tenantDataByKey,
  displayPosition = 'groupDetail',
  copyFactor,
  openMatrixDrawer,
}: Props): JSX.Element | null => {
  const [t] = useTranslation(['market', 'common']);
  const containerEl = useRef<HTMLDivElement>(null);
  const readOnly = mode === DetailPageMode.view || mode === DetailPageMode.copy;
  const [selectedObjectKeyList, setSelectedObjectKeyList] = useState<string[]>([]);
  const [applicationElements, setApplicationElements] = useState<FactorInfo[]>([]);
  const [swichOpened, setSwitchOpened] = useState(false);
  const [modalOpened, setModalOpened] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [sortDrawerVisible, setSortDrawerVisible] = useState(false);
  const [drawerObjectInfo, setDrawerObjectInfo] = useState<{
    // objectCategory: number;
    objectSubCategory: number;
  }>();
  const [enumConfigDrawerInfo, setEnumConfigDrawerInfo] = useState<{
    visible: boolean;
    record?: FactorInfo;
    mode: DetailPageMode;
  }>({
    visible: false,
    record: undefined,
    mode: DetailPageMode.add,
  });
  const [drawerMode, setDrawerMode] = useState(DetailPageMode.add);
  const [isEditLinkedFactors, setIsEditLinkedFactors] = useState(false);
  const [drawerApplicationElements, setDrawerApplicationElements] = useState<FactorInfo[]>([]);
  // 排序会根据customerType、objectCategory分开排序，但是save/factors需要全量数据
  // 所以把未排序的数据记录一下，提交的时候带上
  const [sortDrawerOtherElements, setSortDrawerOtherElements] = useState<FactorInfo[]>([]);
  const [drawerTotalApplicationElements, setDrawerTotalApplicationElements] = useState<FactorInfo[]>([]);
  const [hasConfigedBuildingType, setHasConfigedBuildingType] = useState<ConfigedDataByObjectSubCategory>({});
  const { objectCategoryComponents, objectTypeI18nMap, objectSubCategoryI18nMap } = useObjectCategoryTree();
  const topic = ApplicationElementTopic.object;

  const showSwitch = SupportSwitchTopics.includes(topic);

  const opened = showSwitch ? swichOpened : true;

  const { runAsync: queryApplicationGroupElements, loading } = useRequest(
    (_topic, _applicationElementsPackCode) =>
      NewMarketService.ApplicationElementsPackService.query$POST$mgmt_v2_applicationelements_query_factors({
        topic: _topic,
        applicationElementsPackCode: _applicationElementsPackCode
      }),
    {
      manual: true,
    }
  );

  const objectMatrixList = useMemo(
    () =>
      applicationElementsGroupInfo?.applicationElementPropertyRequestList?.filter(item => item.schemaDefType === 2) ||
      [],
    [applicationElementsGroupInfo]
  );

  useEffect(() => {
    if (objectMatrixList.length > 0) {
      setSwitchOpened(true);
    }
  }, [applicationElementsGroupInfo, objectMatrixList]);

  const queryApplicationElements = useCallback(
    (
      option?: {
        isNeedSetSelectedObjectSubCategory: boolean;
      } = {
        isNeedSetSelectedObjectSubCategory: false,
      }
    ) => {
      queryApplicationGroupElements(
        topic,
        applicationElementsGroupInfo?.applicationElementsPackCode || applicationElementsPackCode
      ).then(res => {
        const factors: FactorInfo[] = res?.factors;

        const configedData = filterApplicationElements(factors, true);

        const configedObjectKeyList = uniq(
          configedData.map(item => `${item.objectCategory!}_${item.objectSubCategory!}`)
        );

        if (configedData.length > 0) {
          setSwitchOpened(true);
        }
        if (option.isNeedSetSelectedObjectSubCategory) {
          if (objectMatrixList.length > 0) {
            setSelectedObjectKeyList(uniq([...objectMatrixList.map(getObjectKeyByRefId), ...configedObjectKeyList]));
          } else {
            setSelectedObjectKeyList(configedObjectKeyList);
          }
        }
        setApplicationElements(factors);
      });
    },
    [queryApplicationGroupElements, topic, applicationElementsGroupInfo, applicationElementsPackCode, objectMatrixList]
  );

  useImperativeHandle(refInstance, () => ({
    getApplicationElements: () => applicationElements,
    isOpened: () => swichOpened,
    queryApplicationElements,
  }));

  useEffect(() => {
    queryApplicationElements({
      isNeedSetSelectedObjectSubCategory: true,
    });
  }, [applicationElementsGroupInfo]);

  const renderSwitch = useCallback(() => {
    if (!showSwitch) {
      return null;
    }
    if (displayPosition === 'package') {
      return null;
    }
    return (
      <Switch
        disabled={readOnly}
        checked={swichOpened}
        onChange={evt => {
          if (evt) {
            beforeAdd?.().then(() => {
              setSwitchOpened(evt);
            })
          } else {
            setSwitchOpened(evt);
          }
        }}
        style={{ marginLeft: 8 }}
        size="small"
      />
    );
  }, [displayPosition, readOnly, showSwitch, swichOpened, beforeAdd]);

  const showDrawer = useCallback(
    (objectCategory: number, objectSubCategory: number, record?: FactorInfo) => {
      const { addressModel, addressDepth, linkageWithZipcode, bankModel, bankCascadedLevel } = tenantDataByKey;
      const clonedapplicationElements = cloneDeep(applicationElements);
      const currentTopicConfigedElements = filterApplicationElements(clonedapplicationElements, true, {
        objectSubCategory,
        objectCategory,
      });
      const currentTopicUnConfigedElements = filterApplicationElements(clonedapplicationElements, false, {
        objectSubCategory,
        objectCategory,
      });
      let tempApplicationElements = currentTopicUnConfigedElements.map((item, index) => {
        item.no = index + 1;

        return item;
      });

      if (record) {
        // 编辑
        let linkageFactors: string[] = [];
        if (record.cascadeFactorCodes) {
          linkageFactors = record.cascadeFactorCodes.sort((a, b) => a.level - b.level).map(item => item.factorCode);
        } else {
          linkageFactors =
            isLinkageEnum(record, {
              addressModel,
              addressDepth,
              linkageWithZipcode,
              bankModel,
              bankCascadedLevel,
            }) || [];
        }
        // 如果是有级联关系投保要素，编辑的时候一起编辑

        if (linkageFactors?.length > 0) {
          const tempList = linkageFactors
            .map(factorCode => {
              const finded = currentTopicConfigedElements.find(
                factorInfo => factorInfo.factorCode === factorCode && factorInfo.configured
              );
              if (finded) {
                finded.inEdit = true;
              }
              return finded;
            })
            .filter(Boolean);
          const configedZipCode = currentTopicConfigedElements.find(factorInfo => factorInfo.factorCode === 'zipCode');
          if (isLinkedZipCode(record.factorCode, linkageWithZipcode) && configedZipCode) {
            configedZipCode.inEdit = true;
            tempList.push(configedZipCode);
          }
          // state.firstDrawerList = this.listAddNo(tempList);
          tempApplicationElements = tempList
            .filter(item => !!item)
            .map((item, index) => {
              item!.no = index + 1;

              return item;
            }) as FactorInfo[];
          setIsEditLinkedFactors(true);
        } else {
          record.no = 1;
          tempApplicationElements = [record];
        }
        setDrawerTotalApplicationElements(clonedapplicationElements);
        setDrawerApplicationElements(tempApplicationElements);
        setDrawerMode(DetailPageMode.edit);
        setDrawerVisible(true);
        return;
      }

      setDrawerTotalApplicationElements(clonedapplicationElements);
      setDrawerApplicationElements(tempApplicationElements);
      setDrawerMode(DetailPageMode.add);
      setDrawerObjectInfo({
        objectSubCategory,
      });
      setDrawerVisible(true);
    },
    [applicationElements, tenantDataByKey]
  );

  const closeDrawer = useCallback(() => {
    setDrawerVisible(false);
    setIsEditLinkedFactors(false);
    setDrawerTotalApplicationElements([]);
    setDrawerApplicationElements([]);
    setDrawerMode(DetailPageMode.add);
  }, []);

  const showSortDrawer = useCallback(
    (objectCategory: number, objectSubCategory: number) => {
      const clonedapplicationElements = cloneDeep(applicationElements);
      const currentTopicConfigedElements = filterApplicationElements(clonedapplicationElements, true, {
        objectSubCategory,
        objectCategory,
      });
      setSortDrawerOtherElements(
        clonedapplicationElements.filter(item => item.configured && item.objectSubCategory !== objectSubCategory)
      );
      setDrawerApplicationElements(currentTopicConfigedElements);
      setSortDrawerVisible(true);
    },
    [applicationElements]
  );

  const closeSortDrawer = useCallback(() => {
    setDrawerApplicationElements([]);
    setSortDrawerVisible(false);
    queryApplicationElements();
  }, [queryApplicationElements]);

  const onEnumSubmit = (value: string[], record: FactorInfo) => {
    const index = drawerApplicationElements.findIndex(item => isSameApplicationElement(item, record));
    drawerApplicationElements[index].factorValue = value.join(',');

    if (getBuildingTypeConfigListByFactorCode(2, record.factorCode)) {
      if (!hasConfigedBuildingType[record.objectSubCategory!]) {
        hasConfigedBuildingType[record.objectSubCategory!] = {};
      }
      hasConfigedBuildingType[record.objectSubCategory!][record.factorCode] = value;

      setHasConfigedBuildingType({ ...hasConfigedBuildingType });
    }

    setDrawerApplicationElements([...drawerApplicationElements]);
  };

  const onDelete = (record: FactorInfo) => {
    const list = applicationElements;

    const { addressModel, addressDepth, bankModel, bankCascadedLevel, linkageWithZipcode } = tenantDataByKey;
    let linkageFactors: string[] = [];
    if (record.cascadeFactorCodes) {
      linkageFactors = record.cascadeFactorCodes.map(item => item.factorCode);
    } else {
      linkageFactors =
        isLinkageEnum(record, {
          addressModel,
          addressDepth,
          linkageWithZipcode,
          bankModel,
          bankCascadedLevel,
        }) || [];
    }

    // 有级联关系的投保要素，删除的时候一起删除
    if (linkageFactors?.length > 0) {
      if (isLinkedZipCode(record.factorCode, linkageWithZipcode)) {
        linkageFactors.push('zipCode');
      }
      const linkageRecord = list
        // list 中有个人和企业的数据，需要过滤一下
        .filter(item => item.customerType === record.customerType)
        .filter(item => linkageFactors.includes(item.factorCode) && item.id);
      Promise.all(linkageRecord.map(item => NewMarketService.ApplicationElementsPackService.deleteItems(item.id!))).then(() => {
        queryApplicationElements();
      });
    } else {
      NewMarketService.ApplicationElementsPackService.deleteItems(record.id!).then(res => {
        queryApplicationElements();
      });
    }
  };

  const onDeleteObjectSubCategory = useCallback(
    (objectCategory: string, objectSubCategory: string) => {
      NewMarketService.ApplicationElementsPackService.clearAllItems({
        applicationElementsPackCode,
        topic,
        objectSubCategory: +objectSubCategory,
        objectCategory: +objectCategory,
      }).then(res => {
        message.success(t('Delete Success'));
        remove(selectedObjectKeyList, item => item === `${objectCategory}_${objectSubCategory}`);
        setSelectedObjectKeyList([...selectedObjectKeyList]);
        queryApplicationElements();
      });
    },
    [applicationElementsPackCode, queryApplicationElements, selectedObjectKeyList, t, topic]
  );

  const renderCopyBtn = useCallback(
    (objectCategory: number, subCategory: number) => {
      if (subCategory !== ObjectSubCategoryType.IndividualRenter && subCategory !== ObjectSubCategoryType.OrgRenter) {
        return null;
      }
      const srcTopic = ApplicationElementTopic.policyHolder;
      const btnText = t('Copy from Policyholder');
      const confirmTitle = t(
        'Copying elements from the policyholder module will overwrite existing content, please confirm.'
      );
      let customerType = ApplicationElementCustomerType.Person;

      if (subCategory === ObjectSubCategoryType.OrgRenter) {
        customerType = ApplicationElementCustomerType.Company;
      }

      return (
        <Popconfirm
          placement="topRight"
          title={confirmTitle}
          onConfirm={() =>
            copyFactor?.(srcTopic, topic, {
              customerType,
              objectCategory,
              objectSubCategory: subCategory,
            })
          }
          disabled={readOnly}
          okText="Yes"
          cancelText="No"
        >
          <Button disabled={readOnly} style={{ width: 180, marginRight: 8 }}>
            {btnText}
          </Button>
        </Popconfirm>
      );
    },
    [copyFactor, readOnly, t, topic]
  );

  const renderObjectSubCategorySection = useCallback(
    ([category, subCategory]: [string, string]) => (
      <React.Fragment>
        <div className={cx('top-section')}>
          <div className={cx('title-wrapper')}>
            <div className={cx('sub-title')}>
              {`${objectTypeI18nMap[`${category}_${subCategory}`]} - ${objectSubCategoryI18nMap[`${category}_${subCategory}`]}`}
              {showMatixObjectSubCategory.includes(+subCategory as ObjectSubCategoryType) ? (
                <Tooltip title={t('Elements Matrix')}>
                  <Icon
                    type="more"
                    onClick={() => {
                      openMatrixDrawer?.(topic, +subCategory as ObjectSubCategoryType);
                    }}
                    style={{
                      marginLeft: 8,
                      cursor: 'pointer',
                      fontWeight: 'bold',
                    }}
                  />
                </Tooltip>
              ) : null}
            </div>
          </div>
          {opened && displayPosition === 'groupDetail' ? (
            <div className="flex items-center">
              {renderCopyBtn(+category, +subCategory)}
              <Popconfirm
                title={t('Are you sure to delete this record?')}
                placement="topLeft"
                okText={t('yes')}
                cancelText={t('no')}
                onConfirm={() => {
                  onDeleteObjectSubCategory(category, subCategory);
                }}
                getPopupContainer={() => containerEl.current! || document.body}
              >
                <Button disabled={readOnly} style={{ marginRight: 8 }} icon={<Icon type="delete" />}>
                  {t('Delete')}
                </Button>
              </Popconfirm>
              <Button
                disabled={readOnly}
                onClick={() => {
                  showSortDrawer(+category, +subCategory);
                }}
                style={{ marginRight: 8 }}
                icon={<Icon type="sort" />}
              >
                {t('Sort')}
              </Button>
              <Button
                disabled={mode === DetailPageMode.view}
                onClick={() => {
                  showDrawer(+category, +subCategory);
                }}
                type="primary"
                ghost
                icon={<Icon type="add" style={{ fontSize: 16 }} />}
              >
                {t('Add')}
              </Button>
            </div>
          ) : null}
        </div>
        <ApplicationElementsTable
          topic={topic}
          objectSubCategory={subCategory}
          applicationElementsPackCode={applicationElementsPackCode}
          tenantDataByKey={tenantDataByKey}
          data={filterApplicationElements(applicationElements, true, {
            objectCategory: +category,
            objectSubCategory: +subCategory,
          })}
          onModify={record => {
            showDrawer(record.objectCategory!, record.objectSubCategory!, record);
          }}
          onDelete={onDelete}
          factorType={ApplicationElementCustomerType.Person}
          loading={loading}
          mode={mode}
          onViewElementsValue={(record: FactorInfo) => {
            setEnumConfigDrawerInfo({
              visible: true,
              record,
              mode: DetailPageMode.view,
            });
          }}
        />
      </React.Fragment>
    ),
    [
      applicationElements,
      applicationElementsPackCode,
      renderCopyBtn,
      displayPosition,
      loading,
      mode,
      onDelete,
      onDeleteObjectSubCategory,
      openMatrixDrawer,
      opened,
      readOnly,
      showDrawer,
      showSortDrawer,
      t,
      tenantDataByKey,
      objectTypeI18nMap,
      objectSubCategoryI18nMap,
      topic,
    ]
  );

  if (
    displayPosition === 'package' &&
    filterApplicationElements(applicationElements, true).length === 0 &&
    objectMatrixList.length === 0
  ) {
    return null;
  }

  return (
    <div id={AnchorIdConfigByTopic[topic]} className={cx('applicetion-section')}>
      <div className={cx('config-table-wrapper')}>
        <div className={cx('top-section')}>
          <div className={cx('title-wrapper')}>
            <div className={cx('title')}>
              {TitleConfigByTopic[topic]}
              {renderSwitch()}
            </div>
          </div>
        </div>

        {opened && !readOnly && displayPosition === 'groupDetail' ? (
          <React.Fragment>
            <Button
              type="primary"
              icon={<Icon type="add" />}
              style={{ marginTop: 16 }}
              onClick={() => {
                setModalOpened(true);
              }}
            >
              {t('Add Object Component')}
            </Button>
            <ObjectSubCategoryModal
              visible={modalOpened}
              objectCategoryComponents={objectCategoryComponents}
              configedSubCategory={uniq(
                filterApplicationElements(applicationElements, true).map(item => `${item.objectSubCategory!}`)
              )}
              selectedObjectKeyList={selectedObjectKeyList}
              onCancel={() => {
                setModalOpened(false);
                return Promise.resolve();
              }}
              onOk={(objectKeyList: string[]) => {
                setSelectedObjectKeyList(objectKeyList);
                setModalOpened(false);
                return Promise.resolve();
              }}
            />
          </React.Fragment>
        ) : null}

        {opened
          ? selectedObjectKeyList.map(objectKey =>
              renderObjectSubCategorySection(objectKey.split('_') as [string, string])
            )
          : null}

        {drawerVisible && opened ? (
          <ApplicationElementsDrawerNew
            visible={drawerVisible}
            topic={topic}
            onClose={closeDrawer}
            drawerApplicationElements={drawerApplicationElements}
            tenantDataByKey={tenantDataByKey}
            hasConfigedBuildingType={hasConfigedBuildingType}
            applicationElementsPackCode={applicationElementsPackCode}
            mode={drawerMode}
            totalApplicationElementsByTopic={drawerTotalApplicationElements}
            alreadySubmit={filterApplicationElements(applicationElements, true)}
            openEnumConfigDrawer={(record: FactorInfo) => {
              setEnumConfigDrawerInfo({
                visible: true,
                record,
                mode: DetailPageMode.add,
              });
            }}
            onSearch={queryApplicationElements}
            isLinked={isEditLinkedFactors}
            drawerObjectInfo={drawerObjectInfo}
          />
        ) : null}
        {enumConfigDrawerInfo.record ? (
          <EnumConfigDrawer
            visible={enumConfigDrawerInfo.visible}
            applicationElementsPackCode={applicationElementsPackCode}
            record={enumConfigDrawerInfo.record}
            mode={enumConfigDrawerInfo.mode}
            topic={topic}
            onDrawerClose={() => {
              setEnumConfigDrawerInfo({
                visible: false,
                record: undefined,
                mode: DetailPageMode.add,
              });
            }}
            onSelectSecondRow={onEnumSubmit}
            configedDataMap={{
              building: hasConfigedBuildingType,
            }}
          />
        ) : null}
        {sortDrawerVisible ? (
          <SortDrawer
            visible={sortDrawerVisible}
            topic={topic}
            applicationElementsPackCode={applicationElementsPackCode}
            applicationElements={drawerApplicationElements}
            sortDrawerOtherElements={sortDrawerOtherElements}
            onClose={closeSortDrawer}
          />
        ) : null}
      </div>
    </div>
  );
};

export default ObjectApplicationElementSection;
