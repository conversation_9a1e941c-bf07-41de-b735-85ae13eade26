.applicetion-section {
  border-bottom: 1px solid var(--disabled-color);
  padding-bottom: 16px;
  margin-bottom: 24px;

  .config-table-wrapper {
    margin-bottom: 24px;
  }

  .top-section {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    .title-wrapper {
      min-height: 32px;
    }

    .title,
    .sub-title {
      color: var(--text-color);
      font-size: 14px;
      line-height: 22px;
    }

    .title {
      margin-top: 5px;
      font-weight: 700;
    }

    .sub-title {
      margin-top: 29px;
    }
  }
}

.object-sub-category-selection {
  margin-top: 24px;
  text-align: center;
  background: var(--layout-body-background);
  border-radius: 16px;
  padding: 16px;
  color: var(--primary-color);
  font-weight: 500;
  border: 1px dashed var(--disabled-color);
  cursor: pointer;
}
