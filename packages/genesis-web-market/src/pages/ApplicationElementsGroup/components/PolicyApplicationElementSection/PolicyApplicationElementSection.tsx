import { Ref, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { <PERSON><PERSON>, Popconfirm, Switch, message } from 'antd';

import { useRequest } from 'ahooks';
import classNames from 'classnames/bind';
import { cloneDeep, findIndex } from 'lodash-es';

import { Icon } from '@zhongan/nagrand-ui';

import {
  ApplicationElementTopic,
  ApplicationElementsGroupInfo,
  FactorInfo,
} from 'genesis-web-service/lib/market';
import { LinkageWithZipcode } from 'genesis-web-service/lib/metadata';
import { NewMarketService } from '@market/services/market/market.service.new';

import { DetailPageMode } from '@market/common/interface';

import { AnchorIdConfigByTopic, SupportSwitchTopics, TitleConfigByTopic } from '../../config';
import { filterApplicationElements, isSameApplicationElement } from '../../dealServiceData';
import { ApplicationElementsSectionRef } from '../../interface';
import ApplicationElementsDrawerNew from '../ApplicationElementsDrawerNew';
import ApplicationElementsExtraForm from '../ApplicationElementsExtraForm';
import ApplicationElementsTable from '../ApplicationElementsTable/ApplicationElementsTable';
import EnumConfigDrawer from '../EnumConfigDrawer';
import SortDrawer from '../SortDrawer';
import styles from './PolicyApplicationElementSection.module.scss';

const cx = classNames.bind(styles);

interface Props {
  displayPosition?: 'package' | 'groupDetail';
  refInstance: Ref<ApplicationElementsSectionRef>;
  topic: ApplicationElementTopic;
  applicationElementsGroupInfo: ApplicationElementsGroupInfo | undefined;
  mode?: DetailPageMode;
  applicationElementsPackCode: string;
  tenantDataByKey: {
    addressModel: string;
    addressDepth: number;
    bankModel: string;
    addressCascadedLevel: number;
    bankCascadedLevel: number;
    linkageWithZipcode: LinkageWithZipcode;
  };
  beforeAdd?: () => Promise<unknown>;
}

export const PolicyApplicationElementSection = ({
  displayPosition,
  refInstance,
  topic,
  applicationElementsGroupInfo,
  mode = DetailPageMode.add,
  applicationElementsPackCode,
  tenantDataByKey,
  beforeAdd,
}: Props): JSX.Element | null => {
  const [t] = useTranslation(['market', 'common']);
  const readOnly = mode === DetailPageMode.view || mode === DetailPageMode.copy;
  const [swichOpened, setSwitchOpened] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [sortDrawerVisible, setSortDrawerVisible] = useState(false);
  const [drawerMode, setDrawerMode] = useState(DetailPageMode.add);
  const [applicationElements, setApplicationElements] = useState<FactorInfo[]>([]);
  const [enumConfigDrawerInfo, setEnumConfigDrawerInfo] = useState<{
    visible: boolean;
    record?: FactorInfo;
    mode: DetailPageMode;
  }>({
    visible: false,
    record: undefined,
    mode: DetailPageMode.add,
  });
  const [drawerApplicationElements, setDrawerApplicationElements] = useState<FactorInfo[]>([]);
  const [drawerTotalApplicationElements, setDrawerTotalApplicationElements] = useState<FactorInfo[]>([]);

  const showSwitch = SupportSwitchTopics.includes(topic);

  const showTable = showSwitch ? swichOpened : true;

  const { runAsync: queryApplicationGroupElements, loading } = useRequest(
    (_topic, _applicationElementsPackCode) =>
      NewMarketService.ApplicationElementsPackService.query$POST$mgmt_v2_applicationelements_query_factors({
        topic: _topic,
        applicationElementsPackCode: _applicationElementsPackCode
      }),
    {
      manual: true,
    }
  );

  const queryApplicationElements = useCallback(() => {
    queryApplicationGroupElements(
      topic,
      applicationElementsGroupInfo?.applicationElementsPackCode || applicationElementsPackCode
    ).then(res => {
      const factors: FactorInfo[] = res?.factors;

      const configedData = filterApplicationElements(factors, true);
      if (configedData.length > 0) {
        setSwitchOpened(true);
      }
      setApplicationElements(factors);
    });
  }, [queryApplicationGroupElements, topic, applicationElementsPackCode, applicationElementsGroupInfo]);

  useEffect(() => {
    queryApplicationElements();
  }, [applicationElementsGroupInfo]);

  useImperativeHandle(refInstance, () => ({
    getApplicationElements: () => applicationElements,
    isOpened: () => swichOpened,
    queryApplicationElements,
  }));

  const renderSwitch = useCallback(() => {
    if (!showSwitch) {
      return null;
    }
    if (displayPosition === 'package') {
      return null;
    }
    return (
      <Switch
        disabled={readOnly}
        checked={swichOpened}
        onChange={evt => {
          if (evt) {
            beforeAdd?.().then(() => {
              setSwitchOpened(evt);
            })
          } else {
            setSwitchOpened(evt);
          }
        }}
        style={{ marginLeft: 8 }}
        size="small"
      />
    );
  }, [displayPosition, readOnly, showSwitch, swichOpened, beforeAdd]);

  const showDrawer = useCallback(
    (record?: FactorInfo) => {
      const clonedapplicationElements = cloneDeep(applicationElements);
      const currentTopicUnConfigedElements = filterApplicationElements(clonedapplicationElements, false);
      let tempApplicationElements = currentTopicUnConfigedElements.map((item, index) => {
        item.no = index + 1;

        return item;
      });

      if (record) {
        // 编辑
        record.no = 1;
        tempApplicationElements = [record];
        setDrawerTotalApplicationElements(clonedapplicationElements);
        setDrawerApplicationElements(tempApplicationElements);
        setDrawerMode(DetailPageMode.edit);
        setDrawerVisible(true);
        return;
      }

      setDrawerTotalApplicationElements(clonedapplicationElements);
      setDrawerApplicationElements(tempApplicationElements);
      setDrawerMode(DetailPageMode.add);
      setDrawerVisible(true);
    },
    [applicationElements]
  );

  const closeDrawer = useCallback(() => {
    setDrawerVisible(false);
    setDrawerTotalApplicationElements([]);
    setDrawerApplicationElements([]);
    setDrawerMode(DetailPageMode.add);
  }, []);

  const showSortDrawer = useCallback(() => {
    const clonedapplicationElements = cloneDeep(applicationElements);
    const currentTopicConfigedElements = filterApplicationElements(clonedapplicationElements, true);
    setDrawerApplicationElements(currentTopicConfigedElements);
    setSortDrawerVisible(true);
  }, [applicationElements]);

  const closeSortDrawer = useCallback(() => {
    setDrawerApplicationElements([]);
    setSortDrawerVisible(false);
    queryApplicationElements();
  }, [queryApplicationElements]);

  const onEnumSubmit = (value: string[], record: FactorInfo) => {
    const index = drawerApplicationElements.findIndex(item => isSameApplicationElement(item, record));
    drawerApplicationElements[index].factorValue = value.join(',');

    setDrawerApplicationElements([...drawerApplicationElements]);
  };

  const onDelete = useCallback(
    (record: FactorInfo) => {
      NewMarketService.ApplicationElementsPackService.deleteItems(record.id!).then(res => {
        queryApplicationElements();
      });
    },
    [queryApplicationElements]
  );

  const clear = useCallback(() => {
    NewMarketService.ApplicationElementsPackService.clearAllItems({
      applicationElementsPackCode,
      topic,
    }).then(res => {
      message.success(t('Delete Success'));
      queryApplicationElements();
    });
  }, [applicationElementsPackCode, queryApplicationElements, t, topic]);

  if (displayPosition === 'package' && filterApplicationElements(applicationElements, true).length === 0) {
    return null;
  }

  return (
    <div id={AnchorIdConfigByTopic[topic]} className={cx('applicetion-section')}>
      <div className={cx('config-table-wrapper')}>
        <div className={cx('top-section')}>
          <div className={cx('title-wrapper')}>
            <div className={cx('title')}>
              {TitleConfigByTopic[topic]}
              {renderSwitch()}
            </div>
            {showTable ? (
              <div style={{ marginTop: 12 }}>
                <ApplicationElementsExtraForm disabled={displayPosition === 'package' || readOnly} topic={topic} />
              </div>
            ) : null}
          </div>
          {showTable && displayPosition === 'groupDetail' ? (
            <div>
              <Popconfirm
                title={t('Are you sure to clear this section?')}
                okText={t('yes')}
                cancelText={t('no')}
                onConfirm={() => {
                  clear();
                }}
                disabled={readOnly}
              >
                <Button disabled={readOnly} style={{ marginRight: 8 }} icon={<Icon type="delete" />}>
                  {t('Delete')}
                </Button>
              </Popconfirm>
              <Button
                disabled={readOnly}
                onClick={() => {
                  showSortDrawer();
                }}
                style={{ marginRight: 8 }}
                icon={<Icon type="sort" />}
              >
                {t('Sort')}
              </Button>
              <Button
                disabled={mode === DetailPageMode.view}
                onClick={() => {
                  showDrawer();
                }}
                type="primary"
                ghost
                icon={<Icon type="add" style={{ fontSize: 16 }} />}
              >
                {t('Add')}
              </Button>
            </div>
          ) : null}
        </div>
        {showTable ? (
          <ApplicationElementsTable
            topic={topic}
            applicationElementsPackCode={applicationElementsPackCode}
            tenantDataByKey={tenantDataByKey}
            data={filterApplicationElements(applicationElements, true)}
            onModify={record => {
              showDrawer(record);
            }}
            onDelete={onDelete}
            loading={loading}
            mode={mode}
            configedDataMap={{}}
            onViewElementsValue={(record: FactorInfo) => {
              setEnumConfigDrawerInfo({
                visible: true,
                record,
                mode: DetailPageMode.view,
              });
            }}
          />
        ) : null}
      </div>
      {drawerVisible ? (
        <ApplicationElementsDrawerNew
          visible={drawerVisible}
          topic={topic}
          onClose={closeDrawer}
          drawerApplicationElements={drawerApplicationElements}
          tenantDataByKey={tenantDataByKey}
          applicationElementsPackCode={applicationElementsPackCode}
          mode={drawerMode}
          totalApplicationElementsByTopic={drawerTotalApplicationElements}
          alreadySubmit={filterApplicationElements(applicationElements, true)}
          openEnumConfigDrawer={(record: FactorInfo) => {
            setEnumConfigDrawerInfo({
              visible: true,
              record,
              mode: DetailPageMode.add,
            });
          }}
          onSearch={queryApplicationElements}
        />
      ) : null}
      {enumConfigDrawerInfo.record ? (
        <EnumConfigDrawer
          visible={enumConfigDrawerInfo.visible}
          applicationElementsPackCode={applicationElementsPackCode}
          record={enumConfigDrawerInfo.record}
          mode={enumConfigDrawerInfo.mode}
          topic={topic}
          onDrawerClose={() => {
            setEnumConfigDrawerInfo({
              visible: false,
              record: undefined,
              mode: DetailPageMode.add,
            });
          }}
          onSelectSecondRow={onEnumSubmit}
          configedDataMap={{}}
        />
      ) : null}
      {sortDrawerVisible ? (
        <SortDrawer
          visible={sortDrawerVisible}
          applicationElements={drawerApplicationElements}
          topic={topic}
          applicationElementsPackCode={applicationElementsPackCode}
          sortDrawerOtherElements={[]}
          onClose={closeSortDrawer}
        />
      ) : null}
    </div>
  );
};

export default PolicyApplicationElementSection;
