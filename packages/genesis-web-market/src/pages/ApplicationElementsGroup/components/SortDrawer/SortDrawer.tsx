import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, message } from 'antd';

import { Table } from '@zhongan/nagrand-ui';

import { ApplicationElementTopic } from 'genesis-web-service/lib/market';
import { FactorInfo } from 'genesis-web-service/lib/market/market.interface';

import GeneralDrawer from '@market/components/GeneralDrawer';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { renderOptionName } from '@market/utils/enum';
import { NewMarketService } from '@market/services/market/market.service.new';

interface Props {
  visible: boolean;
  applicationElements: FactorInfo[];
  sortDrawerOtherElements: FactorInfo[];
  applicationElementsPackCode: string;
  topic: ApplicationElementTopic;
  onClose: () => void;
}

export const SortDrawer = ({
  visible,
  applicationElements,
  sortDrawerOtherElements,
  applicationElementsPackCode,
  topic,
  onClose,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [applicationElementsOnSort, setApplicationElementsOnSort] = useState<FactorInfo[]>(applicationElements);
  const yesNoOptions = useBizDictAsOptions('yesNo');

  useEffect(() => {
    setApplicationElementsOnSort(
      applicationElements
        .sort((a, b) => a.orderNo - b.orderNo)
        .map((item, index) => ({
          ...item,
          orderNo: index + 1,
        }))
    );
  }, [applicationElements]);

  return (
    <GeneralDrawer
      drawerProps={{
        visible,
        title: t('Sort'),
        closable: true,
        width: 1096,
        onClose,
      }}
      content={
        <div>
          <Table
            dataSource={applicationElementsOnSort}
            columns={[
              {
                title: t('Order'),
                width: 80,
                dataIndex: 'orderNo',
              },
              {
                title: t('Field Name'),
                dataIndex: 'factorName',
                width: 200,
              },
              {
                title: t('Display Name of Field'),
                dataIndex: 'factorDisplayName',
                width: 200,
                render: (text, record) => record.factorDisplayName || t('- -'),
              },
              {
                title: t('Mandatory'),
                dataIndex: 'isRequiredName',
                width: 180,
                render: (text, record) => renderOptionName(record.isRequired, yesNoOptions),
              },
            ]}
            className="user-select-none-custom"
            emptyType="text"
            rowKey="factorCode"
            draggable
            setDataSource={newData => {
              newData.forEach((item, index) => {
                item.orderNo = index + 1;
              });
              setApplicationElementsOnSort(newData);
            }}
            pagination={false}
          />
        </div>
      }
      footer={
        <React.Fragment>
          <Button onClick={onClose} size="large" style={{ marginRight: 16 }}>
            {t('Cancel')}
          </Button>
          {applicationElementsOnSort.length > 0 ? (
            <Button
              loading={false}
              onClick={() => {
                NewMarketService.ApplicationElementsPackService.batchSave({
                  applicationElementsPackCode,
                  topic,
                  factors: [...applicationElementsOnSort, ...sortDrawerOtherElements],
                }).then(() => {
                  message.success('Submit Successfully');
                  onClose();
                });
              }}
              type="primary"
              size="large"
            >
              {t('Submit')}
            </Button>
          ) : null}
        </React.Fragment>
      }
    />
  );
};

export default SortDrawer;
