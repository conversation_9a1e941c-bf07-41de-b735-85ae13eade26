import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { message } from 'antd';

import { Drawer, Table } from '@zhongan/nagrand-ui';

import { ApplicationElementTopic } from 'genesis-web-service/lib/market';
import { FactorInfo } from 'genesis-web-service/lib/market/market.interface';

import { useBizDictAsOptions } from '@market/hook/bizDict';
import { NewMarketService } from '@market/services/market/market.service.new';
import { renderOptionName } from '@market/utils/enum';

interface Props {
  visible: boolean;
  applicationElements: FactorInfo[];
  sortDrawerOtherElements: FactorInfo[];
  applicationElementsPackCode: string;
  topic: ApplicationElementTopic;
  onClose: () => void;
}

export const SortDrawer = ({
  visible,
  applicationElements,
  sortDrawerOtherElements,
  applicationElementsPackCode,
  topic,
  onClose,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [applicationElementsOnSort, setApplicationElementsOnSort] = useState<FactorInfo[]>(applicationElements);
  const yesNoOptions = useBizDictAsOptions('yesNo');

  useEffect(() => {
    setApplicationElementsOnSort(
      applicationElements
        .sort((a, b) => a.orderNo - b.orderNo)
        .map((item, index) => ({
          ...item,
          orderNo: index + 1,
        }))
    );
  }, [applicationElements]);

  return (
    <Drawer
      open={visible}
      title={t('Sort')}
      onClose={onClose}
      readonly={!applicationElementsOnSort.length}
      onSubmit={() => {
        NewMarketService.ApplicationElementsPackService.batchSave({
          applicationElementsPackCode,
          topic,
          factors: [...applicationElementsOnSort, ...sortDrawerOtherElements],
        }).then(() => {
          message.success(t('Submit Successfully'));
          onClose();
        });
      }}
    >
      <Table
        dataSource={applicationElementsOnSort}
        columns={[
          {
            title: t('Order'),
            width: 80,
            dataIndex: 'orderNo',
          },
          {
            title: t('Field Name'),
            dataIndex: 'factorName',
            width: 200,
          },
          {
            title: t('Display Name of Field'),
            dataIndex: 'factorDisplayName',
            width: 200,
            render: (text, record) => record.factorDisplayName || t('- -'),
          },
          {
            title: t('Mandatory'),
            dataIndex: 'isRequiredName',
            width: 180,
            render: (text, record) => renderOptionName(record.isRequired, yesNoOptions),
          },
        ]}
        className="user-select-none-custom"
        emptyType="text"
        rowKey="factorCode"
        draggable
        setDataSource={newData => {
          newData.forEach((item, index) => {
            item.orderNo = index + 1;
          });
          setApplicationElementsOnSort(newData);
        }}
        pagination={false}
      />
    </Drawer>
  );
};

export default SortDrawer;
