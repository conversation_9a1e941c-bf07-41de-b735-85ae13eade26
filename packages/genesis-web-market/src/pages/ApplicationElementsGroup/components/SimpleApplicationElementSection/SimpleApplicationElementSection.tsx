import { Ref, useC<PERSON>back, useEffect, useImperative<PERSON><PERSON>le, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { <PERSON><PERSON>, Popconfirm, Switch, Tooltip, message } from 'antd';

import { useRequest } from 'ahooks';
import classNames from 'classnames/bind';
import { cloneDeep, findIndex } from 'lodash-es';

import { Icon } from '@zhongan/nagrand-ui';

import {
  ApplicationElementCustomerType,
  ApplicationElementTopic,
  ApplicationElementsGroupInfo,
  FactorInfo,
} from 'genesis-web-service/lib/market';
import { LinkageWithZipcode } from 'genesis-web-service/lib/metadata';

import { ConfigedDataByCustomerType } from '@market/common/applicationElements.interface';
import { DetailPageMode } from '@market/common/interface';

import {
  AnchorIdConfigByTopic,
  CopyFromPolicyHolderTopics,
  NoCopyTopics,
  SupportOrganizationMatrixTopics,
  SupportOrganizationTopics,
  SupportSwitchTopics,
  TitleConfigByTopic,
} from '../../config';
import { getOccupationConfigListByFactorCode } from '../../config/industryConfig';
import {
  filterApplicationElements,
  getSchemaInfoByTopic,
  isLinkageEnum,
  isLinkedZipCode,
  isSameApplicationElement,
} from '../../dealServiceData';
import { ApplicationElementsSectionRef } from '../../interface';
import ApplicationElementsDrawerNew from '../ApplicationElementsDrawerNew';
import ApplicationElementsExtraForm from '../ApplicationElementsExtraForm';
import ApplicationElementsTable from '../ApplicationElementsTable/ApplicationElementsTable';
import EnumConfigDrawer from '../EnumConfigDrawer';
import SortDrawer from '../SortDrawer';
import styles from './SimpleApplicationElementSection.module.scss';
import { NewMarketService } from '@market/services/market/market.service.new';

const cx = classNames.bind(styles);

interface Props {
  displayPosition?: 'package' | 'groupDetail';
  refInstance: Ref<ApplicationElementsSectionRef>;
  topic: ApplicationElementTopic;
  applicationElementsGroupInfo: ApplicationElementsGroupInfo | undefined;
  mode?: DetailPageMode;
  applicationElementsPackCode: string;
  tenantDataByKey: {
    addressModel: string;
    addressDepth: number;
    bankModel: string;
    addressCascadedLevel: number;
    bankCascadedLevel: number;
    linkageWithZipcode: LinkageWithZipcode;
    positionModel: string;
  };
  copyFactor?: (
    srcTopic: ApplicationElementTopic,
    targetTopic: ApplicationElementTopic,
    options: {
      customerType: ApplicationElementCustomerType;
    }
  ) => void;
  beforeAdd?: () => Promise<unknown>;
  openMatrixDrawer: (topic: ApplicationElementTopic, customerType: ApplicationElementCustomerType) => void;
}

export const SimpleApplicationElementSection = ({
  displayPosition,
  refInstance,
  topic,
  applicationElementsGroupInfo,
  mode = DetailPageMode.add,
  applicationElementsPackCode,
  tenantDataByKey,
  copyFactor,
  beforeAdd,
  openMatrixDrawer,
}: Props): JSX.Element | null => {
  const [t] = useTranslation(['market', 'common']);
  const readOnly = mode === DetailPageMode.view || mode === DetailPageMode.copy;
  const [swichOpened, setSwitchOpened] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [sortDrawerVisible, setSortDrawerVisible] = useState(false);
  const [drawerMode, setDrawerMode] = useState(DetailPageMode.add);
  const [isEditLinkedFactors, setIsEditLinkedFactors] = useState(false);
  const [applicationElements, setApplicationElements] = useState<FactorInfo[]>([]);
  const [drawerApplicationElements, setDrawerApplicationElements] = useState<FactorInfo[]>([]);
  const [enumConfigDrawerInfo, setEnumConfigDrawerInfo] = useState<{
    visible: boolean;
    record?: FactorInfo;
    mode: DetailPageMode;
  }>({
    visible: false,
    record: undefined,
    mode: DetailPageMode.add,
  });
  // 排序会根据customerType、objectCategory分开排序，但是save/factors需要全量数据
  // 所以把未排序的数据记录一下，提交的时候带上
  const [sortDrawerOtherElements, setSortDrawerOtherElements] = useState<FactorInfo[]>([]);
  const [drawerTotalApplicationElements, setDrawerTotalApplicationElements] = useState<FactorInfo[]>([]);
  const [hasConfigedIndustry, setHasConfigedIndustry] = useState<ConfigedDataByCustomerType>({
    1: {}, // 个人
    2: {}, // 企业
  });

  const hasOrganization = useMemo(() => SupportOrganizationTopics.includes(topic), [topic]);
  const showSwitch = SupportSwitchTopics.includes(topic);

  const showTable = showSwitch ? swichOpened : true;

  const { runAsync: queryApplicationGroupElements, loading } = useRequest(
    (_topic, _applicationElementsPackCode) =>
      NewMarketService.ApplicationElementsPackService.query$POST$mgmt_v2_applicationelements_query_factors({
        topic: _topic,
        applicationElementsPackCode: _applicationElementsPackCode
      }),
    {
      manual: true,
    }
  );

  const queryApplicationElements = useCallback(() => {
    queryApplicationGroupElements(
      topic,
      applicationElementsGroupInfo?.applicationElementsPackCode || applicationElementsPackCode
    ).then(res => {
      const factors: FactorInfo[] = res?.factors;

      const configedData = filterApplicationElements(factors, true);
      if (configedData.length > 0) {
        setSwitchOpened(true);
      }

      setApplicationElements(factors);
    });
  }, [queryApplicationGroupElements, topic, applicationElementsPackCode, applicationElementsGroupInfo]);

  useEffect(() => {
    queryApplicationElements();
  }, [applicationElementsGroupInfo]);

  useImperativeHandle(refInstance, () => ({
    getApplicationElements: () => applicationElements,
    isOpened: () => swichOpened,
    queryApplicationElements,
  }));

  const clearByCustomerType = useCallback(
    (customerType: ApplicationElementCustomerType) => {
      NewMarketService.ApplicationElementsPackService.clearAllItems({
        applicationElementsPackCode,
        topic,
        customerType: +customerType,
      }).then(res => {
        message.success(t('Delete Success'));
        queryApplicationElements();
      });
    },
    [applicationElementsPackCode, queryApplicationElements, t, topic]
  );

  const renderSwitch = useCallback(() => {
    if (!showSwitch) {
      return null;
    }
    if (displayPosition === 'package') {
      return null;
    }
    return (
      <Switch
        disabled={readOnly}
        checked={swichOpened}
        onChange={evt => {
          if (evt) {
            beforeAdd?.().then(() => {
              setSwitchOpened(evt);
            })
          } else {
            setSwitchOpened(evt);
          }
        }}
        style={{ marginLeft: 8 }}
        size="small"
      />
    );
  }, [displayPosition, readOnly, showSwitch, swichOpened, beforeAdd]);

  const renderCopyBtn = useCallback(
    (customerType: ApplicationElementCustomerType) => {
      if (topic === ApplicationElementTopic.policyHolder || NoCopyTopics.includes(topic)) {
        return null;
      }
      let srcTopic = ApplicationElementTopic.insured;
      let btnText = t('Copy from Insured');
      let confirmTitle = t('Copying elements from the insured module will overwrite existing content, please confirm.');
      if (CopyFromPolicyHolderTopics.includes(topic)) {
        srcTopic = ApplicationElementTopic.policyHolder;
        btnText = t('Copy from Policyholder');
        confirmTitle = t(
          'Copying elements from the policyholder module will overwrite existing content, please confirm.'
        );
      }

      return (
        <Popconfirm
          placement="topRight"
          title={confirmTitle}
          onConfirm={() => copyFactor?.(srcTopic, topic, { customerType })}
          disabled={readOnly}
          okText="Yes"
          cancelText="No"
        >
          <Button disabled={readOnly} style={{ width: 180, marginRight: 8 }}>
            {btnText}
          </Button>
        </Popconfirm>
      );
    },
    [copyFactor, readOnly, t, topic]
  );

  const showDrawer = useCallback(
    (customerType: ApplicationElementCustomerType, record?: FactorInfo) => {
      const { addressModel, addressDepth, linkageWithZipcode, bankModel, bankCascadedLevel } = tenantDataByKey;
      const clonedapplicationElements = cloneDeep(applicationElements);
      const currentTopicConfigedElements = filterApplicationElements(clonedapplicationElements, true, { customerType });
      const currentTopicUnConfigedElements = filterApplicationElements(clonedapplicationElements, false, {
        customerType,
      });
      let tempApplicationElements = currentTopicUnConfigedElements.map((item, index) => {
        item.no = index + 1;

        return item;
      });

      if (record) {
        // 编辑
        let linkageFactors: string[] = [];
        if (record.cascadeFactorCodes) {
          linkageFactors = record.cascadeFactorCodes.map(item => item.factorCode);
        } else {
          linkageFactors =
            isLinkageEnum(record, {
              addressModel,
              addressDepth,
              linkageWithZipcode,
              bankModel,
              bankCascadedLevel,
            }) || [];
        }
        // 如果是有级联关系投保要素，编辑的时候一起编辑
        if (linkageFactors?.length > 0) {
          const tempList = linkageFactors
            .map(factorCode => {
              const finded = currentTopicConfigedElements.find(
                factorInfo => factorInfo.factorCode === factorCode && factorInfo.configured
              );
              if (finded) {
                finded.inEdit = true;
              }
              return finded;
            })
            .filter(Boolean);
          const configedZipCode = currentTopicConfigedElements.find(factorInfo => factorInfo.factorCode === 'zipCode');
          if (isLinkedZipCode(record.factorCode, linkageWithZipcode) && configedZipCode) {
            configedZipCode.inEdit = true;
            tempList.push(configedZipCode);
          }
          // state.firstDrawerList = this.listAddNo(tempList);
          tempApplicationElements = tempList
            .filter(item => !!item)
            .map((item, index) => {
              item!.no = index + 1;

              return item;
            }) as FactorInfo[];
          setIsEditLinkedFactors(true);
        } else {
          record.no = 1;
          tempApplicationElements = [record];
        }
        setDrawerTotalApplicationElements(clonedapplicationElements);
        setDrawerApplicationElements(tempApplicationElements);
        setDrawerMode(DetailPageMode.edit);
        setDrawerVisible(true);
        return;
      }

      setDrawerTotalApplicationElements(clonedapplicationElements);
      setDrawerApplicationElements(tempApplicationElements);
      setDrawerMode(DetailPageMode.add);
      setDrawerVisible(true);
    },
    [applicationElements, tenantDataByKey]
  );

  const closeDrawer = useCallback(() => {
    setDrawerVisible(false);
    setIsEditLinkedFactors(false);
    setDrawerTotalApplicationElements([]);
    setDrawerApplicationElements([]);
    setDrawerMode(DetailPageMode.add);
  }, []);

  const showSortDrawer = useCallback(
    (customerType: ApplicationElementCustomerType) => {
      const clonedapplicationElements = cloneDeep(applicationElements);
      const currentTopicConfigedElements = filterApplicationElements(clonedapplicationElements, true, { customerType });
      setSortDrawerOtherElements(
        clonedapplicationElements.filter(item => item.configured && item.customerType !== customerType)
      );
      setDrawerApplicationElements(currentTopicConfigedElements);
      setSortDrawerVisible(true);
    },
    [applicationElements]
  );

  const closeSortDrawer = useCallback(() => {
    setDrawerApplicationElements([]);
    setSortDrawerVisible(false);
    queryApplicationElements();
  }, [queryApplicationElements]);

  const onEnumSubmit = (value: string[], record: FactorInfo) => {
    const index = drawerApplicationElements.findIndex(item => isSameApplicationElement(item, record));
    drawerApplicationElements[index].factorValue = value.join(',');

    if (getOccupationConfigListByFactorCode(2, record.factorCode)) {
      if (!hasConfigedIndustry[record.customerType]) {
        hasConfigedIndustry[record.customerType] = {};
      }
      hasConfigedIndustry[record.customerType][record.factorCode] = value;

      setHasConfigedIndustry({ ...hasConfigedIndustry });
    }

    setDrawerApplicationElements([...drawerApplicationElements]);
  };

  const onDelete = useCallback(
    (record: FactorInfo) => {
      const list = applicationElements;

      const { addressModel, addressDepth, bankModel, bankCascadedLevel, linkageWithZipcode } = tenantDataByKey;
      let linkageFactors: string[] = [];
      if (record.cascadeFactorCodes) {
        linkageFactors = record.cascadeFactorCodes.map(item => item.factorCode);
      } else {
        linkageFactors =
          isLinkageEnum(record, {
            addressModel,
            addressDepth,
            linkageWithZipcode,
            bankModel,
            bankCascadedLevel,
          }) || [];
      }

      // 有级联关系的投保要素，删除的时候一起删除
      if (linkageFactors?.length > 0) {
        if (isLinkedZipCode(record.factorCode, linkageWithZipcode)) {
          linkageFactors.push('zipCode');
        }
        const linkageRecord = list
          // list 中有个人和企业的数据，需要过滤一下
          .filter(item => item.customerType === record.customerType)
          .filter(item => linkageFactors.includes(item.factorCode) && item.id);
        Promise.all(linkageRecord.map(item => NewMarketService.ApplicationElementsPackService.deleteItems(item.id!))).then(() => {
          queryApplicationElements();
        });
      } else {
        NewMarketService.ApplicationElementsPackService.deleteItems(record.id!).then(res => {
          queryApplicationElements();
        });
      }
    },
    [applicationElements, queryApplicationElements, tenantDataByKey]
  );

  const schemaInfo = getSchemaInfoByTopic(topic, {
    customerType: ApplicationElementCustomerType.Company,
  });

  const customerMatrixList =
    applicationElementsGroupInfo?.applicationElementPropertyRequestList?.filter(
      item =>
        item.schemaDefType === schemaInfo?.schemaDefType &&
        item.schemaDefRefId === schemaInfo?.schemaDefRefId &&
        item.topic === +topic
    ) || [];

  const renderOrganizationSection = useCallback(() => {
    if (!hasOrganization || !showTable) {
      return null;
    }

    if (
      displayPosition === 'package' &&
      filterApplicationElements(applicationElements, true, {
        customerType: ApplicationElementCustomerType.Company,
      }).length === 0 &&
      customerMatrixList.length === 0
    ) {
      return null;
    }
    return (
      <div className={cx('config-table-wrapper')}>
        <div className={cx('top-section')}>
          <div>
            <div className={cx('sub-title')}>
              {t('Organization')}
              {SupportOrganizationMatrixTopics.includes(topic) ? (
                <Tooltip title={t('Elements Matrix')}>
                  <Icon
                    type="more"
                    onClick={() => {
                      openMatrixDrawer(topic, ApplicationElementCustomerType.Company);
                    }}
                    style={{
                      marginLeft: 8,
                      cursor: 'pointer',
                      fontWeight: 'bold',
                    }}
                  />
                </Tooltip>
              ) : null}
            </div>
          </div>
          {displayPosition === 'groupDetail' ? (
            <div className='flex items-center'>
              {renderCopyBtn(ApplicationElementCustomerType.Company)}
              <Popconfirm
                title={t('Are you sure to clear this section?')}
                okText={t('yes')}
                cancelText={t('no')}
                onConfirm={() => {
                  clearByCustomerType(ApplicationElementCustomerType.Company);
                }}
                disabled={readOnly}
              >
                <Button disabled={readOnly} style={{ marginRight: 8 }} icon={<Icon type="delete" />}>
                  {t('Delete')}
                </Button>
              </Popconfirm>
              <Button
                disabled={readOnly}
                onClick={() => {
                  setSortDrawerVisible(true);
                  showSortDrawer(ApplicationElementCustomerType.Company);
                }}
                style={{ marginRight: 8 }}
                icon={<Icon type="sort" />}
              >
                {t('Sort')}
              </Button>
              <Button
                onClick={() => {
                  showDrawer(ApplicationElementCustomerType.Company);
                }}
                disabled={mode === DetailPageMode.view}
                type="primary"
                ghost
                icon={<Icon type="add" style={{ fontSize: 16 }} />}
              >
                {t('Add')}
              </Button>
            </div>
          ) : null}
        </div>
        <ApplicationElementsTable
          topic={topic}
          applicationElementsPackCode={applicationElementsPackCode}
          tenantDataByKey={tenantDataByKey}
          data={filterApplicationElements(applicationElements, true, {
            customerType: ApplicationElementCustomerType.Company,
          })}
          onModify={record => {
            showDrawer(record.customerType, record);
          }}
          onDelete={onDelete}
          factorType={ApplicationElementCustomerType.Company}
          loading={loading}
          mode={mode}
          configedDataMap={{
            industry: hasConfigedIndustry,
          }}
          onViewElementsValue={(record: FactorInfo) => {
            setEnumConfigDrawerInfo({
              visible: true,
              record,
              mode: DetailPageMode.view,
            });
          }}
        />
      </div>
    );
  }, [
    applicationElements,
    applicationElementsPackCode,
    showSortDrawer,
    displayPosition,
    hasConfigedIndustry,
    hasOrganization,
    loading,
    mode,
    onDelete,
    readOnly,
    renderCopyBtn,
    showDrawer,
    openMatrixDrawer,
    t,
    tenantDataByKey,
    topic,
    showTable,
    customerMatrixList,
    clearByCustomerType,
  ]);

  if (
    displayPosition === 'package' &&
    filterApplicationElements(applicationElements, true).length === 0 &&
    customerMatrixList.length === 0
  ) {
    return null;
  }

  return (
    <div id={AnchorIdConfigByTopic[topic]} className={cx('applicetion-section')}>
      <div className={cx('config-table-wrapper')}>
        <div className={cx('top-section')}>
          <div className={cx('title-wrapper')}>
            <div className={cx('title')}>
              {TitleConfigByTopic[topic]}
              {renderSwitch()}
            </div>
            {showTable ? (
              <div style={{ marginTop: 12 }}>
                <ApplicationElementsExtraForm disabled={displayPosition === 'package' || readOnly} topic={topic} />
              </div>
            ) : null}
            {hasOrganization && showTable ? <div className={cx('sub-title')}>{t('Individual')}</div> : null}
          </div>
          {showTable && displayPosition === 'groupDetail' ? (
            <div className='flex items-center'>
              {renderCopyBtn(ApplicationElementCustomerType.Person)}
              <Popconfirm
                title={t('Are you sure to clear this section?')}
                okText={t('yes')}
                cancelText={t('no')}
                onConfirm={() => {
                  clearByCustomerType(ApplicationElementCustomerType.Person);
                }}
                disabled={readOnly}
              >
                <Button disabled={readOnly} style={{ marginRight: 8 }} icon={<Icon type="delete" />}>
                  {t('Delete')}
                </Button>
              </Popconfirm>
              <Button
                disabled={readOnly}
                onClick={() => {
                  showSortDrawer(ApplicationElementCustomerType.Person);
                }}
                style={{ marginRight: 8 }}
                icon={<Icon type="sort" />}
              >
                {t('Sort')}
              </Button>
              <Button
                disabled={mode === DetailPageMode.view}
                onClick={() => {
                  showDrawer(ApplicationElementCustomerType.Person);
                }}
                type="primary"
                ghost
                icon={<Icon type="add" style={{ fontSize: 16 }} />}
              >
                {t('Add')}
              </Button>
            </div>
          ) : null}
        </div>
        {showTable ? (
          <ApplicationElementsTable
            topic={topic}
            applicationElementsPackCode={applicationElementsPackCode}
            tenantDataByKey={tenantDataByKey}
            data={filterApplicationElements(applicationElements, true, {
              customerType: ApplicationElementCustomerType.Person,
            })}
            onModify={record => {
              showDrawer(record.customerType, record);
            }}
            onDelete={onDelete}
            factorType={ApplicationElementCustomerType.Person}
            loading={loading}
            mode={mode}
            configedDataMap={{
              industry: hasConfigedIndustry,
            }}
            onViewElementsValue={(record: FactorInfo) => {
              setEnumConfigDrawerInfo({
                visible: true,
                record,
                mode: DetailPageMode.view,
              });
            }}
          />
        ) : null}
      </div>
      {renderOrganizationSection()}
      {drawerVisible ? (
        <ApplicationElementsDrawerNew
          visible={drawerVisible}
          topic={topic}
          onClose={closeDrawer}
          drawerApplicationElements={drawerApplicationElements}
          tenantDataByKey={tenantDataByKey}
          hasConfigedIndustry={hasConfigedIndustry}
          applicationElementsPackCode={applicationElementsPackCode}
          mode={drawerMode}
          totalApplicationElementsByTopic={drawerTotalApplicationElements}
          alreadySubmit={filterApplicationElements(applicationElements, true)}
          openEnumConfigDrawer={(record: FactorInfo) => {
            setEnumConfigDrawerInfo({
              visible: true,
              record,
              mode: DetailPageMode.add,
            });
          }}
          onSearch={queryApplicationElements}
          isLinked={isEditLinkedFactors}
        />
      ) : null}
      {enumConfigDrawerInfo.record ? (
        <EnumConfigDrawer
          visible={enumConfigDrawerInfo.visible}
          applicationElementsPackCode={applicationElementsPackCode}
          record={enumConfigDrawerInfo.record}
          mode={enumConfigDrawerInfo.mode}
          topic={topic}
          onDrawerClose={() => {
            setEnumConfigDrawerInfo({
              visible: false,
              record: undefined,
              mode: DetailPageMode.add,
            });
          }}
          onSelectSecondRow={onEnumSubmit}
          configedDataMap={{
            industry: hasConfigedIndustry,
          }}
        />
      ) : null}
      {sortDrawerVisible ? (
        <SortDrawer
          visible={sortDrawerVisible}
          applicationElements={drawerApplicationElements}
          topic={topic}
          applicationElementsPackCode={applicationElementsPackCode}
          sortDrawerOtherElements={sortDrawerOtherElements}
          onClose={closeSortDrawer}
        />
      ) : null}
    </div>
  );
};

export default SimpleApplicationElementSection;
