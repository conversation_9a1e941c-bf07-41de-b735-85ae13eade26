import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Checkbox, Modal, Space } from 'antd';

import { ObjectCategoryComponent } from 'genesis-web-service/lib/metadata';

import styles from './ObjectSubCategoryModal.module.scss';

interface Props {
  visible: boolean;
  objectCategoryComponents: ObjectCategoryComponent[];
  selectedObjectKeyList: string[];
  configedSubCategory: string[];
  onCancel: () => Promise<void>;
  onOk: (value: string[]) => Promise<void>;
}

export const ObjectSubCategoryModal = ({
  visible,
  selectedObjectKeyList,
  configedSubCategory,
  objectCategoryComponents,
  onCancel,
  onOk,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [checkedObjectKeyList, setCheckedObjectKeyList] = useState<string[]>([]);

  useEffect(() => {
    if (visible) {
      setCheckedObjectKeyList(selectedObjectKeyList);
    }
  }, [visible]);

  return (
    <Modal
      title={t('Object Component')}
      open={visible}
      width={804}
      onOk={() => {
        onOk(checkedObjectKeyList);
      }}
      okText={t('Submit')}
      onCancel={onCancel}
      className={styles.subCategoryModal}
    >
      <div className={styles.subCategoryTable} style={{ maxHeight: 800, overflowY: 'scroll', marginBottom: 16 }}>
        <div className={styles.hintTitle}>{t('Please select the object component that you want to add')}</div>
        <Checkbox.Group
          value={checkedObjectKeyList}
          onChange={value => {
            setCheckedObjectKeyList(value);
          }}
        >
          <table>
            <thead>
              <tr>
                <th>{t('Object')}</th>
                <th>{t('Object Component')}</th>
              </tr>
            </thead>
            <tbody>
              {objectCategoryComponents.map(objectCategory => (
                <tr>
                  <td>{objectCategory.name}</td>
                  <td>
                    <Space size="middle" wrap>
                      {objectCategory.components.map(item => (
                        <Checkbox
                          disabled={configedSubCategory.includes(`${item.objectCategory}_${item.objectSubCategory}`)}
                          value={`${item.objectCategory}_${item.objectSubCategory}`}
                        >
                          {item.name}
                        </Checkbox>
                      ))}
                    </Space>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </Checkbox.Group>
      </div>
    </Modal>
  );
};

export default ObjectSubCategoryModal;
