import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Checkbox, Col, Form, Input, Row } from 'antd';
import { ColumnProps } from 'antd/lib/table';

import { useRequest } from 'ahooks';
import classNames from 'classnames/bind';
import { cloneDeep, groupBy } from 'lodash-es';

import { Drawer, Icon, SimpleSectionHeader, Table } from '@zhongan/nagrand-ui';

import { YesNoType } from 'genesis-web-service';
import { ApplicationElementTopic, FactorInfo } from 'genesis-web-service/lib/market';
import { LinkageWithZipcode, MetadataService } from 'genesis-web-service/lib/metadata';

import {
  AppElementLinkageEnum,
  ConfigedDataByCustomerType,
  ConfigedDataByObjectSubCategory,
} from '@market/common/applicationElements.interface';
import { ObjectSubCategoryType } from '@market/common/enums';
import { DetailPageMode } from '@market/common/interface';
import GeneralSelect from '@market/components/GeneralSelect';
import GeneralSteps from '@market/components/GeneralSteps';
import columnSearch from '@market/components/ToolsClass/columnSearch';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { NewMarketService } from '@market/services/market/market.service.new';
import { renderOptionName } from '@market/utils/enum';

import { DEFAULT_SUB_CODE_FONTEND_NAME, linkageEnumConfig } from '../../config/linkageEnumConfig';
import {
  batchSaveAppElementEnums,
  getApplicationElementKey,
  getFactorsForDrawer,
  isEnum,
  isEqualApplicationElement,
  isLinkageEnum,
  isLinkedZipCode,
  isSameApplicationElement,
} from '../../dealServiceData';
import {
  EnumerationConfigurationMethod,
  EnumsUploadFile,
  UploadByTemplateEnumValues,
  UserSelectEnumValues,
} from '../../interface';
import LinkageEnumDrawer from '../LinkageEnumDrawer';
import styles from './ApplicationElementsDrawerNew.module.scss';

const cx = classNames.bind(styles);

interface ApplicationElementFormValues {
  factorDisplayName?: string;
  isRequired?: string;
  isShowInApplicationForm?: string;
  isUpdatableWhenPos?: string;
  isUpdatableWhenUw?: string;
  validationRule?: string;
}

interface Props {
  visible: boolean;
  applicationElementsPackCode: string;
  onClose: () => void;
  onSearch: () => void;
  drawerApplicationElements: FactorInfo[];
  totalApplicationElementsByTopic: FactorInfo[];
  alreadySubmit?: FactorInfo[];
  tenantDataByKey: {
    addressModel: string;
    addressDepth: number;
    bankModel: string;
    addressCascadedLevel: number;
    bankCascadedLevel: number;
    linkageWithZipcode: LinkageWithZipcode;
    positionModel: string;
  };
  hasConfigedIndustry?: ConfigedDataByCustomerType;
  hasConfigedBuildingType?: ConfigedDataByObjectSubCategory;
  mode: DetailPageMode;
  openEnumConfigDrawer: (record: FactorInfo) => void;
  topic: ApplicationElementTopic;
  isLinked?: boolean;
  drawerObjectInfo?: {
    objectSubCategory: number;
  };
}

export const ApplicationElementsDrawerNew = ({
  visible,
  applicationElementsPackCode,
  onClose,
  onSearch,
  drawerApplicationElements,
  totalApplicationElementsByTopic,
  tenantDataByKey,
  hasConfigedIndustry,
  hasConfigedBuildingType,
  mode,
  openEnumConfigDrawer,
  alreadySubmit,
  drawerObjectInfo,
  topic,
  isLinked,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const containerEl = useRef<HTMLDivElement>(null);
  /* ============== 枚举使用start ============== */
  const fieldValidationRuleOptions = useBizDictAsOptions('fieldValidationRule');
  const yesNoOptions = useBizDictAsOptions('yesNo');
  const objectSubCategoryOptions = useBizDictAsOptions('objectSubCategory');
  const customerSubTypeOptions = useBizDictAsOptions('customerSubType');
  /* ============== 枚举使用end ============== */
  const [current, setCurrent] = useState(0);
  const [selectedFactors, setSelectedFactors] = useState<string[]>(['8||beneficiaryRatio']);
  const [linkageEnumDrawervisible, setLinkageEnumDrawervisible] = useState<boolean>(false);
  const [editLinkageEnumData, setEditLinkageEnumData] = useState<FactorInfo[]>([]);
  const [linkageDictkeys, setLinkageDictkeys] = useState<string[]>([]);
  const [extendFactorInfo, setExtendFactorInfo] = useState<FactorInfo>();
  const [selectedCascadeFactorCodes, setSelectedCascadeFactorCodes] = useState<
    {
      level: number;
      factorCode: string;
    }[]
  >([]);
  const [selectedDatabase, setSelectedDatabase] = useState<string>();
  const [selectedEnumsByFactorCode, setSelectedEnumsByFactorCode] = useState<Record<string, string[]>>({});
  const [uploadedFileByFactorCode, setUploadedFileByFactorCode] = useState<Record<string, EnumsUploadFile>>({});
  const [uniquePathMap, setUniquePathMap] = useState<Record<string, AppElementLinkageEnum>>({});
  const [vehicleDatabaseList, setVehicleDatabaseList] = useState<string[]>([]);

  const [form] = Form.useForm();

  const onVehicleDatabaseChange = (subGroupCode: string) => {
    const realSubGroupCode = subGroupCode === DEFAULT_SUB_CODE_FONTEND_NAME ? undefined : subGroupCode;
    MetadataService.queryCustomizedBizDictCascadingDetail('vehicleDatabase', realSubGroupCode || null).then(res => {
      const basicInfo = res.find(item => item.subGroupCode === realSubGroupCode);

      if (basicInfo) {
        const tempCascadeFactorCodes = [];
        let currentData = basicInfo.cascadingLevelDetail;

        while (currentData?.dictKey) {
          tempCascadeFactorCodes.push({
            level: currentData.targetLevel,
            factorCode: currentData.dictKey,
          });
          currentData = currentData.child;
        }
        setSelectedDatabase(subGroupCode);
        setSelectedCascadeFactorCodes(tempCascadeFactorCodes || []);
      }
    });
  };

  useEffect(() => {
    if (drawerObjectInfo?.objectSubCategory === ObjectSubCategoryType.Vehicle) {
      onVehicleDatabaseChange(DEFAULT_SUB_CODE_FONTEND_NAME);
      MetadataService.querySubGroupCodeList('vehicleDatabase').then(subGroupCodeListRes => {
        // 为了兼容历史数据，默认的第一套车型库的subCode是null，前端取到之后兼容一下
        const defaultCodeIndex = subGroupCodeListRes.findIndex(code => code === null);

        subGroupCodeListRes[defaultCodeIndex] = DEFAULT_SUB_CODE_FONTEND_NAME;
        // 将默认车型库排在第一个
        setVehicleDatabaseList(
          subGroupCodeListRes.sort((a, b) => {
            if (a === DEFAULT_SUB_CODE_FONTEND_NAME) {
              return -1;
            }
            return 0;
          }) as string[]
        );
      });
    }
  }, [visible]);

  useEffect(() => {
    if (visible && mode === DetailPageMode.edit) {
      setSelectedFactors(drawerApplicationElements.map(item => getApplicationElementKey(item)));
      setCurrent(1);
    }
  }, [visible]);

  const closeDrawer = useCallback(() => {
    setCurrent(0);
    setSelectedFactors([]);
    setLinkageEnumDrawervisible(false);
    setEditLinkageEnumData([]);
    setLinkageDictkeys([]);
    setExtendFactorInfo(undefined);
    setSelectedCascadeFactorCodes([]);
    setSelectedDatabase(undefined);
    setSelectedEnumsByFactorCode({});
    setUniquePathMap({});
    onClose();
  }, [onClose]);

  const { runAsync: batchSaveService, loading: submitLoading } = useRequest(
    (
      requestBody: {
        factors: FactorInfo[];
        applicationElementsPackCode: string;
        topic: ApplicationElementTopic;
      },
      batchSaveAppElements: {
        factor: FactorInfo;
        enums: string[];
      }[]
    ) =>
      NewMarketService.ApplicationElementsPackService.factorsSave(
        requestBody.applicationElementsPackCode,
        requestBody.topic,
        requestBody.factors
      ).then(() => {
        Promise.all([
          ...batchSaveAppElements.map(factorAndEnums =>
            batchSaveAppElementEnums(
              factorAndEnums.factor,
              factorAndEnums.enums,
              applicationElementsPackCode,
              1,
              uniquePathMap || {}
            )
          ),
        ]).then(() => {
          closeDrawer();
          onSearch();
        });
      }),
    {
      manual: true,
    }
  );

  const renderStep1Content = useCallback(() => {
    let groupedElements = Object.values(groupBy(drawerApplicationElements, 'customerSubType'));
    if (topic === ApplicationElementTopic.object) {
      groupedElements = Object.values(groupBy(drawerApplicationElements, 'objectSubCategory'));
    }

    return (
      <div className="flex-1 h-[calc(100%-57px)] flex flex-col">
        {topic === ApplicationElementTopic.object && vehicleDatabaseList.length > 1 ? (
          <div style={{ width: '100%' }}>
            <Form.Item label={t('Vehicle Database Name')} colon={false} required>
              <GeneralSelect
                // 编辑的时候不允许修改
                // 编辑会自动带出级联的几个字段(比如 Make-Model)，但是不同的车型库会有不同的级联字段(Type-Year)
                // 切换车型库会找不到字段
                disabled={mode === DetailPageMode.edit}
                style={{ width: 240 }}
                placeholder={t('Please input')}
                option={vehicleDatabaseList.map(subGroupCode => ({
                  label: subGroupCode,
                  value: subGroupCode,
                }))}
                allowClear={false}
                onChange={onVehicleDatabaseChange}
                value={selectedDatabase}
              />
            </Form.Item>
          </div>
        ) : null}
        <div className={cx('tips')} style={{ marginBottom: 24 }}>
          {t('Please select at least one element.')}
        </div>
        <div className="flex-1 overflow-auto h-full">
          <Checkbox.Group
            value={selectedFactors}
            onChange={value => {
              setSelectedFactors(value);
            }}
          >
            {groupedElements?.map((group, groupIndex) => (
              <div
                className={styles.elementsGroupWrapper}
                style={{
                  borderBottom: groupIndex === groupedElements.length - 1 ? 0 : '1px solid var(--divider-color)',
                }}
              >
                <SimpleSectionHeader>
                  <div style={{ fontWeight: 700 }}>
                    {topic === ApplicationElementTopic.object
                      ? (objectSubCategoryOptions.find(item => Number(item.value) === group[0].objectSubCategory)
                          ?.label ?? t('Elements'))
                      : (customerSubTypeOptions.find(item => Number(item.value) === group[0].customerSubType)?.label ??
                        t('Elements'))}
                  </div>
                </SimpleSectionHeader>
                <Row>
                  {group.map(factor => (
                    <Col style={{ marginBottom: 24 }} span={8}>
                      <Checkbox value={getApplicationElementKey(factor)}>
                        {`${factor.factorName} (${factor.factorCode})`}
                      </Checkbox>
                    </Col>
                  ))}
                </Row>
              </div>
            ))}
          </Checkbox.Group>
        </div>
      </div>
    );
  }, [
    drawerApplicationElements,
    mode,
    selectedDatabase,
    selectedFactors,
    t,
    topic,
    vehicleDatabaseList,
    customerSubTypeOptions,
  ]);

  const attachVehicleDatabaseProps = useCallback(
    (applicationElementList: FactorInfo[]) => {
      // 只有修改过车型库才需要对投保要素进行操作，否则会把配置错误的清除
      if (selectedCascadeFactorCodes && selectedCascadeFactorCodes.length > 0) {
        // 修改车型库选择的时候更新级联配置数据
        applicationElementList
          .filter(applicationElement => applicationElement.bizDictGroupCode === 'vehicleDatabase')
          .forEach(applicationElement => {
            if (
              selectedCascadeFactorCodes.find(
                cascadeConfig => cascadeConfig.factorCode === applicationElement.factorCode
              )
            ) {
              applicationElement.cascadeFactorCodes = selectedCascadeFactorCodes;
              applicationElement.bizDictSubGroupCode = selectedDatabase;
            } else {
              applicationElement.cascadeFactorCodes = undefined;
              applicationElement.bizDictSubGroupCode = undefined;
            }
          });
      }

      return applicationElementList;
    },
    [selectedCascadeFactorCodes, selectedDatabase]
  );

  const onConfigureClick = useCallback(
    (record: FactorInfo) => {
      const { addressModel, addressDepth, bankModel, bankCascadedLevel, linkageWithZipcode } = tenantDataByKey;
      if (record.cascadeFactorCodes) {
        const linkageFactorCodes = record.cascadeFactorCodes
          .sort((a, b) => a.level - b.level)
          .map(item => item.factorCode);
        const linkageApplicationElements = linkageFactorCodes
          .map(code => totalApplicationElementsByTopic.find(item => item.factorCode === code))
          .filter(factorInfo => (mode === DetailPageMode.edit ? factorInfo?.configured : true));
        const linkageDictKeys = linkageApplicationElements.map(item => item?.bizDictKey);

        setLinkageEnumDrawervisible(true);
        setEditLinkageEnumData(attachVehicleDatabaseProps(linkageApplicationElements.filter(Boolean) as FactorInfo[]));
        setLinkageDictkeys(linkageDictKeys.filter(Boolean) as string[]);
      } else if (
        isLinkageEnum(record, {
          addressModel,
          addressDepth,
          linkageWithZipcode,
          bankModel,
          bankCascadedLevel,
        })
      ) {
        const linkageFactorsForDrawer = getFactorsForDrawer(record, {
          addressDepth,
          bankCascadedLevel,
        });

        const currentCustomerTypeList = totalApplicationElementsByTopic.filter(
          factorInfo => factorInfo.customerType === record.customerType
        );
        const linkageEnumData = linkageFactorsForDrawer
          .map(factorCode => currentCustomerTypeList.find(factorInfo => factorInfo.factorCode === factorCode))
          // 编辑的时候过滤掉联动层级中没有配置的投保要素
          .filter(factorInfo => (mode === DetailPageMode.edit ? factorInfo?.configured : true));

        const zipCodeApplicationElement = isLinkedZipCode(record.factorCode, linkageWithZipcode)
          ? currentCustomerTypeList.find(factorInfo => {
              // 编辑情况下必须已经配置过zipcode才会联动
              if (mode === DetailPageMode.edit) {
                return factorInfo.factorCode === 'zipCode' && factorInfo.configured;
              }
              // 新增情况
              return factorInfo.factorCode === 'zipCode';
            })
          : undefined;

        setLinkageEnumDrawervisible(true);
        // 级联的因子不一定存在，customer elements那边可以取消因子
        setEditLinkageEnumData(linkageEnumData.filter(Boolean) as FactorInfo[]);
        setLinkageDictkeys(linkageEnumConfig[record.factorCode].linkageMetadataKey);
        setExtendFactorInfo(zipCodeApplicationElement);
      } else {
        openEnumConfigDrawer?.(record);
      }
    },
    [attachVehicleDatabaseProps, mode, openEnumConfigDrawer, tenantDataByKey, totalApplicationElementsByTopic]
  );

  const getColumns = useCallback(() => {
    const { getColumnSearchProps } = new columnSearch();
    const tempColumns: ColumnProps<FactorInfo>[] = [
      {
        title: t('Field Name', { ns: 'market' }),
        dataIndex: 'factorName',
        width: 160,
        ...getColumnSearchProps('factorName'),
      },
      {
        title: t('Field Code'),
        dataIndex: 'factorCode',
        width: 160,
      },
      {
        title: t('Display Name of Field', { ns: 'market' }),
        width: 180,
        dataIndex: 'factorDisplayName',
        render: (text, record) => (
          <Form.Item
            initialValue={record.factorDisplayName}
            name={[getApplicationElementKey(record), 'factorDisplayName']}
            noStyle
          >
            <Input disabled={mode === DetailPageMode.view} />
          </Form.Item>
        ),
      },
      {
        title: t('Field Validation Rule', { ns: 'market' }),
        dataIndex: 'validationRule',
        width: 180,
        render: (text, record) => (
          <Form.Item
            initialValue={record.validationRule}
            name={[getApplicationElementKey(record), 'validationRule']}
            noStyle
          >
            <GeneralSelect
              style={{ width: '130px' }}
              option={fieldValidationRuleOptions.map(option => ({
                value: +option.value,
                label: option.label,
              }))}
              disabled={mode === DetailPageMode.view}
              getPopupContainer={() => containerEl?.current || document.body}
            />
          </Form.Item>
        ),
      },
      {
        title: t('Enumerated', { ns: 'market' }),
        width: 120,
        render: (text, record) => {
          const canConfig = isEnum(record, {
            ...tenantDataByKey,
            hasConfigedIndustry,
            hasConfigedBuildingType,
          });

          return (
            <Button
              type="default"
              className="enum-configure-btn"
              onClick={() => {
                onConfigureClick(record);
              }}
              disabled={!canConfig}
            >
              {t('Configure', { ns: 'market' })}
            </Button>
          );
        },
      },
      {
        title: t('Mandatory', { ns: 'market' }),
        dataIndex: 'isRequired',
        width: 120,
        render: (text, record) => (
          <Form.Item
            initialValue={record.isRequired || YesNoType.No}
            name={[getApplicationElementKey(record), 'isRequired']}
            noStyle
          >
            <GeneralSelect
              showSearch={false}
              style={{ width: '130px' }}
              option={yesNoOptions.map(option => ({
                value: option.value,
                label: option.label,
              }))}
              allowClear={false}
              disabled={mode === DetailPageMode.view}
              getPopupContainer={() => containerEl?.current || document.body}
            />
          </Form.Item>
        ),
      },
      {
        title: t('Show in Application Form'),
        dataIndex: 'isShowInApplicationForm',
        width: 210,
        render: (_, record) => (
          <Form.Item
            initialValue={record.isShowInApplicationForm || +YesNoType.Yes}
            name={[getApplicationElementKey(record), 'isShowInApplicationForm']}
            noStyle
          >
            <GeneralSelect
              allowClear={false}
              showSearch={false}
              style={{ width: '130px' }}
              disabled={mode === DetailPageMode.view}
              getPopupContainer={() => containerEl?.current || document.body}
              option={yesNoOptions.map(({ value, label }) => ({
                label,
                value: +value,
              }))}
            />
          </Form.Item>
        ),
      },
      {
        title: t('Updatable When POS', { ns: 'market' }),
        dataIndex: 'isUpdatableWhenPos',
        width: 180,
        render: (text, record) => (
          <Form.Item
            initialValue={record.isUpdatableWhenPos || +YesNoType.Yes}
            name={[getApplicationElementKey(record), 'isUpdatableWhenPos']}
            noStyle
          >
            <GeneralSelect
              showSearch={false}
              style={{ width: '130px' }}
              option={yesNoOptions.map(option => ({
                value: +option.value,
                label: option.label,
              }))}
              allowClear={false}
              disabled={mode === DetailPageMode.view}
              getPopupContainer={() => containerEl?.current || document.body}
            />
          </Form.Item>
        ),
      },
      {
        title: t('Updatable When Manual UW', { ns: 'market' }),
        dataIndex: 'isUpdatableWhenUw',
        width: 240,
        render: (text, record) => (
          <Form.Item
            initialValue={record.isUpdatableWhenUw || +YesNoType.Yes}
            name={[getApplicationElementKey(record), 'isUpdatableWhenUw']}
            noStyle
          >
            <GeneralSelect
              showSearch={false}
              style={{ width: '130px' }}
              option={yesNoOptions.map(option => ({
                value: +option.value,
                label: option.label,
              }))}
              allowClear={false}
              disabled={mode === DetailPageMode.view}
              getPopupContainer={() => containerEl?.current || document.body}
            />
          </Form.Item>
        ),
      },
    ];

    if (topic === ApplicationElementTopic.object) {
      tempColumns.splice(1, 0, {
        title: t('Object Sub-category', { ns: 'market' }),
        dataIndex: 'objectSubCategory',
        width: 140,
        render: (text: string) => renderOptionName(`${text}`, objectSubCategoryOptions),
      });
    }

    return tempColumns;
  }, [
    fieldValidationRuleOptions,
    hasConfigedBuildingType,
    hasConfigedIndustry,
    mode,
    objectSubCategoryOptions,
    onConfigureClick,
    t,
    tenantDataByKey,
    topic,
    yesNoOptions,
  ]);

  const renderStep2Content = useCallback(
    () => (
      <Form form={form}>
        <Table
          columns={getColumns()}
          dataSource={attachVehicleDatabaseProps(
            drawerApplicationElements
              .filter(item => selectedFactors.findIndex(factorKey => isEqualApplicationElement(factorKey, item)) >= 0)
              .sort((a, b) => (a.orderNo || 999) - (b.orderNo || 999))
          )}
          scroll={{ x: 'max-content', y: 700 }}
          pagination={false}
        />
      </Form>
    ),
    [attachVehicleDatabaseProps, drawerApplicationElements, form, getColumns, selectedFactors]
  );

  const onLinkageDrawerClose = () => {
    setLinkageDictkeys([]);
    setExtendFactorInfo(undefined);
    setEditLinkageEnumData([]);
    setLinkageEnumDrawervisible(false);
  };

  const onLinkageEnumDrawerSave = (values: UserSelectEnumValues | UploadByTemplateEnumValues) => {
    if (values.uploadApplicationElementsType === EnumerationConfigurationMethod.userSelect) {
      const {
        selectedKeysMap: selectedEnumKeysByDictKey,
        selectedNamesMap: selectedEnumNamesByDictKey,
        extendsList: selectedExtendKeyList,
        uniquePathMap: _uniquePathMap,
      } = values;
      const tempSelectedEnumsByFactorCode = selectedEnumsByFactorCode ? { ...selectedEnumsByFactorCode } : {};

      linkageDictkeys.forEach((dictKey, index) => {
        if (selectedEnumKeysByDictKey[dictKey] && editLinkageEnumData[index]) {
          tempSelectedEnumsByFactorCode[editLinkageEnumData[index].factorCode] = selectedEnumKeysByDictKey[dictKey];

          // bank 连带逻辑
          if (editLinkageEnumData[index].factorCode === 'bankCode') {
            tempSelectedEnumsByFactorCode.bankName = selectedEnumNamesByDictKey[dictKey];
          } else if (editLinkageEnumData[index].factorCode === 'bankBranchCode') {
            tempSelectedEnumsByFactorCode.bankBranchName = selectedEnumNamesByDictKey[dictKey];
          }
        }
      });

      if (extendFactorInfo) {
        tempSelectedEnumsByFactorCode[extendFactorInfo.factorCode] = selectedExtendKeyList;
      }

      setSelectedEnumsByFactorCode(tempSelectedEnumsByFactorCode);
      setUniquePathMap(_uniquePathMap);
    } else {
      setUploadedFileByFactorCode({
        ...uploadedFileByFactorCode,
        ...(values.fileInfoMap || {}),
      });
    }
  };

  const getBatchSaveAppElements = useCallback(
    (applicationElement: FactorInfo) => {
      const tempList = [];
      /**
       * 这里不需要判断级联的层级，因为在层级以外的投保要素不会存在枚举值
       * 没有枚举值不会发批量存储的接口
       */
      if (linkageEnumConfig[applicationElement.factorCode] || applicationElement.cascadeFactorCodes) {
        tempList.push({
          factor: applicationElement,
          enums: selectedEnumsByFactorCode[applicationElement.factorCode] || [],
        });
      }

      return tempList;
    },
    [selectedEnumsByFactorCode]
  );

  const onSubmit = useCallback(() => {
    form.validateFields().then((values: Record<string, ApplicationElementFormValues>) => {
      const applicationElementList = cloneDeep(
        drawerApplicationElements.filter(
          item => selectedFactors.findIndex(factorKey => isEqualApplicationElement(factorKey, item)) >= 0
        )
      );
      const applicationElementListForSubmit: FactorInfo[] = [];
      const appElementsForBatchSave: {
        factor: FactorInfo;
        enums: string[];
      }[] = [];

      applicationElementList.forEach(applicationElement => {
        const key = getApplicationElementKey(applicationElement);
        const formValues = values[key] || {};

        applicationElement.validationRule = formValues.validationRule || '';
        applicationElement.isShowInApplicationForm = formValues.isShowInApplicationForm || '';
        applicationElement.isUpdatableWhenPos = formValues.isUpdatableWhenPos || '';
        applicationElement.isUpdatableWhenUw = formValues.isUpdatableWhenUw || '';
        applicationElement.factorDisplayName = formValues.factorDisplayName || '';
        applicationElement.isRequired = formValues.isRequired || '';
        applicationElement.configured = true;

        const enumsFileInfo = uploadedFileByFactorCode[applicationElement.factorCode];

        if (enumsFileInfo) {
          applicationElement.uploadApplicationElementsType = EnumerationConfigurationMethod.uploadByTemplate;
          applicationElement.fileName = enumsFileInfo.fileName;
          applicationElement.fileUniqueCode = enumsFileInfo.fileUniqueCode;
        }

        if (selectedEnumsByFactorCode[applicationElement.factorCode]) {
          applicationElement.uploadApplicationElementsType = EnumerationConfigurationMethod.userSelect;
          applicationElement.fileName = undefined;
          applicationElement.fileUniqueCode = undefined;
        }

        applicationElementListForSubmit.push(applicationElement);

        appElementsForBatchSave.push(...getBatchSaveAppElements(applicationElement));
      });

      attachVehicleDatabaseProps(applicationElementListForSubmit);

      applicationElementListForSubmit.forEach(applicationElement => {
        if (applicationElement.bizDictSubGroupCode === DEFAULT_SUB_CODE_FONTEND_NAME) {
          applicationElement.bizDictSubGroupCode = undefined;
        }
      });

      if (mode === DetailPageMode.add || isLinked) {
        const requestBody = {
          factors: applicationElementListForSubmit
            .sort((a, b) => (a.orderNo || 9999) - (b.orderNo || 9999))
            .map((item, index) => ({
              ...item,
              orderNo: index + 1,
            })),
          applicationElementsPackCode,
          topic,
        };

        batchSaveService(requestBody, appElementsForBatchSave);
        return;
      }

      const editingApplicationElement = applicationElementListForSubmit[0];

      NewMarketService.ApplicationElementsPackService.update(editingApplicationElement).then(() => {
        closeDrawer();
        onSearch();
      });
    });
  }, [
    alreadySubmit,
    applicationElementsPackCode,
    selectedEnumsByFactorCode,
    batchSaveService,
    closeDrawer,
    drawerApplicationElements,
    form,
    getBatchSaveAppElements,
    isLinked,
    mode,
    onSearch,
    selectedFactors,
    uploadedFileByFactorCode,
    attachVehicleDatabaseProps,
    topic,
  ]);

  return (
    <Drawer
      open={visible}
      title={t('Application Elements')}
      onClose={closeDrawer}
      footer={
        current === 0 ? (
          <div className="text-right">
            <Button onClick={closeDrawer} size="large" className="mr-md">
              {t('Cancel')}
            </Button>
            <Button
              onClick={() => {
                setCurrent(1);
              }}
              type="primary"
              size="large"
              disabled={selectedFactors.length === 0}
            >
              {t('Next')}
            </Button>
          </div>
        ) : (
          <div className="text-right">
            {mode === DetailPageMode.add ? (
              <Button
                type="text"
                onClick={() => {
                  setCurrent(0);
                }}
                style={{ float: 'left' }}
                icon={<Icon type="left" />}
              >
                {t('Back')}
              </Button>
            ) : null}
            <Button onClick={closeDrawer} size="large" className="mr-md">
              {t('Cancel')}
            </Button>
            <Button onClick={onSubmit} loading={submitLoading} type="primary" size="large">
              {t('Submit')}
            </Button>
          </div>
        )
      }
    >
      <div ref={containerEl} className="flex flex-col h-full">
        <div style={{ width: '80%', marginBottom: 24 }}>
          <GeneralSteps
            current={current}
            steps={[
              {
                title: t('Elements Selection'),
              },
              {
                title: t('Elements Detail Configuration'),
              },
            ]}
          />
        </div>
        {current === 0 ? renderStep1Content() : renderStep2Content()}
        <LinkageEnumDrawer
          applicationElementsPackCode={applicationElementsPackCode}
          mode={mode === DetailPageMode.edit ? DetailPageMode.edit : DetailPageMode.add}
          visible={linkageEnumDrawervisible}
          initData={selectedEnumsByFactorCode}
          linkageFactors={editLinkageEnumData}
          linkageDictkeys={linkageDictkeys}
          extendFactorInfo={extendFactorInfo}
          onDrawerClose={onLinkageDrawerClose}
          onSave={onLinkageEnumDrawerSave}
          topic={topic}
        />
      </div>
    </Drawer>
  );
};

export default ApplicationElementsDrawerNew;
