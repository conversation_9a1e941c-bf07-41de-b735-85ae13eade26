import { useTranslation } from 'react-i18next';

import classNames from 'classnames/bind';

import { Modal } from '@zhongan/nagrand-ui';

import ModalTitle from '@market/components/ModalTitle';

import styles from './EditReminderModal.module.scss';

const cx = classNames.bind(styles);

interface Props {
  visible: boolean;
  onOk: () => void;
  onCancel: () => void;
  packageNameList: string[];
}

export const EditReminderModal = ({ visible, onOk, onCancel, packageNameList }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  return (
    <Modal
      title={<ModalTitle type="warning" text={t('Edit Reminder')} />}
      visible={visible}
      onOk={onOk}
      onCancel={onCancel}
      okText={t('Confirm')}
      closable={false}
      zIndex={4000}
    >
      <p style={{ fontSize: 14 }}>
        {t(
          'The following package(s) have used this application elements group. Editing this record and the following package(s) will be affected. Please check before change:'
        )}
      </p>
      <ol className={cx('package-name-list')}>
        {packageNameList.map(name => (
          <li style={{ fontSize: 14 }}>{name}</li>
        ))}
      </ol>
    </Modal>
  );
};

export default EditReminderModal;
