import { useTranslation } from 'react-i18next';

import { Form, Radio } from 'antd';

import { ApplicationElementTopic } from 'genesis-web-service';

import { YesNoOptions } from '@market/common/enums';
import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';

interface Props {
  topic: ApplicationElementTopic;
  disabled?: boolean;
}

export const ApplicationElementsExtraForm = ({ topic, disabled = false }: Props): JSX.Element | null => {
  const [t] = useTranslation(['market', 'common']);
  /* ============== 枚举使用start ============== */
  const supportBeneficiaryTypeOptions = useBizDictAsOptions('supportBeneficiaryType');
  const assigneeTypeOptions = useBizDictAsOptions('assigneeType');
  /* ============== 枚举使用end ============== */

  if (topic === ApplicationElementTopic.insured) {
    return (
      <div key={ApplicationElementTopic.insured}>
        <Form.Item
          className="mb-0"
          label={t('Is Insured Mandatory?')}
          name="insuredMandatory"
          initialValue
          id="insuredMandatory"
          rules={[
            {
              required: true,
              message: t('Please select'),
            },
          ]}
        >
          <Radio.Group disabled={disabled} options={YesNoOptions} />
        </Form.Item>
      </div>
    );
  }

  if (topic === ApplicationElementTopic.beneficiary) {
    return (
      <div key={ApplicationElementTopic.beneficiary}>
        <Form.Item
          label={t('Supported Beneficiary Type')}
          name="supportBeneficiaryTypes"
          rules={[
            {
              required: true,
              message: t('Please select'),
            },
          ]}
          id="supportBeneficiaryTypes"
        >
          <GeneralSelect
            disabled={disabled}
            option={supportBeneficiaryTypeOptions.map(item => ({
              ...item,
              value: +item.value,
            }))}
            style={{ width: 500 }}
            mode="multiple"
          />
        </Form.Item>
      </div>
    );
  }

  if (topic === ApplicationElementTopic.assignee) {
    return (
      <div key={ApplicationElementTopic.assignee}>
        <Form.Item
          label={t('Supported Assignee Type')}
          name="supportAssigneeTypes"
          id="supportAssigneeTypes"
          rules={[
            {
              required: true,
              message: t('Please select'),
            },
          ]}
        >
          <GeneralSelect
            disabled={disabled}
            option={assigneeTypeOptions.map(item => ({
              ...item,
              value: +item.value,
            }))}
            style={{ width: 500 }}
            mode="multiple"
          />
        </Form.Item>
      </div>
    );
  }

  return null;
};

export default ApplicationElementsExtraForm;
