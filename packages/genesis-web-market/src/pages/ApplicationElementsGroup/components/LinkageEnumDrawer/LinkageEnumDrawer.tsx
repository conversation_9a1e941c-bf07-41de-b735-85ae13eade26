import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Radio, Spin } from 'antd';

import { useRequest } from 'ahooks';

import { Drawer, Icon } from '@zhongan/nagrand-ui';

import { BizDictItem } from 'genesis-web-service/lib/foundation/foundation.interface';
import { ApplicationElementTopic, FactorInfo } from 'genesis-web-service/lib/market';
import { MetadataService } from 'genesis-web-service/lib/metadata';

import { AppElementLinkageEnum } from '@market/common/applicationElements.interface';
import { UsageScenario } from '@market/common/enums';
import { DetailPageMode } from '@market/common/interface';
import { ColumnOption } from '@market/components/DynamicLinkageSelectColumn/DynamicLinkageSelectColumn';
import LinkageEnumSelector from '@market/components/LinkageEnumSelector';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { getGroupCascadingEnums } from '@market/marketService/package.service';

import { DEFAULT_SUB_CODE_FONTEND_NAME } from '../../config/linkageEnumConfig';
import { collectUniquePathMap, generateBizDictUniquePath, getAppElementConfigedEnums } from '../../dealServiceData';
import {
  EnumerationConfigurationMethod,
  EnumsUploadFile,
  UploadByTemplateEnumValues,
  UserSelectEnumValues,
} from '../../interface';
import LinkageEnumUploadSection from '../LinkageEnumUploadSection';

const mapBizdictToEnum = (bizDict: BizDictItem, index: number): AppElementLinkageEnum => ({
  parentValue: bizDict.parentValue as string,
  code: bizDict.dictValue as string,
  name: bizDict.dictValueName,
  // key: `${bizDict.dictValue}${CONNECTOR_CODE}${bizDict.dictValueName}${CONNECTOR_CODE}${bizDict.itemExtend1 || ''}`,
  key: generateBizDictUniquePath(bizDict.dictKey)(
    bizDict.dictValue,
    bizDict.dictValueName,
    bizDict.itemExtend1,
    bizDict.parentValue,
    bizDict.parentValueName
  ),
  dictKey: bizDict.dictKey,
  extraInfo: bizDict.itemExtend1,
  no: index + 1,
  childList: bizDict.childList?.map(mapBizdictToEnum) || [],
});

const mapEnumToColumnOption = (item: AppElementLinkageEnum): ColumnOption => ({
  key: item.key,
  code: item.code,
  name: item.name,
  columnKey: item.dictKey!,
  extraInfo: item.extraInfo,
  childList: item.childList?.map(mapEnumToColumnOption) || [],
});

interface Props {
  mode: DetailPageMode;
  visible: boolean;
  applicationElementsPackCode: string;
  initData?: Record<string, string[]>;
  linkageFactors: FactorInfo[];
  linkageDictkeys: string[];
  extendFactorInfo?: FactorInfo;
  topic: ApplicationElementTopic;
  onDrawerClose: () => void;
  onSave?: (values: UserSelectEnumValues | UploadByTemplateEnumValues) => void;
}

export const LinkageEnumDrawer = ({
  mode,
  visible,
  applicationElementsPackCode,
  initData,
  linkageFactors,
  linkageDictkeys,
  onDrawerClose,
  onSave,
  extendFactorInfo,
  topic,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [appElementLinkageEnums, setAppElementLinkageEnums] = useState<AppElementLinkageEnum[]>([]);
  /* ============== 枚举使用start ============== */
  const uploadApplicationElementsTypeOptions = useBizDictAsOptions('uploadApplicationElementsType');
  /* ============== 枚举使用end ============== */
  /**
   * 以columnKey为key，选中的code数组为值组成的map
   */
  const [selectedEnumsByDictKeyMap, setSelectedEnumsByDictKeyMap] = useState<Record<string, string[]>>({});
  const [configMethod, setConfigMethod] = useState<EnumerationConfigurationMethod>(
    EnumerationConfigurationMethod.userSelect
  );
  const [file, setFile] = useState<EnumsUploadFile>();

  const { runAsync: queryBizDictService, loading } = useRequest(
    (applicationElement: FactorInfo) => {
      if (applicationElement.bizDictGroupCode) {
        const realSubGroupCode =
          applicationElement?.bizDictSubGroupCode === DEFAULT_SUB_CODE_FONTEND_NAME
            ? null
            : applicationElement.bizDictSubGroupCode!;
        return getGroupCascadingEnums([
          {
            groupCode: applicationElement.bizDictGroupCode,
            subGroupCode: realSubGroupCode,
          },
        ]);
      }
      const key = applicationElement.bizDictKey!;

      if (['address1', 'bank', 'industry'].includes(key)) {
        return MetadataService.queryBizDictBigKey(applicationElement.bizDictKey!).then(res => {
          /* Loan Company 枚举来自于配置中心维护的bank，且分类标记为loan company的bank */
          if (key === 'bank' && linkageFactors[0].factorCode === 'loanCompany') {
            return res.filter(
              option => option.itemExtend1 && (option.itemExtend1 as string).includes(UsageScenario.LoanCompany)
            );
          }
          return res;
        });
      }

      return MetadataService.queryBizDict({
        dictKeys: [applicationElement.bizDictKey!],
      });
    },
    {
      manual: true,
    }
  );

  // 数据回显
  const getLinkageConfigedEnums = useCallback(
    (): Promise<Record<string, string[]>> =>
      new Promise((resolve, reject) => {
        const tempselectedEnumsByDictKey: Record<string, string[]> = {};
        if (initData) {
          linkageFactors.forEach((factor, index) => {
            if (initData[factor.factorCode] && linkageDictkeys[index]) {
              tempselectedEnumsByDictKey[linkageDictkeys[index]] = initData[factor.factorCode];
            }
          });
          resolve(tempselectedEnumsByDictKey);
        } else {
          const requestArr = linkageFactors.map(factor =>
            getAppElementConfigedEnums(factor, applicationElementsPackCode)
          );
          Promise.all(requestArr).then(responseArr => {
            linkageFactors.forEach((factor, index) => {
              // 查询结果有值才会赋值
              // 这边赋值会影响默认选中全部的逻辑
              if (responseArr[index]?.length > 0) {
                tempselectedEnumsByDictKey[linkageDictkeys[index]] = responseArr[index].map(factorValueObj =>
                  generateBizDictUniquePath(linkageDictkeys[index])(
                    factorValueObj.factorValue,
                    factorValueObj.dictValueName,
                    factorValueObj.itemExtend1,
                    factorValueObj.parentCodePath?.split('/')[0],
                    factorValueObj.parentCodePath?.split('/')[1]
                  )
                );
              }
            });
            resolve(tempselectedEnumsByDictKey);
          });
        }
      }),
    [initData, linkageDictkeys, linkageFactors, applicationElementsPackCode]
  );

  useEffect(() => {
    if (visible) {
      const firstLevelFactor = { ...linkageFactors[0] };
      firstLevelFactor.bizDictKey = linkageDictkeys[0];
      if (firstLevelFactor.uploadApplicationElementsType === EnumerationConfigurationMethod.uploadByTemplate) {
        setConfigMethod(EnumerationConfigurationMethod.uploadByTemplate);
        setFile({
          fileName: firstLevelFactor.fileName!,
          fileUniqueCode: firstLevelFactor.fileUniqueCode!,
        });
      }
      Promise.all([queryBizDictService(firstLevelFactor), getLinkageConfigedEnums()]).then(
        ([bizDicts, initSelectedEnumsByDictKey]) => {
          const enumsList = bizDicts.map(mapBizdictToEnum);

          setSelectedEnumsByDictKeyMap(initSelectedEnumsByDictKey);
          setAppElementLinkageEnums(enumsList);
        }
      );
    }
  }, [visible]);

  const closeDrawer = useCallback(() => {
    setConfigMethod(EnumerationConfigurationMethod.userSelect);
    setFile(undefined);
    onDrawerClose();
    setAppElementLinkageEnums([]);
    setSelectedEnumsByDictKeyMap({});
  }, [onDrawerClose]);

  const saveKeys = useCallback(() => {
    if (configMethod === EnumerationConfigurationMethod.userSelect) {
      if (selectedEnumsByDictKeyMap) {
        const tempUniquePathMap: Record<string, AppElementLinkageEnum> = {};
        const selectedEnumNamesByDictKey: Record<string, string[]> = {};
        collectUniquePathMap(appElementLinkageEnums, tempUniquePathMap);

        Object.keys(selectedEnumsByDictKeyMap).forEach(key => {
          // 当投保要素配了枚举a、b、c三个，下次进来的时候metadata那边删掉了枚举c，这时候需要过滤掉c，不用再提交
          // 过滤掉之前配过，但是现在已经不存在的枚举项
          selectedEnumsByDictKeyMap[key] = selectedEnumsByDictKeyMap[key].filter(path => tempUniquePathMap[path]);
        });

        const extendsList: string[] = [];
        Object.keys(selectedEnumsByDictKeyMap).forEach(dictKey => {
          selectedEnumNamesByDictKey[dictKey] = [];

          const list = selectedEnumsByDictKeyMap[dictKey];

          // 根据选中的code，查出对应的name、扩展字段
          list.forEach(path => {
            if (tempUniquePathMap[path]?.extraInfo) {
              extendsList.push(tempUniquePathMap[path].extraInfo!);
            }
            if (tempUniquePathMap[path]?.key) {
              selectedEnumNamesByDictKey[dictKey].push(tempUniquePathMap[path]?.key);
            }
          });
        });

        // 这种动态处理枚举的， 配置枚举时需要设置bizDictKey
        linkageFactors.forEach((factor, index) => {
          factor.bizDictKey = linkageDictkeys[index];
          factor.entryBizDictKey = linkageDictkeys[0];
        });

        onSave?.({
          uploadApplicationElementsType: configMethod,
          selectedKeysMap: selectedEnumsByDictKeyMap,
          selectedNamesMap: selectedEnumNamesByDictKey,
          extendsList,
          uniquePathMap: tempUniquePathMap,
        });
      }
    } else if (file) {
      const fileInfoMap: Record<
        string,
        {
          fileName: string;
          fileUniqueCode: string;
        }
      > = {};
      linkageFactors.forEach(factor => {
        fileInfoMap[factor.factorCode] = {
          fileName: file?.fileName,
          fileUniqueCode: file?.fileUniqueCode,
        };
      });
      onSave?.({
        uploadApplicationElementsType: configMethod,
        fileInfoMap,
      });
    }
    closeDrawer();
  }, [
    file,
    appElementLinkageEnums,
    closeDrawer,
    linkageDictkeys,
    linkageFactors,
    onSave,
    selectedEnumsByDictKeyMap,
    configMethod,
  ]);

  const columnOptions = useMemo(() => appElementLinkageEnums.map(mapEnumToColumnOption), [appElementLinkageEnums]);

  const renderEnumEditor = useCallback(
    () => (
      <div
        style={{
          width: '100%',
          overflow: 'auto',
        }}
      >
        <LinkageEnumSelector
          linkageConfigs={linkageFactors.map(factor => ({
            key: factor.factorCode,
            title: factor.factorName,
            // 已经配置过的投保要素并且当前没有编辑它，禁止修改
            disabled: factor.configured && !factor.inEdit,
          }))}
          options={columnOptions}
          linkageDictkeys={linkageDictkeys}
          selectedEnumsByDictKey={selectedEnumsByDictKeyMap}
          setSelectedEnumsByDictKey={setSelectedEnumsByDictKeyMap}
          extraConfig={
            extendFactorInfo
              ? {
                  key: extendFactorInfo.factorCode,
                  title: extendFactorInfo.factorName,
                }
              : undefined
          }
          defaultSelectAll={mode !== DetailPageMode.view}
        />
      </div>
    ),
    [columnOptions, extendFactorInfo, linkageDictkeys, linkageFactors, mode, selectedEnumsByDictKeyMap]
  );

  return (
    <Drawer
      open={visible}
      title={t('Enumerate Configuration')}
      onClose={closeDrawer}
      footer={
        <div className="text-right">
          <div
            style={{
              float: 'left',
              cursor: 'pointer',
              lineHeight: '40px',
            }}
            onClick={closeDrawer}
          >
            <Icon type="left" style={{ marginRight: 0 }} />
            {t('Back')}
          </div>
          {mode === DetailPageMode.view ? null : (
            <Button disabled={loading} size="large" type="primary" onClick={saveKeys}>
              {t('Save')}
            </Button>
          )}
        </div>
      }
    >
      <Spin spinning={loading}>
        {linkageFactors[0]?.factorCode === 'industryCode' ? (
          <React.Fragment>
            <div style={{ marginBottom: 12 }}>{t('Enumeration Configuration Method')}</div>
            <Radio.Group
              disabled={mode === DetailPageMode.view}
              style={{ marginBottom: 24 }}
              value={configMethod}
              onChange={evt => {
                setConfigMethod(evt.target.value);
              }}
            >
              {uploadApplicationElementsTypeOptions.map(option => (
                <Radio value={option.value}>{option.label}</Radio>
              ))}
            </Radio.Group>
          </React.Fragment>
        ) : null}
        {configMethod === EnumerationConfigurationMethod.userSelect ? renderEnumEditor() : null}
        {configMethod === EnumerationConfigurationMethod.uploadByTemplate ? (
          <LinkageEnumUploadSection
            disabled={mode === DetailPageMode.view}
            entryDictKey={linkageDictkeys[0]}
            customerType={linkageFactors[0]?.customerType}
            customerSubType={linkageFactors[0]?.customerSubType}
            topic={topic}
            applicationElementsPackCode={applicationElementsPackCode}
            linkageFactors={linkageFactors}
            file={file}
            setFile={setFile}
          />
        ) : null}
      </Spin>
    </Drawer>
  );
};

export default LinkageEnumDrawer;
