import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { ColumnProps } from 'antd/es/table';

import classNames from 'classnames/bind';

import { DeleteAction, EditAction, Table } from '@zhongan/nagrand-ui';

import { ApplicationElementTopic, FactorInfo, LinkageWithZipcode, YesNoType } from 'genesis-web-service';

import {
  ConfigedDataByCustomerType,
  ConfigedDataByObjectSubCategory,
} from '@market/common/applicationElements.interface';
import { DetailPageMode } from '@market/common/interface';
import { useBizDict, useBizDictAsOptions } from '@market/hook/bizDict';
import { renderOptionName } from '@market/utils/enum';

import { linkageEnumConfig, totalAddressFactorsMap, totalBankFactorsMap } from '../../config/linkageEnumConfig';
import {
  applicationElementUniqueKey,
  getFactorsForDrawer,
  getLinkageFactors,
  isEnum,
  isLinkageEnum,
  isLinkedZipCode,
} from '../../dealServiceData';
import LinkageEnumDrawer from '../LinkageEnumDrawer';
import styles from './ApplicationElementsTable.module.scss';

const cx = classNames.bind(styles);

interface Props {
  topic: ApplicationElementTopic;
  applicationElementsPackCode: string;
  onModify: (record: FactorInfo) => void;
  data: FactorInfo[];
  loading: boolean;
  onDelete: (record: FactorInfo, topic: number) => void;
  mode?: DetailPageMode;
  tenantDataByKey: {
    addressModel: string;
    addressDepth: number;
    bankModel: string;
    addressCascadedLevel: number;
    bankCascadedLevel: number;
    linkageWithZipcode: LinkageWithZipcode;
    hasConfigedIndustry: ConfigedDataByCustomerType;
    hasConfigedBuildingType: ConfigedDataByCustomerType;
    positionModel: string;
  };
  onViewElementsValue: (record: FactorInfo) => void;
}

const SelectFactorTable = ({
  data,
  applicationElementsPackCode,
  topic,
  loading,
  mode,
  tenantDataByKey,
  onModify,
  onDelete,
  onViewElementsValue,
}: Props) => {
  const [t] = useTranslation(['market', 'common']);
  const containerEl = React.useRef<HTMLDivElement>(null);
  const [pageSize, setPageSize] = useState<number>(10);
  /* ============== 枚举使用start ============== */
  const yesNoOptions = useBizDictAsOptions('yesNo');
  /* ============== 枚举使用end ============== */

  const [linkageEnumDrawervisible, setLinkageEnumDrawervisible] = useState<boolean>(false);
  const [linkageEnumData, setLinkageEnumData] = useState<FactorInfo[]>([]);
  const [linkageDictkeys, setLinkageDictkeys] = useState<string[]>([]);
  const [extendFactorInfo, setExtendFactorInfo] = useState<FactorInfo>();

  // 分页器change逻辑
  const onShowSizeChange = useCallback((current, size) => {
    setPageSize(size);
  }, []);

  const isShowLinkageDrawer = useCallback(
    (record: FactorInfo) =>
      isLinkageEnum(record, {
        addressModel: tenantDataByKey.addressModel,
        addressDepth: tenantDataByKey.addressDepth,
        linkageWithZipcode: tenantDataByKey.linkageWithZipcode,
        bankModel: tenantDataByKey.bankModel,
        bankCascadedLevel: tenantDataByKey.bankCascadedLevel,
      }) || record.cascadeFactorCodes,
    [tenantDataByKey]
  );

  // 子表格展开关闭逻辑
  const showLinkageDrawer = useCallback(
    (record: FactorInfo) => {
      let linkageFactorsForDrawer = getFactorsForDrawer(record, {
        addressDepth: tenantDataByKey.addressDepth,
        bankCascadedLevel: tenantDataByKey.bankCascadedLevel,
      });
      let linkageDictkeysForDrawer = linkageEnumConfig[record.factorCode]?.linkageMetadataKey || [];
      if (record.cascadeFactorCodes) {
        linkageFactorsForDrawer =
          record?.cascadeFactorCodes?.sort((a, b) => a.level - b.level)?.map(item => item.factorCode) || [];
        const linkageApplicationElements = linkageFactorsForDrawer
          .map(code => data.find(item => item.factorCode === code && item.configured))
          .filter(Boolean);
        linkageDictkeysForDrawer = linkageApplicationElements.map(item => item!.bizDictKey!);
      }
      setLinkageEnumData(
        linkageFactorsForDrawer
          .map(factorCode => data.find(factorInfo => factorInfo.factorCode === factorCode))
          .filter(Boolean)
          .filter(factorInfo => factorInfo!.configured) as FactorInfo[]
      );
      setLinkageEnumDrawervisible(true);
      setLinkageDictkeys(linkageDictkeysForDrawer);
      setExtendFactorInfo(
        isLinkedZipCode(record.factorCode, tenantDataByKey.linkageWithZipcode)
          ? data.find(
              factorInfo =>
                // 编辑情况下必须已经配置过zipcode才会联动
                factorInfo.factorCode === 'zipCode' && factorInfo.configured
            )
          : undefined
      );
    },
    [data, tenantDataByKey]
  );

  const showTxtCheck = useCallback(
    (record: { factorCode: string }): string => {
      if (
        totalAddressFactorsMap[record.factorCode] &&
        getLinkageFactors(record.factorCode, tenantDataByKey.addressDepth)?.includes(record.factorCode)
      ) {
        return t('All relevant addresses will be deleted together, please confirm!');
      }
      if (
        totalBankFactorsMap[record.factorCode] &&
        getLinkageFactors(record.factorCode, tenantDataByKey.bankCascadedLevel)?.includes(record.factorCode)
      ) {
        return t('All relevant bankCode will be deleted together, please confirm!');
      }
      return t('Are you sure to delete this record?');
    },
    [t, tenantDataByKey]
  );

  const getColumns = useMemo(() => {
    const readOnly = mode === DetailPageMode.view || mode === DetailPageMode.copy;
    const tempColumn: ColumnProps<FactorInfo>[] = [
      {
        title: t('No.'),
        width: 60,
        render(text, record, index) {
          return index + 1;
        },
      },
      {
        title: t('Field Name'),
        dataIndex: 'factorName',
        width: 200,
      },
      {
        title: t('Display Name of Field'),
        dataIndex: 'factorDisplayName',
        width: 200,
        render: (text, record) => record.factorDisplayName || t('- -'),
      },
      {
        title: t('Elements Value'),
        width: 200,
        render: (text, record) =>
          isEnum(record, tenantDataByKey) ? (
            <span
              onClick={() => {
                if (isShowLinkageDrawer(record)) {
                  showLinkageDrawer(record);
                } else {
                  onViewElementsValue(record);
                }
              }}
              className={cx('view-elements-value')}
            >
              {t('View')}
            </span>
          ) : (
            t('- -')
          ),
      },
      {
        title: t('Field Validation Rule'),
        dataIndex: 'validationRule',
        width: 200,
        render: (text, record) => (record?.validationRuleName as string) || t('- -'),
      },
      {
        title: t('Mandatory'),
        dataIndex: 'isRequiredName',
        width: 180,
        render: (text, record) => renderOptionName(record.isRequired, yesNoOptions),
      },
      {
        title: t('Show in Application Form'),
        dataIndex: 'isShowInApplicationForm',
        width: 210,
        render: text => renderOptionName(String(text), yesNoOptions),
      },
      {
        title: t('Updatable When POS'),
        dataIndex: 'isUpdatableWhenPos',
        render: (text, record) =>
          renderOptionName(
            record.isUpdatableWhenPos ? `${record.isUpdatableWhenPos as number}` : YesNoType.Yes,
            yesNoOptions
          ),
        width: 180,
      },
      {
        title: t('Updatable When Manual UW'),
        dataIndex: 'isUpdatableWhenUw',
        render: (text, record) =>
          renderOptionName(
            record.isUpdatableWhenUw ? `${record.isUpdatableWhenUw as number}` : YesNoType.Yes,
            yesNoOptions
          ),
        width: 240,
      },
    ];
    if (!readOnly) {
      tempColumn.push({
        title: t('Actions'),
        align: 'right',
        fixed: 'right',
        width: 128,
        key: 'operation',
        render: (text: string, record: { factorCode: string }) => (
          <div>
            <EditAction
              disabled={readOnly}
              onClick={() => {
                if (readOnly) {
                  return;
                }
                onModify(record);
              }}
            />
            <DeleteAction
              disabled={readOnly}
              doubleConfirmPlacement="topLeft"
              doubleConfirmType="popconfirm"
              onClick={() => onDelete(record, topic)}
              deleteConfirmContent={showTxtCheck(record)}
            />
          </div>
        ),
      });
    }
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return tempColumn;
  }, [
    isShowLinkageDrawer,
    mode,
    onDelete,
    onModify,
    onViewElementsValue,
    showLinkageDrawer,
    showTxtCheck,
    t,
    tenantDataByKey,
    topic,
    yesNoOptions,
  ]);

  return (
    <div ref={containerEl} style={{ marginTop: 8 }} className={cx('application-elements-table')}>
      {/* 带层级的表格+分页器 */}
      <Table
        loading={loading}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          total: data?.length || 0,
          pageSize,
          hideOnSinglePage: false,
          onShowSizeChange,
        }}
        rowKey={applicationElementUniqueKey}
        columns={getColumns}
        dataSource={data.sort((a, b) => a.orderNo - b.orderNo)}
        scroll={{ x: 'max-content' }}
      />
      <LinkageEnumDrawer
        mode={DetailPageMode.view}
        topic={topic}
        applicationElementsPackCode={applicationElementsPackCode}
        visible={linkageEnumDrawervisible}
        linkageFactors={linkageEnumData}
        linkageDictkeys={linkageDictkeys}
        extendFactorInfo={extendFactorInfo}
        onDrawerClose={() => {
          setLinkageEnumDrawervisible(false);
          setLinkageDictkeys([]);
          setLinkageEnumData([]);
          setExtendFactorInfo(undefined);
        }}
      />
    </div>
  );
};

export default SelectFactorTable;
