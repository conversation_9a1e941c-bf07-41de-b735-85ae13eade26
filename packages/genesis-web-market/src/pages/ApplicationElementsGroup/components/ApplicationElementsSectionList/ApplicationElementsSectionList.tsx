import { Ref, RefObject, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { message } from 'antd';

import { remove } from 'lodash-es';

import {
  ApplicationElementCustomerType,
  ApplicationElementTopic,
  ApplicationElementsGroupInfo,
} from 'genesis-web-service/lib/market';
import { LinkageWithZipcode, MetadataService } from 'genesis-web-service/lib/metadata';

import { ObjectSubCategoryType } from '@market/common/enums';
import { DetailPageMode } from '@market/common/interface';

import { ApplicationElementsSectionSequence, PolicyLevelTopics } from '../../config';
import { filterApplicationElements } from '../../dealServiceData';
import { ApplicationElementsSectionListRef, ApplicationElementsSectionRef } from '../../interface';
import MatrixDrawer from '../MatrixDrawer';
import ObjectApplicationElementSection from '../ObjectApplicationElementSection';
import PolicyApplicationElementSection from '../PolicyApplicationElementSection';
import SimpleApplicationElementSection from '../SimpleApplicationElementSection';
import { NewMarketService } from '@market/services/market/market.service.new';

interface Props {
  refInstance: Ref<ApplicationElementsSectionListRef>;
  displayPosition?: 'package' | 'groupDetail';
  mode: DetailPageMode;
  applicationElementsGroupInfo: ApplicationElementsGroupInfo | undefined;
  applicationElementsPackCode: string;
  beforeAdd?: () => Promise<void> | Promise<boolean>;
}

export const ApplicationElementsSectionList = ({
  refInstance,
  displayPosition,
  mode,
  applicationElementsGroupInfo, // 取的接口返回的字段，该字段变化时需要重新调接口查投保要素
  applicationElementsPackCode, // 取的表单中的字段，该字段变化时不需要重新查
  beforeAdd,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [matrixDrawerVisible, setMatrixDrawerVisible] = useState(false);
  const [matrixDrawerInfo, setMatrixDrawerInfo] = useState<{
    topic: ApplicationElementTopic;
    customerType?: ApplicationElementCustomerType;
    objectSubCategory?: ObjectSubCategoryType;
  }>();
  const policyHolderRef = useRef<ApplicationElementsSectionRef>(null);
  const insuredRef = useRef<ApplicationElementsSectionRef>(null);
  const objectRef = useRef<ApplicationElementsSectionRef>(null);
  const beneficiaryRef = useRef<ApplicationElementsSectionRef>(null);
  const payerRef = useRef<ApplicationElementsSectionRef>(null);
  const premiumFunderRef = useRef<ApplicationElementsSectionRef>(null);
  const nomineeRef = useRef<ApplicationElementsSectionRef>(null);
  const consenteeRef = useRef<ApplicationElementsSectionRef>(null);
  const trusteeRef = useRef<ApplicationElementsSectionRef>(null);
  const beneficialOwnerRef = useRef<ApplicationElementsSectionRef>(null);
  const policyRef = useRef<ApplicationElementsSectionRef>(null);
  const assigneeRef = useRef<ApplicationElementsSectionRef>(null);
  const secondaryLifeInsuredRef = useRef<ApplicationElementsSectionRef>(null);
  const contactPersonRef = useRef<ApplicationElementsSectionRef>(null);
  const unnamedInsuredRef = useRef<ApplicationElementsSectionRef>(null);

  const refMapByTopic: Record<ApplicationElementTopic, RefObject<ApplicationElementsSectionRef>> = {
    [ApplicationElementTopic.policyHolder]: policyHolderRef,
    [ApplicationElementTopic.insured]: insuredRef,
    [ApplicationElementTopic.unnamedInsured]: unnamedInsuredRef,
    [ApplicationElementTopic.object]: objectRef,
    [ApplicationElementTopic.beneficiary]: beneficiaryRef,
    [ApplicationElementTopic.payer]: payerRef,
    [ApplicationElementTopic.nominee]: nomineeRef,
    [ApplicationElementTopic.secondaryLifeInsured]: secondaryLifeInsuredRef,
    [ApplicationElementTopic.consentee]: consenteeRef,
    [ApplicationElementTopic.trustee]: trusteeRef,
    [ApplicationElementTopic.beneficialOwner]: beneficialOwnerRef,
    [ApplicationElementTopic.policy]: policyRef,
    [ApplicationElementTopic.assignee]: assigneeRef,
    [ApplicationElementTopic.premiumFunder]: premiumFunderRef,
    [ApplicationElementTopic.contactPerson]: contactPersonRef,
  };

  useImperativeHandle(refInstance, () => ({
    getApplicationElements: topic => refMapByTopic[topic].current?.getApplicationElements(),
    isOpened: topic => refMapByTopic[topic].current?.isOpened(),
    queryApplicationElements: topic => refMapByTopic[topic].current?.queryApplicationElements(),
  }));

  const [tenantDataByKey, setTenantDataByKey] = useState<{
    addressModel: string;
    addressDepth: number;
    bankModel: string;
    positionModel: string;
    addressCascadedLevel: number;
    bankCascadedLevel: number;
    linkageWithZipcode: LinkageWithZipcode;
  }>({});

  const queryMetadataConfig = useCallback(() => {
    MetadataService.queryBizDict({
      dictKeys: [
        'addressCascadedLevel',
        'bankCascadedLevel',
        'linkageWithZipcode',
        'addressModel',
        'bankModel',
        'positionModel',
      ],
    }).then(res => {
      const addressDepthRes = res.find(item => item.dictKey === 'addressCascadedLevel');
      const bankCascadedLevelRes = res.find(item => item.dictKey === 'bankCascadedLevel');
      const linkageWithZipcode = res.find(item => item.dictKey === 'linkageWithZipcode');
      const addressModelRes = res.find(item => item.dictKey === 'addressModel');
      const bankModelRes = res.find(item => item.dictKey === 'bankModel');
      const positionModelRes = res.find(item => item.dictKey === 'positionModel');

      setTenantDataByKey({
        addressModel: addressModelRes?.dictValue,
        bankModel: bankModelRes?.dictValue,
        addressDepth: addressDepthRes?.dictValue ? +addressDepthRes.dictValue : 0,
        bankCascadedLevel: bankCascadedLevelRes?.dictValue ? +bankCascadedLevelRes.dictValue : 0,
        linkageWithZipcode: linkageWithZipcode?.dictValue,
        positionModel: positionModelRes?.dictValue,
      });
    });
  }, []);

  const copyApplicetionElements = useCallback(
    (
      srcTopic: ApplicationElementTopic,
      targetTopic: ApplicationElementTopic,
      {
        customerType,
        objectCategory,
        objectSubCategory,
      }: {
        customerType: ApplicationElementCustomerType;
        objectCategory?: number;
        objectSubCategory?: number;
      }
    ) => {
      const sourceRef = refMapByTopic[srcTopic].current;
      const targetRef = refMapByTopic[targetTopic].current;
      if (!sourceRef || !targetRef) {
        return;
      }
      const applicationElements = sourceRef?.getApplicationElements() || [];
      const configedApplicationElements = filterApplicationElements(applicationElements, true, {
        customerType,
      });

      if (applicationElements.length > 0) {
        NewMarketService.ApplicationElementsPackService.copyPackageApplicationElements({
          applicationElementsPackCode,
          srcTopic,
          targetTopic,
          objectCategory,
          objectSubCategory,
          copyItems: configedApplicationElements.map(item => ({
            customerType: item.customerType,
            customerSubType: item.customerSubType,
            objectCategory: item.objectCategory,
            objectSubCategory: item.objectSubCategory,
            factorCode: item.factorCode,
          })),
        }).then(() => {
          message.success('Copy Success');
          targetRef.queryApplicationElements();
        });
      }
    },
    [applicationElementsPackCode, refMapByTopic]
  );

  useEffect(() => {
    queryMetadataConfig();
  }, [queryMetadataConfig]);

  const renderApplicationElementsSection = useCallback(
    () =>
      ApplicationElementsSectionSequence.map((topic: ApplicationElementTopic) => {
        if (PolicyLevelTopics.includes(topic)) {
          return (
            <PolicyApplicationElementSection
              displayPosition={displayPosition}
              applicationElementsGroupInfo={applicationElementsGroupInfo}
              refInstance={refMapByTopic[topic]}
              topic={topic}
              applicationElementsPackCode={applicationElementsPackCode}
              tenantDataByKey={tenantDataByKey}
              mode={mode}
              beforeAdd={beforeAdd}
            />
          );
        }
        return topic === ApplicationElementTopic.object ? (
          <ObjectApplicationElementSection
            displayPosition={displayPosition}
            applicationElementsGroupInfo={applicationElementsGroupInfo}
            refInstance={refMapByTopic[topic]}
            copyFactor={copyApplicetionElements}
            mode={mode}
            beforeAdd={beforeAdd}
            applicationElementsPackCode={applicationElementsPackCode}
            tenantDataByKey={tenantDataByKey}
            openMatrixDrawer={(_topic, objectSubCategory) => {
              if (displayPosition === 'package') {
                setMatrixDrawerInfo({
                  topic: _topic,
                  objectSubCategory,
                });
                setMatrixDrawerVisible(true);
              } else {
                beforeAdd?.().then(() => {
                  setMatrixDrawerInfo({
                    topic: _topic,
                    objectSubCategory,
                  });
                  setMatrixDrawerVisible(true);
                });
              }
            }}
          />
        ) : (
          <SimpleApplicationElementSection
            displayPosition={displayPosition}
            applicationElementsGroupInfo={applicationElementsGroupInfo}
            refInstance={refMapByTopic[topic]}
            topic={topic}
            applicationElementsPackCode={applicationElementsPackCode}
            tenantDataByKey={tenantDataByKey}
            copyFactor={copyApplicetionElements}
            mode={mode}
            beforeAdd={beforeAdd}
            openMatrixDrawer={(_topic, _customerType) => {
              if (displayPosition === 'package') {
                setMatrixDrawerInfo({
                  topic: _topic,
                  customerType: _customerType,
                });
                setMatrixDrawerVisible(true);
              } else {
                beforeAdd?.().then(() => {
                  setMatrixDrawerInfo({
                    topic: _topic,
                    customerType: _customerType,
                  });
                  setMatrixDrawerVisible(true);
                });
              }
            }}
          />
        );
      }),
    [
      displayPosition,
      applicationElementsGroupInfo,
      refMapByTopic,
      mode,
      beforeAdd,
      applicationElementsPackCode,
      tenantDataByKey,
      copyApplicetionElements,
    ]
  );

  return (
    <div>
      {renderApplicationElementsSection()}
      <MatrixDrawer
        {...matrixDrawerInfo}
        visible={matrixDrawerVisible}
        applicationElementPropertyRequestList={
          applicationElementsGroupInfo?.applicationElementPropertyRequestList || []
        }
        mode={displayPosition === 'groupDetail' ? DetailPageMode.edit : DetailPageMode.view}
        onClose={() => {
          setMatrixDrawerVisible(false);
        }}
        onSubmit={(values, schemaInfo) => {
          const matrixTableCode = values.matrixTableCode;
          const applicationElementPropertyRequestList =
            applicationElementsGroupInfo?.applicationElementPropertyRequestList || [];

          if (matrixTableCode) {
            const savedItem = applicationElementPropertyRequestList.find(
              item =>
                item.schemaDefType === schemaInfo.schemaDefType &&
                item.schemaDefRefId === schemaInfo.schemaDefRefId &&
                item.topic === matrixDrawerInfo?.topic
            );

            if (savedItem) {
              // 修改
              savedItem.matrixTableCode = matrixTableCode;
            } else {
              // 新增
              applicationElementPropertyRequestList.push({
                ...schemaInfo,
                topic: matrixDrawerInfo!.topic,
                matrixTableCode,
              });
            }
          } else {
            // 删除
            remove(
              applicationElementPropertyRequestList,
              item =>
                item.schemaDefType === schemaInfo.schemaDefType &&
                item.schemaDefRefId === schemaInfo.schemaDefRefId &&
                item.topic === matrixDrawerInfo?.topic
            );
          }

          NewMarketService.ApplicationElementsPackService.save({
            ...applicationElementsGroupInfo!,
            applicationElementPropertyRequestList,
          }).then((id: number) => {
            message.success(t('Submit Successfully'));
            applicationElementsGroupInfo!.applicationElementPropertyRequestList = applicationElementPropertyRequestList;
            setMatrixDrawerInfo(undefined);
            setMatrixDrawerVisible(false);
          });
        }}
      />
    </div>
  );
};

export default ApplicationElementsSectionList;
