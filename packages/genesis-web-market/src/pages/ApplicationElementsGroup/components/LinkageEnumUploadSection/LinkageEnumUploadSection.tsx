import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { UploadProps } from 'antd';
import { Button, Divider, Upload, message } from 'antd';

import { Icon, UploadFileItem } from '@zhongan/nagrand-ui';

import { ApplicationElementTopic, FactorInfo, MetadataService } from 'genesis-web-service';
import { security } from 'genesis-web-shared';
import { downloadFile } from 'genesis-web-shared/lib/util/downloadFile';

import { useFileLoadingInfo } from '@market/hook/utils';
import { NewMarketService } from '@market/services/market/market.service.new';

import { EnumsUploadFile } from '../../interface';

interface Props {
  entryDictKey: string;
  applicationElementsPackCode: string;
  topic: ApplicationElementTopic;
  customerType: number;
  customerSubType: number;
  linkageFactors: FactorInfo[];
  file?: EnumsUploadFile;
  disabled: boolean;
  setFile: (file: EnumsUploadFile) => void;
}

export const LinkageEnumUploadSection = ({
  entryDictKey,
  topic,
  applicationElementsPackCode,
  customerType,
  customerSubType,
  file,
  disabled,
  linkageFactors,
  setFile,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [disabledUpload, setDisabledUpload] = useState(true);

  const { loadingInfo, setupLoading, completeLoading, cancelLoading } = useFileLoadingInfo();

  const downloadTemplate = useCallback(() => {
    MetadataService.exportTenantConfig(entryDictKey, false)
      .then(downloadFile)
      .then(() => {
        setDisabledUpload(false);
      })
      .catch((error: Error) => message.error(error?.message || t('Download failed')));
  }, [entryDictKey, t]);

  const props: UploadProps<string | Error> = useMemo(
    () => ({
      name: 'file',
      action: '/api/market/v2/application-elements/import-factor-items',
      headers: {
        ...security.csrf(),
      },
      data: {
        topic,
        entryDictKey,
        applicationElementsPackCode,
        customerType,
        customerSubType,
        factorCodes: linkageFactors.map(factor => factor.factorCode).join(','),
      },
      beforeUpload(_file, FileList) {
        setupLoading(_file.name);
      },
      onChange(info) {
        if (info.file.status === 'done') {
          completeLoading(info.file.name);

          setFile({
            fileName: info.file.name,
            fileUniqueCode: info.file.response as string,
          });
          message.success(t('Upload Successfully'));
        } else if (info.file.status === 'error') {
          message.error((info.file.response as Error)?.message);
          cancelLoading();
        }
      },
      showUploadList: false,
    }),
    [
      applicationElementsPackCode,
      cancelLoading,
      completeLoading,
      customerSubType,
      customerType,
      entryDictKey,
      linkageFactors,
      setFile,
      setupLoading,
      t,
      topic,
    ]
  );

  const downloadEnumsFile = useCallback(() => {
    NewMarketService.ApplicationElementsPackService.downloadPackageApplicationElementCustomizedListFile({
      uniqueFileCode: file!.fileUniqueCode,
    }, {
      observe: 'response',
      responseType: 'blob',
    })
      .then(downloadFile)
      .catch((error: Error) => message.error(error?.message || t('Download failed')));
  }, [file, t]);

  return (
    <div>
      <Button disabled={disabled} onClick={downloadTemplate} icon={<Icon type="download" />}>
        {t('Download Template')}
      </Button>
      <Divider />
      <Upload disabled={disabled || disabledUpload} {...props}>
        <Button disabled={disabled || disabledUpload} icon={<Icon type="upload" />}>
          {t('Upload')}
        </Button>
      </Upload>
      <div>{t('You can only upload xls, xlsx')}</div>
      {file && !loadingInfo.visible ? (
        <div style={{ marginTop: 24 }}>
          <UploadFileItem
            needPreview={false}
            hoverInfoList={[
              {
                key: 'download',
                icon: (
                  <span onClick={() => downloadEnumsFile()}>
                    <Icon type="download" />
                  </span>
                ),
              },
            ]}
            fileName={file.fileName}
          />
        </div>
      ) : null}
      {loadingInfo.visible ? (
        <UploadFileItem
          style={{
            marginTop: '24px',
          }}
          needPreview={false}
          fileName={loadingInfo.name}
          isShowProgress
          percent={loadingInfo.percent}
        />
      ) : null}
    </div>
  );
};

export default LinkageEnumUploadSection;
