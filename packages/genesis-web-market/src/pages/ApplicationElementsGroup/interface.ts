import { FactorInfo } from 'genesis-web-service/lib/market';

import { AppElementLinkageEnum } from '@market/common/applicationElements.interface';

export enum ApplicationElementTopic {
  policyHolder = 1,
  insured = 2,
  beneficiary = 3,
  object = 6,
  payer = 7,
  nominee = 8,
  consentee = 9,
  trustee = 10,
}

export enum EnumerationConfigurationMethod {
  userSelect = 'USER_SELECT',
  uploadByTemplate = 'UPLOAD_BY_TEMPLATE',
}

export interface UserSelectEnumValues {
  uploadApplicationElementsType: EnumerationConfigurationMethod.userSelect;
  selectedKeysMap: Record<string, string[]>;
  selectedNamesMap: Record<string, string[]>;
  extendsList: string[];
  uniquePathMap: Record<string, AppElementLinkageEnum>; // 用来根据dictValue来获取枚举信息
}

export interface UploadByTemplateEnumValues {
  uploadApplicationElementsType: EnumerationConfigurationMethod.uploadByTemplate;
  fileInfoMap: Record<
    string,
    {
      fileName: string;
      fileUniqueCode: string;
    }
  >;
}

export interface EnumsUploadFile {
  fileName: string;
  fileUniqueCode: string;
}

export interface ApplicationElementsSectionRef {
  isOpened: () => boolean;
  getApplicationElements: () => FactorInfo[];
  queryApplicationElements: () => void;
}

export interface ApplicationElementsSectionListRef {
  isOpened: (topic: ApplicationElementTopic) => boolean | undefined;
  getApplicationElements: (topic: ApplicationElementTopic) => FactorInfo[] | undefined;
  queryApplicationElements: (topic: ApplicationElementTopic) => void;
}
