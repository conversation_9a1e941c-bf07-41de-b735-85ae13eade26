import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { ColumnProps } from 'antd/es/table';

import { keyBy } from 'lodash-es';
import { PackageAgreementDTO } from 'market-types';

import { EditAction, Table } from '@zhongan/nagrand-ui';

import { PackageClaimStack, StackValueType } from 'genesis-web-service';
import { ClaimStackConfigResponseDTO } from 'genesis-web-service/service-types/product-types/package/index';

import { DetailPageMode } from '@market/common/interface';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { NewMarketService } from '@market/services/market/market.service.new';
import { NewProductService } from '@market/services/product/product.service.new';
import { renderOptionName } from '@market/utils/enum';

import StackValueMatrix from './components/StackValueMatrix/StackValueMatrix';
import StackValueSettingDrawer from './components/StackValueSettingDrawer';

interface Props {
  disabled: boolean;
  packageId: string;
  packageCode: string;
  mode: DetailPageMode;
}

export const StackValueSetting = ({ packageId, packageCode, disabled, mode }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const claimStackValueTypeOptions = useBizDictAsOptions('claimStackValueType');
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [packageAgreement, setPackageAgreement] = useState<PackageAgreementDTO>();
  const [claimStackInfoMap, setClaimStackInfoMap] = useState<Record<string, ClaimStackConfigResponseDTO>>({});
  const [editRecord, setEditRecord] = useState<PackageClaimStack>();

  const queryPackageAgreement = useCallback(
    () =>
      NewMarketService.PackageAgreementConfigMgmtService.queryPackageAgreement({
        packageId: +packageId,
        status: mode,
      }).then(res => {
        const packageAgreementRes = res.value;
        setPackageAgreement(packageAgreementRes);
        return res.value;
      }),
    [mode, packageId]
  );

  useEffect(() => {
    queryPackageAgreement().then(res => {
      const list = res?.packageClaimStackRequestList || [];
      if (list.length > 0) {
        NewProductService.ClaimStackService.queryByStackCodes(list.map(item => item.stackCode!)).then(
          stackInfoListRes => {
            setClaimStackInfoMap(keyBy(stackInfoListRes.value, 'stackCode'));
          }
        );
      }
    });
  }, []);

  const columns = useMemo(() => {
    const tempColumns: ColumnProps<PackageClaimStack>[] = [
      {
        title: t('No.'),
        render: (text: string, record: any, index: number) => index + 1,
      },
      {
        title: t('Stack Code'),
        dataIndex: 'stackCode',
      },
      {
        title: t('Stack Value Type'),
        dataIndex: 'stackValueType',
        render: (text: number, record) => renderOptionName(`${text}`, claimStackValueTypeOptions),
      },
      {
        title: t('Stack Value'),
        dataIndex: 'stackValue',
        render: (text: string, record) => {
          if (record.stackValueType === StackValueType.Enumeration) {
            return record.enumStackValue!.join('; ');
          }
          return text;
        },
      },
      {
        title: t('Stack Description'),
        render: (text, record) => claimStackInfoMap[record.stackCode]?.stackDesc,
      },
    ];

    if (!disabled) {
      tempColumns.push({
        fixed: 'right',
        width: 80,
        title: t('Actions'),
        render: (text, record) => (
          <EditAction
            onClick={() => {
              setEditRecord(record);
              setDrawerVisible(true);
            }}
          />
        ),
      });
    }

    return tempColumns;
  }, [claimStackInfoMap, claimStackValueTypeOptions, disabled, t]);

  const onClose = useCallback(() => {
    setDrawerVisible(false);
    setEditRecord(undefined);
  }, []);

  return (
    <React.Fragment>
      <Table columns={columns} dataSource={packageAgreement?.packageClaimStackRequestList || []} pagination={false} />
      <StackValueMatrix
        packageCode={packageCode}
        packageId={packageId}
        claimStackList={packageAgreement?.packageClaimStackRequestList || []}
        claimStackMatrixTableCode={packageAgreement?.claimStackMatrixTableCode}
        mode={mode}
      />
      {drawerVisible ? (
        <StackValueSettingDrawer
          visible={drawerVisible}
          stackInfo={editRecord ? claimStackInfoMap[editRecord.stackCode] : undefined}
          record={editRecord}
          onClose={onClose}
          onSubmit={value => {
            const tempList = packageAgreement!.packageClaimStackRequestList!;
            const index = tempList.findIndex(row => row.stackCode === value.stackCode);
            if (index === -1) {
              return Promise.reject();
            }
            tempList[index] = value;

            return NewMarketService.PackageAgreementConfigMgmtService.savePackageAgreement({
              id: packageAgreement!.id,
              packageId: packageAgreement!.packageId,
              packageClaimStackRequestList: tempList,
            }).then(() => {
              queryPackageAgreement();
              onClose();
            });
          }}
        />
      ) : null}
    </React.Fragment>
  );
};

export default StackValueSetting;
