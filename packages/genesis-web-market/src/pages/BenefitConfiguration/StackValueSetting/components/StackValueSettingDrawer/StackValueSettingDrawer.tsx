import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Input, InputNumber, Row, message } from 'antd';

import { Drawer } from '@zhongan/nagrand-ui';

import {
  ClaimPeriodType,
  MainConditionType,
  PackageClaimStack,
  StackType,
  StackUnit,
  StackValueType,
} from 'genesis-web-service';
import { ClaimStackConfigResponseDTO } from 'genesis-web-service/service-types/product-types/package/index';

import { EnumerationSettingFormList } from '@market/components/EnumerationSettingFormList';
import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';

const FormItem = Form.Item;

interface Props {
  visible: boolean;
  onClose: () => void;
  onSubmit: (value: PackageClaimStack) => Promise<void>;
  stackInfo?: ClaimStackConfigResponseDTO;
  record?: PackageClaimStack;
}

const MainConditionsShowSubCondition = [MainConditionType.PerObjectUnderPolicy];
const StackTypeShowSubCondition = [StackType.Limit, StackType.Deductible];

const PeriodTypesShowDays = [ClaimPeriodType.BeforeAndAfterHospitalization];

export const StackValueSettingDrawer = ({ visible, record, stackInfo, onSubmit, onClose }: Props): JSX.Element => {
  const [form] = Form.useForm();
  const [t] = useTranslation(['market', 'common']);

  /* ============== 枚举使用start ============== */
  const stackTypeOptions = useBizDictAsOptions('stackType');
  const claimDaysTypeOptions = useBizDictAsOptions('claimDaysType');
  const stackUnitOptions = useBizDictAsOptions('stackUnit');
  const claimStackValueTypeOptions = useBizDictAsOptions('claimStackValueType');
  const claimTimesTypeOptions = useBizDictAsOptions('claimTimesType');
  const mainConditionTypeOptions = useBizDictAsOptions('mainConditionType');
  const claimBillObjectTypeOptions = useBizDictAsOptions('claimBillObjectType');
  const claimPeriodTypeOptions = useBizDictAsOptions('claimPeriodType');
  /* ============== 枚举使用end ============== */

  useEffect(() => {
    if (record?.stackValueType === StackValueType.Enumeration && Array.isArray(record.enumStackValue) && visible) {
      form.setFieldValue('enumStackValue', record.enumStackValue);
    }
  }, [visible, form, record]);

  const claimPeriodTypeFilterArr =
    stackInfo?.mainConditionType === MainConditionType.PerPerson
      ? claimPeriodTypeOptions
      : claimPeriodTypeOptions.filter(item => +item.value !== ClaimPeriodType.PerCalendarYear);

  const closeDrawer = useCallback(() => {
    form.resetFields();
    onClose();
  }, [form, onClose]);

  const _onSubmit = useCallback(() => {
    form.validateFields().then((values: PackageClaimStack) => {
      onSubmit({
        id: record!.id,
        packageId: record!.packageId,
        stackValueType: values.stackValueType,
        stackValue: values.stackValueType === StackValueType.InputValue ? values.stackValue : '',
        stackCode: record!.stackCode,
        enumStackValue: values.stackValueType === StackValueType.Enumeration ? values.enumStackValue : [],
      }).then(() => {
        message.success(t('Submit successfully'));
        form.resetFields();
      });
    });
  }, [form, onSubmit, record]);

  return (
    <Drawer open={visible} title={t('Edit Claim Stack Definition')} onClose={closeDrawer} onSubmit={_onSubmit}>
      <Form form={form} layout="vertical">
        <Row>
          <Col span={8}>
            <FormItem colon={false} label={t('Stack Name')}>
              <Input style={{ width: 240 }} value={stackInfo?.stackName} placeholder={t('Please input')} disabled />
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem colon={false} label={t('Stack Code')}>
              <Input style={{ width: 240 }} value={stackInfo?.stackCode} placeholder={t('Please input')} disabled />
            </FormItem>
          </Col>
          <Col span={8}>
            <FormItem colon={false} label={t('Stack Type')}>
              <GeneralSelect
                option={stackTypeOptions.map(option => ({
                  ...option,
                  value: +option.value,
                }))}
                value={stackInfo?.stackType}
                placeholder={t('Please select')}
                style={{ width: 240 }}
                disabled
              />
            </FormItem>
          </Col>
          <Col span={24}>
            <FormItem colon={false} label={t('Stack Description')}>
              <Input.TextArea
                value={stackInfo?.stackDesc}
                style={{ width: 580 }}
                placeholder={t('Please input')}
                disabled
              />
            </FormItem>
          </Col>
          <Row style={{ width: '100%' }}>
            <Col span={8}>
              <FormItem colon={false} label={t('Main Condition Type')}>
                <GeneralSelect
                  option={mainConditionTypeOptions.map(option => ({
                    ...option,
                    value: +option.value,
                  }))}
                  value={stackInfo?.mainConditionType}
                  placeholder={t('Please select')}
                  style={{ width: 240 }}
                  disabled
                />
              </FormItem>
            </Col>
            {stackInfo &&
            MainConditionsShowSubCondition.includes(stackInfo.mainConditionType!) &&
            StackTypeShowSubCondition.includes(stackInfo.stackType!) ? (
              <Col span={8}>
                <FormItem colon={false} label={t('Sub Condition Type')}>
                  <GeneralSelect
                    option={claimBillObjectTypeOptions.map(option => ({
                      ...option,
                      value: +option.value,
                    }))}
                    value={stackInfo?.subConditionType}
                    placeholder={t('Please select')}
                    style={{ width: 240 }}
                    disabled
                  />
                </FormItem>
              </Col>
            ) : null}
          </Row>
          <Row className="w-full">
            {stackInfo && StackTypeShowSubCondition.includes(stackInfo?.stackType as StackType) ? (
              <Col span={8}>
                <FormItem colon={false} label={t('Period Type')}>
                  <GeneralSelect
                    option={claimPeriodTypeFilterArr.map(option => ({
                      ...option,
                      value: +option.value,
                    }))}
                    value={stackInfo?.periodType}
                    placeholder={t('Please select')}
                    style={{ width: 240 }}
                    disabled
                  />
                </FormItem>
              </Col>
            ) : null}
            {stackInfo &&
            PeriodTypesShowDays.includes(stackInfo.periodType as ClaimPeriodType) &&
            StackTypeShowSubCondition.includes(stackInfo?.stackType as StackType) ? (
              <React.Fragment>
                <Col span={8}>
                  <FormItem colon={false} label={t('Before(Days)')}>
                    <InputNumber
                      style={{ width: 240 }}
                      value={stackInfo?.beforeDays}
                      placeholder={t('Please input')}
                      max={1000}
                      min={0}
                      disabled
                    />
                  </FormItem>
                </Col>
                <Col span={8}>
                  <FormItem colon={false} label={t('After(Days)')} name="afterDays" initialValue={stackInfo?.afterDays}>
                    <InputNumber style={{ width: 240 }} placeholder={t('Please input')} max={1000} min={0} disabled />
                  </FormItem>
                </Col>
              </React.Fragment>
            ) : null}
          </Row>
          <Row style={{ width: '100%' }}>
            <Col span={8}>
              <FormItem label={t('Stack Unit')} colon={false}>
                <GeneralSelect
                  option={stackUnitOptions.map(option => ({
                    ...option,
                    value: +option.value,
                  }))}
                  value={stackInfo?.stackUnit}
                  placeholder={t('Please select')}
                  style={{ width: 240 }}
                  disabled
                />
              </FormItem>
            </Col>
            {stackInfo?.stackUnit === StackUnit.NumberOfTimes ? (
              <Col span={8}>
                <FormItem label={t('Times Type')} colon={false}>
                  <GeneralSelect
                    option={claimTimesTypeOptions.map(option => ({
                      ...option,
                      value: +option.value,
                    }))}
                    value={stackInfo?.timesType}
                    placeholder={t('Please select')}
                    style={{ width: 240 }}
                    disabled
                  />
                </FormItem>
              </Col>
            ) : null}
            {stackInfo?.stackUnit === StackUnit.NumberOfDays ? (
              <Col span={8}>
                <FormItem label={t('Days Type')} colon={false}>
                  <GeneralSelect
                    option={claimDaysTypeOptions.map(option => ({
                      ...option,
                      value: +option.value,
                    }))}
                    value={stackInfo?.daysTypes}
                    mode="multiple"
                    placeholder={t('Please select')}
                    style={{ width: 240 }}
                    disabled
                  />
                </FormItem>
              </Col>
            ) : null}
            <Col span={8}>
              <FormItem
                label={t('Stack Value Type')}
                colon={false}
                name="stackValueType"
                initialValue={record?.stackValueType}
                rules={[
                  {
                    required: true,
                    message: t('Please select'),
                  },
                ]}
              >
                <GeneralSelect
                  option={claimStackValueTypeOptions
                    .filter(option => +option.value !== StackValueType.SelectFormula)
                    .map(option => ({
                      ...option,
                      value: +option.value,
                    }))}
                  placeholder={t('Please select')}
                  style={{ width: 240 }}
                  allowClear={false}
                />
              </FormItem>
            </Col>
            <FormItem shouldUpdate noStyle>
              {({ getFieldValue }) =>
                getFieldValue('stackValueType') === StackValueType.Enumeration ? (
                  <EnumerationSettingFormList label={t('Stack Value')} name="enumStackValue" disabled={false} />
                ) : getFieldValue('stackValueType') === StackValueType.InputValue ? (
                  <Col span={8}>
                    <FormItem
                      label={t('Stack Value')}
                      colon={false}
                      name="stackValue"
                      initialValue={record?.stackValue}
                      rules={[
                        {
                          required: true,
                          message: t('Please input'),
                        },
                      ]}
                    >
                      <InputNumber
                        style={{ width: 240 }}
                        min={0}
                        max={form.getFieldValue('stackType') === StackType.CoPay ? 100 : Number.MAX_SAFE_INTEGER}
                        placeholder={t('Please input')}
                      />
                    </FormItem>
                  </Col>
                ) : null
              }
            </FormItem>
          </Row>
        </Row>
      </Form>
    </Drawer>
  );
};

export default StackValueSettingDrawer;
