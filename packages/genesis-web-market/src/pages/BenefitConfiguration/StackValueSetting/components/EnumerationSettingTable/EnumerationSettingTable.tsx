/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { useTranslation } from 'react-i18next';

import { message } from 'antd';

import EditTable from '@market/components/F-EditableTable';
import { Mode } from '@market/request/interface';

export interface EnumerationTableList {
  key?: string;
  editing?: boolean;
  fieldValue: string;
}

interface Props {
  disabled: boolean;
  editTableData: EnumerationTableList[];
  setEditTableData: (info: EnumerationTableList[]) => void;
  getPopupContainer: () => any;
}

export const EnumerationSettingTable = (props: Props) => {
  const { setEditTableData, getPopupContainer, editTableData, disabled } = props;
  const [t] = useTranslation(['market', 'common']);

  const columns = [
    {
      title: t('Field Value'),
      dataIndex: 'fieldValue',
      width: '740px',
      inputType: 'number',
      controllerprops: {
        style: { width: '700px' },
        placeholder: t('Please input'),
        getPopupContainer,
      },
      render: (text: string) => text,
      validate: [
        {
          trigger: 'onBlur',
          rules: [
            {
              required: true,
              message: t('Please input number'),
            },
          ],
        },
      ],
      editable: true,
    },
  ];

  return (
    <div className="w-full">
      <EditTable
        newAddTop
        onSubmit={(value: EnumerationTableList, key: string) =>
          new Promise((resolve, reject) => {
            const tempTableData = [...editTableData];
            value.key = `${value.fieldValue}${Math.random()}`;
            if (tempTableData.find(item => item.fieldValue === value.fieldValue)) {
              message.error(t('Exists duplicate value'));
              reject();
              return;
            }
            if (key === Mode.Add) {
              tempTableData.push(value);
              setEditTableData(tempTableData);
            } else {
              const editIndex = tempTableData.findIndex(row => row.key === key);
              tempTableData[editIndex] = value;
              setEditTableData(tempTableData);
            }
            resolve(true);
          })
        }
        onDelete={(key: string) => {
          const tempTableData = [...editTableData];
          const deleteIndex = tempTableData.findIndex(row => row.key === key);
          tempTableData.splice(deleteIndex, 1);
          setEditTableData(tempTableData);
        }}
        onCancel={() => {
          editTableData.forEach(row => {
            row.editing = false;
          });
        }}
        deleteIconProps={{
          getPopupContainer,
        }}
        scroll={{ x: 856, y: 216 }}
        columns={columns}
        disabled={disabled}
        data={editTableData}
      />
    </div>
  );
};

export default EnumerationSettingTable;
