import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Checkbox, Form, Space, Tooltip, Upload, UploadProps, message } from 'antd';

import { useRequest } from 'ahooks';
import { PackageClaimStackRequest } from 'market-types';

import { Icon, Modal, UploadFileItem } from '@zhongan/nagrand-ui';

import { DetailPageMode } from '@market/common/interface';
import GeneralSelect from '@market/components/GeneralSelect';
import { NewCalculatorService } from '@market/services/calculator/calculator.service.new';
import { NewMarketService } from '@market/services/market/market.service.new';
import { downloadFile } from '@market/util';

const generateMatrixName = (packageCode: string, packageId: string) => `${packageCode}_${packageId}_ClaimStackMartix`;

interface Props {
  claimStackList: PackageClaimStackRequest[];
  packageCode: string;
  packageId: string;
  claimStackMatrixTableCode?: string;
  mode: DetailPageMode;
}

export const StackValueMatrix = ({
  claimStackList,
  packageCode,
  packageId,
  claimStackMatrixTableCode,
  mode,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [selectedStackCodes, setSelectedStackCodes] = useState<string[]>([]);
  const [checked, setChecked] = useState(false);
  const [fileName, setFileName] = useState<string>('');
  const [matrixTableCode, setMatrixTableCode] = useState<string>(''); // 用来判断有没有生成过matrixTable

  useEffect(() => {
    if (claimStackMatrixTableCode) {
      NewCalculatorService.MatrixTableService.queryMatrixTable(claimStackMatrixTableCode).then(res => {
        if (res) {
          setMatrixTableCode(claimStackMatrixTableCode);
          setFileName(res?.fileName ?? '');
          setSelectedStackCodes(res?.resultCodeList ?? []);
          setChecked(true);
        }
      });
    }
  }, [claimStackMatrixTableCode]);

  const { runAsync: generateMatrixTable, loading } = useRequest(
    () =>
      NewCalculatorService.MatrixTableService.saveMatrixTable({
        // 一个package只会有一个ClaimStackMartix，所以code、name是固定的
        matrixTableCode: generateMatrixName(packageCode, packageId),
        matrixTableName: generateMatrixName(packageCode, packageId),
        matrixTableType: 58, // 固定的type
        resultCodeList: selectedStackCodes,
      }).then(() => {
        message.success(t('Generate Matrix Table Successfully'));
      }),
    {
      manual: true,
    }
  );

  const downloadTemplate = useCallback(
    (results?: string[]) => {
      downloadFile(`/api/calculator/matrixTable/generateTemplate/${matrixTableCode}`, {}, 'get');
    },
    [matrixTableCode]
  );

  const renderDownloadTemplateSection = useCallback(() => {
    if (!matrixTableCode) {
      return null;
    }
    if (fileName) {
      return (
        <div>
          <a onClick={() => downloadTemplate()} className="!underline font-[500]">
            {`${t('Download Template')}`}
          </a>
        </div>
      );
    }

    return (
      <div>
        <span className="text-@divider-color font-[500]">{`${t('Please')} `}</span>
        <a onClick={() => downloadTemplate()} className="!underline font-[500]">
          {`${t('Download Template')}`}
        </a>
        <span className="text-@divider-color font-[500]">{` ${t('First')}`}</span>
      </div>
    );
  }, [downloadTemplate, fileName, matrixTableCode, t]);

  const attachToPackage = useCallback(async () => {
    const { errors = [], warning = [] } =
      await NewMarketService.GoodsPackageValidateService.validatePackageClaimStackMatrix({
        packageId: +packageId,
        claimStackMatrixTableCode: matrixTableCode,
      });

    if (errors?.length > 0) {
      message.error(errors?.join(';'));
      return Promise.reject();
    }
    if (warning.length > 0) {
      message.warning(warning?.join(';'));
      return Promise.reject();
    }

    await NewMarketService.PackageAgreementConfigMgmtService.savePackageAgreement({
      packageId: +packageId,
      claimStackMatrixTableCode: matrixTableCode,
    });
  }, [matrixTableCode, packageId]);

  const deleteMatrixTable = useCallback(() => {
    Modal.confirm({
      title: t('Are you sure to delete this record?'),
      onOk: async () => {
        await NewCalculatorService.MatrixTableService.deleteMatrixTable(matrixTableCode);
        setMatrixTableCode('');
        setFileName('');
        setChecked(false);
        setSelectedStackCodes([]);
        await NewMarketService.PackageAgreementConfigMgmtService.savePackageAgreement({
          packageId: +packageId,
          claimStackMatrixTableCode: '',
        });
        message.success(t('Delete Successfully'));
      },
    });
  }, [matrixTableCode, packageId, t]);

  const uploadProps: UploadProps = {
    method: 'POST',
    name: 'multipartFile',
    accept: '.xls, .xlsx',
    showUploadList: false,
    action: '/api/calculator/matrixTable/upload',
    data: {
      matrixTableCode,
    },
    async onChange(info) {
      if (info.file.status === 'done') {
        await attachToPackage();
        setFileName(info.file.name);
        message.success(t('Upload Successful.'));
      }
      if (info.file.status === 'error') {
        message.error(t('Upload Fail!'));
      }
    },
  };

  const downloadMatrixTableFile = useCallback(() => {
    downloadFile(
      `/api/calculator/v2/matrix-table/download`,
      {
        matrixTableCode,
        matrixTableRevisionId: -1, // 没有多版本，写死-1
      },
      'post'
    );
  }, [matrixTableCode]);

  const renderFileSection = useCallback(() => {
    if (!matrixTableCode) {
      return null;
    }
    if (fileName) {
      return (
        <UploadFileItem
          style={{ width: 600 }}
          fileName={fileName}
          needPreview={false}
          hoverInfoList={
            mode === 'view'
              ? [
                  {
                    icon: <Icon type="download" />,
                    onClick: downloadMatrixTableFile,
                  },
                ]
              : [
                  {
                    icon: (
                      <Upload {...uploadProps}>
                        <Icon type="reload" />
                      </Upload>
                    ),
                  },
                  {
                    icon: <Icon type="download" />,
                    onClick: downloadMatrixTableFile,
                  },
                  {
                    icon: <Icon type="delete" />,
                    onClick: deleteMatrixTable,
                  },
                ]
          }
        />
      );
    }

    return (
      <Upload {...uploadProps}>
        <Button icon={<Icon type="upload" />}>{t('Upload')}</Button>
      </Upload>
    );
  }, [matrixTableCode, fileName, uploadProps, t, mode, downloadMatrixTableFile, deleteMatrixTable]);

  const confirmBeforeSubmit = useCallback(
    () =>
      new Promise(resolve => {
        if (matrixTableCode) {
          Modal.confirm({
            title: t('Confirm'),
            content: t('Stack code have been changed. The uploaded file will be cleared. Please confirm.'),
            onOk: () => {
              resolve(true);
            },
            onCancel: () => {
              resolve(false);
            },
          });
        } else {
          resolve(true);
        }
      }),
    [matrixTableCode, t]
  );

  return (
    <div className="mt-6">
      <Checkbox
        onChange={evt => {
          setChecked(evt.target.checked);
        }}
        checked={checked}
        disabled={mode === DetailPageMode.view}
      >
        {t('Stack Value Matrix')}
        <Tooltip
          title={t(
            'Support maintaining the association between stack values, only support value types of fixed amount and enumeration.'
          )}
          overlayStyle={{ width: 350 }}
        >
          <Icon
            type="info-circle"
            style={{ marginLeft: 8, fontSize: 16, display: 'inline-block', verticalAlign: 'middle' }}
          />
        </Tooltip>
      </Checkbox>
      {checked ? (
        <React.Fragment>
          <Form.Item label={t('Select Stack Code')} className="mt-4">
            <Space.Compact>
              <GeneralSelect
                option={claimStackList.map(item => ({
                  label: item.stackCode,
                  value: item.stackCode,
                }))}
                style={{ width: 660 }}
                mode="multiple"
                value={selectedStackCodes}
                onChange={async value => {
                  const result = await confirmBeforeSubmit();
                  if (result) {
                    setSelectedStackCodes(value as string[]);
                    setFileName('');
                    setMatrixTableCode('');
                  } else {
                    setSelectedStackCodes([...selectedStackCodes]);
                  }
                }}
              />
              {matrixTableCode && (
                <span className="flex items-center justify-center w-8 rounded-lg border-solid border-[1px] border-@primary-color ml-4 cursor-pointer">
                  <Icon type="snyc" className="text-@primary-color" onClick={generateMatrixTable} />
                </span>
              )}
            </Space.Compact>
          </Form.Item>
          {!matrixTableCode && (
            <Button
              className="mt-[-8px] mb-4"
              disabled={selectedStackCodes.length === 0}
              onClick={() => {
                generateMatrixTable().then(() => {
                  setMatrixTableCode(generateMatrixName(packageCode, packageId));
                });
              }}
              loading={loading}
              icon={<Icon type="snyc" />}
            >
              {t('Generate Matrix Table')}
            </Button>
          )}
          {renderDownloadTemplateSection()}
          {renderFileSection()}
        </React.Fragment>
      ) : null}
    </div>
  );
};

export default StackValueMatrix;
