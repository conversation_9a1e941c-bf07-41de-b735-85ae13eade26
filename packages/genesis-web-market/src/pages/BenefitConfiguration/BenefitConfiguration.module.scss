.premium-calculation-method-label {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.calculate-premium {
  color: var(--info-color);
  background-color: var(--info-color-bg);
}

.calculate-sa {
  color: var(--success-color-text-dark);
  background-color: var(--success-color-bg);
}

.specified-sa-premium {
  color: var(--primary-light);
  background-color: var(--primary-disabled-color);
}

.liability-order-table {
  td {
    user-select: none;
  }
}

:export {
  textColorTertiary: var(--text-color-tertiary);
}
