import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';
import { FormInstance } from 'antd/lib';

import { pick } from 'lodash-es';

import { EditableTable, FieldType } from '@zhongan/nagrand-ui';

import { PackageProductBenefit } from 'genesis-web-service';
import {
  PackageManualAnnualPremiumBase,
  ProductManualAnnualPremiumBase,
} from 'genesis-web-service/service-types/market-types/package';

import { findOptionNameFromList } from '@market/components/ConditionSearch/util';
import { useBizDictAsOptions } from '@market/hook/bizDict';

interface AnnualPremiumProps {
  disabled: boolean;
  insuranceList: PackageProductBenefit[];
  form: FormInstance;
}

export const AnnualPremium = ({ disabled, insuranceList, form }: AnnualPremiumProps) => {
  const initialValues = form?.getFieldValue('manualAnnualPremium') as PackageManualAnnualPremiumBase;
  const [dataSource, setDataSource] = useState<ProductManualAnnualPremiumBase[]>(
    initialValues?.manualAnnualPremiumList || []
  );
  const [t] = useTranslation(['market', 'common']);
  const manualAnnualPremiumInputMethodOptions = useBizDictAsOptions('manualAnnualPremiumInputMethod');

  const insuranceOptions = useMemo(
    () =>
      insuranceList?.map(item => ({
        label: item.productName,
        value: item.productId,
      })),
    [insuranceList]
  );

  const columns = [
    { title: t('No.'), render: (text: string, record: any, index: number) => index + 1 },
    {
      title: t('Product Name'),
      dataIndex: 'productId',
      editable: true,
      formItemProps: {
        rules: [{ required: true, message: t('Please select') }],
      },
      fieldProps: (record: ProductManualAnnualPremiumBase) => ({
        type: FieldType.Select,
        extraProps: {
          options: insuranceOptions?.map(option => ({
            ...option,
            disabled:
              dataSource.findIndex(item => option.value === item.productId && option.value !== record.productId) !== -1,
          })),
        },
      }),
      render: (text: string) => findOptionNameFromList(text, insuranceOptions),
    },
    {
      title: t('Input Method'),
      dataIndex: 'manualAnnualPremiumInputMethods',
      editable: true,
      formItemProps: {
        rules: [{ required: true, message: t('Please select') }],
      },
      fieldProps: {
        type: FieldType.Select,
        extraProps: {
          options: manualAnnualPremiumInputMethodOptions,
          mode: 'multiple',
          style: {
            width: '240px',
          },
        },
      },
      render: (list: string[]) =>
        list?.map(text => findOptionNameFromList(text, manualAnnualPremiumInputMethodOptions))?.join(' , '),
    },
  ];

  const setFieldValue = (list: ProductManualAnnualPremiumBase[]) => {
    form.setFieldValue(
      ['manualAnnualPremium', 'manualAnnualPremiumList'],
      list?.map(item => pick(item, ['productId', 'manualAnnualPremiumInputMethods']))
    );
    setDataSource(list);
  };

  return (
    <React.Fragment>
      <Form.Item name="manualAnnualPremium" noStyle />
      <div className="text-@text-color mb-2">{t('Manual Annual Premium')}</div>
      <EditableTable
        readonly={disabled}
        columns={columns}
        dataSource={dataSource}
        setDataSource={setDataSource}
        rowKey="productId"
        handleConfirm={(record, editingKey, list) => {
          setFieldValue(list!);
        }}
        pagination={false}
        deleteBtnProps={{
          handleDelete: deleteIndex => {
            const list = dataSource?.filter((item, index) => deleteIndex !== index);
            setFieldValue(list);
          },
        }}
      />
    </React.Fragment>
  );
};
