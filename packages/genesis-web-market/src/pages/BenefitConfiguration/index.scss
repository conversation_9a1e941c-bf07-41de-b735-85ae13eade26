.benefit-configuration-container {
  height: 100%;
  .right-content-wrapper {
    background-color: transparent !important;
  }
  .base-section .base-section-title {
    font-size: 16px;
    color: var(--text-color);
    line-height: 24px;
  }
  .benefit-config-form-wrapper {
    border: 1px solid var(--border-light);
    border-radius: 8px;
    .product-name-version {
      padding: 16px 24px;
      font-weight: bold;
    }
    .benefit-config-form {
      border-top: 1px solid var(--border-light);
      padding: 16px 0 24px;
      margin: 0 24px;
    }
  }
  .benefit-config-form-wrapper + .benefit-config-form-wrapper {
    margin-top: 16px;
  }
  .template-download-btn {
    padding: 0 30px 0 8px;
    width: 240px;
    height: 38px;
    line-height: 38px;
    color: var(--text-color-secondary);
    cursor: pointer;

    &:hover {
      background-color: var(--primary-disabled-color);
    }
    .anticon-download {
      float: right;
      margin-top: 11px;
    }
  }
  .template-file-name {
    position: relative;
    padding-right: 12px;
    width: 240px;
    height: 32px;
    line-height: 32px;
    border-bottom: 1px solid var(--border-light);
    color: $disabled-color;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .anticon-close {
      position: absolute;
      right: 0;
      top: 0;
      margin-top: 11px;
      font-size: 12px;
      cursor: pointer;
    }
  }
  .upload-tips {
    margin-top: 4px;
    font-size: 12px;
    color: $disabled-color;
  }
  .market-ant4-table-cell {
    .market-ant4-legacy-form-item {
      margin-bottom: 0 !important;
    }
  }
}
