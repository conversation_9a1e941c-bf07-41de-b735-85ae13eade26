/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
export interface EnumerationTableList {
  key?: string;
  editing?: boolean;
  fieldValue: number;
}

export interface PreDefineValues {
  minValue?: number;
  maxValue?: number;
  code?: string;
  valueType?: number;
  valueList?: number[];
  value?: number;
}

export interface ProductSASettingFormFields {
  fixedAmount: number | undefined;
  minProductDeductibleAmount: number | undefined;
  maxProductDeductibleAmount: number | undefined;
  ProductDeductibleAmount: number;
  fieldValue?: string[];
}

export enum ProductDeductibleAmountType {
  FixedAmount = 1,
  Range = 2,
  Enumeration = 3,
  ByFormula = 4,
}

export enum PremiumCalculationMethod {
  CalculatePremium = '1',
  CalculateSA = '2',
  Unit = '3',
  BenefitLevel = '4',
  SpecifiedSAPremium = '5',
  UserInput = '6',
}

export const EffectivePremiumCalculationMethodList: string[] = [
  PremiumCalculationMethod.CalculatePremium,
  PremiumCalculationMethod.CalculateSA,
  PremiumCalculationMethod.SpecifiedSAPremium,
  PremiumCalculationMethod.UserInput,
];
