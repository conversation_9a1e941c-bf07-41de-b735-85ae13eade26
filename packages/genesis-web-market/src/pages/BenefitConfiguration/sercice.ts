/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { message } from 'antd';

import { MktYesNo, PackageProductBenefit } from 'genesis-web-service/lib/market';

import I18nInstance from '@market/i18n';
import { NewMarketService } from '@market/services/market/market.service.new';

import { PreDefineValues, ProductDeductibleAmountType } from './interface';

export const handlePreDefineValues = (defineValues: PreDefineValues) => {
  let resultValue = '';
  if (defineValues.valueType === ProductDeductibleAmountType.FixedAmount) {
    resultValue = `${defineValues.value as number}`;
  } else if (defineValues.valueType === ProductDeductibleAmountType.Range) {
    const min = defineValues.minValue !== undefined ? defineValues.minValue : '';
    const max = defineValues.maxValue !== undefined ? defineValues.maxValue : '';
    resultValue = `${min} - ${max}`;
  } else if (defineValues.valueType === ProductDeductibleAmountType.ByFormula) {
    resultValue = I18nInstance.t('By Formula', { ns: ['market', 'common'] });
  } else {
    resultValue = `${(defineValues.valueList as number[]).toString()}`;
  }
  return resultValue;
};

export const getProductValues = (
  row: {
    productSchemaCodePreDefineValues: PreDefineValues[];
    isVirtualProduct: boolean;
    elementValue: string;
  },
  code: string
) => {
  let resultText = '';
  if (Array.isArray(row.productSchemaCodePreDefineValues) && row.productSchemaCodePreDefineValues.length > 0) {
    const valueItem = row.productSchemaCodePreDefineValues.find(item => item.code === code);
    if (valueItem) {
      resultText = handlePreDefineValues(valueItem);
    } else {
      resultText = I18nInstance.t('Unset', { ns: ['market', 'common'] });
    }
  } else if (row.isVirtualProduct) {
    resultText = '0';
  } else {
    resultText = row.elementValue || I18nInstance.t('Unset', { ns: ['market', 'common'] });
  }
  return resultText;
};

export const getLiabilityValues = (
  row: {
    liabilitySchemaCodePreDefineValues: PreDefineValues[];
    isVirtualProduct: boolean;
    liabilityAmount: string;
  },
  code: string
) => {
  let resultText = '';
  if (Array.isArray(row.liabilitySchemaCodePreDefineValues) && row.liabilitySchemaCodePreDefineValues.length > 0) {
    const valueItem = row.liabilitySchemaCodePreDefineValues.find(item => item.code === code);
    if (valueItem) {
      resultText = handlePreDefineValues(valueItem);
    } else {
      resultText = I18nInstance.t('Unset', { ns: ['market', 'common'] });
    }
  } else if (row.isVirtualProduct) {
    resultText = '0';
  } else {
    resultText = row.liabilityAmount || I18nInstance.t('Unset', { ns: ['market', 'common'] });
  }
  return resultText;
};

export const validateStackValueMatrix = async (packageId: number, mode: string) => {
  const packageAgreement = await NewMarketService.PackageAgreementConfigMgmtService.queryPackageAgreement({
    packageId: +packageId,
    status: mode,
  });

  if (!packageAgreement.value?.claimStackMatrixTableCode) {
    return true;
  }

  const { errors = [], warning = [] } =
    await NewMarketService.GoodsPackageValidateService.validatePackageClaimStackMatrix({
      packageId: +packageId,
      claimStackMatrixTableCode: packageAgreement.value.claimStackMatrixTableCode,
    });

  if (errors?.length > 0) {
    message.error(errors?.join(';'));
    return false;
  }
  if (warning.length > 0) {
    message.warning(warning?.join(';'));
    return false;
  }

  return true;
};

export const queryPackageProductList = async (packageId: string | number) => {
  const tempConfigedValues: Record<string, any> = {};
  const res = await NewMarketService.PackageProductInsuranceMgmtService.getProductInsuranceList({ packageId: +packageId });
  if (res.success && res.value) {
    const productList = res.value as (PackageProductBenefit & {
      elementValue: string;
    })[];
    let isDriven = false;
    let hasInterest = false;
    productList.forEach(product => {
      if (product.productTypeCode === 1 && product.productSubCategoryId) {
        isDriven = true;
      }
      if (`${product.interestAvailable}` === MktYesNo.Yes) {
        hasInterest = true;
      }
      if (product.unitType === 1) {
        product.elementValue = product.securityAmount;
      } else if (product.unitType === 2) {
        product.elementValue = product.premium;
        product.packageLiabilitys.forEach(j => {
          j.liabilityAmount = j.liabilityPremium;
          // j.liabilityAmountRemark = j.liabilityPremiumRemark;
        });
      } else if (product.unitType === 3) {
        product.elementValue = '';
      } else if (product.unitType === 4) {
        product.elementValue = product.benefitLevel;
      } else if (product.unitType === 5) {
        product.elementValue = product.securityAmount;
      }
    });
    productList.sort((i, j) => i.orderNo - j.orderNo);

    productList.forEach((product, productIndex) => {
      product.packageLiabilitys?.forEach((liability, liabilityIndex) => {
        liability.liabilityInterestBenefitList?.forEach(liabilityInterest => {
          const configedInterestValue = liabilityInterest?.interestSchemaCodePreDefineValues?.[0];
          if (configedInterestValue) {
            if (!tempConfigedValues[productIndex]) {
              tempConfigedValues[productIndex] = {};
              tempConfigedValues[productIndex][liabilityIndex] = {};
            } else if (!tempConfigedValues[productIndex][liabilityIndex]) {
              tempConfigedValues[productIndex][liabilityIndex] = {};
            }
            tempConfigedValues[productIndex][liabilityIndex][liabilityInterest.interestId] = configedInterestValue;
          } else {
            // 查询接口没有返回interestSchemaCodePreDefineValues，保存接口interestSchemaCodePreDefineValues必填，兼容一下
            liabilityInterest.interestSchemaCodePreDefineValues = [];
          }
        });
      });
    });

    return {
      isDriven,
      hasInterest,
      productLiabilityInterestConfigedValues: tempConfigedValues,
      productList,
    };
  }

  return {
    isDriven: true,
    hasInterest: false,
    productLiabilityInterestConfigedValues: tempConfigedValues,
    productList: [],
  };
};
