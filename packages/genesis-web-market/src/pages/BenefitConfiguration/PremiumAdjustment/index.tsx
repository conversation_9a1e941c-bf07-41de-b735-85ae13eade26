import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Radio, Select } from 'antd';
import { FormInstance } from 'antd/lib';

import { useRequest } from 'ahooks';
import { pick } from 'lodash-es';
import { YesNoEnum } from 'market-types';

import { EditableTable, FieldType } from '@zhongan/nagrand-ui';

import { PackageProductBenefit } from 'genesis-web-service';
import {
  PackageNetPremiumAdjustmentAgreementBase,
  ProductNetPremiumAdjustmentAgreementBase,
} from 'genesis-web-service/service-types/market-types/package';

import { findOptionNameFromList } from '@market/components/ConditionSearch/util';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { NewCalculatorService } from '@market/services/calculator/calculator.service.new';

interface PremiumAdjustmentProps {
  disabled: boolean;
  insuranceList: PackageProductBenefit[];
  form: FormInstance;
}

export const PremiumAdjustment = ({ disabled, insuranceList, form }: PremiumAdjustmentProps) => {
  const initialValues = form?.getFieldValue(
    'netPremiumAdjustmentAgreement'
  ) as PackageNetPremiumAdjustmentAgreementBase;
  const [dataSource, setDataSource] = useState<ProductNetPremiumAdjustmentAgreementBase[]>(
    initialValues?.netPremiumAdjustmentAgreementList || []
  );

  const [isAutoAdjustNetPremium, setIsAutoAdjustNetPremium] = useState<YesNoEnum>();
  const [t] = useTranslation(['market', 'common']);
  const yesNoOptions = useBizDictAsOptions('yesNo', {
    labelKey: 'dictValueName',
    valueKey: 'enumItemName',
  });

  const insuranceOptions = useMemo(
    () =>
      insuranceList?.map(item => ({
        label: item.productName,
        value: item.productId,
      })),
    [insuranceList]
  );

  const { data: formulaList } = useRequest(async () => {
    const { value } = await NewCalculatorService.FormulaService.list({});

    return value?.map(item => ({
      label: item.formulaName!,
      value: item.formulaCode!,
    }));
  });

  const columns = [
    {
      title: t('Product Name'),
      dataIndex: 'productId',
      editable: true,
      formItemProps: {
        rules: [{ required: true, message: t('Please select') }],
      },
      fieldProps: (record: ProductNetPremiumAdjustmentAgreementBase) => ({
        type: FieldType.Select,
        extraProps: {
          options: insuranceOptions?.map(option => ({
            ...option,
            disabled:
              dataSource.findIndex(item => option.value === item.productId && option.value !== record.productId) !== -1,
          })),
        },
      }),
      render: (text: string) => findOptionNameFromList(text, insuranceOptions),
    },
    {
      title: t('Allow Manual Adjustment'),
      dataIndex: ['adjustNetPremium', 'isManualAdjustNetPremium'],
      editable: true,
      formItemProps: {
        rules: [{ required: true, message: t('Please select') }],
      },
      fieldProps: {
        type: FieldType.Select,
        extraProps: {
          options: yesNoOptions,
        },
      },
      render: (text: string) => findOptionNameFromList(text, yesNoOptions),
    },
    {
      title: t('Automatic Adjustment'),
      dataIndex: ['adjustNetPremium', 'isAutoAdjustNetPremium'],
      editable: true,
      formItemProps: {
        rules: [{ required: true, message: t('Please select') }],
      },
      fieldProps: {
        type: FieldType.Select,
        extraProps: {
          options: yesNoOptions,
          onChange: (value: YesNoEnum) => {
            setIsAutoAdjustNetPremium(value);
          },
        },
      },
      render: (text: string) => findOptionNameFromList(text, yesNoOptions),
    },
    {
      title: t('Formula'),
      dataIndex: ['adjustNetPremium', 'adjustNetPremiumFormula', 'formulaCode'],
      editable: true,
      formItemProps: {
        rules: [{ required: isAutoAdjustNetPremium === YesNoEnum.YES, message: t('Please select') }],
      },
      fieldProps: {
        type: FieldType.Customized,
        render:
          isAutoAdjustNetPremium === YesNoEnum.YES ? (
            <Select options={formulaList} placeholder={t('Please select')} showSearch optionFilterProp="label" />
          ) : (
            t('- -')
          ),
      },
      render: (text: string) => findOptionNameFromList(text, formulaList),
    },
  ];

  const setFieldValue = (list: ProductNetPremiumAdjustmentAgreementBase[]) => {
    form.setFieldValue(
      ['netPremiumAdjustmentAgreement', 'netPremiumAdjustmentAgreementList'],
      list?.map(item => pick(item, ['productId', 'adjustNetPremium']))
    );
    setDataSource(list);
  };

  return (
    <React.Fragment>
      <Form.Item name="netPremiumAdjustmentAgreement" noStyle />
      <div className="text-@text-color mb-2">{t('Policy Net Premium Adjustment')}</div>
      <Form.Item
        label={t('Allow Manual Adjustment')}
        name={['netPremiumAdjustmentAgreement', 'manualAdjustPolicyNetPremium']}
        layout="horizontal"
        className="mb-2"
        initialValue={false}
      >
        <Radio.Group
          options={[
            {
              label: t('Yes'),
              value: true,
            },
            {
              label: t('No'),
              value: false,
            },
          ]}
        />
      </Form.Item>
      <div className="text-@text-color mb-2 mt-4">{t('Product Net Premium Adjustment')}</div>
      <EditableTable
        addBtnProps={{
          type: 'default',
        }}
        readonly={disabled}
        columns={columns}
        dataSource={dataSource}
        setDataSource={setDataSource}
        rowKey="productId"
        handleConfirm={(record, editingKey, list) => {
          const editRecord = list?.find(item => item.productId === record?.productId);
          if (editRecord?.adjustNetPremium?.isAutoAdjustNetPremium === YesNoEnum.NO) {
            delete editRecord?.adjustNetPremium?.adjustNetPremiumFormula;
          }
          setFieldValue(list!);
        }}
        pagination={false}
        scroll={{ x: 'max-content' }}
        deleteBtnProps={{
          handleDelete: deleteIndex => {
            const list = dataSource?.filter((item, index) => deleteIndex !== index);
            setFieldValue(list);
          },
        }}
      />
    </React.Fragment>
  );
};
