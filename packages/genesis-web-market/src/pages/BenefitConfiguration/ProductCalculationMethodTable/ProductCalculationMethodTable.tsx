import { useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';

import { Table } from '@zhongan/nagrand-ui';

import { PackageProductBenefit } from 'genesis-web-service';

import ColumnRequiredTitle from '@market/components/ColumnRequiredTitle';
import GeneralSelect from '@market/components/GeneralSelect';
import { isILPProduct } from '@market/util';

interface calculationRuleDataInfo {
  productName: string;
  productCode: string;
  id: number;
  unitType: string | number;
}

interface Props {
  disabled: boolean;
  insuranceList: PackageProductBenefit[];
  agreementBasisOptions: {
    value: string;
    label: string;
  };
  renewInsuranceListInfo: (id: number, key: string, value: string) => void;
}

export const ProductCalculationMethodTable = ({
  disabled,
  insuranceList,
  agreementBasisOptions,
  renewInsuranceListInfo,
}: Props) => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();
  const container = useRef<HTMLDivElement | null>(null);

  const calculationRuleData: calculationRuleDataInfo[] = useMemo(
    () =>
      (insuranceList || [])
        .filter(packageproductItem => !isILPProduct(packageproductItem.productCategoryId))
        .map(packageProductItem => ({
          productName: packageProductItem.productName,
          productCode: packageProductItem.insuranceProductCode,
          id: packageProductItem.id,
          unitType: packageProductItem.unitType,
        })),
    [insuranceList]
  );

  const columns = [
    {
      title: t('Product Name / Code'),
      dataIndex: 'productCode',
      render: (text: string, record: calculationRuleDataInfo) => `${record.productCode}/${record.productName}`,
    },
    {
      title: <ColumnRequiredTitle title={t('Calculation Method')} />,
      dataIndex: 'unitType',
      width: 300,
      render: (text: string | number, record: calculationRuleDataInfo, index: number) => (
        <div>
          <Form.Item
            label={null}
            initialValue={text ? `${text}` : undefined}
            style={{ marginBottom: 0, height: '30px' }}
            name={`unitType${index}`}
            rules={[{ required: true, message: t('Please select') }]}
          >
            <GeneralSelect
              allowClear={false}
              disabled={disabled}
              style={{ width: '240px' }}
              placeholder={t('Please select')}
              getPopupContainer={() => container?.current || document.body}
              onChange={(value: string) => {
                renewInsuranceListInfo(record.id, 'unitType', value);
              }}
              option={agreementBasisOptions}
            />
          </Form.Item>
        </div>
      ),
    },
  ];

  return (
    <div style={{ padding: '0 0 16px' }} ref={container}>
      <Form form={form}>
        <Table columns={columns} dataSource={calculationRuleData} pagination={false} scroll={{ y: 268 }} />
      </Form>
    </div>
  );
};

export default ProductCalculationMethodTable;
