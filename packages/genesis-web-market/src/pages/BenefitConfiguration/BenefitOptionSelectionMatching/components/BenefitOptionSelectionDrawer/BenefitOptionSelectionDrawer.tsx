/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import Icon from '@ant-design/icons';

import classNames from 'classnames/bind';
import { cloneDeep, keyBy } from 'lodash-es';

import { Drawer } from '@zhongan/nagrand-ui';

import {
  BenefitOptions,
  ConfiguredBenefitOptions,
} from 'genesis-web-service/lib/market/benefitOptionSelection.interface';

import tick from '@market/asset/svg/tick.svg';

import styles from './BenefitOptionSelectionDrawer.module.scss';

const cx = classNames.bind(styles);

interface Props {
  visible: boolean;
  closeDrawer: () => void;
  selectedProductBenefitOptions: ConfiguredBenefitOptions[];
  onDrawerSubmit: (list: ConfiguredBenefitOptions[]) => void;
  productConfiguredBenefitOptions: ConfiguredBenefitOptions[];
}

export const BenefitOptionSelectionDrawer = ({
  visible,
  closeDrawer,
  onDrawerSubmit,
  selectedProductBenefitOptions,
  productConfiguredBenefitOptions,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [drawerSelectedProductBenefitOptions, setDrawerSelectedProductBenefitOptions] = useState<
    ConfiguredBenefitOptions[]
  >([]);

  const selectedBenefitOptionsMap = useMemo(
    () => keyBy(drawerSelectedProductBenefitOptions, 'productCode'),
    [drawerSelectedProductBenefitOptions]
  );
  const productConfiguredBenefitOptionsMap = useMemo(
    () => keyBy(productConfiguredBenefitOptions, 'productCode'),
    [productConfiguredBenefitOptions]
  );

  useEffect(() => {
    setDrawerSelectedProductBenefitOptions(selectedProductBenefitOptions);
  }, [selectedProductBenefitOptions]);

  const checkLabel = useCallback(
    (productCode: string, childItem: BenefitOptions) => {
      const tempDrawerSelectedProductBenefitOptions = cloneDeep(drawerSelectedProductBenefitOptions);
      const currentProductBenefit = tempDrawerSelectedProductBenefitOptions.find(
        groupItem => groupItem.productCode === productCode
      );
      if (currentProductBenefit) {
        if (currentProductBenefit.benefitOptions.find(optionItem => optionItem.optionCode === childItem.optionCode)) {
          currentProductBenefit.benefitOptions = currentProductBenefit.benefitOptions.filter(
            filterItem => filterItem.optionCode !== childItem.optionCode
          );
        } else {
          currentProductBenefit.benefitOptions.push(childItem);
        }
      } else {
        tempDrawerSelectedProductBenefitOptions.push({
          productId: productConfiguredBenefitOptionsMap[productCode].productId,
          productName: productConfiguredBenefitOptionsMap[productCode].productName,
          productCode: productConfiguredBenefitOptionsMap[productCode].productCode,
          benefitOptions: [childItem],
        });
      }
      setDrawerSelectedProductBenefitOptions(tempDrawerSelectedProductBenefitOptions);
    },
    [drawerSelectedProductBenefitOptions, productConfiguredBenefitOptionsMap]
  );

  const renderBenefitOptionsList = useCallback(
    (productCode: string, childList: BenefitOptions[]) =>
      childList.map(childItemInfo => (
        <div
          className={cx('label-item', {
            'selected-label-item': selectedBenefitOptionsMap?.[productCode]?.benefitOptions?.find(
              option => option.optionCode === childItemInfo.optionCode
            ),
          })}
          onClick={() => checkLabel(productCode, childItemInfo)}
        >
          {childItemInfo.optionName}
          <Icon className={cx('selected-icon')} component={tick} />
        </div>
      )),
    [checkLabel, selectedBenefitOptionsMap]
  );

  const close = useCallback(() => {
    closeDrawer();
    setDrawerSelectedProductBenefitOptions([]);
  }, [closeDrawer]);

  return (
    <Drawer
      open={visible}
      title={t('Select Benefit Factors')}
      onClose={close}
      onSubmit={() => {
        onDrawerSubmit(drawerSelectedProductBenefitOptions);
        close();
      }}
    >
      {(productConfiguredBenefitOptions || []).map(benefitOptionItem => (
        <div key={benefitOptionItem.productCode}>
          <span>{benefitOptionItem.productCode}</span>
          <div className={cx('label-box')}>
            {renderBenefitOptionsList(benefitOptionItem.productCode, benefitOptionItem?.benefitOptions || [])}
          </div>
        </div>
      ))}
    </Drawer>
  );
};

export default BenefitOptionSelectionDrawer;
