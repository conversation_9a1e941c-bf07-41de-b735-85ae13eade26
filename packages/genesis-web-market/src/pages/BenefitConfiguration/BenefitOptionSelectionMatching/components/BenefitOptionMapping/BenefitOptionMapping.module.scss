.label-box {
  display: flex;
  margin-top: 8px;
  margin-bottom: 12px;
  .disabled-label-item {
    display: inline-block;
    padding: 4px 8px;
    margin-right: 10px;
    border-radius: 4px;
    text-align: center;
    font-size: 12px;
    line-height: $font-size-lg;
    background: var(--primary-disabled-color);
  }
}
.condition-box {
  width: 100%;
  padding: 24px;
  margin-bottom: 24px;
  .inputGroup-item {
    width: 100%;
    display: flex;
    margin-top: 8px;
    line-height: 32px;
    height: 36px;
    .product-code {
      width: 120px;
      line-height: 22px;
      font-size: 14px;
      padding: 5px 8px;
      border-radius: 8px 0px 0px 8px;
      background: var(--primary-disabled-color);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .add-button {
    margin-top: 8px;
  }
}

.layout {
  display: flex;
  background-color: transparent;

  .delete-icon {
    padding-top: 8px;
    margin-left: 10px;
    display: none;
    cursor: pointer;
  }
  &:hover {
    .delete-icon {
      display: inline-block;
    }
  }
  .layout-sider {
    position: relative;
    margin-left: 15px;
    margin-bottom: 8px;
    border-left: 1px solid var(--disabled-color);
    min-height: 45px;
    width: 40px;
    overflow: initial;

    .relation-button {
      position: absolute;
      top: calc(50% - 16px);
      left: -46%;
      font-weight: bold !important;
      min-width: 35.5px !important;
      padding: 0 1px !important;
      color: var(--info-color);
      background: var(--info-color-bg);
    }
  }
  .layout-content {
    display: flex;
    align-items: center;
  }
}
