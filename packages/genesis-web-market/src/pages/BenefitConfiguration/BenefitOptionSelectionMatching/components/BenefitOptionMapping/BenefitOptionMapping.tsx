/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, Input, Tooltip } from 'antd';

import classNames from 'classnames/bind';
import { cloneDeep, keyBy } from 'lodash-es';

import { Icon } from '@zhongan/nagrand-ui';

import {
  BenefitOptions,
  ConfiguredBenefitOptions,
  MatchGroupInfo,
} from 'genesis-web-service/lib/market/benefitOptionSelection.interface';

import GeneralSelect from '@market/components/GeneralSelect';

import styles from './BenefitOptionMapping.module.scss';

const cx = classNames.bind(styles);

interface Props {
  disabled: boolean;
  selectedProductBenefitOptions: ConfiguredBenefitOptions[];
  matchGroups: MatchGroupInfo[];
  setMatchGroups: (arr: MatchGroupInfo[]) => void;
  setHasChangedStatus: (bool: boolean) => void;
}

export const BenefitOptionMapping = ({
  disabled,
  selectedProductBenefitOptions,
  matchGroups,
  setMatchGroups,
  setHasChangedStatus,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();

  const selectedBenefitOptionsMap = useMemo(
    () => keyBy(selectedProductBenefitOptions, 'productCode'),
    [selectedProductBenefitOptions]
  );

  const deleteConditionItem = useCallback(
    (index: number) => {
      const tempMatchGroups = cloneDeep(matchGroups);
      tempMatchGroups.splice(index, 1);
      setMatchGroups(tempMatchGroups);
      setHasChangedStatus(true);
    },
    [matchGroups, setHasChangedStatus, setMatchGroups]
  );

  const addconditionDetailListItem = useCallback(() => {
    const tempMatchGroups = cloneDeep(matchGroups);
    const tempMatchGroupItem = {
      groupNo: cloneDeep(matchGroups).length + 1,
      groupOptions: cloneDeep(selectedProductBenefitOptions).map(item => ({
        ...item,
        benefitOptions: [],
      })),
    };
    tempMatchGroups.push(tempMatchGroupItem);
    setMatchGroups(tempMatchGroups);
    setHasChangedStatus(true);
  }, [matchGroups, selectedProductBenefitOptions, setHasChangedStatus, setMatchGroups]);

  const selectMatchGroups = useCallback(
    (productCode: string, groupIndex: number, optionIndex: number, values: string[]) => {
      const tempMatchGroups = cloneDeep(matchGroups);
      let currentBenefitOptions: BenefitOptions[] = [];
      if (values && values.length > 0) {
        currentBenefitOptions = selectedBenefitOptionsMap[productCode].benefitOptions.filter(item =>
          values.includes(item.optionCode)
        );
      }

      tempMatchGroups[groupIndex].groupOptions[optionIndex].benefitOptions = currentBenefitOptions;
      setMatchGroups(tempMatchGroups);
    },
    [matchGroups, selectedBenefitOptionsMap, setMatchGroups]
  );

  return (
    <Form layout="vertical" style={{ marginTop: 32 }} form={form} colon={false}>
      <div style={{ marginBottom: 8 }}>
        <span style={{ fontWeight: 600, marginRight: 8 }}>{t('Benefit Option Mapping')}</span>
      </div>
      {selectedProductBenefitOptions.length > 0 ? (
        <div className={cx('condition-box')}>
          {matchGroups.map((item, groupIndex) => (
            <div className={cx('layout')}>
              <div className={cx('layout-sider')}>
                <Button type="text" className={cx('relation-button')}>
                  {t('AND')}
                </Button>
              </div>
              <div className={cx('layout-content')}>
                <div>
                  {(item.groupOptions || []).map((optionItem, optionIndex) => (
                    <div className={cx('inputGroup-item')}>
                      <Input.Group compact>
                        <div className={cx('product-code')}>
                          <Tooltip title={optionItem.productCode}>{optionItem.productCode}</Tooltip>
                        </div>
                        <Form.Item
                          noStyle
                          rules={[
                            {
                              required: true,
                              message: t('Please select'),
                            },
                          ]}
                        >
                          <GeneralSelect
                            option={selectedBenefitOptionsMap[optionItem.productCode].benefitOptions.map(option => ({
                              value: option.optionCode,
                              label: option.optionName,
                            }))}
                            mode="multiple"
                            placeholder={t('Please select')}
                            style={{ width: 560 }}
                            disabled={disabled}
                            value={optionItem.benefitOptions.map(groupBenefitOptions => groupBenefitOptions.optionCode)}
                            onChange={values => {
                              selectMatchGroups(optionItem.productCode, groupIndex, optionIndex, values);
                              setHasChangedStatus(true);
                            }}
                          />
                        </Form.Item>
                      </Input.Group>
                    </div>
                  ))}
                </div>
                {disabled ? null : (
                  <Icon type="delete" className={cx('delete-icon')} onClick={() => deleteConditionItem(groupIndex)} />
                )}
              </div>
            </div>
          ))}
          {disabled ? null : (
            <div className={cx('add-button')}>
              <Button onClick={() => addconditionDetailListItem()} size="middle">
                <Icon type="add" /> {t('Add New')}
              </Button>
            </div>
          )}
        </div>
      ) : null}
    </Form>
  );
};

export default BenefitOptionMapping;
