import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Modal } from 'antd';

import classNames from 'classnames/bind';

import { Icon } from '@zhongan/nagrand-ui';

import {
  ConfiguredBenefitOptions,
  MatchGroupInfo,
} from 'genesis-web-service/lib/market/benefitOptionSelection.interface';

import BenefitOptionSelectionDrawer from '../BenefitOptionSelectionDrawer';
import styles from './BenefitOptionSelection.module.scss';

const cx = classNames.bind(styles);

interface Props {
  disabled: boolean;
  setMatchGroups: (arr: MatchGroupInfo[]) => void;
  setHasChangedStatus: (bool: boolean) => void;
  selectedProductBenefitOptions: ConfiguredBenefitOptions[];
  productConfiguredBenefitOptions: ConfiguredBenefitOptions[];
  setSelectedProductBenefitOptions: (arr: ConfiguredBenefitOptions[]) => void;
}

export const BenefitOptionSelection = ({
  disabled,
  setMatchGroups,
  setHasChangedStatus,
  productConfiguredBenefitOptions,
  selectedProductBenefitOptions,
  setSelectedProductBenefitOptions,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [drawerVisible, setDrawerVisible] = useState(false);

  const resetState = useCallback(() => {
    setDrawerVisible(false);
  }, []);

  const onDrawerSubmit = useCallback(
    (list: ConfiguredBenefitOptions[]) => {
      if (list.length === 0) {
        resetState();
        return;
      }
      setSelectedProductBenefitOptions(list);
    },
    [resetState, setSelectedProductBenefitOptions]
  );

  return (
    <div style={{ marginTop: '16px' }}>
      <div style={{ marginBottom: 8 }}>
        <span style={{ fontWeight: 600, marginRight: 8 }}>{t('Benefit Option Selection')}</span>
        <span>
          {' '}
          {disabled ? null : (
            <Icon
              type="edit"
              onClick={() => {
                if (selectedProductBenefitOptions.length > 0) {
                  Modal.confirm({
                    title: null,
                    content: t(
                      'Editing benefit factors will clear the existing uploaded benefit details，please confirm.'
                    ),
                    cancelText: t('Cancel'),
                    okText: t('Confirm'),
                    onOk: () => {
                      setMatchGroups([]);
                      setDrawerVisible(true);
                      setHasChangedStatus(true);
                    },
                    onCancel: () => {},
                  });
                } else {
                  setDrawerVisible(true);
                }
              }}
            />
          )}
        </span>
      </div>
      <div>
        {(selectedProductBenefitOptions || []).map(benefitOptionItem => (
          <div>
            <span>{benefitOptionItem.productCode}</span>
            <div className={cx('label-box')}>
              {benefitOptionItem?.benefitOptions
                ?.slice(0, 10)
                .map(childItemInfo => <div className={cx('disabled-label-item')}>{childItemInfo.optionName}</div>)}
              {benefitOptionItem?.benefitOptions?.length > 10 ? (
                <div className={cx('disabled-label-item')}>...</div>
              ) : null}
            </div>
          </div>
        ))}
      </div>
      {drawerVisible ? (
        <BenefitOptionSelectionDrawer
          visible={drawerVisible}
          onDrawerSubmit={onDrawerSubmit}
          closeDrawer={resetState}
          selectedProductBenefitOptions={selectedProductBenefitOptions}
          productConfiguredBenefitOptions={productConfiguredBenefitOptions}
        />
      ) : null}
    </div>
  );
};

export default BenefitOptionSelection;
