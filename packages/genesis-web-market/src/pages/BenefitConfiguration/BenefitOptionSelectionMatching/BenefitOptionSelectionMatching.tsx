import { Ref, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form, Tooltip, message } from 'antd';

import { Icon } from '@zhongan/nagrand-ui';

import { YesNoType } from 'genesis-web-service';
import {
  BenefitOptionSelectionMatchingInfo,
  ConfiguredBenefitOptions,
  MatchGroupInfo,
} from 'genesis-web-service/lib/market/benefitOptionSelection.interface';

import AgreementContainerLayout from '@market/components/AgreementContainerLayout';
import { NewMarketService } from '@market/services/market/market.service.new';
import { urlQuery } from '@market/util';

import BenefitOptionMapping from './components/BenefitOptionMapping';
import BenefitOptionSelection from './components/BenefitOptionSelection';

interface Props {
  disabled: boolean;
  refInstance: Ref<unknown>;
  nextPage: () => void;
  agreementCode: string;
  navList: {
    href: string;
    title: string;
    visible: boolean;
  }[];
}

export const BenefitOptionSelectionMatching = ({ disabled, refInstance, nextPage, navList, agreementCode }: Props) => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();
  const container = useRef<HTMLDivElement | null>(null);
  const packageId = urlQuery('packageId');

  const [productConfiguredBenefitOptions, setProductConfiguredBenefitOptions] = useState<ConfiguredBenefitOptions[]>(
    []
  );
  const [selectedProductBenefitOptions, setSelectedProductBenefitOptions] = useState<ConfiguredBenefitOptions[]>([]);
  const [matchGroups, setMatchGroups] = useState<MatchGroupInfo[]>([]);
  const [hasChangedStatus, setHasChangedStatus] = useState<boolean>(false);

  useEffect(() => {
    NewMarketService.PackageBenefitMatchService.queryBenefitMatchInfo(+packageId!).then(res => {
      setProductConfiguredBenefitOptions(res?.productConfiguredBenefitOptions || []);
      setSelectedProductBenefitOptions(res?.selectedProductBenefitOptions || []);
      setMatchGroups(res?.matchGroups || []);
      const navItem = navList?.find(item => item.href === agreementCode);
      if (navItem) {
        navItem.visible = !!res?.productConfiguredBenefitOptions?.length;
      }
    });
  }, [packageId]);

  useImperativeHandle(refInstance, () => ({
    getPackageBenefitMatchParam: () =>
      form.validateFields().then(() => {
        const param: BenefitOptionSelectionMatchingInfo = {
          packageId,
          isConfiguredBenefitOptionMatch: matchGroups.length > 0 ? YesNoType.Yes : YesNoType.No,
          productConfiguredBenefitOptions,
          selectedProductBenefitOptions,
          matchGroups,
        };
        NewMarketService.PackageBenefitMatchService.saveBenefitMatchInfo(param).then((res: { code: string }) => {
          if (res?.code) {
            return;
          }
          message.success(t('Save successfully'));
          nextPage();
        });
      }),
    hasChanged: () => hasChangedStatus,
  }));

  return (
    <div style={{ padding: '0 0 16px' }} ref={container}>
      {productConfiguredBenefitOptions.length > 0 ? (
        <Form form={form}>
          <AgreementContainerLayout
            title={
              <span>
                <span className="mr-1">{t('Benefit Option Selection & Matching')}</span>
                <Tooltip
                  title={t(
                    'If multiple versions are defined for the benefit option at the product level, multi-version matching is not currently supported.'
                  )}
                >
                  <Icon type="info-circle" />
                </Tooltip>
              </span>
            }
            agreementCode="benefitOptionSelectionMatching"
          >
            <BenefitOptionSelection
              disabled={disabled}
              setMatchGroups={setMatchGroups}
              setHasChangedStatus={setHasChangedStatus}
              productConfiguredBenefitOptions={productConfiguredBenefitOptions}
              selectedProductBenefitOptions={selectedProductBenefitOptions}
              setSelectedProductBenefitOptions={setSelectedProductBenefitOptions}
            />
            <BenefitOptionMapping
              disabled={disabled}
              matchGroups={matchGroups}
              selectedProductBenefitOptions={selectedProductBenefitOptions}
              setMatchGroups={setMatchGroups}
              setHasChangedStatus={setHasChangedStatus}
            />
          </AgreementContainerLayout>
        </Form>
      ) : null}
    </div>
  );
};

export default BenefitOptionSelectionMatching;
