import React, { Component, createRef } from 'react';
import { withTranslation } from 'react-i18next';
import { connect } from 'react-redux';
import { sortableContainer, sortableElement, sortableHandle } from 'react-sortable-hoc';

import { Button, Col, Form, InputNumber, Layout, Popconfirm, Radio, Row, Skeleton, Space, message } from 'antd';

import { arrayMoveImmutable } from 'array-move';
import classNames from 'classnames/bind';
import { cloneDeep, keyBy } from 'lodash-es';

import { Icon, Table } from '@zhongan/nagrand-ui';

import { IsVirtualType } from 'genesis-web-service';

import { CalculationRuleType } from '@market/common/enums';
import Collapse from '@market/components/Collapse';
import GeneralSelect from '@market/components/GeneralSelect';
import LabelWithTooltip from '@market/components/LabelWithTooltip';
import ProductAnchor from '@market/components/ProductAnchor';
import { selectPermissionCheckMap } from '@market/redux/selector';
import { NewMarketService } from '@market/services/market/market.service.new';
import { NewProductService } from '@market/services/product/product.service.new';
import { renderEnumName } from '@market/utils/enum';
import { formErrorHandler } from '@market/utils/formUtils';

import AgreementContainerLayout from '../../components/AgreementContainerLayout';
import { FMarketHeader } from '../../components/F-Market-Header';
import FMarketMenu from '../../components/F-Market-Menu';
import Input from '../../components/Input';
import { isILPProduct, urlQuery } from '../../util';
import { AnnualPremium } from './AnnualPremium';
import styles from './BenefitConfiguration.module.scss';
import BenefitOptionSelectionMatching from './BenefitOptionSelectionMatching';
import { PolicySA } from './PolicySA';
import { PremiumAdjustment } from './PremiumAdjustment';
import PremiumCurrencyFormItem from './PremiumCurrencyFormItem/PremiumCurrencyFormItem';
import AppointProductCalculationRule from './ProductCalculationMethodTable';
import { ProductSASetting } from './ProductSASetting/ProductSASetting';
import SACurrencyFormItem from './SACurrencyFormItem/SACurrencyFormItem';
import ClaimStackOrderSetting from './SMEStackValueSetting/ClaimStackOrderSetting';
import SMEStackValueSetting from './SMEStackValueSetting/SMEStackValueSetting';
import StackValueSetting from './StackValueSetting/index.ts';
import './index.scss';
import { EffectivePremiumCalculationMethodList, PremiumCalculationMethod } from './interface';
import {
  getLiabilityValues,
  getProductValues,
  handlePreDefineValues,
  queryPackageProductList,
  validateStackValueMatrix,
} from './sercice';

const cx = classNames.bind(styles);

const DragHandle = sortableHandle(() => <Icon type="drag" className=" cursor-pointer text-@primary-light text-base" />);
const SortableItem = sortableElement(props => <tr {...props} />);
const SortableContainer = sortableContainer(props => <tbody {...props} />);

const FormItem = Form.Item;
const { Sider, Content } = Layout;

class BenefitConfiguration extends Component {
  constructor(props) {
    super(props);
    this.benefitOptionSelectionMatchingRef = createRef();
    this.claimStackOrderSettingRef = createRef();

    this.state = {
      insuranceList: [],
      mode: 'edit',
      isShowEditButton: false,
      tableStateList: [],
      totalLiabilityList: [],
      isDriven: false,
      drawerVisible: false,
      productIndex: 0,
      liabilityIndex: 0,
      currentType: '',
      currentCode: '',
      drawerInitialValue: undefined,
      hasInterest: true,
      totalInterestListMap: {},
      productLiabilityInterestConfigedValues: {},
      skeletonVisible: true,
      calculationRuleInitialValue: undefined,
      useNewClaimStackStructure: undefined,
      liabilityLevelClaimStackTemplateRelationList: [],
      productLevelClaimStackTemplateRelationList: [],
      packageLevelClaimStackTemplateRelationList: [],
      navMap: {
        premiumAgreement: 'premiumAgreement',
        annualPremium: 'annualPremium',
        premiumAdjustment: 'premiumAdjustment',
        policySA: 'policySA',
        claimStackSetting: 'claimStackSetting',
        claimStackExecutionOrder: 'claimStackExecutionOrder',
        liabilityOrder: 'liabilityOrder',
        benefitOptionSelectionMatching: 'benefitOptionSelectionMatching',
      },
      navList: [],
    };
    this.formRef = createRef();
  }

  getNavTitle = key => this.state.navList?.find(nav => nav.href === key)?.title;

  onSortEnd = ({ oldIndex, newIndex }) => {
    const { totalLiabilityList } = this.state;
    if (oldIndex !== newIndex) {
      const newData = arrayMoveImmutable([].concat(totalLiabilityList), oldIndex, newIndex).filter(el => !!el);
      newData.forEach((item, index) => {
        item.orderNo = index + 1;
      });
      this.setState({ totalLiabilityList: newData });
    }
  };

  DraggableBodyRow = ({ className, style, ...restProps }) => {
    const { totalLiabilityList, mode, isShowEditButton } = this.state;
    const readOnly = mode === 'view' || !isShowEditButton;
    // function findIndex base on Table rowKey props and should always be a right array index
    const index = totalLiabilityList.findIndex(x => x.packageLiabilityId === restProps['data-row-key']);
    return <SortableItem index={index} disabled={readOnly} {...restProps} />;
  };

  queryMainProductBaseCurrency(productId) {
    NewProductService.ProductStructureService.queryMgmtProduct({
      productId,
      queryProductBasicInfo: true,
    }).then(res => {
      this.setState({
        mainProductBaseCurrency: res.productBasicInfo.baseCurrency,
      });
    });
  }

  async queryConfiguredData() {
    const {
      isDriven,
      hasInterest,
      productLiabilityInterestConfigedValues,
      productList: packageProductList,
    } = await queryPackageProductList(urlQuery('packageId'));
    this.setState({
      isDriven,
      hasInterest,
      productLiabilityInterestConfigedValues,
    });
    this.formRef.current?.setFieldsValue({
      unitType: packageProductList?.[0]?.unitType?.toString(),
    });
    const mainProductId = packageProductList.find(item => item.productTypeCode === 1).productId;
    this.queryMainProductBaseCurrency(mainProductId);
    if (hasInterest) {
      this.getAllInterests();
    }

    packageProductList?.forEach((packageProduct, index) => {
      packageProduct.isVirtualProduct = packageProduct.isVirtual === IsVirtualType.Yes;
    });
    const packageLiabilityList = (packageProductList || []).reduce((cur, next) => {
      const packageLiabilitys = next?.packageLiabilitys || [];
      return [...cur, ...packageLiabilitys];
    }, []);
    packageLiabilityList.sort((i, j) => i.orderNo - j.orderNo);
    packageLiabilityList.forEach((_item, _index) => {
      _item.orderNo = _index + 1;
    });

    this.setState(
      {
        insuranceList: packageProductList,
        totalLiabilityList: packageLiabilityList,
        skeletonVisible: false,
      },
      () => {
        const currencyIsSingle = this.unitTypeIsSpecifiedSAPremium();
        const mainCurrency = packageProductList?.[0]?.multiCurrency?.toString();
        const premiumCurrency = packageProductList?.[0]?.premiumCurrency?.toString();
        const pricingCurrency = packageProductList?.[0]?.pricingCurrency?.toString();
        // 没配multiCurrency就认为是第一次进来，默认选中sameAsSaCurrency
        const sameAsSaCurrency = packageProductList?.[0]?.sameAsSaCurrency ?? !packageProductList?.[0]?.multiCurrency;
        this.formRef.current?.setFieldsValue({
          baseCurrency: currencyIsSingle ? mainCurrency : mainCurrency?.split(','),
          premiumCurrency: currencyIsSingle ? premiumCurrency : premiumCurrency?.split(','),
          sameAsSaCurrency,
          pricingCurrency,
        });
        setTimeout(() => {
          this.sumProductPremium();
        }, 300);
      }
    );
  }

  queryPackageStructure() {
    const packageId = urlQuery('packageId');
    const { userInfo, hasEditAuth, isSuperUser } = this.props;

    return NewMarketService.MarketStructureMgmtService.queryPackageStructure({
      packageId,
      queryBasic: true,
      queryPackageBenefitConfiguration: true,
    }).then(res => {
      const basicInfo = res.packageBasicInfo;
      const packageBenefit = res.packageBenefit;

      this.setState({
        packageCode: basicInfo.packageCode,
        packageId: basicInfo.packageId,
        packageName: basicInfo.packageName,
        calculationRule: packageBenefit.calculationRule,
        isShowEditButton: (basicInfo.creator === `${userInfo.userId}` && hasEditAuth) || isSuperUser,
        useNewClaimStackStructure: res.useNewClaimStackStructure,
        liabilityLevelClaimStackTemplateRelationList: res.liabilityLevelClaimStackTemplateRelationList,
        productLevelClaimStackTemplateRelationList: res.productLevelClaimStackTemplateRelationList,
        packageLevelClaimStackTemplateRelationList: res.packageLevelClaimStackTemplateRelationList,
      });
      this.formRef.current?.setFieldsValue({
        [`calculationRule`]: packageBenefit.calculationRule?.toString(),
        accumulateProducts: packageBenefit.accumulateProducts,
        manualAnnualPremium: packageBenefit.manualAnnualPremium,
        netPremiumAdjustmentAgreement: packageBenefit.netPremiumAdjustmentAgreement,
      });
    });
  }

  componentDidMount() {
    const { state } = this.props.location;
    const mode = state && state.mode;
    const packageId = urlQuery('packageId');
    const { navMap } = this.state;
    const { t } = this.props;

    if (!packageId) {
      this.props.history.push('/market/package/search');
      return;
    }
    this.setState({
      packageId,
      mode: mode || 'edit',
      navList: [
        {
          href: navMap.premiumAgreement,
          title: t('Premium Agreement'),
          visible: true,
        },
        {
          href: navMap.annualPremium,
          title: t('Annual Premium'),
          visible: true,
        },
        {
          href: navMap.premiumAdjustment,
          title: t('Premium Adjustment'),
          visible: true,
        },
        {
          href: navMap.policySA,
          title: t('Policy SA'),
          visible: true,
        },
        {
          href: navMap.claimStackSetting,
          title: t('Claim Stack Setting'),
          visible: true,
        },
        {
          href: navMap.claimStackExecutionOrder,
          title: t('Claim Stack Execution Order'),
          visible: true,
        },
        {
          href: navMap.liabilityOrder,
          title: t('Liability Order'),
          visible: true,
        },
        {
          href: navMap.benefitOptionSelectionMatching,
          title: t('Benefit Option Selection & Matching'),
          visible: true,
        },
      ],
    });

    this.queryPackageStructure().then(res => {
      this.queryConfiguredData();
    });
  }

  getCurrentUnitType(productUnitType) {
    const calculationRule = this.formRef.current?.getFieldValue('calculationRule');
    const formUnitType = this.formRef.current?.getFieldValue('unitType');
    const isDefineCalculationMethodRorEachProduct =
      calculationRule === CalculationRuleType.DefineCalculationMethodForEachProduct;
    const currentUnitType = isDefineCalculationMethodRorEachProduct
      ? productUnitType
        ? `${productUnitType}`
        : undefined
      : formUnitType;
    return currentUnitType;
  }

  goToNextPage = () => {
    this.props.navigate(`/market/package/agreement?packageId=${this.state.packageId}`, {
      state: {
        mode: this.state.mode,
      },
    });
  };

  saveBenefitOptionSelectionMatching = async isNext => {
    const hasChanged = this.benefitOptionSelectionMatchingRef?.current?.hasChanged();
    if (this.benefitOptionSelectionMatchingRef?.current && hasChanged) {
      this.benefitOptionSelectionMatchingRef?.current?.getPackageBenefitMatchParam();
    } else {
      const valid = await validateStackValueMatrix(this.state.packageId, this.state.mode);
      if (!valid) {
        return;
      }
      await this.queryPackageStructure();
      message.success(this.props.t('Save successfully'));
      if (isNext) {
        this.goToNextPage();
      }
    }
  };

  onSubmit = isNext => {
    if (this.state.tableStateList.includes('Editing')) {
      message.warning(this.props.t('Changes have not been saved.'));
      return;
    }
    const { productLiabilityInterestConfigedValues, hasInterest, totalLiabilityList } = this.state;
    this.formRef.current
      ?.validateFields()
      .then(values => {
        const unitType = this.formRef.current?.getFieldValue('unitType');
        // Collpase 会不渲染FormItem，所以需要通过getFieldsValue获取insuranceList，values中无法获取
        const insuranceListInForm = this.formRef.current?.getFieldValue('insuranceList');
        const insuranceList = cloneDeep(this.state.insuranceList);
        const calculationRule = this.formRef.current?.getFieldValue('calculationRule');

        const formInsuranceDataMap = insuranceListInForm
          ? keyBy(
              insuranceListInForm.filter(item => !!item),
              'packageProductId'
            )
          : {};
        const isDefineCalculationMethodRorEachProduct =
          calculationRule === CalculationRuleType.DefineCalculationMethodForEachProduct;
        const liabilityMap = keyBy(totalLiabilityList, 'packageLiabilityId');
        // Check orderNo for duplicate values
        for (let index = 0; index < insuranceList.length; index++) {
          const item = insuranceList[index];
          // 表单数据填充进insuranceList，入参通过insuranceList处理
          if (formInsuranceDataMap?.[item.id]) {
            // 单条配置Calculation Method时使用list中的unitType，否则使用表单数据
            item.unitType = isDefineCalculationMethodRorEachProduct ? item.unitType : unitType;
            item.elementValue = formInsuranceDataMap[item.id]?.elementValue;
            item.productPremium = formInsuranceDataMap[item.id]?.productPremium;
            item.liabilityRemark = formInsuranceDataMap[item.id]?.liabilityRemark;
            item.packageLiabilitys = formInsuranceDataMap[item.id]?.packageLiabilitys?.map(liability => ({
              ...liability,
              orderNo: liabilityMap[liability.packageLiabilityId].orderNo,
            }));
            item.liabilityLimitAmount = formInsuranceDataMap[item.id]?.liabilityLimitAmount;
            item.productSchemaCodePreDefineValues = formInsuranceDataMap[item.id]?.productSchemaCodePreDefineValues;
          }

          const orderNos = item.packageLiabilitys?.map(child => child.orderNo);
          if (orderNos && new Set(orderNos).size !== orderNos.length) {
            message.warning(this.props.t('Exists duplicate liability orders'), 5);
            return;
          }
        }

        // 将interest数据从state中拿出来
        if (
          hasInterest &&
          insuranceList.find(item => `${item.unitType}` === PremiumCalculationMethod.CalculatePremium)
        ) {
          insuranceList.forEach((product, productIndex) => {
            product.packageLiabilitys?.forEach((liability, liabilityIndex) => {
              liability.liabilityInterestBenefitList =
                this.state.insuranceList[productIndex].packageLiabilitys[liabilityIndex]
                  ?.liabilityInterestBenefitList || [];
              liability.liabilityInterestBenefitList = liability.liabilityInterestBenefitList
                ?.filter(liabilityInterest => {
                  const configedInterestValue =
                    productLiabilityInterestConfigedValues[productIndex]?.[liabilityIndex]?.[
                      liabilityInterest.interestId
                    ];
                  return !!configedInterestValue;
                })
                .map(liabilityInterest => {
                  const configedInterestValue =
                    productLiabilityInterestConfigedValues[productIndex]?.[liabilityIndex]?.[
                      liabilityInterest.interestId
                    ];
                  liabilityInterest.interestSchemaCodePreDefineValues = [configedInterestValue];
                  return liabilityInterest;
                });
            });
          });
        }

        const currencyIsSingle = this.unitTypeIsSpecifiedSAPremium();
        const { accumulateProducts, manualAnnualPremium, netPremiumAdjustmentAgreement } =
          this.formRef.current.getFieldsValue() ?? {};

        const param = {
          calculationRule,
          packageId: this.state.packageId,
          packageProductsDTO: insuranceList.map(product => {
            const currentUnitType = isDefineCalculationMethodRorEachProduct
              ? product.unitType
                ? `${product.unitType}`
                : undefined
              : unitType;
            const obj = {
              securityAmount:
                currentUnitType === PremiumCalculationMethod.CalculatePremium ||
                currentUnitType === PremiumCalculationMethod.SpecifiedSAPremium
                  ? product.elementValue
                  : '', // SA or Unit or SA&Premium
              premium: currentUnitType === PremiumCalculationMethod.CalculateSA ? product.elementValue : '', // Premium
              // benefitLevel: unitType === '4' ? product.elementValue : '', // Benefit level
              // 现在选不到Benefit level了
              benefitLevel: '',
              unitType: currentUnitType,
              packageProductId: product.packageProductId || product.id,
              packageLiability: product.packageLiabilitys || [],
              // 当unitType为6时，values.baseCurrency类型为数组，其他type为字符串
              multiCurrency: currencyIsSingle
                ? Array.isArray(values.baseCurrency)
                  ? values.baseCurrency[0]
                  : values.baseCurrency
                : values.baseCurrency?.toString(),
              orderNo: product.orderNo,
              productSchemaCodePreDefineValues: product.productSchemaCodePreDefineValues || [],
              sameAsSaCurrency: values.sameAsSaCurrency ?? false, // 没填提交传false，用来区分是否已经配置过
              pricingCurrency:
                currentUnitType === PremiumCalculationMethod.CalculatePremium ? values.pricingCurrency : '',
            };

            if (obj.sameAsSaCurrency) {
              obj.premiumCurrency = obj.multiCurrency;
            } else if (this.isSupportPremiumCurrency(currentUnitType) && !isDefineCalculationMethodRorEachProduct) {
              obj.premiumCurrency = currencyIsSingle
                ? Array.isArray(values.premiumCurrency)
                  ? values.premiumCurrency[0]
                  : values.premiumCurrency
                : values.premiumCurrency.toString();
            } else {
              // 需要设置空字符串才能把旧配置清掉
              obj.premiumCurrency = '';
            }

            if (currentUnitType === PremiumCalculationMethod.CalculateSA) {
              // 选择了CalculateSA，修改liabilities里的字段传值
              obj.packageLiability.forEach(liability => {
                liability.liabilityPremium = liability.liabilityAmount;
                liability.liabilityPremiumRemark = liability.liabilityAmountRemark;
                liability.liabilityInterestBenefitList = [];
                liability.liabilitySchemaCodePreDefineValues = (
                  liability.liabilitySchemaCodePreDefineValues || []
                ).filter(liabilityValueItem => liabilityValueItem.code === 'periodStandardPremium');
                liability.liabilityAmount = '';
                liability.liabilityAmountRemark = '';
              });
              obj.productSchemaCodePreDefineValues = obj.productSchemaCodePreDefineValues.filter(
                productValueItem => productValueItem.code === 'periodStandardPremium'
              );
            } else if (currentUnitType === PremiumCalculationMethod.CalculatePremium) {
              // 选择了CalculatePremium，修改liabilities里的字段传值
              obj.packageLiability.forEach(liability => {
                liability.liabilitySchemaCodePreDefineValues = (
                  liability.liabilitySchemaCodePreDefineValues || []
                ).filter(liabilityValueItem => liabilityValueItem.code === 'sumInsured');
                liability.liabilityAmount = '';
                liability.liabilityPremiumRemark = '';
              });
              obj.productSchemaCodePreDefineValues = obj.productSchemaCodePreDefineValues.filter(
                productValueItem => productValueItem.code === 'sumInsured'
              );
            } else if (currentUnitType === PremiumCalculationMethod.SpecifiedSAPremium) {
              // 选择了SpecifiedSAPremium，修改liabilities里的字段传值
              obj.packageLiability.forEach(liability => {
                liability.liabilityPremiumRemark = '';
                liability.liabilitySchemaCodePreDefineValues = [];
                liability.liabilityInterestBenefitList = [];
              });
              obj.productSchemaCodePreDefineValues = [];
            } else if (currentUnitType === PremiumCalculationMethod.UserInput) {
              // 在别的unitType情况下配置的数据需要删除，user input不需要配置这些数据
              obj.packageLiability.forEach(liability => {
                liability.liabilityPremium = '';
                liability.liabilityPremiumRemark = '';
                liability.liabilityAmount = '';
                liability.liabilityAmountRemark = '';
                liability.liabilityRemark = '';
                liability.liabilityLimitAmount = '';
                liability.liabilityLimitAmountRemark = '';
                liability.liabilitySchemaCodePreDefineValues = [];
                liability.liabilityInterestBenefitList = [];
              });
              obj.productSchemaCodePreDefineValues = [];
            }
            return obj;
          }),
          accumulateProducts,
          manualAnnualPremium,
          netPremiumAdjustmentAgreement,
        };
        NewMarketService.PackageProductInsuranceMgmtService.updatePackageProductInsuranceList(param).then(res => {
          if (res.success) {
            this.claimStackOrderSettingRef?.current?.saveClaimStackOrder();
            this.saveBenefitOptionSelectionMatching(isNext);
          }
        });
      })
      .catch(formErrorHandler(this.formRef.current))
      .catch(error => {
        console.error(error);
      });
  };

  backToLastHistory = () => {
    this.props.navigate(`/market/package/basic?packageId=${this.state.packageId}`, {
      state: {
        mode: this.state.mode || 'edit',
      },
    });
  };

  onEdit = () => {
    if (this.state.mode !== 'view') {
      return;
    }
    this.setState({ mode: 'edit' });
  };

  renderTableTitle = (unitType, dataIndex) => {
    // 表格第四列
    if (dataIndex === 'liabilityAmount') {
      if (
        unitType === PremiumCalculationMethod.CalculatePremium ||
        unitType === PremiumCalculationMethod.SpecifiedSAPremium
      ) {
        return this.props.t('Liability SA');
      }
      if (unitType === PremiumCalculationMethod.CalculateSA) {
        return this.props.t('liabilityPremium');
      }
      return this.props.t('Liability Coverage / Premium');
    }

    // 表格第五列
    if (dataIndex === 'liabilityAmountRemark') {
      if (
        unitType === PremiumCalculationMethod.CalculatePremium ||
        unitType === PremiumCalculationMethod.SpecifiedSAPremium
      ) {
        return this.props.t('Liability SA Remark');
      }
      if (unitType === PremiumCalculationMethod.CalculateSA) {
        return this.props.t('LiabilityPremiumRemarks');
      }
      return this.props.t('Liability Coverage / Premium Remarks');
    }

    // 表格第六列
    if (dataIndex === 'liabilityLimitAmount') {
      if (
        unitType === PremiumCalculationMethod.CalculatePremium ||
        unitType === PremiumCalculationMethod.CalculateSA ||
        unitType === PremiumCalculationMethod.SpecifiedSAPremium
      ) {
        return this.props.t('Liability Limit');
      }
      return this.props.t('Amount Limit');
    }

    // 表格第七列
    if (dataIndex === 'liabilityLimitAmountRemark') {
      if (
        unitType === PremiumCalculationMethod.CalculatePremium ||
        unitType === PremiumCalculationMethod.SpecifiedSAPremium
      ) {
        return this.props.t('Liability Limit Remark');
      }
      return this.props.t('Amount Limit Remark');
    }
  };

  isDisabled = (readOnly, isVirtualProduct, type, unitType) => {
    if (type === 'liability') {
      return readOnly || isVirtualProduct;
    }
    if (type === 'product') {
      return readOnly || isVirtualProduct || !unitType;
    }
  };

  renderInterestRemoveIcon = (productIndex, liabilityIndex, interestId, disabled) => {
    const { productLiabilityInterestConfigedValues } = this.state;

    return (
      <span style={{ marginLeft: 15 }}>
        <Popconfirm
          title={this.props.t('Are you sure to delete this record?')}
          disabled={disabled}
          onConfirm={() => {
            productLiabilityInterestConfigedValues[productIndex][liabilityIndex][interestId] = undefined;

            this.setState({
              productLiabilityInterestConfigedValues: {
                ...productLiabilityInterestConfigedValues,
              },
            });
          }}
          okText={this.props.t('yes')}
          cancelText={this.props.t('no')}
        >
          <Icon type="delete" disabled={disabled} />
        </Popconfirm>
      </span>
    );
  };

  renderLiabilityRemoveIcon = (i_index, record_index, disabled) => {
    const { insuranceList } = this.state;
    const { setFieldValue } = this.formRef.current;
    return (
      <span style={{ marginLeft: 15 }}>
        <Popconfirm
          title={this.props.t('Are you sure to delete this record?')}
          disabled={disabled}
          onConfirm={() => {
            insuranceList[i_index].packageLiabilitys[record_index].liabilityAmount = '';
            setFieldValue(
              ['insuranceList', i_index, 'packageLiabilitys', record_index, 'liabilitySchemaCodePreDefineValues'],
              []
            );
            insuranceList[i_index].packageLiabilitys[record_index].liabilitySchemaCodePreDefineValues = [];
          }}
          okText={this.props.t('yes')}
          cancelText={this.props.t('no')}
        >
          <Icon type="delete" disabled={disabled} />
        </Popconfirm>
      </span>
    );
  };

  renderProductRemoveIcon = (i_index, disabled) => {
    const { setFieldValue } = this.formRef.current;
    const { insuranceList } = this.state;
    return (
      <span style={{ marginLeft: 15 }}>
        <Popconfirm
          title={this.props.t('Are you sure to delete this record?')}
          disabled={disabled}
          onConfirm={() => {
            const tempProductList = cloneDeep(insuranceList);
            tempProductList[i_index].productSchemaCodePreDefineValues = [];
            tempProductList[i_index].elementValue = '';
            this.setState(
              {
                insuranceList: tempProductList,
              },
              () => {
                setTimeout(() => {
                  this.sumProductPremium();
                }, 300);
              }
            );
            setFieldValue(['insuranceList', i_index, 'productSchemaCodePreDefineValues'], []);
          }}
          okText={this.props.t('yes')}
          cancelText={this.props.t('no')}
        >
          <Icon type="delete" disabled={disabled} />
        </Popconfirm>
      </span>
    );
  };

  renewInsuranceListInfo = (id, key, value) => {
    const { insuranceList } = this.state;
    const { setFieldsValue } = this.formRef.current;
    const tempProductList = cloneDeep(insuranceList);
    const product_index = tempProductList.findIndex(item => item.id === id);

    tempProductList[product_index][key] = value;
    tempProductList[product_index].packageLiabilitys.forEach(insuranceItemLiablity => {
      insuranceItemLiablity.liabilitySchemaCodePreDefineValues = [];
      insuranceItemLiablity.liabilityRemark = '';
      insuranceItemLiablity.liabilityPremium = '';
    });
    this.setState({
      insuranceList: tempProductList,
    });
    setFieldsValue({
      [['insuranceList', product_index, 'elementValue']]: '',
      [['insuranceList', product_index, 'productSchemaCodePreDefineValues']]: [],
    });
  };

  getColumns = (i, i_index, readOnly, unitType) => {
    const disabled = this.isDisabled(readOnly, i.isVirtualProduct, 'liability', unitType);
    const columns = [
      {
        title: this.props.t('dutyText'),
        dataIndex: 'liabilityName',
        width: 240,
      },
      {
        title: () => {
          return <span>{this.renderTableTitle(unitType, 'liabilityAmount')}</span>;
        },
        dataIndex: 'liabilityAmount',
        width: 300,
        render: (text, record, record_index) => {
          if (!record.enableSumInsured && unitType !== PremiumCalculationMethod.CalculateSA) {
            return this.props.t('- -');
          }
          if (unitType === PremiumCalculationMethod.SpecifiedSAPremium) {
            return (
              <FormItem
                name={['insuranceList', i_index, 'packageLiabilitys', record_index, 'liabilityAmount']}
                initialValue={text}
                preserve={true}
                rules={[
                  {
                    message: this.props.t('Please input'),
                  },
                  {
                    validator: (rule, inputValue, callback) => {
                      const regx = /^(0|[1-9][0-9]*)(\.\d+)?$/g;
                      try {
                        if (inputValue && !regx.test(inputValue)) {
                          callback(this.props.t('Please enter numeric value'));
                        }
                        callback();
                      } catch (err) {
                        callback(err);
                      }
                    },
                  },
                ]}
                noStyle
              >
                <Input disabled={disabled} />
              </FormItem>
            );
          }
          return (
            <FormItem
              name={['insuranceList', i_index, 'packageLiabilitys', record_index, 'liabilitySchemaCodePreDefineValues']}
              initialValue={record.liabilitySchemaCodePreDefineValues}
              preserve={true}
              noStyle
            >
              <span>
                <span>
                  {getLiabilityValues(
                    record,
                    unitType === PremiumCalculationMethod.CalculateSA ? 'periodStandardPremium' : 'sumInsured'
                  )}
                </span>
                <span style={{ marginLeft: 15 }}>
                  <Icon
                    type="edit"
                    disabled={disabled}
                    onClick={() => {
                      if (!disabled) {
                        this.onOpenDrawer(
                          i_index,
                          'liability',
                          unitType === PremiumCalculationMethod.CalculateSA ? 'periodStandardPremium' : 'sumInsured',
                          record_index
                        );
                      }
                    }}
                  />
                </span>
                {getLiabilityValues(
                  record,
                  unitType === PremiumCalculationMethod.CalculateSA ? 'periodStandardPremium' : 'sumInsured'
                ) !== this.props.t('Unset')
                  ? this.renderLiabilityRemoveIcon(i_index, record_index, disabled)
                  : null}
              </span>
            </FormItem>
          );
        },
      },
      {
        title: () => {
          return <span>{this.renderTableTitle(unitType, 'liabilityLimitAmount')}</span>;
        },
        dataIndex: 'liabilityLimitAmount',
        width: 300,
        render: (text, record, record_index) => {
          return (
            <React.Fragment>
              <FormItem
                name={['insuranceList', i_index, 'packageLiabilitys', record_index, 'liabilityLimitAmount']}
                initialValue={text && parseFloat(text)}
                preserve={true}
                noStyle
              >
                <InputNumber
                  style={{ width: 240 }}
                  placeholder={this.props.t('Please input')}
                  disabled={disabled}
                  max={Infinity}
                  min={-Infinity}
                />
              </FormItem>
              <FormItem
                name={['insuranceList', i_index, 'packageLiabilitys', record_index, 'orderNo']}
                initialValue={record.orderNo}
                preserve={true}
                noStyle
              >
                <Input type="hidden" />
              </FormItem>
            </React.Fragment>
          );
        },
      },
      {
        title: this.props.t('Liability Remark'),
        dataIndex: 'liabilityRemark',
        width: 300,
        render: (text, record, record_index) => {
          return (
            <FormItem
              name={['insuranceList', i_index, 'packageLiabilitys', record_index, 'liabilityRemark']}
              initialValue={text}
              preserve={true}
              noStyle
            >
              <Input disabled={disabled} />
            </FormItem>
          );
        },
      },

      {
        title: () => {
          return <span>{this.renderTableTitle(unitType, 'liabilityAmountRemark')}</span>;
        },
        dataIndex: 'liabilityAmountRemark',
        width: 300,
        render: (text, record, record_index) => {
          return (
            <div>
              <FormItem
                name={['insuranceList', i_index, 'packageLiabilitys', record_index, 'liabilityAmountRemark']}
                initialValue={text}
                preserve={true}
                noStyle
              >
                <Input disabled={disabled} />
              </FormItem>
              <FormItem
                name={['insuranceList', i_index, 'packageLiabilitys', record_index, 'packageLiabilityId']}
                style={{ display: 'none' }}
                initialValue={record.packageLiabilityId}
                preserve={true}
              >
                <Input type="hidden" />
              </FormItem>
            </div>
          );
        },
      },
      {
        title: () => {
          return <span>{this.renderTableTitle(unitType, 'liabilityLimitAmountRemark')}</span>;
        },
        dataIndex: 'liabilityLimitAmountRemark',
        width: 300,
        render: (text, record, record_index) => {
          return (
            <FormItem
              name={['insuranceList', i_index, 'packageLiabilitys', record_index, 'liabilityLimitAmountRemark']}
              preserve={true}
              initialValue={text}
              noStyle
            >
              <Input disabled={disabled} />
            </FormItem>
          );
        },
      },
    ];
    if (unitType === PremiumCalculationMethod.SpecifiedSAPremium) {
      columns.splice(3, 0, {
        title: () => {
          return <span>{this.props.t('liabilityPremium')}</span>;
        },
        dataIndex: 'liabilityPremium',
        width: 300,
        render: (text, record, record_index) => {
          return (
            <FormItem
              name={['insuranceList', i_index, 'packageLiabilitys', record_index, 'liabilityPremium']}
              initialValue={text}
              rules={[
                {
                  message: this.props.t('Please input'),
                },
                {
                  validator: (rule, inputValue, callback) => {
                    const regx = /^(0|[1-9][0-9]*)(\.\d+)?$/g;
                    try {
                      if (inputValue && !regx.test(inputValue)) {
                        callback(this.props.t('Please enter numeric value'));
                      }
                      callback();
                    } catch (err) {
                      callback(err);
                    }
                  },
                },
              ]}
              noStyle
              preserve
            >
              <Input
                onChange={() => {
                  setTimeout(() => {
                    this.sumProductPremium();
                  }, 30);
                }}
                disabled={disabled}
              />
            </FormItem>
          );
        },
      });
    }
    return columns;
  };

  checkInsuranceList = (formInsuranceList = [], insuranceList) => {
    // 获取到有数据可以求和的数据源
    if (formInsuranceList.filter(formArrItem => !formArrItem.packageLiabilitys).length > 0) {
      return insuranceList;
    } else {
      return formInsuranceList;
    }
  };

  sumProductPremium = () => {
    const { setFieldValue, getFieldValue } = this.formRef.current;
    const { insuranceList } = this.state;
    let liabilitiesList = [];
    const tempInsuranceList = this.checkInsuranceList(getFieldValue(`insuranceList`), insuranceList);
    tempInsuranceList.forEach((item, index) => {
      if (Array.isArray(item.packageLiabilitys)) {
        liabilitiesList = item.packageLiabilitys;
      }
      const liabilityPremiumList = [];
      if (Array.isArray(liabilitiesList) && liabilitiesList.length > 0) {
        liabilitiesList.forEach(liabilitiesItem => {
          liabilityPremiumList.push(liabilitiesItem.liabilityPremium);
        });
      } else {
        return;
      }
      if (liabilityPremiumList.every(item => !item)) {
        setFieldValue(['insuranceList', index, 'productPremium'], undefined);
      } else {
        let sum = 0;
        liabilityPremiumList.forEach(num => {
          sum += Number(num);
        });
        setFieldValue(['insuranceList', index, 'productPremium'], sum);
      }
    });
  };

  onCancelChange = () => {
    this.setState({
      drawerVisible: false,
    });
  };

  onSubmitDrawerInfo = preDefineValues => {
    //SA与Premium自定义配置后的数据处理
    const { setFieldValue } = this.formRef.current;
    const {
      currentType,
      insuranceList,
      productIndex,
      liabilityIndex,
      interestId,
      productLiabilityInterestConfigedValues,
    } = this.state;
    const tempInsuranceList = cloneDeep(insuranceList);

    if (currentType === 'product') {
      const tempProductItem = tempInsuranceList[productIndex];
      if (
        Array.isArray(tempProductItem.productSchemaCodePreDefineValues) &&
        tempProductItem.productSchemaCodePreDefineValues.length > 0
      ) {
        const valueIndex = tempProductItem.productSchemaCodePreDefineValues.findIndex(
          item => item.code === preDefineValues.code
        );
        if (valueIndex > -1) {
          tempProductItem.productSchemaCodePreDefineValues[valueIndex] = preDefineValues;
        } else {
          tempProductItem.productSchemaCodePreDefineValues.push(preDefineValues);
        }
      } else tempProductItem.productSchemaCodePreDefineValues = [preDefineValues];
      setFieldValue(
        ['insuranceList', productIndex, 'productSchemaCodePreDefineValues'],
        tempProductItem.productSchemaCodePreDefineValues
      );
    } else if (currentType === 'liability') {
      const tempLiabilityItem = tempInsuranceList[productIndex].packageLiabilitys[liabilityIndex];
      if (
        Array.isArray(tempLiabilityItem.liabilitySchemaCodePreDefineValues) &&
        tempLiabilityItem.liabilitySchemaCodePreDefineValues.length > 0
      ) {
        const valueIndex = tempLiabilityItem.liabilitySchemaCodePreDefineValues.findIndex(
          item => item.code === preDefineValues.code
        );
        if (valueIndex > -1) {
          tempLiabilityItem.liabilitySchemaCodePreDefineValues[valueIndex] = preDefineValues;
        } else {
          tempLiabilityItem.liabilitySchemaCodePreDefineValues.push(preDefineValues);
        }
      } else {
        tempLiabilityItem.liabilitySchemaCodePreDefineValues = [preDefineValues];
      }
      setFieldValue(
        ['insuranceList', productIndex, 'packageLiabilitys', liabilityIndex, 'liabilitySchemaCodePreDefineValues'],
        tempLiabilityItem.liabilitySchemaCodePreDefineValues
      );
    } else if (currentType === 'interest') {
      const tempConfigedValues = cloneDeep(productLiabilityInterestConfigedValues);
      if (!tempConfigedValues[productIndex]) {
        tempConfigedValues[productIndex] = {};
        tempConfigedValues[productIndex][liabilityIndex] = {};
      } else if (!tempConfigedValues[productIndex][liabilityIndex]) {
        tempConfigedValues[productIndex][liabilityIndex] = {};
      }
      tempConfigedValues[productIndex][liabilityIndex][interestId] = preDefineValues;

      this.setState({
        productLiabilityInterestConfigedValues: { ...tempConfigedValues },
      });
    }
    this.setState({
      insuranceList: tempInsuranceList,
    });
  };

  onOpenDrawer = (productIndex, currentType, currentCode, liabilityIndex, interestId) => {
    const { getFieldValue } = this.formRef.current;
    const { productLiabilityInterestConfigedValues } = this.state;
    let defineValues;
    if (currentType === 'product') {
      defineValues = getFieldValue(['insuranceList', productIndex, 'productSchemaCodePreDefineValues']);
    } else if (currentType === 'liability') {
      defineValues =
        this.state.insuranceList[productIndex].packageLiabilitys[liabilityIndex].liabilitySchemaCodePreDefineValues;
    } else if (currentType === 'interest') {
      const configuredValue = productLiabilityInterestConfigedValues?.[productIndex]?.[liabilityIndex]?.[interestId];
      defineValues = configuredValue ? [configuredValue] : undefined;
    }

    const initialValue = defineValues ? defineValues.find(item => item.code === currentCode) : undefined;
    this.setState({
      drawerVisible: true,
      productIndex,
      currentType,
      currentCode,
      liabilityIndex: liabilityIndex || 0,
      interestId,
      drawerInitialValue: initialValue,
    });
  };

  getAllInterests = () => {
    return NewProductService.InsurableInterestMgmtService.queryAllInsurableInterests().then(interests => {
      this.setState({
        totalInterestListMap: keyBy(interests, 'id'),
      });
    });
  };

  expandedRowRender = (product, product_index, readOnly) => (productLiability, productLiabilityIndex) => {
    const { t, enums, mode } = this.props;
    const { totalInterestListMap, productLiabilityInterestConfigedValues } = this.state;

    const disabled = this.isDisabled(
      readOnly,
      product.isVirtualProduct,
      'liability',
      PremiumCalculationMethod.CalculatePremium
    );
    const columns = [
      {
        title: t('Insurable Interest'),
        dataIndex: 'interestId',
        width: '240px',
        render: (id, interest) => {
          return totalInterestListMap?.[interest.interestId]?.interestName;
        },
      },
      {
        title: t('Insurable Interest SA'),
        render: (text, record) => {
          const interestSchemaCodePreDefineValues =
            productLiabilityInterestConfigedValues?.[product_index]?.[productLiabilityIndex]?.[record.interestId];

          return (
            <span>
              {interestSchemaCodePreDefineValues ? (
                <span>{handlePreDefineValues(interestSchemaCodePreDefineValues)}</span>
              ) : (
                t('Unset')
              )}
              <span style={{ marginLeft: 15 }}>
                <Icon
                  type="edit"
                  disabled={disabled}
                  onClick={() => {
                    if (!disabled) {
                      this.onOpenDrawer(
                        product_index,
                        'interest',
                        'sumInsured',
                        productLiabilityIndex,
                        record.interestId
                      );
                    }
                  }}
                />
              </span>
              {interestSchemaCodePreDefineValues
                ? this.renderInterestRemoveIcon(product_index, productLiabilityIndex, record.interestId, disabled)
                : null}
            </span>
          );
        },
      },
    ];
    const interestList = productLiability.liabilityInterestBenefitList || [];

    if (interestList.length === 0) {
      return null;
    }
    return (
      <Table rowKey={interest => interest.interestId} columns={columns} dataSource={interestList} pagination={false} />
    );
  };

  renderBenefitConfigForm(product, product_index, unitType) {
    const { getFieldValue } = this.formRef.current;
    const { mode, isShowEditButton, hasInterest } = this.state;

    const isUnifiedCalculationMethodForAllProducts =
      getFieldValue('calculationRule') === CalculationRuleType.UnifiedCalculationMethodForAllProducts;
    const readOnly = mode === 'view' || !isShowEditButton;
    const disabled = this.isDisabled(readOnly, product.isVirtualProduct, 'product', unitType);
    return (
      <div className="benefit-config-form">
        <div>
          {unitType === PremiumCalculationMethod.SpecifiedSAPremium ? (
            <Row>
              <Col span={8}>
                <FormItem
                  label={this.props.t('Product SA')}
                  colon={false}
                  name={['insuranceList', product_index, 'elementValue']}
                  initialValue={product.isVirtualProduct ? '0' : product.elementValue}
                  preserve={true}
                >
                  <InputNumber
                    disabled={readOnly || product.isVirtualProduct || !unitType}
                    style={{ width: 240 }}
                    min={0}
                    placeholder={this.props.t('Please input')}
                  />
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem
                  label={this.props.t('Product Premium')}
                  colon={false}
                  preserve
                  name={['insuranceList', product_index, 'productPremium']}
                  initialValue={product.productPremium > 0 ? product.productPremium : undefined}
                >
                  <Input disabled />
                </FormItem>
              </Col>
            </Row>
          ) : null}
          {unitType === PremiumCalculationMethod.CalculatePremium ||
          unitType === PremiumCalculationMethod.CalculateSA ? (
            <FormItem
              noStyle
              colon={false}
              name={['insuranceList', product_index, 'productSchemaCodePreDefineValues']}
              initialValue={product.productSchemaCodePreDefineValues}
              preserve={true}
            >
              <span className="flex mt-6 mb-4">
                <span className="font-bold">{`${
                  unitType === PremiumCalculationMethod.CalculateSA
                    ? this.props.t('Product Premium')
                    : this.props.t('Product SA')
                }:`}</span>
                <span className="flex items-center">
                  <span className="font-normal text-@label ml-[10px]">
                    {getProductValues(
                      {
                        productSchemaCodePreDefineValues: getFieldValue([
                          'insuranceList',
                          product_index,
                          'productSchemaCodePreDefineValues',
                        ]),
                        isVirtualProduct: product.isVirtualProduct,
                        elementValue: getFieldValue(['insuranceList', product_index, 'elementValue']),
                      },
                      unitType === PremiumCalculationMethod.CalculateSA ? 'periodStandardPremium' : 'sumInsured'
                    )}
                  </span>
                  <span className="mx-4 font-normal text-@label inline-block w-[1px] h-4 bg-@label"></span>
                  <Icon
                    type="edit"
                    disabled={disabled}
                    onClick={() => {
                      if (!disabled) {
                        this.onOpenDrawer(
                          product_index,
                          'product',
                          unitType === PremiumCalculationMethod.CalculateSA ? 'periodStandardPremium' : 'sumInsured'
                        );
                      }
                    }}
                  />
                  {getProductValues(
                    {
                      productSchemaCodePreDefineValues: getFieldValue([
                        'insuranceList',
                        product_index,
                        'productSchemaCodePreDefineValues',
                      ]),
                      isVirtualProduct: product.isVirtualProduct,
                      elementValue: getFieldValue(['insuranceList', product_index, 'elementValue']),
                    },
                    unitType === PremiumCalculationMethod.CalculateSA ? 'periodStandardPremium' : 'sumInsured'
                  ) !== this.props.t('Unset')
                    ? this.renderProductRemoveIcon(product_index, disabled)
                    : null}
                </span>
              </span>
            </FormItem>
          ) : null}
          {isUnifiedCalculationMethodForAllProducts ? (
            <FormItem
              style={{ display: 'none' }}
              name={['insuranceList', product_index, 'packageProductId']}
              initialValue={product.id}
              preserve={true}
            >
              <Input />
            </FormItem>
          ) : null}
        </div>

        {!product.isVirtualProduct && (
          <Table
            expandable={{
              rowExpandable: record => !!record.liabilityInterestBenefitList?.length,
              defaultExpandAllRows: true,
              expandedRowRender:
                hasInterest && unitType === PremiumCalculationMethod.CalculatePremium
                  ? this.expandedRowRender(product, product_index, readOnly)
                  : undefined,
            }}
            expandType="nestedTable"
            scroll={{ x: 'max-content' }}
            dataSource={product.packageLiabilitys}
            pagination={false}
            emptyType="text"
            rowKey="packageLiabilityId"
            columns={this.getColumns(product, product_index, readOnly, unitType)}
          />
        )}
      </div>
    );
  }

  agreementBasisOptions() {
    const { enums, envConfig } = this.props;
    return (enums.agreementBasis || [])
      .filter(option => {
        if (envConfig.env === 'cfg') {
          return true;
        }
        return EffectivePremiumCalculationMethodList.includes(option.itemExtend1);
      })
      .map(option => ({
        value: option.itemExtend1,
        label: option.itemName,
      }));
  }

  renderInsuranceItem(product, product_index) {
    const { enums } = this.props;
    const { getFieldValue } = this.formRef.current;
    const formUnitType = getFieldValue('unitType');
    const isDefineCalculationMethodRorEachProduct =
      getFieldValue('calculationRule') === CalculationRuleType.DefineCalculationMethodForEachProduct;
    const currentUnitType = isDefineCalculationMethodRorEachProduct
      ? product.unitType
        ? `${product.unitType}`
        : product?.unitType
      : formUnitType;
    if (currentUnitType === PremiumCalculationMethod.UserInput) {
      return null;
    }
    return (
      <div key={product.id} hidden={product.isVirtualProduct}>
        <Collapse
          destroyOnClose
          title={
            <div>
              <span className="mr-4 font-bold text-root">{`${product.insuranceProductCode} - ${product.productVersion} - ${product.productType}`}</span>
              <span
                className={cx('px-2 py-1 rounded', {
                  'calculate-premium': currentUnitType === PremiumCalculationMethod.CalculatePremium,
                  'calculate-sa': currentUnitType === PremiumCalculationMethod.CalculateSA,
                  'specified-sa-premium': currentUnitType === PremiumCalculationMethod.SpecifiedSAPremium,
                })}
              >
                {renderEnumName(currentUnitType, enums.agreementBasis)}
              </span>
            </div>
          }
          visible={false}
        >
          {isDefineCalculationMethodRorEachProduct ? (
            <FormItem
              style={{ display: 'none' }}
              name={['insuranceList', product_index, 'packageProductId']}
              initialValue={product.id}
              preserve={true}
            >
              <Input />
            </FormItem>
          ) : null}
          {this.renderBenefitConfigForm(product, product_index, currentUnitType)}
        </Collapse>
      </div>
    );
  }

  unitTypeIsUserInput() {
    const { insuranceList, isDriven } = this.state;
    const { getFieldValue } = this.formRef.current;
    const tempProductList = cloneDeep(insuranceList);
    const unitType = getFieldValue('unitType');
    const isDefineCalculationMethodRorEachProduct =
      getFieldValue('calculationRule') === CalculationRuleType.DefineCalculationMethodForEachProduct;
    let result = false;

    if (isDefineCalculationMethodRorEachProduct) {
      const isUserInput = tempProductList.every(item => item.unitType == PremiumCalculationMethod.UserInput);
      result = isUserInput && isDriven;
    } else {
      result = unitType === PremiumCalculationMethod.UserInput && isDriven;
    }
    return result;
  }

  unitTypeIsSpecifiedSAPremium() {
    const { insuranceList, isDriven } = this.state;
    const { getFieldValue } = this.formRef.current;
    const tempProductList = cloneDeep(insuranceList);
    const unitType = getFieldValue('unitType');
    const isDefineCalculationMethodRorEachProduct =
      getFieldValue('calculationRule') === CalculationRuleType.DefineCalculationMethodForEachProduct;
    let result = false;

    if (isDefineCalculationMethodRorEachProduct) {
      const isUserInput = tempProductList.find(item => item.unitType == PremiumCalculationMethod.SpecifiedSAPremium);
      result = isUserInput && isDriven;
    } else {
      result = unitType === PremiumCalculationMethod.SpecifiedSAPremium && isDriven;
    }
    return result;
  }

  DraggableContainer = props => (
    <SortableContainer useDragHandle helperClass="row-dragging" onSortEnd={this.onSortEnd} {...props} />
  );

  renderPremiumCalculationMethod() {
    const { enums, t } = this.props;
    const { setFieldsValue } = this.formRef.current;
    const { insuranceList, mode, isShowEditButton, navMap, calculationRule } = this.state;

    const readOnly = mode === 'view' || !isShowEditButton;

    if (!insuranceList) {
      return null;
    }

    if (insuranceList.length === 0 || insuranceList.every(item => isILPProduct(item.productCategoryId))) {
      // 如果只配了ILP Product，传统险rider全没有，隐藏这个区块
      return null;
    }

    return (
      <AgreementContainerLayout
        title={this.getNavTitle(navMap.premiumAgreement)}
        agreementCode={navMap.premiumAgreement}
        hideDivider
      >
        <FormItem
          label={this.props.t('Premium Calculation Method')}
          colon={false}
          required
          name="calculationRule"
          initialValue={calculationRule || CalculationRuleType.UnifiedCalculationMethodForAllProducts}
        >
          <Radio.Group
            onChange={() => {
              setFieldsValue({
                unitType: insuranceList?.[0]?.unitType ? `${insuranceList[0].unitType}` : undefined,
              });
            }}
            disabled={readOnly}
          >
            <Space direction="vertical">
              {enums.calculationRule &&
                enums.calculationRule.map((item, index) => {
                  return (
                    <Radio key={index} value={item.dictValue}>
                      {item.dictValueName}
                    </Radio>
                  );
                })}
            </Space>
          </Radio.Group>
        </FormItem>
        <Row>
          <FormItem
            shouldUpdate={(prevValues, currentValues) => prevValues.calculationRule !== currentValues.calculationRule}
            noStyle
          >
            {({ getFieldValue }) => {
              if (getFieldValue('calculationRule') === CalculationRuleType.UnifiedCalculationMethodForAllProducts) {
                return (
                  <Col span={8}>
                    <FormItem
                      label={this.props.t('Calculation Method')}
                      colon={false}
                      name="unitType"
                      initialValue={
                        insuranceList?.[0]?.unitType ? `${insuranceList[0].unitType}` : undefined // 所有险种的 unitType 都是一致的
                      }
                      rules={[
                        {
                          required: true,
                          message: this.props.t('Please select'),
                        },
                      ]}
                    >
                      <GeneralSelect
                        allowClear={false}
                        placeholder={this.props.t('Please select')}
                        disabled={readOnly}
                        onChange={value => {
                          setTimeout(() => {
                            this.sumProductPremium();
                          }, 30);
                          if (!this.unitTypeIsUserInput()) {
                            insuranceList.map((i, i_index) => {
                              i.productSchemaCodePreDefineValues = [];
                              setFieldsValue({
                                [['insuranceList', i_index, 'elementValue']]: '',
                                [['insuranceList', i_index, 'productSchemaCodePreDefineValues']]: [],
                                [['insuranceList', i_index, 'unitType']]: '',
                              });
                              i.packageLiabilitys.forEach(insuranceItemLiability => {
                                insuranceItemLiability.liabilitySchemaCodePreDefineValues = [];
                              });
                              const packageLiabilitys = getFieldValue(['insuranceList', i_index])
                                ? getFieldValue(['insuranceList', i_index]).packageLiabilitys
                                : [];
                              packageLiabilitys?.forEach((ii, l_index) => {
                                setFieldsValue({
                                  [['insuranceList', i_index, 'packageLiabilitys', l_index, 'liabilityAmount']]: '',
                                  [[
                                    'insuranceList',
                                    i_index,
                                    'packageLiabilitys',
                                    l_index,
                                    'liabilitySchemaCodePreDefineValues',
                                  ]]: [],
                                });
                              });
                            });
                          }
                          setFieldsValue({
                            baseCurrency: undefined,
                            premiumCurrency: undefined,
                            pricingCurrency: undefined,
                          });
                          this.setState({
                            navList: this.state.navList.map(item => {
                              if ([navMap.premiumAdjustment, navMap.annualPremium].includes(item.href)) {
                                return {
                                  ...item,
                                  visible: value === PremiumCalculationMethod.CalculatePremium,
                                };
                              }
                              return item;
                            }),
                          });
                        }}
                        option={this.agreementBasisOptions()}
                      />
                    </FormItem>
                  </Col>
                );
              } else if (
                getFieldValue('calculationRule') === CalculationRuleType.DefineCalculationMethodForEachProduct
              ) {
                return (
                  <AppointProductCalculationRule
                    form={this.formRef.current}
                    insuranceList={insuranceList}
                    agreementBasisOptions={this.agreementBasisOptions()}
                    renewInsuranceListInfo={this.renewInsuranceListInfo}
                  />
                );
              } else {
                return null;
              }
            }}
          </FormItem>
          <FormItem
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.unitType !== currentValues.unitType ||
              prevValues.calculationRule !== currentValues.calculationRule
            }
            noStyle
          >
            {({ getFieldValue }) => {
              if (
                getFieldValue('unitType') === PremiumCalculationMethod.CalculatePremium &&
                getFieldValue('calculationRule') === CalculationRuleType.UnifiedCalculationMethodForAllProducts
              ) {
                return (
                  <Col span={8}>
                    <Form.Item
                      name="pricingCurrency"
                      label={
                        <LabelWithTooltip
                          title={t('Pricing Currency')}
                          tooltip={t(
                            'Pricing Currency corresponding to the calculated result of the formula. If select "Premium Currency", the formula\'s calculated result will be output directly.If "SA Currency", the calculated result of the formula will be converted from the SA currency to the premium currency using the exchange rate.'
                          )}
                        />
                      }
                    >
                      <GeneralSelect
                        allowClear={false}
                        option={
                          enums.pricingCurrency?.map(item => ({
                            label: item.dictValueName,
                            value: item.dictValue,
                          })) || []
                        }
                        disabled={readOnly}
                      />
                    </Form.Item>
                  </Col>
                );
              }
            }}
          </FormItem>
          <Col span={4}>
            <Form.Item label={t('Base Currency')}>{this.props.baseCurrency?.enumItemName || t('--')}</Form.Item>
          </Col>
        </Row>
      </AgreementContainerLayout>
    );
  }

  isSupportPremiumCurrency(unitType) {
    return (
      unitType === PremiumCalculationMethod.CalculatePremium || unitType === PremiumCalculationMethod.SpecifiedSAPremium
    );
  }

  renderSaCurrencyFormItem(title) {
    const { mode, isShowEditButton } = this.state;
    const readOnly = mode === 'view' || !isShowEditButton;

    return (
      <SACurrencyFormItem title={title} readOnly={readOnly} currencyIsSingle={this.unitTypeIsSpecifiedSAPremium()} />
    );
  }

  renderPremiumCurrencyFormItem() {
    const { mode, isShowEditButton } = this.state;
    const readOnly = mode === 'view' || !isShowEditButton;

    return <PremiumCurrencyFormItem readOnly={readOnly} currencyIsSingle={this.unitTypeIsSpecifiedSAPremium()} />;
  }

  renderCurrencyGroup(unitType, calculationRule) {
    if (
      this.isSupportPremiumCurrency(unitType) &&
      calculationRule === CalculationRuleType.UnifiedCalculationMethodForAllProducts
    ) {
      return (
        <Row key="sa-premium-currency">
          <Col span={8}>{this.renderSaCurrencyFormItem(this.props.t('SA Currency'))}</Col>
          <Col span={16}>{this.renderPremiumCurrencyFormItem()}</Col>
        </Row>
      );
    }

    return (
      <Row key="single-currency">
        <Col span={8}>{this.renderSaCurrencyFormItem(this.props.t('Currency'))}</Col>
      </Row>
    );
  }

  renderEditSaveBtn() {
    const { isShowEditButton, mode } = this.state;
    if (!isShowEditButton || this.props.envConfig.env === 'prd') {
      return null;
    }

    if (mode === 'view') {
      return (
        <Button size="large" onClick={() => this.onEdit()}>
          {this.props.t('Edit')}
        </Button>
      );
    }

    return (
      <Button size="large" onClick={() => this.onSubmit()}>
        {this.props.t('Save')}
      </Button>
    );
  }

  render() {
    const { t } = this.props;
    const {
      insuranceList,
      mode,
      isShowEditButton,
      navMap,
      navList,
      packageId,
      skeletonVisible,
      useNewClaimStackStructure,
      liabilityLevelClaimStackTemplateRelationList,
      productLevelClaimStackTemplateRelationList,
      packageLevelClaimStackTemplateRelationList,
      packageCode,
      totalLiabilityList,
      currentType,
      currentCode,
      drawerVisible,
      drawerInitialValue,
    } = this.state;
    if (!insuranceList) {
      return <Skeleton active />;
    }
    //  Whether Input by Campaign or Customer 选yes的时候，benefit config 不需要配置，采用C端传入的配置
    const readOnly = mode === 'view' || !isShowEditButton || this.props.envConfig.env === 'prd';

    const dragTableColumns = [
      {
        title: this.props.t('Order'),
        dataIndex: 'sort',
        width: 50,
        align: 'center',
        render: () => <DragHandle />,
      },
      {
        title: this.props.t('No.'),
        dataIndex: 'orderNo',
        className: 'drag-visible',
        width: 120,
      },
      {
        title: this.props.t('dutyText'),
        dataIndex: 'liabilityName',
        className: 'drag-visible',
        width: 280,
      },
      {
        title: this.props.t('Product'),
        dataIndex: 'productName',
        width: 380,
        className: 'drag-visible',
      },
    ];

    navList.forEach(item => {
      if (item.href === navMap.claimStackExecutionOrder) {
        item.visible = useNewClaimStackStructure;
      }
    });

    return (
      <Layout className="market-layout benefit-configuration-container">
        <FMarketHeader backPath="/market/package/search" subMenu="Package"></FMarketHeader>
        <div className="flex" style={{ height: 'calc(100% - 50px)' }}>
          <ProductAnchor navList={navList.filter(item => item.visible)} />
          <Sider width={208} className="market-sider">
            <FMarketMenu
              page="PACKAGE"
              type={mode}
              defaultSelectedKeys={[PremiumCalculationMethod.CalculateSA]}
              category={1}
              packageId={packageId}
              onBack={() => this.props.navigate('/market/package/search')}
              navigate={this.props.navigate}
            />
          </Sider>
          <Content className="market-content">
            <Form ref={this.formRef} name="BenefitConfigForm" layout="vertical">
              {this.formRef.current ? (
                <Skeleton active loading={skeletonVisible}>
                  <div className="p-6">
                    {this.renderPremiumCalculationMethod()}
                    <FormItem
                      shouldUpdate={(pre, curr) =>
                        pre.unitType !== curr.unitType || pre.calculationRule !== curr.calculationRule
                      }
                    >
                      {({ getFieldValue }) =>
                        this.renderCurrencyGroup(getFieldValue('unitType'), getFieldValue('calculationRule'))
                      }
                    </FormItem>
                    <FormItem noStyle shouldUpdate={() => true}>
                      {({ getFieldValue }) =>
                        this.unitTypeIsUserInput() ? null : (
                          <React.Fragment>
                            {insuranceList.map((product, product_index) =>
                              this.renderInsuranceItem(product, product_index)
                            )}
                            {getFieldValue('unitType') === PremiumCalculationMethod.CalculatePremium && (
                              <>
                                <AgreementContainerLayout
                                  title={this.getNavTitle(navMap.annualPremium)}
                                  agreementCode={navMap.annualPremium}
                                >
                                  <AnnualPremium
                                    disabled={readOnly}
                                    insuranceList={insuranceList}
                                    form={this.formRef.current}
                                  />
                                </AgreementContainerLayout>
                                <AgreementContainerLayout
                                  title={this.getNavTitle(navMap.premiumAdjustment)}
                                  agreementCode={navMap.premiumAdjustment}
                                >
                                  <PremiumAdjustment
                                    disabled={readOnly}
                                    insuranceList={insuranceList}
                                    form={this.formRef.current}
                                  />
                                </AgreementContainerLayout>
                              </>
                            )}
                            <AgreementContainerLayout
                              title={this.getNavTitle(navMap.policySA)}
                              agreementCode={navMap.policySA}
                            >
                              <PolicySA disabled={readOnly} insuranceList={insuranceList}></PolicySA>
                            </AgreementContainerLayout>

                            {packageId && useNewClaimStackStructure !== undefined ? (
                              useNewClaimStackStructure === true ? (
                                <>
                                  <AgreementContainerLayout
                                    title={this.getNavTitle(navMap.claimStackSetting)}
                                    agreementCode={navMap.claimStackSetting}
                                  >
                                    <SMEStackValueSetting
                                      disabled={readOnly}
                                      packageId={packageId}
                                      queryPackageStructure={this.queryPackageStructure.bind(this)}
                                      liabilityLevelClaimStackTemplateRelationList={
                                        liabilityLevelClaimStackTemplateRelationList
                                      }
                                      productLevelClaimStackTemplateRelationList={
                                        productLevelClaimStackTemplateRelationList
                                      }
                                      productList={insuranceList}
                                      packageLevelClaimStackTemplateRelationList={
                                        packageLevelClaimStackTemplateRelationList
                                      }
                                    />
                                  </AgreementContainerLayout>
                                  <ClaimStackOrderSetting
                                    packageId={packageId}
                                    refInstance={this.claimStackOrderSettingRef}
                                    agreementCode={navMap.claimStackExecutionOrder}
                                  />
                                </>
                              ) : (
                                <AgreementContainerLayout
                                  title={this.getNavTitle(navMap.claimStackSetting)}
                                  agreementCode={navMap.claimStackSetting}
                                >
                                  <StackValueSetting
                                    disabled={readOnly}
                                    packageId={packageId}
                                    packageCode={packageCode}
                                    mode={mode}
                                  />
                                </AgreementContainerLayout>
                              )
                            ) : null}
                            <AgreementContainerLayout
                              title={this.getNavTitle(navMap.liabilityOrder)}
                              agreementCode={navMap.liabilityOrder}
                              showExpandBtn
                            >
                              <Table
                                className={cx('liability-order-table')}
                                dataSource={totalLiabilityList}
                                columns={dragTableColumns}
                                emptyType="text"
                                rowKey="packageLiabilityId"
                                components={{
                                  body: {
                                    wrapper: this.DraggableContainer,
                                    row: this.DraggableBodyRow,
                                  },
                                }}
                                pagination={false}
                                size="small"
                              />
                            </AgreementContainerLayout>
                          </React.Fragment>
                        )
                      }
                    </FormItem>
                    <BenefitOptionSelectionMatching
                      disabled={readOnly}
                      refInstance={this.benefitOptionSelectionMatchingRef}
                      nextPage={this.goToNextPage}
                      navList={navList}
                      agreementCode={navMap.benefitOptionSelectionMatching}
                    />
                  </div>
                </Skeleton>
              ) : null}
            </Form>
          </Content>
          <ProductSASetting
            currentType={currentType}
            currentCode={currentCode}
            visible={drawerVisible}
            onSubmit={this.onSubmitDrawerInfo}
            onCancelChange={this.onCancelChange}
            initialValue={drawerInitialValue}
          />
          <div className="bottom-action-bar">
            {readOnly && (
              <Button size="large" disabled={skeletonVisible} type="primary" onClick={this.goToNextPage}>
                {this.props.t('Next')}
              </Button>
            )}
            {this.renderEditSaveBtn()}
            {!readOnly && (
              <Button size="large" disabled={skeletonVisible} type="default" onClick={() => this.onSubmit(true)}>
                {this.props.t('Next')}
              </Button>
            )}
          </div>
        </div>
      </Layout>
    );
  }
}

export default withTranslation(['market', 'common'])(
  connect(state => ({
    envConfig: state.envConfig,
    hasEditAuth: !!selectPermissionCheckMap(state)['market.edit'],
    isSuperUser: !!selectPermissionCheckMap(state)['market.edit-all'],
    userInfo: state.userInfo,
    baseCurrency: state.globalConfig.baseCurrency,
  }))(BenefitConfiguration)
);
