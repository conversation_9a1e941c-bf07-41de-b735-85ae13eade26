import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { useRequest } from 'ahooks';

import { useStackCondition } from 'genesis-web-component/lib/components/ClaimStack/hooks/useStackCondition';
import { ConditionItem } from 'genesis-web-service/lib/product';
import {
  ClaimStackInstanceRelationType,
  ClaimStackValueTypeEnum,
} from 'genesis-web-service/service-types/market-types/package';
import { FormulaListRelationResponse } from 'genesis-web-service/service-types/product-types/package';

import { BizTopic } from '@market/common/enums';
import { TransBizDictOption } from '@market/common/interface';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { NewProductService } from '@market/services/product/product.service.new';

import {
  ClaimStackTemplateStatus,
  EnhanceStackComponentInstanceResponse,
  EnhanceStackTemplateResponse,
  TemplateFactor,
} from './interface';
import { convertComponent } from './util';

export const useStackTemplateList = (active: boolean) => {
  const { data, loading: templateLoading } = useRequest(
    () => {
      if (!active) {
        return Promise.resolve([]);
      }
      const param = {
        page: 0,
        size: 100000, // fetch all
        status: ClaimStackTemplateStatus.PUBLISHED,
      };
      return NewProductService.StackTemplateService.listTemplate(param).then(result => {
        if (result?.content) {
          return result.content;
        }
      });
    },
    {
      cacheKey: 'listTemplate',
      refreshDeps: [active],
    }
  );

  return {
    templateList: data as {
      name: string;
      code: string;
    }[],
    templateLoading,
  };
};

export const useStackTemplateDetail = (
  templateCode: string | undefined,
  relationType: ClaimStackInstanceRelationType,
  record?: EnhanceStackTemplateResponse
) => {
  const { data, loading: templateDetailLoading } = useRequest(
    () => {
      // edit编辑
      if (record && record.code === templateCode) {
        const hasHandled = record?.components?.[0]?.preValueType;
        if (hasHandled) {
          return Promise.resolve(record);
        }

        if (
          relationType === ClaimStackInstanceRelationType.PACKAGE ||
          relationType === ClaimStackInstanceRelationType.PACKAGE_PRODUCT
        ) {
          return NewProductService.StackTemplateService.getTemplate(record.code as string).then(tempDetail => {
            const { components: stackComps, ...restTemplate } = record || {};
            const stackTemplateValues = {
              ...restTemplate,
              components: stackComps?.map(convertComponent)?.map((comp, index) => {
                const originalComp = tempDetail.components?.[index];
                return {
                  ...comp,
                  // 从tempDetail找到原始的valueType
                  preValueType: originalComp?.componentParams?.valueType,
                  // 从tempDetail找到原始的value, 例如用来限定Range的选择范围
                  preValue: originalComp?.componentParams?.value,
                  // 从tempDetail找到原始的conditionalValues, 例如用来限定conditionalValues的选择范围
                  preConditionalValues: originalComp?.componentParams?.conditionalValues?.values,
                };
              }),
            };
            return stackTemplateValues;
          });
        }

        const { components: stackComps, ...restTemplate } = record || {};
        const stackTemplateValues = {
          ...restTemplate,
          components: stackComps?.map(convertComponent)?.map((comp, index) => ({
            ...comp,
            // 从benchmarkComponentParams中取出原始的值，作为修改的范围
            // 如果没查到，这边兼容一下，给个FIXED_VALUE，禁用页面编辑，防止错误逻辑发生
            preValueType: comp?.benchmarkComponentParams?.valueType || ClaimStackValueTypeEnum.FIXED_VALUE,
            preValue: comp?.benchmarkComponentParams?.value,
            preConditionalValues: comp?.benchmarkComponentParams?.conditionalValues?.values,
          })),
        };
        return Promise.resolve(stackTemplateValues);
      }
      // add新增
      if (templateCode) {
        return NewProductService.StackTemplateService.getTemplate(templateCode).then(detailRes => {
          const { components: stackComps, ...restTemplate } = (detailRes || {}) as EnhanceStackTemplateResponse;
          const stackTemplateValues = {
            ...restTemplate,
            components: stackComps?.map(convertComponent)?.map(comp => ({
              ...comp,
              // 用来记录原始的valueType
              preValueType: comp?.componentParams?.valueType,
              // 用来记录原始的value, 例如用来限定Range的选择范围
              preValue: comp?.componentParams?.value,
              // 用来记录原始的conditionalValues, 例如用来限定conditionalValues的选择范围
              preConditionalValues: comp?.componentParams?.conditionalValues?.values,
            })),
          };
          return stackTemplateValues;
        });
      }
      return Promise.resolve({});
    },
    { refreshDeps: [templateCode] }
  );

  const templateDetail = data as EnhanceStackTemplateResponse;

  return {
    templateDetail,
    templateDetailLoading,
  };
};

export const useFormulaInClaimStack = () => {
  const [formulaList, setFormulaList] = useState<FormulaListRelationResponse[]>();

  const { runAsync: queryFormula, loading } = useRequest(
    () =>
      NewProductService.ProductSchemaFormulaMgmtService.list({
        formulaCategoryCode: +BizTopic.ClaimStack,
      }).then(res => {
        if (res) {
          const tempFormulaList = res.filter(formula => {
            // 过滤掉在产品内部创建出来的公式
            if (formula.schemaFormulaRelation && formula.schemaFormulaRelation.createdInProductId) {
              return false;
            }
            return true;
          });
          setFormulaList(tempFormulaList);
        }
      }),
    {
      manual: true,
    }
  );

  useEffect(() => {
    queryFormula();
  }, []);

  return {
    formulaList,
    queryFormula,
    loading,
  };
};

export const useTemplateFactors = (templateComponents?: EnhanceStackComponentInstanceResponse[]) => {
  const [t] = useTranslation(['market', 'common']);
  const { dictListMap, claimStackConditionOptions = [] } = useStackCondition();

  const mapFactor = (factor: string) => ({
    label: t(factor),
    factorCode: factor,
  });

  const getConditionLabels = (cond: ConditionItem): string[] => {
    const conditionLabels = Object.entries(cond?.conditions || {}).reduce(
      (final: string[], [conditionKey, conditionValues]) => {
        const conditionValueNames = conditionValues?.map(val => {
          const findLabel = dictListMap?.[conditionKey]?.find(dict => dict.dictValue === val)?.dictValueName as string;
          return findLabel;
        });

        const conditionLabel = claimStackConditionOptions?.find(condOpt => condOpt.value === conditionKey)
          ?.label as string;
        final.push(`${conditionValueNames?.join('/')}(${conditionLabel})`);
        return final;
      },
      []
    );
    return conditionLabels;
  };

  const getTemplateFactors = (components?: EnhanceStackComponentInstanceResponse[]) =>
    components?.reduce((factorResult: Record<string, TemplateFactor[]>, comp) => {
      const code = comp.code!;
      if (!factorResult[code]) {
        const hasConditions = !!comp.componentParams?.conditionalValues?.conditionKeys?.length;
        if (!hasConditions) {
          // 没有condition, 根据factors组装templateFactor结构
          factorResult[code] = comp.factors?.map(mapFactor);
        } else {
          // 有condition, 需要将condition 与factorCode为value的因子进行组装
          const conditions = comp.componentParams?.conditionalValues?.values;
          // 将component的factors过滤不是value的因子
          factorResult[code] = comp.factors?.filter(fac => fac !== 'value')?.map(mapFactor);
          conditions?.forEach(cond => {
            // eslint-disable-next-line @typescript-eslint/no-shadow
            const { value, ...restCond } = cond;
            const conditionLabels = getConditionLabels(restCond as ConditionItem);
            if (conditionLabels?.length) {
              // conditionLabelsStr拼接方式：Condition Value(Condition Name)/Condition Value(Condition Name)
              // 例如：Baggage(Property Object) / a-gao_test(Incident Reason)
              const factorLabel = `${t('value') as string}-${conditionLabels.join('&')}`;
              factorResult[code].push({
                label: factorLabel,
                factorCode: 'value',
                conditions: { ...restCond?.conditions },
              });
            }
          });
        }
      }
      return factorResult;
    }, {});

  const templateFactors = useMemo(() => getTemplateFactors(templateComponents), [templateComponents]);

  return { getTemplateFactors, templateFactors };
};

const transBizDictOption: TransBizDictOption = {
  valueKey: 'enumItemName',
  labelKey: 'dictValueName',
};

export const useClaimStackManagementBizDict = () => {
  const claimPeriodTypes = useBizDictAsOptions('claimPeriodType', transBizDictOption);
  const stackTypes = useBizDictAsOptions('stackType', transBizDictOption);
  const mainConditionTypes = useBizDictAsOptions('mainConditionType', transBizDictOption);
  const stackUnits = useBizDictAsOptions('stackUnit', transBizDictOption);

  const claimBillObjectTypes = useBizDictAsOptions('claimBillObjectType');
  const claimStackValueTypes = useBizDictAsOptions('claimStackValueType');
  const claimTimesTypes = useBizDictAsOptions('claimTimesType');
  const claimDaysTypes = useBizDictAsOptions('claimDaysType');
  const numberItems = useBizDictAsOptions('numberItems');
  const appliedUnits = useBizDictAsOptions('appliedUnit');
  const billConditions = useBizDictAsOptions('billConditions');
  const claimIncidentTypes = useBizDictAsOptions('claimIncidentType');

  return {
    claimPeriodTypes,
    stackTypes,
    mainConditionTypes,
    stackUnits,
    claimBillObjectTypes,
    claimStackValueTypes,
    claimTimesTypes,
    claimDaysTypes,
    numberItems,
    appliedUnits,
    billConditions,
    claimIncidentTypes,
  };
};
