import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from 'antd';
import { ColumnProps } from 'antd/es/table';

import {
  DeleteAction,
  EditAction,
  Icon,
  OperationContainer,
  Table,
  TableActionsContainer,
  TextEllipsisDetect,
  message,
} from '@zhongan/nagrand-ui';

import { PackageProductBenefit } from 'genesis-web-service/lib/market';
import {
  ClaimStackInstanceRelationType,
  CreateClaimStackTemplateRelationRequest,
  ProductLevelClaimStackTemplateRelationResponse,
  SaveStackTemplateRequest,
} from 'genesis-web-service/service-types/market-types/package';

import { DetailPageMode } from '@market/common/interface';
import { useDrawerState } from '@market/hook/useDrawerState';

import {
  useDeleteTemplateRelation,
  useUpdateTemplateRelation,
} from '../../../../../hook/claim-stack/useStackTemplateService';
import { StackTemplateValues } from '../../interface';
import ClaimStackCorrelationDrawer from '../ClaimStackCorrelationDrawer/ClaimStackCorrelationDrawer';
import ClaimStackTemplateDetailDrawer from '../ClaimStackTemplateDetailDrawer/ClaimStackTemplateDetailDrawer';

interface Props {
  disabled: boolean;
  packageId: string;
  queryPackageStructure: () => Promise<void>;
  productList: PackageProductBenefit[];
  productLevelClaimStackTemplateRelationList: ProductLevelClaimStackTemplateRelationResponse[];
}

export const ProductClaimStack = ({
  disabled,
  packageId,
  productList,
  queryPackageStructure,
  productLevelClaimStackTemplateRelationList,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [visible, setVisible] = useState(false);
  const [drawerState, setDrawerState] = useDrawerState<StackTemplateValues>();

  const { deleteTemplateRelation } = useDeleteTemplateRelation();
  const { updateTemplateRelation } = useUpdateTemplateRelation();

  const columns = useMemo(() => {
    const tempColumns: ColumnProps<ProductLevelClaimStackTemplateRelationResponse>[] = [
      {
        title: t('No.'),
        width: 60,
        render: (text: string, record: any, index: number) => index + 1,
      },
      {
        title: t('Product Name'),
        dataIndex: 'productNames',
      },
      {
        title: t('Stack Content'),
        width: 600,
        render: (_, record) => {
          if (!record.template?.content) {
            return t('--');
          }
          return <TextEllipsisDetect text={record.template?.content} width={600} line={2} />;
        },
      },
    ];

    if (!disabled) {
      tempColumns.push({
        fixed: 'right',
        width: 80,
        title: t('Actions'),
        render: (text, record) => (
          <TableActionsContainer>
            <EditAction
              onClick={() => {
                setDrawerState({
                  record,
                  visible: true,
                  mode: DetailPageMode.edit,
                });
              }}
            />
            <DeleteAction
              doubleConfirmType="popconfirm"
              doubleConfirmPlacement="left"
              onClick={() => {
                deleteTemplateRelation(record.id!).then(() => {
                  message.success(t('Delete Successfully'));
                  queryPackageStructure();
                });
              }}
            />
          </TableActionsContainer>
        ),
      });
    }

    return tempColumns;
  }, [disabled, t]);

  return (
    <div>
      <div className="mb-2">{t('Product Claim Stack')}</div>
      <OperationContainer>
        <OperationContainer.Left>
          <Button
            icon={<Icon type="add" />}
            className="mb-2"
            onClick={() => {
              setVisible(true);
            }}
          >
            {t('Add')}
          </Button>
        </OperationContainer.Left>
      </OperationContainer>
      <Table columns={columns} dataSource={productLevelClaimStackTemplateRelationList} pagination={false} />
      <ClaimStackCorrelationDrawer
        visible={visible}
        relationType={ClaimStackInstanceRelationType.PACKAGE_PRODUCT}
        productList={productList}
        onClose={() => {
          setVisible(false);
        }}
        onSubmit={() => {
          queryPackageStructure();
          setVisible(false);
        }}
        packageId={packageId}
      />
      <ClaimStackTemplateDetailDrawer
        {...drawerState}
        relationType={ClaimStackInstanceRelationType.PACKAGE_PRODUCT}
        onClose={() => {
          setDrawerState({ visible: false });
        }}
        onSubmitSuccess={newRecord => {
          // 只有编辑的时候会进入这个逻辑
          const updateParam = {
            ...newRecord,
          } as SaveStackTemplateRequest;

          updateTemplateRelation(drawerState.record!.id!, updateParam).then(() => {
            message.success(t('Submit Successfully'));
            queryPackageStructure();
            setDrawerState({ visible: false });
          });
        }}
      />
    </div>
  );
};

export default ProductClaimStack;
