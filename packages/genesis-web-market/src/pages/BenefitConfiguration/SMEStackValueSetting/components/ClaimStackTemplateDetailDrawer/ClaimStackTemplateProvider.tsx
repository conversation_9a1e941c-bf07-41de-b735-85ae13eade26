import { FC, createContext, useContext, useState } from 'react';

import { ClaimStackComponent } from 'genesis-web-service/lib/product/claimStack.interface';
import { FormulaListRelationResponse } from 'genesis-web-service/service-types/product-types/package';

import { useFormulaInClaimStack } from '../../hooks';

interface ClaimStackTemplateContextType {
  selectedComponents: ClaimStackComponent[];
  setSelectedComponents: React.Dispatch<React.SetStateAction<ClaimStackComponent[]>>;
  formulaList: FormulaListRelationResponse[];
  queryFormula: () => void;
}

const ClaimStackTemplateContext = createContext<ClaimStackTemplateContextType>({
  selectedComponents: [],
  setSelectedComponents: () => {},
  formulaList: [],
  queryFormula: () => {},
});

export const useClaimStackTemplateContext = () => useContext(ClaimStackTemplateContext);

export const ClaimStackTemplateProvider: FC = ({ children }) => {
  const [selectedComponents, setSelectedComponents] = useState<ClaimStackComponent[]>([]);

  const { formulaList = [], queryFormula } = useFormulaInClaimStack();

  return (
    <ClaimStackTemplateContext.Provider
      value={{
        selectedComponents,
        setSelectedComponents,
        formulaList,
        queryFormula,
      }}
    >
      {children}
    </ClaimStackTemplateContext.Provider>
  );
};
