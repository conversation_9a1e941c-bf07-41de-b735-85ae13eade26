import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';

import { cloneDeep } from 'lodash-es';

import { Drawer } from '@zhongan/nagrand-ui';

import { ProductTypeEnum } from 'genesis-web-service/lib/common.interface';
import { PackageProductBenefit } from 'genesis-web-service/lib/market/package.interface';
import {
  BaseRelationResponse,
  ClaimStackInstanceRelationType,
} from 'genesis-web-service/service-types/market-types/package';
import { MultiLanguageContent } from 'genesis-web-service/service-types/product-types/package';

import { DetailPageMode } from '@market/common/interface';
import { DrawerState } from '@market/hook/useDrawerState';

import { useStackTemplateDetail, useStackTemplateList } from '../../hooks';
import { EnhanceStackTemplateResponse, StackTemplateValues } from '../../interface';
import { convertLanguageContents, revertComponent } from '../../util';
import TemplateCodeNameSelect from '../TemplateCodeNameSelect/TemplateCodeNameSelect';
import ProductStackTemplateDetail from './ProductStackTemplateDetail';

interface Props extends DrawerState<BaseRelationResponse> {
  onClose: () => void;
  onSubmitSuccess?: (newRecord: StackTemplateValues) => void;
  relationType: ClaimStackInstanceRelationType;
  productInfo?: PackageProductBenefit;
}

export const ClaimStackTemplateDetailDrawer = ({
  visible,
  mode,
  record,
  onClose,
  onSubmitSuccess,
  productInfo,
  relationType,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();
  const templateCode = Form.useWatch(['code'], form) as string;

  const { templateList } = useStackTemplateList(visible);

  const { templateDetail = {} } = useStackTemplateDetail(templateCode, relationType, record?.template);

  const { components: templateComps = [], languages, multiLanguageContents } = templateDetail;

  const languageContents = useMemo(
    () => convertLanguageContents(multiLanguageContents as MultiLanguageContent[]),
    [multiLanguageContents]
  );

  useEffect(() => {
    form.setFieldValue('components', [...templateComps]);
    form.setFieldValue('languages', languages);
    form.setFieldValue('multiLanguageContents', languageContents);
  }, [templateComps, languages, languageContents, record]);

  useEffect(() => {
    if (visible) {
      form.setFieldValue('code', record?.template?.code);
    }
  }, [visible]);

  const resetFields = useCallback(() => {
    form.setFieldsValue({
      code: undefined,
    });
  }, [form]);

  const closeDrawer = useCallback(() => {
    resetFields();

    onClose();
  }, [onClose, resetFields]);

  const onSubmit = useCallback(() => {
    form.validateFields().then((values: EnhanceStackTemplateResponse) => {
      const { components = [] } = values || {};
      const formattedComponents = cloneDeep(components).map(revertComponent);
      const newRecord = {
        ...templateDetail,
        components: formattedComponents,
      };
      onSubmitSuccess?.(newRecord);
    });
  }, [form, onSubmitSuccess, templateDetail]);

  return (
    <Drawer
      open={visible}
      title={t('Claim Stack Correlation')}
      onClose={closeDrawer}
      destroyOnClose
      submitBtnShow={DetailPageMode.view !== mode}
      submitBtnProps={{
        disabled: !templateCode,
      }}
      onSubmit={() => onSubmit()}
      readonly={DetailPageMode.view === mode}
    >
      <Form form={form} layout="vertical" initialValues={templateDetail}>
        <Form.Item colon={false} label={t('Template Code / Name')} name={['code']}>
          <TemplateCodeNameSelect
            readonly={mode === DetailPageMode.view || mode === DetailPageMode.edit}
            options={templateList}
          />
        </Form.Item>
        {templateCode && (
          <ProductStackTemplateDetail
            // GIS-115443
            // liability 上面挂的stack，如果险种是rider，不能改value type
            disableValueType={
              productInfo?.productTypeCode === ProductTypeEnum.RIDER &&
              relationType === ClaimStackInstanceRelationType.PACKAGE_PRODUCT_LIABILITY
            }
            readonly={mode === DetailPageMode.view}
            templateDetail={templateDetail}
          />
        )}
      </Form>
    </Drawer>
  );
};

export default ClaimStackTemplateDetailDrawer;
