import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Row } from 'antd';
import { useWatch } from 'antd/es/form/Form';

import { StackComponent } from 'genesis-web-component/lib/components/ClaimStack/components/StackComponent';
import { StackValueTypeEnum } from 'genesis-web-service';
import { MultiLanguageContent, StackComponentSaveCmd } from 'genesis-web-service/service-types/product-types/package';

import { DetailPageMode, TransBizDictOption } from '@market/common/interface';
import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { useDrawerState } from '@market/hook/useDrawerState';

import { useFormulaInClaimStack, useTemplateFactors } from '../../hooks';
import { ComponentValues, EnhanceStackTemplateResponse } from '../../interface';
import { convertLanguageContents } from '../../util';
import ClaimStackComponentDrawer from '../ClaimStackComponentDrawer/ClaimStackComponentDrawer';
import { TemplateContent } from '../TemplateContent/TemplateContent';
import { ClaimStackTemplateProvider } from './ClaimStackTemplateProvider';

const FormItem = Form.Item;

const transBizDictOption: TransBizDictOption = {
  valueKey: 'enumItemName',
  labelKey: 'dictValueName',
};

interface Props {
  disableValueType?: boolean;
  readonly?: boolean;
  templateDetail: EnhanceStackTemplateResponse;
}
const ProductStackTemplateDetail: React.FC<Props> = ({ readonly, templateDetail, disableValueType }) => {
  const [t] = useTranslation(['market', 'common']);
  const form = Form.useFormInstance();
  const claimStackValueTypeBizDictOptions = useBizDictAsOptions('claimStackValueType', transBizDictOption);
  const comparisonTypeOptions = useBizDictAsOptions('claimStackValueRule', transBizDictOption);
  const { formulaList } = useFormulaInClaimStack();
  const [componentDrawerState, setComponentDrawerState] = useDrawerState<StackComponentSaveCmd>();
  const templateCode = templateDetail.code as string;
  const { templateFactors } = useTemplateFactors(templateDetail?.components);

  const componentsMap = useMemo(() => {
    const codesSet: Record<string, string> = {};

    templateDetail?.components?.forEach(item => {
      codesSet[item.code] = item.name;
    });

    return codesSet;
  }, [templateDetail?.components]);

  const languageOptions = useBizDictAsOptions('language', transBizDictOption);

  const selectedLanguages = useWatch(['languages'], form) as string[];

  const selectedLanguageOptions = languageOptions?.filter(lang => selectedLanguages?.includes(lang.value as string));

  return (
    <ClaimStackTemplateProvider>
      <header className="max-w-[624px]">
        <Row>
          <Col>
            <FormItem
              colon={false}
              label={t('Multi Language')}
              name="languages"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <GeneralSelect
                mode="multiple"
                option={languageOptions ?? []}
                placeholder={t('Please select')}
                style={{ width: 626 }}
                disabled
              />
            </FormItem>
          </Col>
        </Row>
      </header>
      {/* component list */}
      <Form.List name="components">
        {fields =>
          fields.map((field, index) => {
            const { key, name, ...restField } = field;
            const components = (form.getFieldValue('components') || []) as ComponentValues[];
            const component = components[name];
            // 这里使用preValueType（记录的是原始的valueType）
            // 因为UserInput形式允许重新定义valueType
            const { preValueType } = component || {};
            // fixed value type 只读
            // formula 当作fixed value type
            const isFixedOrFormulaValueType = [
              StackValueTypeEnum.FixedValue,
              StackValueTypeEnum.SelectFormula,
            ].includes(preValueType as StackValueTypeEnum);
            return (
              <StackComponent
                readonly={readonly || isFixedOrFormulaValueType}
                fieldData={field}
                component={component}
                key={`${templateCode}_${component.code}`}
                openFormulaDrawer={() => {}}
                withinProduct
                bizdicts={{
                  comparisonTypeOptions,
                  claimStackValueTypeBizDictOptions,
                }}
                claimStackFormulaList={formulaList || []}
                disableValueType={disableValueType}
                handleView={() =>
                  setComponentDrawerState({
                    visible: true,
                    record: component,
                  })
                }
              />
            );
          })
        }
      </Form.List>
      {/* Template Content */}
      {selectedLanguageOptions?.length > 0 && (
        <TemplateContent
          readonly
          key={`${templateDetail.code as string}_multiLanguageContents`}
          languages={selectedLanguageOptions}
          templateFactors={templateFactors}
          componentCodeNameMap={componentsMap}
          value={convertLanguageContents(templateDetail.multiLanguageContents as MultiLanguageContent[])}
        />
      )}
      <ClaimStackComponentDrawer
        {...componentDrawerState}
        onClose={() => {
          setComponentDrawerState({
            visible: false,
            mode: DetailPageMode.view,
            record: undefined,
          });
        }}
      />
    </ClaimStackTemplateProvider>
  );
};

export default ProductStackTemplateDetail;
