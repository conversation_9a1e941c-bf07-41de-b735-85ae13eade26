import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Divider } from 'antd';
import { ColumnProps } from 'antd/es/table';

import { DeleteAction, Drawer, EditAction, Icon, Table, TextBody, ViewAction, message } from '@zhongan/nagrand-ui';

import { PackageProductBenefit } from 'genesis-web-service/lib/market';
import {
  ClaimStackInstanceRelationType,
  CreateClaimStackTemplateRelationRequest,
  MultiLanguageContent,
} from 'genesis-web-service/service-types/market-types/package';

import { DetailPageMode } from '@market/common/interface';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { useDrawerState } from '@market/hook/useDrawerState';
import { renderOptionName } from '@market/utils/enum';

import { useCreateTemplateRelation } from '../../../../../hook/claim-stack/useStackTemplateService';
import { StackTemplateValues } from '../../interface';
import ClaimStackTemplateDetailDrawer from '../ClaimStackTemplateDetailDrawer/ClaimStackTemplateDetailDrawer';

interface Props {
  visible: boolean;
  packageId: string;
  relationType: ClaimStackInstanceRelationType;
  productList?: PackageProductBenefit[];
  onClose: () => void;
  onSubmit: () => void;
}

export const ClaimStackCorrelationDrawer = ({
  visible,
  packageId,
  productList = [],
  relationType,
  onClose,
  onSubmit,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [selectedProductIds, setSelectedProductIds] = useState<number[]>([]);
  const [productSelectionDisabled, setProductSelectionDisabled] = useState(false);
  const [drawerState, setDrawerState] = useDrawerState<StackTemplateValues>();
  const [tableData, setTableData] = useState<StackTemplateValues[]>([]);

  /* ============== 枚举使用start ============== */
  const isOptionalOptions = useBizDictAsOptions('isOptional');
  /* ============== 枚举使用end ============== */
  const productShouldExist =
    relationType === ClaimStackInstanceRelationType.PACKAGE_PRODUCT ? selectedProductIds.length > 0 : true;

  const { createTemplateRelation } = useCreateTemplateRelation();

  const closeDrawer = useCallback(() => {
    onClose();
    setSelectedProductIds([]);
    setProductSelectionDisabled(false);
    setTableData([]);
    setDrawerState(undefined);
  }, []);

  const columns: ColumnProps<StackTemplateValues>[] = useMemo(
    () => [
      {
        title: t('Stack Template Code'),
        dataIndex: 'code',
      },
      {
        title: t('Stack Template Name'),
        dataIndex: 'name',
      },
      {
        title: t('Content'),
        dataIndex: 'content',
        ellipsis: true,
        render: (_, record) => (record?.multiLanguageContents as MultiLanguageContent[])?.[0]?.content || '',
      },
      {
        title: t('Actions'),
        fixed: 'right',
        render: (_, record, index) => (
          <React.Fragment>
            <ViewAction
              onClick={() => {
                setDrawerState({
                  visible: true,
                  mode: DetailPageMode.view,
                  record: {
                    template: record,
                  },
                });
              }}
            />
            <EditAction
              onClick={() => {
                setDrawerState({
                  visible: true,
                  mode: DetailPageMode.edit,
                  record: {
                    template: record,
                  },
                });
              }}
            />
            <DeleteAction
              doubleConfirmPlacement="left"
              doubleConfirmType="popconfirm"
              onClick={() => {
                tableData.splice(index, 1);
                setTableData([...tableData]);
              }}
              deleteConfirmContent={t('Are you sure to clear this section?')}
            />
          </React.Fragment>
        ),
      },
    ],
    [setDrawerState, t, tableData]
  );

  const submit = useCallback(() => {
    if (tableData.length === 0) {
      message.error(t('Please add stack correlation'));
      return;
    }

    const saveParam = {
      productIdSet: selectedProductIds,
      packageId: +packageId,
      stackTemplateList: tableData.map((item, index) => ({
        ...item,
        order: index + 1,
      })),
      relationType,
    } as CreateClaimStackTemplateRelationRequest;

    createTemplateRelation(saveParam).then(() => {
      message.success(t('Submit Successfully'));
      closeDrawer();
      onSubmit();
    });
  }, [selectedProductIds, tableData, packageId, onSubmit, relationType]);

  return (
    <Drawer
      open={visible}
      title={t('Product Claim Stack')}
      onClose={closeDrawer}
      onSubmit={submit}
      submitBtnProps={{
        disabled: !productShouldExist || tableData.length === 0,
      }}
    >
      {relationType === ClaimStackInstanceRelationType.PACKAGE_PRODUCT && (
        <React.Fragment>
          <div className="mb-2">{t('Select Product')}</div>
          <Table<PackageProductBenefit>
            columns={[
              {
                title: t('No.'),
                render: (_, record, index) => index + 1,
              },
              {
                title: t('Product Category'),
                dataIndex: 'productCategoryName',
              },
              {
                title: t('Product Name'),
                dataIndex: 'productName',
              },
              {
                title: t('Product Code'),
                dataIndex: 'insuranceProductCode',
              },
              {
                title: t('Optional / Required'),
                dataIndex: 'isOptional',
                render: (text: number) => renderOptionName(text?.toString(), isOptionalOptions),
              },
            ]}
            rowKey="productId"
            dataSource={productList}
            rowSelection={{
              getCheckboxProps: () => ({
                disabled: productSelectionDisabled,
              }),
              selectedRowKeys: selectedProductIds,
              onChange: selectedRowKeys => {
                setSelectedProductIds(selectedRowKeys as number[]);
              },
            }}
            pagination={false}
          />
          <Divider style={{ margin: '40px 0 16px' }} />
        </React.Fragment>
      )}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 8,
        }}
      >
        <TextBody weight="bold">{t('Existing Stack in the Product')}</TextBody>
        <Button
          disabled={!productShouldExist}
          onClick={() => {
            setDrawerState({
              visible: true,
              mode: DetailPageMode.add,
            });
            setProductSelectionDisabled(true);
          }}
          icon={<Icon type="add" />}
        >
          {t('Add New')}
        </Button>
      </div>
      <Table columns={columns} dataSource={tableData} scroll={{ x: 'max-content' }} />
      <ClaimStackTemplateDetailDrawer
        {...drawerState}
        relationType={relationType}
        onClose={() => {
          setDrawerState({ visible: false });
        }}
        onSubmitSuccess={newRecord => {
          if (drawerState.mode === DetailPageMode.add) {
            setTableData([...tableData, newRecord]);
          } else if (drawerState.mode === DetailPageMode.edit) {
            setTableData(
              tableData.map(item => {
                if (item.code === newRecord.code) {
                  return newRecord;
                }
                return item;
              })
            );
          }
          setDrawerState({ visible: false });
        }}
      />
    </Drawer>
  );
};

export default ClaimStackCorrelationDrawer;
