import React from 'react';
import { useTranslation } from 'react-i18next';

import GeneralSelect from '@market/components/GeneralSelect';

interface ITemplateOption {
  name: string;
  code: string;
}

interface ITemplateCodeNameSelectProps {
  value?: string;
  onChange?: (value: string) => void;
  options: ITemplateOption[];
  readonly?: boolean;
}

const TemplateCodeNameSelect: React.FC<ITemplateCodeNameSelectProps> = ({
  value,
  onChange,
  readonly,
  options = [],
}) => {
  const [t] = useTranslation(['market', 'common']);

  return (
    <GeneralSelect
      disabled={readonly}
      value={value}
      onChange={onChange}
      showSearch
      placeholder={t('Please select')}
      style={{ width: 624 }}
      options={options?.map(option => ({
        value: option.code,
        label: `${option.code} / ${option.name}`,
      }))}
    />
  );
};

export default TemplateCodeNameSelect;
