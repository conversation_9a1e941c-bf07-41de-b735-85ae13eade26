import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Divider, Form, Input, Row } from 'antd';

import { ClaimPeriodTypeEnum, MainConditionTypeEnum, StackTypeEnum } from 'genesis-web-service/lib/product';
import { StackComponentSaveCmd } from 'genesis-web-service/service-types/product-types/package';

import GeneralSelect from '@market/components/GeneralSelect';

import { useClaimStackManagementBizDict } from '../../hooks';
import ClaimStackUnitAndValueFormItems from './ClaimStackUnitAndValueFormItems';

const FormItem = Form.Item;

const StackTypeShowSubCondition = [StackTypeEnum.Limit, StackTypeEnum.Deductible];

export const visibleClaimPeriodType = [
  ClaimPeriodTypeEnum.PerPolicyYear,
  ClaimPeriodTypeEnum.CoveragePeriod,
  ClaimPeriodTypeEnum.Daily,
  ClaimPeriodTypeEnum.PerCalendarYear,
  ClaimPeriodTypeEnum.PerInstallment,
  ClaimPeriodTypeEnum.PerLifeTime,
];

export const visibleMainConditionType = [
  MainConditionTypeEnum.PerPolicy,
  MainConditionTypeEnum.PerPerson,
  MainConditionTypeEnum.PerPersonUnderPolicy,
];

interface Props {
  record?: StackComponentSaveCmd;
}

// 只支持查看功能
export const ClaimStackComponentForm = ({ record }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const form = Form.useFormInstance();

  const readOnly = true;

  /* ============== 枚举使用start ============== */
  const { appliedUnits, claimPeriodTypes, stackTypes, mainConditionTypes } = useClaimStackManagementBizDict();
  /* ============== 枚举使用end ============== */

  const mainConditionTypeOptions = useMemo(
    () => mainConditionTypes.filter(item => visibleMainConditionType.includes(item.value)),
    [mainConditionTypes]
  );

  const claimPeriodTypeOptions = useMemo(
    () => claimPeriodTypes.filter(item => visibleClaimPeriodType.includes(item.value)),
    [claimPeriodTypes]
  );

  return (
    <div className="pb-[70px]">
      <Row>
        <Col span={8}>
          <FormItem
            colon={false}
            label={t('Component Name')}
            name="name"
            rules={[
              {
                required: true,
                message: t('Please input'),
              },
            ]}
          >
            <Input className="w-[240px]" placeholder={t('Please input')} disabled={readOnly} />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem colon={false} label={t('Component Code')} name="code">
            <Input className="w-[240px]" placeholder={t('Please input')} disabled />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem
            colon={false}
            label={t('Stack Type')}
            name="stackType"
            rules={[
              {
                required: true,
                message: t('Please select'),
              },
            ]}
          >
            <GeneralSelect
              option={stackTypes}
              onChange={() => {
                form.setFieldsValue({
                  stackUnit: undefined,
                });
              }}
              placeholder={t('Please select')}
              className="w-[240px]"
              disabled={readOnly}
            />
          </FormItem>
        </Col>
        <Col span={24}>
          <FormItem
            colon={false}
            label={t('Component Description')}
            name="description"
            rules={[
              {
                required: true,
                message: t('Please input'),
              },
            ]}
          >
            <Input.TextArea className="w-[580px]" placeholder={t('Please input')} disabled={readOnly} />
          </FormItem>
        </Col>
        <Col span={8}>
          <FormItem
            colon={false}
            label={t('Main Condition Type')}
            name="mainConditionType"
            rules={[
              {
                required: true,
                message: t('Please select'),
              },
            ]}
          >
            <GeneralSelect
              option={mainConditionTypeOptions || []}
              onChange={() => {
                form.setFieldsValue({
                  periodType: undefined,
                });
              }}
              placeholder={t('Please select')}
              className="w-[240px]"
              disabled={readOnly}
            />
          </FormItem>
        </Col>
        <Form.Item noStyle shouldUpdate={() => true}>
          {({ getFieldValue }) =>
            StackTypeShowSubCondition.includes(getFieldValue('stackType')) ? (
              <Col span={8}>
                <FormItem
                  colon={false}
                  label={t('Period Type')}
                  name="periodType"
                  rules={[
                    {
                      required: true,
                      message: t('Please select'),
                    },
                  ]}
                >
                  <GeneralSelect
                    option={claimPeriodTypeOptions}
                    placeholder={t('Please select')}
                    className="w-[240px]"
                    disabled={readOnly}
                  />
                </FormItem>
              </Col>
            ) : null
          }
        </Form.Item>
        <Col span={8}>
          <FormItem colon={false} label={t('Applied Unit')} name="appliedUnit">
            <GeneralSelect
              option={appliedUnits.filter(item => item.value !== 'INCIDENT_REASON')}
              placeholder={t('Please select')}
              className="w-[240px]"
              disabled={readOnly}
            />
          </FormItem>
        </Col>
        <Divider className="my-4" />
        <ClaimStackUnitAndValueFormItems readOnly={readOnly} />
      </Row>
    </div>
  );
};

export default ClaimStackComponentForm;
