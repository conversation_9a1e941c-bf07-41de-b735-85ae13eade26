import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Row } from 'antd';

import { StackTypeEnum, StackUnitEnum } from 'genesis-web-service/lib/product';

import GeneralSelect from '@market/components/GeneralSelect';

import { useClaimStackManagementBizDict } from '../../hooks';

interface Props {
  readOnly: boolean;
}

export const visibleStackUnitByLimit = [
  StackUnitEnum.NumberOfDays,
  StackUnitEnum.NumberOfTimes,
  StackUnitEnum.Number,
  StackUnitEnum.Amount,
];

export const ClaimStackUnitAndValueFormItems = ({ readOnly }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  /* ============== 枚举使用start ============== */

  const { stackUnits, claimTimesTypes } = useClaimStackManagementBizDict();
  /* ============== 枚举使用end ============== */
  const renderFromByStackUnit = useCallback(
    stackUnitValue => {
      if (stackUnitValue === StackUnitEnum.NumberOfTimes) {
        return (
          <Col span={8}>
            <Form.Item
              label={t('Stack Unit Type')}
              colon={false}
              name="unitType"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <GeneralSelect
                option={claimTimesTypes}
                placeholder={t('Please select')}
                className="w-[240px]"
                disabled={readOnly}
              />
            </Form.Item>
          </Col>
        );
      }

      return null;
    },
    [claimTimesTypes, readOnly, t]
  );

  const getOptions = (stackType: StackTypeEnum) => {
    switch (stackType) {
      case StackTypeEnum.CoPay:
        return stackUnits.filter(item => item.value === StackUnitEnum.OfcoPay);
      case StackTypeEnum.Limit:
        return stackUnits.filter(item => visibleStackUnitByLimit.includes(item.value));
      case StackTypeEnum.Deductible:
        return stackUnits.filter(item => item.value !== StackUnitEnum.OfcoPay);
      default:
        return stackUnits;
    }
  };

  return (
    <Row style={{ width: '100%' }}>
      <Form.Item noStyle shouldUpdate={() => true}>
        {({ getFieldValue }) => (
          <React.Fragment>
            <Col span={8}>
              <Form.Item
                label={t('Stack Unit')}
                colon={false}
                name="stackUnit"
                rules={[
                  {
                    required: true,
                    message: t('Please select'),
                  },
                ]}
              >
                <GeneralSelect
                  option={getOptions(getFieldValue('stackType'))}
                  placeholder={t('Please select')}
                  className="w-[240px]"
                  disabled={readOnly}
                />
              </Form.Item>
            </Col>
            {renderFromByStackUnit(getFieldValue('stackUnit'))}
          </React.Fragment>
        )}
      </Form.Item>
    </Row>
  );
};

export default ClaimStackUnitAndValueFormItems;
