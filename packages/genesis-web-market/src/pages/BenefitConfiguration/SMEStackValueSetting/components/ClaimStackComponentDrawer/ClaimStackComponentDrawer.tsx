import React, { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';

import { Drawer } from '@zhongan/nagrand-ui';

import { StackComponentSaveCmd } from 'genesis-web-service/service-types/product-types/package';

import ClaimStackComponentForm from './ClaimStackComponentForm';

interface Props {
  visible: boolean;
  record: StackComponentSaveCmd;
  onClose: () => void;
}

// Component Drawer 只能查看
const ClaimStackComponentDrawer = (props: Props) => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();

  const { visible, record, onClose } = props;

  useEffect(() => {
    form.setFieldsValue(record);
  }, [record, form]);

  const closeDrawer = useCallback(() => {
    form.resetFields();
    onClose();
  }, [form, onClose]);

  return (
    <Drawer open={visible} title={t('View Component')} onClose={closeDrawer} readonly>
      <Form layout="vertical" form={form} colon={false}>
        <ClaimStackComponentForm record={record} />
      </Form>
    </Drawer>
  );
};

export default ClaimStackComponentDrawer;
