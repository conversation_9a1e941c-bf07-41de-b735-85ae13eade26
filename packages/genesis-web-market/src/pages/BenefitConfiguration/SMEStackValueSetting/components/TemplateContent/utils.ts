export function extractFilterWord(text: string): {
  needFilter: boolean;
  filterWord: string;
  replaceIndex: number;
} {
  // 去掉完整的 {{ xxx }} 格式，这种不需要提示了
  let lastOpenBracketIndex = -1;
  let hasUnmatchedCloseBracked = false;
  for (let index = text.length - 1; index >= 0; index--) {
    if (text[index] === '{' && text[index - 1] === '{') {
      if (hasUnmatchedCloseBracked) {
        hasUnmatchedCloseBracked = false;
      } else {
        lastOpenBracketIndex = index - 1;
      }
      continue;
    }

    if (text[index] === '}' && text[index - 1] === '}') {
      hasUnmatchedCloseBracked = true;
    }
  }

  // 没有 {{ ，不需要提示
  if (lastOpenBracketIndex === -1) {
    return {
      needFilter: false,
      filterWord: '',
      replaceIndex: -1,
    };
  }

  let filterWord = '';

  for (let index = lastOpenBracketIndex + 2; index < text.length; index++) {
    if (filterWord && text[index] === ' ') {
      break;
    }
    filterWord += text[index];
  }

  return {
    needFilter: true,
    filterWord: filterWord,
    replaceIndex: lastOpenBracketIndex,
  };
}
