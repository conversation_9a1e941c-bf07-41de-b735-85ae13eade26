.cm-wrapper {
  :global {
    .cm-editor {
      position: unset !important;
      outline: none;
      border: 1px solid $divider-color;
      border-radius: 8px;
      font-size: 14px;

      &:focus-within {
        border-color: $primary-color;
        box-shadow: 0 0 4px 0 rgba(0, 136, 255, 0.5);
      }
    }

    .cm-focused .cm-matchingBracket {
      background-color: $warning-color-bg;
    }

    .cm-line {
      margin-left: 3px;
      margin-top: 1.5px;
      font-family: Intel, sans-serif;
    }

    .cm-placeholder {
      font-size: 14px;
      color: $divider-color;
      font-family: Intel, sans-serif;
    }

    .cm-scroller {
      overflow: hidden;
    }
  }
}
