import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Space } from 'antd';

import { Tabs } from '@zhongan/nagrand-ui';

import { Completion, autocompletion } from '@codemirror/autocomplete';
import { SelectOption } from '@market/common/interface';
import { t } from '@market/i18n';
import ReactCodeMirror, { EditorView } from '@uiw/react-codemirror';
import { Statistics } from '@uiw/react-codemirror/src/utils';

import { TemplateComponentFactors } from '../../interface';
import { customDecoration } from './decoration';
import styles from './style.module.scss';
import { extractFilterWord } from './utils';

interface ITemplateContentProps {
  languages: SelectOption[];
  templateFactors?: TemplateComponentFactors;
  readonly?: boolean;
  onChange?: (value: Record<string, string>) => void;
  value?: Record<string, string>;
  componentCodeNameMap: Record<string, string>;
}

export const TemplateContent: React.FC<ITemplateContentProps> = ({
  languages,
  value = {},
  templateFactors = {},
  readonly,
  onChange,
  componentCodeNameMap,
}) => {
  const [cursorPosition, setCursorPosition] = useState<number>(0);
  const [selectedLanguage, setSelectedLanguage] = useState<string>(languages?.[0]?.value as string);
  const [editing, setEditing] = useState(false);

  useEffect(() => {
    setSelectedLanguage(languages?.[0]?.value as string);
  }, [languages]);

  const handleChange = useCallback(
    (text: string) => {
      if (selectedLanguage) {
        value[selectedLanguage] = text;
        onChange?.({
          ...value,
        });
      }
    },
    [selectedLanguage]
  );

  const onFieldClick = useCallback(
    (componentCode: string, item: { label: string }) => () => {
      const currentText = value[selectedLanguage] || '';

      handleChange(
        `${currentText.substring(0, cursorPosition)}{{${componentCode} - ${item.label}}}${currentText.substring(cursorPosition)}`
      );
    },
    [selectedLanguage, value, cursorPosition]
  );

  const items = useMemo(
    () =>
      Object.keys(templateFactors).map(componentCode => ({
        key: componentCode,
        label: <span title={componentCodeNameMap[componentCode]}>{componentCode}</span>,
        type: 'group',
        children: templateFactors[componentCode]?.map(item => ({
          key: `${componentCode}//${item.label as string}`,
          onClick: onFieldClick(componentCode, item),
          label: item.label,
          title: `${componentCodeNameMap[componentCode]} - ${item.label as string}`,
        })),
      })),
    [templateFactors, onFieldClick]
  );

  const autoCompleteOptions = useMemo(() => {
    const options: Completion[] = [];
    Object.keys(templateFactors).forEach(componentCode => {
      templateFactors[componentCode].forEach(item => {
        options.push({
          label: `{{${componentCode} - ${item.label as string}}}`,
          displayLabel: `{{${componentCode} - ${item.label as string}}}`,
        });
      });
    });
    return options;
  }, [templateFactors]);

  const handleStatistics = useCallback(
    (data: Statistics) => {
      if (editing) {
        setCursorPosition(data.selectionAsSingle.to);
      }
    },
    [editing]
  );

  return (
    <Tabs
      onChange={key => {
        setSelectedLanguage(key);
      }}
      defaultActiveKey={selectedLanguage}
      type="border-card"
    >
      {languages.map(language => (
        <Tabs.TabPane tab={language.label} key={language.value}>
          <div className="border-solid border-@border-color-base rounded-lg">
            <div className="px-3 py-2 border-solid border-t-0 border-x-0 border-b-@border-color-base">
              <Dropdown menu={{ items }} disabled={readonly}>
                <Space>
                  {'{ }'}
                  {t('Insert fields')}
                  <DownOutlined />
                </Space>
              </Dropdown>
            </div>
            <div className="p-4">
              <ReactCodeMirror
                readOnly={readonly}
                editable={!readonly}
                value={value[selectedLanguage]}
                width="100%"
                minHeight="152px"
                height="152px"
                onChange={handleChange}
                onStatistics={handleStatistics}
                onClick={() => setEditing(true)}
                indentWithTab={false}
                className={styles.cmWrapper}
                extensions={[
                  customDecoration,
                  EditorView.lineWrapping,
                  autocompletion({
                    override: [
                      context => {
                        //   /\{\{(.*?)[^\}|^\s]/
                        const word = context.matchBefore(/\{\{(.*?)/);
                        if (!word) {
                          return null;
                        }
                        const { filterWord, needFilter, replaceIndex } = extractFilterWord(word.text);
                        if (needFilter === false) {
                          return null;
                        }

                        const options = autoCompleteOptions.filter(option =>
                          option.displayLabel?.toLowerCase()?.includes(filterWord.trim().toLowerCase())
                        );

                        const insertIndex = word.from + replaceIndex;
                        return {
                          filter: false,
                          from: insertIndex > word.to ? word.to : insertIndex,
                          options,
                        };
                      },
                    ],
                  }),
                ]}
                basicSetup={{
                  foldGutter: false,
                  lineNumbers: false,
                  syntaxHighlighting: false,
                  highlightActiveLine: false,
                  allowMultipleSelections: false,
                  searchKeymap: false,
                  // historyKeymap: false,
                  foldKeymap: false,
                  lintKeymap: false,
                  closeBrackets: false,
                  highlightSpecialChars: true,
                }}
              />
            </div>
          </div>
        </Tabs.TabPane>
      ))}
    </Tabs>
  );
};
