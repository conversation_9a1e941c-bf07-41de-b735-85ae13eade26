import { lessVars } from '@zhongan/nagrand-ui';

import {
  Decoration,
  DecorationSet,
  EditorView,
  MatchDecorator,
  ViewPlugin,
  ViewUpdate,
  WidgetType,
} from '@uiw/react-codemirror';

class CustomWidget extends WidgetType {
  value: string;
  constructor(value: string) {
    super();
    this.value = value;
  }

  toDOM() {
    let wrap = document.createElement('span');
    wrap.textContent = this.value;
    wrap.style.color = lessVars['@primary-color'];

    return wrap;
  }

  ignoreEvent() {
    return false;
  }
}

const regexMatcher = new MatchDecorator({
  // regexp: /\{\{(.*?)\}\}/g,
  regexp: /\{\{[^{}]*\}\}/g,
  decoration: match =>
    Decoration.replace({
      widget: new CustomWidget(match[0]),
    }),
});

export const customDecoration = ViewPlugin.fromClass(
  class {
    decorationRangeSet: DecorationSet;
    constructor(view: EditorView) {
      this.decorationRangeSet = regexMatcher.createDeco(view);
    }
    update(update: ViewUpdate) {
      this.decorationRangeSet = regexMatcher.updateDeco(update, this.decorationRangeSet);
    }
  },
  {
    decorations: instance => instance.decorationRangeSet,
    provide: plugin =>
      EditorView.atomicRanges.of(view => {
        return view.plugin(plugin)?.decorationRangeSet || Decoration.none;
      }),
  }
);
