import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from 'antd';
import { ColumnProps } from 'antd/es/table';

import {
  DeleteAction,
  EditAction,
  Icon,
  OperationContainer,
  Table,
  TableActionsContainer,
  TextEllipsisDetect,
  message,
} from '@zhongan/nagrand-ui';

import {
  ClaimStackInstanceRelationType,
  CreateClaimStackTemplateRelationRequest,
  PackageLevelClaimStackTemplateRelationResponse,
  SaveStackTemplateRequest,
} from 'genesis-web-service/service-types/market-types/package';

import { DetailPageMode } from '@market/common/interface';
import { useDrawerState } from '@market/hook/useDrawerState';

import {
  useCreateTemplateRelation,
  useDeleteTemplateRelation,
  useUpdateTemplateRelation,
} from '../../../../../hook/claim-stack/useStackTemplateService';
import { StackTemplateValues } from '../../interface';
import ClaimStackTemplateDetailDrawer from '../ClaimStackTemplateDetailDrawer/ClaimStackTemplateDetailDrawer';

interface Props {
  disabled: boolean;
  packageId: string;
  queryPackageStructure: () => Promise<void>;
  packageLevelClaimStackTemplateRelationList: PackageLevelClaimStackTemplateRelationResponse[];
}

export const PolicyClaimStack = ({
  disabled,
  packageId,
  queryPackageStructure,
  packageLevelClaimStackTemplateRelationList,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [drawerState, setDrawerState] = useDrawerState<StackTemplateValues>();
  const { deleteTemplateRelation } = useDeleteTemplateRelation();
  const { createTemplateRelation } = useCreateTemplateRelation();
  const { updateTemplateRelation } = useUpdateTemplateRelation();

  const columns = useMemo(() => {
    const tempColumns: ColumnProps<PackageLevelClaimStackTemplateRelationResponse>[] = [
      {
        title: t('No.'),
        width: 60,
        render: (text, record, index: number) => index + 1,
      },
      {
        title: t('Stack Content'),
        render: (_, record) => {
          if (!record.template?.content) {
            return t('--');
          }
          return <TextEllipsisDetect text={record.template?.content} width={1000} line={2} />;
        },
      },
    ];

    if (!disabled) {
      tempColumns.push({
        fixed: 'right',
        width: 80,
        title: t('Actions'),
        render: (text, record) => (
          <TableActionsContainer>
            <EditAction
              onClick={() => {
                setDrawerState({
                  record,
                  visible: true,
                  mode: DetailPageMode.edit,
                });
              }}
            />
            <DeleteAction
              doubleConfirmType="popconfirm"
              doubleConfirmPlacement="left"
              onClick={() => {
                deleteTemplateRelation(record.id!).then(() => {
                  message.success(t('Delete Successfully'));
                  queryPackageStructure();
                });
              }}
            />
          </TableActionsContainer>
        ),
      });
    }

    return tempColumns;
  }, [disabled, t]);

  return (
    <div>
      <div className="mb-2">{t('Policy Claim Stack')}</div>
      <OperationContainer>
        <OperationContainer.Left>
          <Button
            icon={<Icon type="add" />}
            className="mb-2"
            onClick={() => {
              setDrawerState({ visible: true, mode: DetailPageMode.add });
            }}
          >
            {t('Add')}
          </Button>
        </OperationContainer.Left>
      </OperationContainer>
      <Table columns={columns} dataSource={packageLevelClaimStackTemplateRelationList} pagination={false} />
      <ClaimStackTemplateDetailDrawer
        {...drawerState}
        relationType={ClaimStackInstanceRelationType.PACKAGE}
        onClose={() => {
          setDrawerState({ visible: false });
        }}
        onSubmitSuccess={newRecord => {
          if (drawerState.mode === DetailPageMode.add) {
            const saveParam = {
              packageId: +packageId,
              stackTemplateList: [
                {
                  ...newRecord,
                  order: 1,
                },
              ],
              relationType: ClaimStackInstanceRelationType.PACKAGE,
            } as CreateClaimStackTemplateRelationRequest;
            createTemplateRelation(saveParam).then(() => {
              message.success(t('Submit Successfully'));
              queryPackageStructure();
              setDrawerState({ visible: false });
            });
          } else if (drawerState.mode === DetailPageMode.edit) {
            const updateParam = {
              ...newRecord,
            } as SaveStackTemplateRequest;
            updateTemplateRelation(drawerState.record!.id!, updateParam).then(() => {
              message.success(t('Submit Successfully'));
              queryPackageStructure();
              setDrawerState({ visible: false });
            });
          }
        }}
      />
    </div>
  );
};

export default PolicyClaimStack;
