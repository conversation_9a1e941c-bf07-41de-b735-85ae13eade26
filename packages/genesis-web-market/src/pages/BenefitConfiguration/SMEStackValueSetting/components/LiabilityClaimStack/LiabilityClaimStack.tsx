import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { ColumnProps } from 'antd/es/table';

import { EditAction, Table, TextEllipsisDetect } from '@zhongan/nagrand-ui';

import { PackageProductBenefit } from 'genesis-web-service/lib/market/package.interface';
import {
  ClaimStackInstanceRelationType,
  LiabilityLevelClaimStackTemplateRelationResponse,
} from 'genesis-web-service/service-types/market-types/package';

import { DetailPageMode } from '@market/common/interface';
import { useDrawerState } from '@market/hook/useDrawerState';

import { useUpdateInstanceRelation } from '../../../../../hook/claim-stack/useStackTemplateService';
import { StackTemplateValues } from '../../interface';
import ClaimStackTemplateDetailDrawer from '../ClaimStackTemplateDetailDrawer/ClaimStackTemplateDetailDrawer';

interface Props {
  disabled: boolean;
  productList: PackageProductBenefit[];
  packageId: string;
  queryPackageStructure: () => Promise<void>;
  liabilityLevelClaimStackTemplateRelationList: LiabilityLevelClaimStackTemplateRelationResponse[];
}

export const LiabilityClaimStack = ({
  disabled,
  packageId,
  productList,
  queryPackageStructure,
  liabilityLevelClaimStackTemplateRelationList,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [drawerState, setDrawerState] = useDrawerState<StackTemplateValues>();

  const { updateInstanceRelation } = useUpdateInstanceRelation();

  const columns = useMemo(() => {
    const tempColumns: ColumnProps<LiabilityLevelClaimStackTemplateRelationResponse>[] = [
      {
        title: t('No.'),
        width: 60,
        render: (text: string, record: any, index: number) => index + 1,
      },
      {
        title: t('Product Name'),
        dataIndex: 'productName',
      },
      {
        title: t('Liability Name'),
        dataIndex: 'liabilityNames',
      },
      {
        title: t('Stack Content'),
        width: 600,
        render: (_, record) => {
          if (!record.template?.content) {
            return t('--');
          }
          return <TextEllipsisDetect text={record.template?.content} width={600} line={2} />;
        },
      },
    ];

    if (!disabled) {
      tempColumns.push({
        fixed: 'right',
        width: 80,
        title: t('Actions'),
        render: (text, record) => (
          <EditAction
            onClick={() => {
              setDrawerState({
                record,
                visible: true,
                mode: DetailPageMode.edit,
              });
            }}
          />
        ),
      });
    }

    return tempColumns;
  }, [disabled, t]);

  return (
    <div>
      <div className="mb-2">{t('Liability Claim Stack')}</div>
      <Table columns={columns} dataSource={liabilityLevelClaimStackTemplateRelationList} pagination={false} />
      <ClaimStackTemplateDetailDrawer
        {...drawerState}
        productInfo={productList?.find(product => product.productId === drawerState?.record?.productId)}
        relationType={ClaimStackInstanceRelationType.PACKAGE_PRODUCT_LIABILITY}
        onClose={() => {
          setDrawerState({ visible: false });
        }}
        onSubmitSuccess={newRecord => {
          updateInstanceRelation({
            packageId: +packageId,
            relationType: ClaimStackInstanceRelationType.PACKAGE_PRODUCT_LIABILITY,
            stackTemplate: newRecord,
            liabilityIdSet: drawerState.record.liabilityIdSet,
            productId: drawerState.record.productId,
          }).then(() => {
            queryPackageStructure();
            setDrawerState({ visible: false });
          });
        }}
      />
    </div>
  );
};

export default LiabilityClaimStack;
