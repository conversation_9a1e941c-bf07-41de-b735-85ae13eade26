import {
  ClaimStackComponent,
  ConditionItem,
  StackComponentValue,
} from 'genesis-web-service/lib/product/claimStack.interface';
import {
  BaseRelationResponse,
  ClaimStackInstanceRelationType,
  ClaimStackLevel,
  MultiLanguageContent,
  StackComponentInstanceResponse,
  StackTemplateResponse,
} from 'genesis-web-service/service-types/market-types/package';
import { StackTemplateBase } from 'genesis-web-service/service-types/product-types/package';

import { t } from '@market/i18n';

export interface EnhanceStackComponentInstanceResponse extends StackComponentInstanceResponse {
  comparisonType?: string;
  preValueType?: string; // for product: 用来记录原始的valueType
  preValue?: StackComponentValue; // for product: 用来记录原始的valueType, 用来限定选择范围
  preConditionalValues?: ConditionItem[];
}

export interface EnhanceStackTemplateResponse extends StackTemplateResponse {
  components?: EnhanceStackComponentInstanceResponse[];
}

export interface SortTemplateRelation {
  relationId: number;
  order: number;
  templateCode: string;
}

export enum ClaimStackTemplateStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
}

export interface ComponentValues extends ClaimStackComponent {
  comparisonType?: string;
  preValueType?: string; // for product: 用来记录原始的valueType
  preValue?: StackComponentValue; // for product: 用来记录原始的valueType, 用来限定选择范围
  preConditionalValues?: ConditionItem[];
}

export interface StackTemplateValues extends StackTemplateBase {
  components?: ComponentValues[];
  multiLanguageContents?: MultiLanguageContent[] | Record<string, string>;
  order?: number; // for product
  relationId?: number; // for product
}

export interface TemplateFactor {
  label?: string;
  factorCode: string;
  conditions?: Record<string, string[]>;
}

export interface TemplateComponentFactors {
  [key: string]: TemplateFactor[];
}

export const StackLevelLabels: Record<ClaimStackLevel, string> = {
  PACKAGE: t('Policy Stack'),
  LIABILITY: t('Liability Stack'),
  PRODUCT: t('Product Stack'),
};
