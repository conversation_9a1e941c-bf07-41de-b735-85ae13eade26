import React, { Ref, useEffect, useImperativeHandle, useState } from 'react';

import { Table } from '@zhongan/nagrand-ui';

import {
  ClaimStackLevel,
  ClaimStackOrderPrdGroupResponse,
  ClaimStackOrderResponse,
} from 'genesis-web-service/service-types/market-types/package';

import { DetailPageMode } from '@market/common/interface';
import AgreementContainerLayout from '@market/components/AgreementContainerLayout';
import Collapse from '@market/components/Collapse';
import { useDrawerState } from '@market/hook/useDrawerState';
import { t } from '@market/i18n';
import { NewMarketService } from '@market/services/market/market.service.new';

import ClaimStackTemplateDetailDrawer from './components/ClaimStackTemplateDetailDrawer/ClaimStackTemplateDetailDrawer';
import { StackLevelLabels } from './interface';

interface Props {
  packageId: number;
  refInstance: Ref<{
    saveClaimStackOrder: () => Promise<void>;
  }>;
  agreementCode: string;
  disabled: boolean;
}

export const ClaimStackOrderSetting = ({ packageId, refInstance, agreementCode, disabled }: Props): JSX.Element => {
  const [claimStackOrderPrdGroupResponses, setClaimStackOrderPrdGroupResponses] =
    useState<ClaimStackOrderPrdGroupResponse[]>();
  const [drawerState, setDrawerState] = useDrawerState<ClaimStackOrderResponse>();

  useEffect(() => {
    NewMarketService.PackageProductInsuranceMgmtService.queryClaimOrder(packageId).then(res => {
      setClaimStackOrderPrdGroupResponses(res);
    });
  }, [packageId]);

  useImperativeHandle(refInstance, () => ({
    saveClaimStackOrder: () =>
      NewMarketService.PackageProductInsuranceMgmtService.saveClaimOrder(packageId, claimStackOrderPrdGroupResponses!),
  }));

  return (
    <AgreementContainerLayout showExpandBtn title={t('Claim Stack Execution Order')} agreementCode={agreementCode}>
      <React.Fragment>
        {claimStackOrderPrdGroupResponses?.map((product, productIndex) => (
          <Collapse
            title={
              <div>
                <span className="mr-4 font-bold text-root">{`${product.productCode!} - ${product.productVersion!} - ${product.productType === 'MAIN' ? t('Main Benefit') : t('Rider')}`}</span>
              </div>
            }
          >
            {product.claimStackOrderGroups?.map((liability, liabilityIndex) => (
              <div className="mt-4 mb-6 user-select-none-custom">
                <div className="mb-2 font-medium">
                  {t('Liability Name')}: {liability.liabilityName}
                </div>
                <Table
                  draggable={!disabled}
                  rowKey="stackOrder"
                  columns={[
                    {
                      title: t('No.'),
                      render: (text, record) => record.stackOrder! + 1,
                    },
                    {
                      title: t('Stack Level'),
                      dataIndex: 'stackLevel',
                      render: (text: ClaimStackLevel) => StackLevelLabels[text],
                    },
                    {
                      title: t('Stack Template Name'),
                      dataIndex: ['template', 'name'],
                      render: (text, record) => (
                        <span
                          className="text-@primary-color cursor-pointer underline"
                          onClick={() => {
                            setDrawerState({
                              record,
                              visible: true,
                              mode: DetailPageMode.view,
                            });
                          }}
                        >
                          {text}
                        </span>
                      ),
                    },
                    {
                      title: t('Stack Component Name'),
                      dataIndex: 'componentCode',
                    },
                  ]}
                  dataSource={liability.claimStackOrders}
                  pagination={false}
                  setDataSource={newList => {
                    claimStackOrderPrdGroupResponses[productIndex].claimStackOrderGroups[
                      liabilityIndex
                    ].claimStackOrders = newList.map((item, itemIndex) => ({
                      ...item,
                      stackOrder: itemIndex,
                    }));

                    setClaimStackOrderPrdGroupResponses([...claimStackOrderPrdGroupResponses]);
                  }}
                />
              </div>
            ))}
          </Collapse>
        ))}

        <ClaimStackTemplateDetailDrawer
          {...drawerState}
          relationType={drawerState.record?.stackLevel}
          onClose={() => {
            setDrawerState({ visible: false });
          }}
        />
      </React.Fragment>
    </AgreementContainerLayout>
  );
};

export default ClaimStackOrderSetting;
