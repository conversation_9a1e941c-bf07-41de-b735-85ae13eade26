import { ClaimStackComponent, PercentageValue } from 'genesis-web-service';
import {
  ClaimStackValueTypeEnum,
  LanguageEnum,
  StackUnitEnum,
} from 'genesis-web-service/service-types/market-types/package';
import { MultiLanguageContent } from 'genesis-web-service/service-types/product-types/package';

import { EnhanceStackComponentInstanceResponse } from './interface';

// 将component后端数据转换成需要的前端数据
export const convertComponent = (component: EnhanceStackComponentInstanceResponse) => {
  const { stackUnit } = component;
  // if (stackUnit === StackUnitEnum.ComparisonDeductible) {
  if (stackUnit === StackUnitEnum.COMPARISON_OF_AMOUNT_AND_PERCENTAGE) {
    const value = component?.componentParams?.value as PercentageValue[];
    const comparisonType = value?.[0]?.comparisonType;
    component.comparisonType = comparisonType;
  }
  return component;
};

// 将前端数据转换成后端的component数据
export const revertComponent = (component: EnhanceStackComponentInstanceResponse) => {
  const { comparisonType, ...restProps } = component;
  const valueType = restProps?.componentParams?.valueType;
  const conditionKeys = restProps?.componentParams?.conditionalValues?.conditionKeys;
  if (restProps?.componentParams?.conditionalValues) {
    if (valueType && valueType === ClaimStackValueTypeEnum.FORMULA) {
      restProps.componentParams.conditionalValues = undefined;
    }
    if (!conditionKeys?.length) {
      restProps.componentParams.conditionalValues = undefined;
    }
  }
  if (restProps.stackUnit === StackUnitEnum.COMPARISON_OF_AMOUNT_AND_PERCENTAGE) {
    const oldValue = restProps?.componentParams?.value as PercentageValue[];
    if (restProps?.componentParams?.value) {
      restProps.componentParams.value = oldValue.map(val => ({
        ...val,
        comparisonType,
      })) as PercentageValue[];
    }
  }
  return restProps as ClaimStackComponent;
};

// 将 multiLanguageContents 后端数据转换成需要的前端数据
export const convertLanguageContents = (multiLanguageContents: MultiLanguageContent[]) => {
  const convertFrontResult = multiLanguageContents?.reduce((result: Record<string, string>, current) => {
    if (current.language) {
      const language = current.language as string;
      result[language] = current.content || '';
    }

    return result;
  }, {});
  return convertFrontResult;
};

// 将前端数据转换成后端的 multiLanguageContents 数据
export const revertLanguageContents = (templateLanguageContents: Record<string, string> = {}) => {
  const convertParamResult: MultiLanguageContent[] = [];
  Object.entries(templateLanguageContents).forEach(([key, content]) => {
    const languageContent: MultiLanguageContent = {
      language: key as LanguageEnum,
      content,
    };
    convertParamResult.push(languageContent);
  });
  return convertParamResult;
};
