import React from 'react';
import { useTranslation } from 'react-i18next';

import { PackageProductBenefit } from 'genesis-web-service/lib/market';
import {
  LiabilityLevelClaimStackTemplateRelationResponse,
  PackageLevelClaimStackTemplateRelationResponse,
  ProductLevelClaimStackTemplateRelationResponse,
} from 'genesis-web-service/service-types/market-types/package';

import LiabilityClaimStack from './components/LiabilityClaimStack/LiabilityClaimStack';
import PolicyClaimStack from './components/PolicyClaimStack/PolicyClaimStack';
import ProductClaimStack from './components/ProductClaimStack/ProductClaimStack';

interface Props {
  disabled: boolean;
  packageId: string;
  queryPackageStructure: () => Promise<void>;
  liabilityLevelClaimStackTemplateRelationList: LiabilityLevelClaimStackTemplateRelationResponse[];
  productLevelClaimStackTemplateRelationList: ProductLevelClaimStackTemplateRelationResponse[];
  packageLevelClaimStackTemplateRelationList: PackageLevelClaimStackTemplateRelationResponse[];
  productList: PackageProductBenefit[];
}

export const SMEStackValueSetting = ({
  disabled,
  packageId,
  liabilityLevelClaimStackTemplateRelationList,
  productLevelClaimStackTemplateRelationList,
  packageLevelClaimStackTemplateRelationList,
  queryPackageStructure,
  productList,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  return (
    <React.Fragment>
      <div className="mb-6">
        <LiabilityClaimStack
          disabled={disabled}
          packageId={packageId}
          productList={productList}
          queryPackageStructure={queryPackageStructure}
          liabilityLevelClaimStackTemplateRelationList={liabilityLevelClaimStackTemplateRelationList}
        />
      </div>
      <div className="mb-6">
        <ProductClaimStack
          disabled={disabled}
          packageId={packageId}
          productList={productList}
          queryPackageStructure={queryPackageStructure}
          productLevelClaimStackTemplateRelationList={productLevelClaimStackTemplateRelationList}
        />
      </div>
      <PolicyClaimStack
        disabled={disabled}
        packageId={packageId}
        queryPackageStructure={queryPackageStructure}
        packageLevelClaimStackTemplateRelationList={packageLevelClaimStackTemplateRelationList}
      />
    </React.Fragment>
  );
};

export default SMEStackValueSetting;
