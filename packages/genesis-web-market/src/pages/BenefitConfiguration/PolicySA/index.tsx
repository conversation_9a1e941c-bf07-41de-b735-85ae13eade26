import React from 'react';
import { useTranslation } from 'react-i18next';

import { Checkbox, Form } from 'antd';

import { Select } from '@zhongan/nagrand-ui';

import { PackageProductBenefit } from 'genesis-web-service';

export const PolicySA = ({
  insuranceList,
  disabled,
}: {
  insuranceList: PackageProductBenefit[];
  disabled: boolean;
}) => {
  const [t] = useTranslation(['market', 'common']);

  return (
    <React.Fragment>
      <Form.Item name={['accumulateProducts', 'packageAgreementParamsId']} noStyle />
      <Form.Item name={['accumulateProducts', 'accumulateProductSaToPolicySa']} valuePropName="checked" noStyle>
        <Checkbox disabled={disabled}>{t('Accumulate Product SA to Policy SA')}</Checkbox>
      </Form.Item>
      <Form.Item noStyle dependencies={['accumulateProducts', 'accumulateProductSaToPolicySa']}>
        {({ getFieldValue }) =>
          getFieldValue(['accumulateProducts', 'accumulateProductSaToPolicySa']) ? (
            <Form.Item
              name={['accumulateProducts', 'accumulationProducts']}
              label={t('Products Involved in Accumulation')}
              className="mt-2"
            >
              <Select
                tooltipOnEditMode
                className="!w-[560px]"
                disabled={disabled}
                placeholder={t('Please select')}
                options={insuranceList?.map(product => ({
                  value: product.productId,
                  label: product.productName,
                }))}
                mode="multiple"
                maxTagCount={2}
              />
            </Form.Item>
          ) : null
        }
      </Form.Item>
    </React.Fragment>
  );
};
