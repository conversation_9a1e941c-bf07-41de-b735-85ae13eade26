import { useTranslation } from 'react-i18next';

import { Form } from 'antd';

import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';

interface Props {
  title: string;
  readOnly: boolean;
  currencyIsSingle: boolean; // 货币是否支持多选
}

export const SACurrencyFormItem = ({ title, readOnly, currencyIsSingle }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const currencyOptions = useBizDictAsOptions('currencys');

  return (
    <Form.Item
      label={<span>{title}</span>}
      colon={false}
      required
      name="baseCurrency"
      rules={[
        {
          required: true,
          message: t('Please select'),
        },
      ]}
    >
      <GeneralSelect
        allowClear={false}
        disabled={readOnly}
        showSearch
        mode={currencyIsSingle ? undefined : 'multiple'}
        placeholder={t('Please select')}
        option={currencyOptions}
      />
    </Form.Item>
  );
};

export default SACurrencyFormItem;
