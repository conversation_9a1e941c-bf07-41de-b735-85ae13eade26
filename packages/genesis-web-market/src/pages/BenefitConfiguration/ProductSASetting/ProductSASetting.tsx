import React, { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Input, InputNumber, Radio, Row, Space, message } from 'antd';

import classNames from 'classnames/bind';

import { Modal, Drawer } from '@zhongan/nagrand-ui';

import { EnumerationSettingFormList } from '@market/components/EnumerationSettingFormList';

import { PreDefineValues, ProductDeductibleAmountType } from '../interface';
import styles from './ProductSASetting.module.scss';

const cx = classNames.bind(styles);

interface Props {
  type?: 'drawer' | 'modal';
  visible: boolean;
  onSubmit: (preDefineValues: PreDefineValues) => void;
  onCancelChange: () => void;
  currentType: 'product' | 'liability' | 'interest';
  currentCode: string;
  initialValue?: PreDefineValues;
}

export const ProductSASetting = ({
  visible,
  onSubmit,
  onCancelChange,
  currentCode,
  currentType,
  initialValue,
  type = 'drawer',
}: Props) => {
  const [form] = Form.useForm();
  const [t] = useTranslation(['market', 'common']);

  useEffect(() => {
    if (visible) {
      const data = {
        ProductDeductibleAmount: initialValue?.valueType || ProductDeductibleAmountType.FixedAmount,
      };
      if (initialValue?.value) {
        data.fixedAmount = initialValue?.value;
      }
      if (initialValue?.maxValue) {
        data.maxProductDeductibleAmount = initialValue?.maxValue;
      }
      if (initialValue?.minValue) {
        data.minProductDeductibleAmount = initialValue?.minValue;
      }
      if (
        initialValue?.valueType === ProductDeductibleAmountType.Enumeration &&
        Array.isArray(initialValue.valueList) &&
        initialValue.valueList.length > 0
      ) {
        data.fieldValue = initialValue.valueList;
      }

      form.setFieldsValue(data);
    } else {
      form.resetFields();
    }
  }, [visible, initialValue]);

  const onCancel = useCallback(() => {
    onCancelChange();
  }, [form, onCancelChange]);

  const onChangeRecordInfo = useCallback(() => {
    const values = form.getFieldsValue(true);
    const preDefineValues: PreDefineValues = {
      valueType: values.ProductDeductibleAmount,
      code: currentCode,
    };
    if (values.ProductDeductibleAmount === ProductDeductibleAmountType.FixedAmount) {
      preDefineValues.value = values.fixedAmount;
    } else if (values.ProductDeductibleAmount === ProductDeductibleAmountType.Range) {
      if (values.minProductDeductibleAmount || values.maxProductDeductibleAmount) {
        preDefineValues.minValue = values.minProductDeductibleAmount;
        preDefineValues.maxValue = values.maxProductDeductibleAmount;
      } else {
        return message.error(t('Please configure the min or max value of the range.'));
      }
    } else if (values.ProductDeductibleAmount === ProductDeductibleAmountType.Enumeration) {
      if (Array.isArray(values?.fieldValue) && values?.fieldValue?.length > 0) {
        preDefineValues.valueList = values?.fieldValue;
      } else {
        return message.error(t('Enumeration values cannot be empty.'));
      }
    }
    onCancel();
    onSubmit(preDefineValues);
  }, [currentCode, form, onCancel, onSubmit, t]);

  const checkCurrentTitle = useMemo(() => {
    if (currentType === 'product' && currentCode === 'periodStandardPremium') {
      return t('Product Premium Setting');
    }
    if (currentType === 'product' && currentCode === 'sumInsured') {
      return t('Product SA Setting');
    }
    if (currentType === 'liability' && currentCode === 'periodStandardPremium') {
      return t('Liability Premium Setting');
    }
    if (currentType === 'liability' && currentCode === 'sumInsured') {
      return t('Liability SA Setting');
    }
    if (currentType === 'interest' && currentCode === 'sumInsured') {
      return t('Insurable Interest SA Setting');
    }
  }, [currentCode, currentType, t]);

  const checkCurrentLabel = useMemo(() => {
    if (currentType === 'product' && currentCode === 'periodStandardPremium') {
      return t('Product Premium');
    }
    if (currentType === 'product' && currentCode === 'sumInsured') {
      return t('Product SA');
    }
    if (currentType === 'liability' && currentCode === 'periodStandardPremium') {
      return t('Liability Premium');
    }
    if (currentType === 'liability' && currentCode === 'sumInsured') {
      return t('Liability SA');
    }
    if (currentType === 'interest' && currentCode === 'sumInsured') {
      return t('Insurable Interest SA');
    }
  }, [currentCode, currentType, t]);

  const ProductDeductibleAmount = Form.useWatch('ProductDeductibleAmount', form);

  const ProductSASettingForm = (
    <Form form={form} layout="vertical" onFinish={onChangeRecordInfo}>
      <Row type="flex">
        <Col span={24}>
          <Form.Item
            colon={false}
            label={checkCurrentLabel}
            name="ProductDeductibleAmount"
            rules={[
              {
                required: true,
                message: t('Please select'),
              },
            ]}
          >
            <Radio.Group>
              <Radio value={ProductDeductibleAmountType.FixedAmount}>{t('Fixed Amount')}</Radio>
              <Radio value={ProductDeductibleAmountType.Range}>{t('Range')}</Radio>
              <Radio value={ProductDeductibleAmountType.Enumeration}>{t('Enumeration')}</Radio>
              {currentType === 'liability' && currentCode === 'sumInsured' ? (
                <Radio value={ProductDeductibleAmountType.ByFormula}>{t('By Formula')}</Radio>
              ) : null}
            </Radio.Group>
          </Form.Item>
        </Col>
        {ProductDeductibleAmount === ProductDeductibleAmountType.FixedAmount ? (
          <Col span={8}>
            <Form.Item
              colon={false}
              label={t('Fixed Amount')}
              name="fixedAmount"
              rules={[
                {
                  required: true,
                  message: t('Please input number'),
                },
              ]}
            >
              <InputNumber min={0} maxLength={12} style={{ width: 240 }} placeholder={t('Please input')} />
            </Form.Item>
          </Col>
        ) : null}
        {ProductDeductibleAmount === ProductDeductibleAmountType.Range ? (
          <React.Fragment>
            <Col span={24}>
              <Form.Item label={t('Amount Range')}>
                <Space.Compact block>
                  <Form.Item name="minProductDeductibleAmount" noStyle>
                    <InputNumber min={0} maxLength={12} placeholder={t('Min')} style={{ width: '45%' }} />
                  </Form.Item>
                  <Form.Item noStyle>
                    <Input
                      style={{
                        width: 30,
                        marginLeft: 1,
                        borderLeft: 0,
                        borderRight: 0,
                        pointerEvents: 'none',
                        backgroundColor: '#fff',
                      }}
                      placeholder="~"
                      disabled
                    />
                  </Form.Item>
                  <Form.Item name="maxProductDeductibleAmount" noStyle>
                    <InputNumber min={0} maxLength={12} placeholder={t('Max')} style={{ width: '45%' }} />
                  </Form.Item>
                </Space.Compact>
              </Form.Item>
            </Col>
          </React.Fragment>
        ) : null}
        {ProductDeductibleAmount === ProductDeductibleAmountType.Enumeration ? (
          <EnumerationSettingFormList label={t('Field Value')} name="fieldValue" />
        ) : null}
        {form.getFieldValue('ProductDeductibleAmount') === ProductDeductibleAmountType.ByFormula ? (
          <span className={cx('tip-box')}>
            {t('Please configure intermediate SA calculation formula in Product Center for corresponding liability.')}
          </span>
        ) : null}
      </Row>
    </Form>
  );

  return type === 'drawer' ? (
    <Drawer
      title={checkCurrentTitle}
      open={visible}
      onClose={onCancel}
      onSubmit={form.submit}
    >
      {ProductSASettingForm}
    </Drawer>
  ) : (
    <Modal
      open={visible}
      title={checkCurrentTitle}
      onOk={form.submit}
      onCancel={onCancel}
      okText={t('Save')}
      cancelText={t('Cancel')}
      closable={false}
    >
      {ProductSASettingForm}
    </Modal>
  );
};
