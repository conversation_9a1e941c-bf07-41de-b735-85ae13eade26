import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { EnumerationTableList } from '@market/common/interface';
import EditTable from '@market/components/F-EditableTable';
import { OptEnum } from '@market/request/interface';

interface Props {
  editTableData: EnumerationTableList[];
  setEditTableData: (info: EnumerationTableList[]) => void;
  getPopupContainer: () => any;
  valueType?: 'number' | 'percentage';
}

export const SettingOfEnumeration = (props: Props): JSX.Element => {
  const { setEditTableData, getPopupContainer, editTableData } = props;
  const [t] = useTranslation(['market', 'common']);

  const columns = useMemo(
    () => [
      {
        title: t('Field Value'),
        dataIndex: 'fieldValue',
        width: '740px',
        inputType: 'number',
        controllerprops: {
          style: { width: '700px' },
          placeholder: t('Please input'),
          getPopupContainer,
        },
        render: (text: string) => text,
        validate: [
          {
            trigger: 'onBlur',
            rules: [
              {
                required: true,
                message: t('Please input number'),
              },
            ],
          },
        ],
        editable: true,
      },
    ],
    [t]
  );

  return (
    <div>
      <EditTable
        newAddTop
        onSubmit={(value: EnumerationTableList, key: string) =>
          new Promise(resolve => {
            const tempTableData = [...editTableData];
            value.key = `${value.fieldValue}${Math.random()}`;
            if (key === OptEnum.Add) {
              tempTableData.push(value);
              setEditTableData(tempTableData);
            } else {
              const editIndex = tempTableData.findIndex(row => row.key === key);
              tempTableData[editIndex] = value;
              setEditTableData(tempTableData);
            }
            resolve(true);
          })
        }
        onDelete={(key: string) => {
          const tempTableData = [...editTableData];
          const deleteIndex = tempTableData.findIndex(row => row.key === key);
          tempTableData.splice(deleteIndex, 1);
          setEditTableData(tempTableData);
        }}
        onCancel={() => {
          editTableData.forEach(row => {
            row.editing = false;
          });
        }}
        deleteIconProps={{
          getPopupContainer,
        }}
        scroll={{ x: 856 }}
        columns={columns}
        data={editTableData}
      />
    </div>
  );
};

export default SettingOfEnumeration;
