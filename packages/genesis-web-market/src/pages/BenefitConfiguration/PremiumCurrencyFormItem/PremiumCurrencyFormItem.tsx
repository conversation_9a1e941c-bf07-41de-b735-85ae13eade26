import { useTranslation } from 'react-i18next';

import { Checkbox, Form } from 'antd';

import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';

interface Props {
  readOnly: boolean;
  currencyIsSingle: boolean; // 货币是否支持多选
}

export const PremiumCurrencyFormItem = ({ readOnly, currencyIsSingle }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const currencyOptions = useBizDictAsOptions('currencys');

  return (
    <Form.Item label={<span>{t('Premium Currency')}</span>} required>
      <div className="inline-flex">
        <span className="mr-8 inline-flex items-center">
          <Form.Item name="sameAsSaCurrency" noStyle valuePropName="checked">
            <Checkbox disabled={readOnly}>{t('Same as SA Currency')}</Checkbox>
          </Form.Item>
        </span>
        <Form.Item
          noStyle
          shouldUpdate={(prev: { sameAsSaCurrency: boolean }, curr) => prev.sameAsSaCurrency !== curr.sameAsSaCurrency}
        >
          {({ getFieldValue }) => {
            if (!getFieldValue('sameAsSaCurrency')) {
              return (
                <Form.Item
                  colon={false}
                  required
                  name="premiumCurrency"
                  noStyle
                  rules={[
                    {
                      required: true,
                      message: t('Please select'),
                    },
                  ]}
                >
                  <GeneralSelect
                    allowClear={false}
                    disabled={readOnly}
                    showSearch
                    mode={currencyIsSingle ? undefined : 'multiple'}
                    placeholder={t('Please select')}
                    option={currencyOptions}
                  />
                </Form.Item>
              );
            }

            return null;
          }}
        </Form.Item>
      </div>
    </Form.Item>
  );
};

export default PremiumCurrencyFormItem;
