import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';

import { Form, message } from 'antd';

import {
  ApplicableSenario,
  CustomerLoyaltyService,
  GoodsStatusType,
  VoucherBasicInfo,
  VoucherEffectiveTimeRule,
  VoucherExpiryTimeRule,
  VoucherGenerationInfo,
  VoucherUsageInfo,
} from 'genesis-web-service';
import { useL10n } from 'genesis-web-shared/lib/l10n';

import { DetailPageMode } from '@market/common/interface';
import FixHeaderLayout from '@market/components/FixHeaderLayout';
import { useChannelList } from '@market/hook/channel.serivce';
import { useGoodsList } from '@market/hook/goods.service';
import { RouteComponentProps } from '@market/router';

import { useVoucherDetail } from '../hooks';
import ApplicationInfo from './components/ApplicationInfo';
import BasicInfo from './components/BasicInfo';
import Header from './components/Header';
import RuleConfig from './components/RuleConfig';
import { GoodsPlanTableRow } from './interface';

interface Props extends RouteComponentProps {}

const VoucherDetail: React.FC<Props> = ({}): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [mode, setMode] = useState(DetailPageMode.add);
  const goodsList = useGoodsList({
    filterStatus: GoodsStatusType.Effective,
  });
  const channelList = useChannelList();
  const L10nUtil = useL10n();

  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [form] = Form.useForm();

  const { voucherResponse, voucherGenerateResponse, voucherUsageResponse, reload } = useVoucherDetail(
    searchParams.get('id')!,
    mode
  );

  useEffect(() => {
    if (voucherResponse) {
      form.setFieldsValue({
        voucherName: voucherResponse?.voucherName,
        voucherAmountType: voucherResponse.voucherAmountType && `${voucherResponse.voucherAmountType}`,
        description: voucherResponse?.description,
      });
    }
    if (voucherGenerateResponse) {
      const fieldsValue = {
        voucherAmount: voucherGenerateResponse.voucherAmount,
        rule: {
          first: voucherGenerateResponse.voucherCodePrefix.substring(0, 1),
          second: voucherGenerateResponse.voucherCodePrefix.substring(1, 2),
          third: voucherGenerateResponse.voucherCodePrefix.substring(2, 3),
        },
        EffectiveFromGroup: {
          effectiveTimeRule: voucherGenerateResponse.effectiveTimeRule
            ? `${voucherGenerateResponse.effectiveTimeRule}`
            : undefined,
          effectiveTime: voucherGenerateResponse.effectiveTime
            ? dateFormat.l10nMoment(voucherGenerateResponse.effectiveTime)
            : undefined,
        },
        EffectivePeriodGroup: {
          expiryTimeRule: voucherGenerateResponse.expiryTimeRule
            ? `${voucherGenerateResponse.expiryTimeRule}`
            : undefined,
          expiryTime: voucherGenerateResponse.expiryTime
            ? dateFormat.l10nMoment(voucherGenerateResponse.expiryTime)
            : undefined,
          effectivePeriod: voucherGenerateResponse.effectivePeriod,
          effectivePeriodUnit: voucherGenerateResponse.effectivePeriodUnit
            ? `${voucherGenerateResponse.effectivePeriodUnit}`
            : undefined,
        },
      };
      form.setFieldsValue(fieldsValue);
    }

    if (voucherUsageResponse) {
      const fieldsValue = {
        channelCodes: voucherUsageResponse.channelCodes,
        applicableBizTypes: voucherUsageResponse.applicableBizTypes
          ? voucherUsageResponse.applicableBizTypes.map(item => `${item}`)
          : undefined,
        maximumVoucherNumber: voucherUsageResponse.maximumVoucherNumber,
      };
      form.setFieldsValue(fieldsValue);
    }
  }, [voucherResponse, voucherGenerateResponse, voucherUsageResponse, form]);

  const { dateFormat } = L10nUtil.l10n;

  const [associatedGoodsAndPlans, setAssociatedGoodsAndPlans] = useState<GoodsPlanTableRow[]>([]);

  const selectedValueType = Form.useWatch('voucherAmountType', form);

  useEffect(() => {
    const state = location.state as {
      mode: DetailPageMode;
    };
    const propsMode = state?.mode || DetailPageMode.view;
    setMode(propsMode);
  }, []);

  useEffect(() => {
    if (voucherUsageResponse) {
      const tempGoodsAndPlans: GoodsPlanTableRow[] = [];
      Object.keys(voucherUsageResponse.goodsPlansRelationship).map(goodsId => {
        tempGoodsAndPlans.push({
          goodsId,
          planIds: voucherUsageResponse.goodsPlansRelationship[goodsId],
        });
      });

      setAssociatedGoodsAndPlans(tempGoodsAndPlans);
    }
  }, [voucherUsageResponse]);

  const onSave = useCallback(
    async (isSubmit: boolean) => {
      if (associatedGoodsAndPlans.length === 0) {
        message.error(t('Please associate goods and plan.'));
        return;
      }
      try {
        const values = await form.validateFields();

        if (
          values.EffectiveFromGroup.effectiveTime &&
          values.EffectivePeriodGroup?.expiryTimeRule === VoucherExpiryTimeRule.FixedDate
        ) {
          if (
            !values.EffectiveFromGroup.effectiveTime
              .endOf('day')
              .isSameOrBefore(values.EffectivePeriodGroup?.expiryTime?.endOf('day'))
          ) {
            message.error(t('Effective end date cannot be earlier than the start date.'));
            return;
          }
        }
        const basicInfo: VoucherBasicInfo = {
          voucherName: values.voucherName,
          voucherAmountType: values.voucherAmountType,
          description: values.description,
          id: voucherResponse?.id,
        };

        const generateInfo: VoucherGenerationInfo = {
          voucherCodePrefix: `${values.rule.first}${values.rule.second}${values.rule.third}`,
          voucherAmount: values.voucherAmount,
          effectiveTimeRule: values.EffectiveFromGroup.effectiveTimeRule!,
          effectiveTime:
            values.EffectiveFromGroup.effectiveTimeRule === VoucherEffectiveTimeRule.FixedDate
              ? dateFormat.formatTz(values.EffectiveFromGroup.effectiveTime?.startOf('day'))!
              : undefined,
          expiryTimeRule: values.EffectivePeriodGroup.expiryTimeRule!,
          id: voucherGenerateResponse?.id,
        };

        if (values.EffectivePeriodGroup.expiryTimeRule === VoucherExpiryTimeRule.FixedDate) {
          generateInfo.expiryTime = dateFormat.formatTz(
            values.EffectivePeriodGroup.expiryTime?.endOf('day').set('milliseconds', 0)
          )!;
        } else if (values.EffectivePeriodGroup.expiryTimeRule === VoucherExpiryTimeRule.CertainPeriod) {
          generateInfo.effectivePeriod = values.EffectivePeriodGroup.effectivePeriod;
          generateInfo.effectivePeriodUnit = values.EffectivePeriodGroup.effectivePeriodUnit;
        }

        const goodsIds: number[] = [];
        const planIds: number[] = [];
        const goodsPlansRelationship: Record<string, number[]> = {};
        associatedGoodsAndPlans.forEach(goods => {
          goodsIds.push(+goods.goodsId);
          const goodsPlanIdList = goods.planIds.map(planId => +planId);
          planIds.push(...goodsPlanIdList);
          goodsPlansRelationship[goods.goodsId] = goodsPlanIdList;
        });

        const usageInfo: VoucherUsageInfo = {
          channelCodes: values.channelCodes,
          applicableBizTypes: values.applicableBizTypes as ApplicableSenario[],
          maximumVoucherNumber: values.maximumVoucherNumber,
          goodsIds,
          planIds,
          goodsPlansRelationship,
          whetherToKeepBalance: 2, // 该字段无法编辑，固定为No
          id: voucherUsageResponse?.id,
        };

        CustomerLoyaltyService.saveVoucher({
          voucherRequest: basicInfo,
          voucherGenerateRequest: generateInfo,
          voucherUsageRequest: usageInfo,
          isSubmit,
        }).then(
          id => {
            if (isSubmit) {
              message.success(t('Submit Successfully'));
            } else {
              message.success(t('Save Successfully'));
            }
            if (searchParams.get('id')! === `${id}`) {
              reload();
            }
            searchParams.set('id', `${id}`);
            navigate(`${location.pathname}?${searchParams.toString()}`);
            setMode(DetailPageMode.edit);
          },
          (error: Error) => {
            message.error(error.message);
          }
        );
      } catch (error) {
        console.error(error);
      }
    },
    [
      associatedGoodsAndPlans,
      dateFormat,
      form,
      reload,
      voucherGenerateResponse,
      voucherResponse,
      voucherUsageResponse,
      t,
    ]
  );

  return (
    <div
      style={{
        paddingBottom: 0,
      }}
    >
      <FixHeaderLayout
        header={
          <Header
            mode={mode}
            status={voucherResponse?.status}
            onSave={() => onSave(false)}
            onSubmit={() => onSave(true)}
            onEdit={() => {
              setMode(DetailPageMode.edit);
            }}
            backPath="/market/customer-loyalty/voucher-management"
          />
        }
        content={
          <div>
            <BasicInfo form={form} mode={mode} />
            <RuleConfig form={form} selectedValueType={selectedValueType} mode={mode} dateFormat={dateFormat} />
            <ApplicationInfo
              form={form}
              mode={mode}
              goodsList={goodsList}
              channelList={channelList}
              associatedGoodsAndPlans={associatedGoodsAndPlans}
              setAssociatedGoodsAndPlans={setAssociatedGoodsAndPlans}
            />
          </div>
        }
      />
    </div>
  );
};

export default VoucherDetail;
