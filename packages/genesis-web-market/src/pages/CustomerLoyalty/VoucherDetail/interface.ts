import type { VoucherAmountType } from 'genesis-web-service';

import type { EffectiveFromFieldGroup } from './components/EffectiveFromGroup/EffectiveFromGroup';
import type { EffectivePeriodFieldGroup } from './components/EffectivePeriodGroup/EffectivePeriodGroup';

export interface GoodsPlanTableRow {
  goodsId: number | string;
  planIds: (number | string)[];
}

export interface VoucherDetailFormValue {
  voucherName: string;
  voucherAmountType: VoucherAmountType;
  description: string;
  voucherAmount?: number;
  maximumVoucherNumber: number;
  channelCodes: string[];
  applicableBizTypes: string[];
  rule: {
    first: string;
    second: string;
    third: string;
  };
  EffectiveFromGroup: EffectiveFromFieldGroup;
  EffectivePeriodGroup: EffectivePeriodFieldGroup;
}
