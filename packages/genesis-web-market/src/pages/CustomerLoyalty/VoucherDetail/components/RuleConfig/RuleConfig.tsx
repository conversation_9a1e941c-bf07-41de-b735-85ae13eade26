import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, InputNumber, Row } from 'antd';
import { FormInstance } from 'antd/lib';
import classNames from 'classnames/bind';
import { Moment } from 'moment';

import { VoucherAmountType, VoucherGenerationInfo } from 'genesis-web-service';
import { DateFormat } from 'genesis-web-shared/lib/l10n';

import { DetailPageMode } from '@market/common/interface';
import SectionContainer from '@market/pages/CustomerLoyalty/components/SectionContainer';

import EffectiveFromGroup from '../EffectiveFromGroup';
import { EffectiveFromFieldGroup } from '../EffectiveFromGroup/EffectiveFromGroup';
import EffectivePeriodGroup from '../EffectivePeriodGroup';
import VoucherNumberGenerationRule from '../VoucherNumberGenerationRule';
import { RuleCode } from '../VoucherNumberGenerationRule/VoucherNumberGenerationRule';
import styles from './RuleConfig.module.scss';

const cx = classNames.bind(styles);

interface Props {
  form: FormInstance;
  mode: DetailPageMode;
  selectedValueType?: VoucherAmountType;
  dateFormat: DateFormat;
}

export const RuleConfig = ({ form, mode, selectedValueType, dateFormat }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const readOnly = mode === DetailPageMode.view;

  const faceAmountPreFix = useMemo(() => {
    if (!selectedValueType) {
      return ' ';
    }

    const mapping: Record<VoucherAmountType, string> = {
      [VoucherAmountType.VariableFaceAmount]: t('Variable Amount'),
      [VoucherAmountType.FixedFaceAmount]: t('Fixed Amount'),
      [VoucherAmountType.LimitedFaceAmount]: t('Maximum Amount'),
    };

    return mapping[selectedValueType] || ' ';
  }, [selectedValueType, t]);

  const isVariableAmount = selectedValueType === VoucherAmountType.VariableFaceAmount;
  const effectiveFromGroupValue = Form.useWatch('EffectiveFromGroup', form);

  return (
    <SectionContainer className={cx('rule-config')} title={t('Voucher Calculation & Effective Rule')}>
      <Form layout="vertical" form={form}>
        <Row type="flex">
          <Col span={24}>
            {isVariableAmount ? (
              <Form.Item label={t('Voucher Face Amount')}>
                <InputNumber
                  addonBefore={faceAmountPreFix}
                  placeholder={t('- -')}
                  style={{ width: 132 }}
                  min={0}
                  disabled
                />
              </Form.Item>
            ) : (
              <Form.Item
                label={t('Voucher Face Amount')}
                name="voucherAmount"
                rules={[
                  {
                    required: true,
                    message: t('Please input'),
                  },
                  {
                    validator(rule, value, callback) {
                      if (value === 0) {
                        callback(t('The input number cannot be zero'));
                      }
                      callback();
                    },
                  },
                ]}
              >
                <InputNumber
                  addonBefore={faceAmountPreFix}
                  placeholder={t('Please input')}
                  style={{ width: 220 }}
                  min={0}
                  disabled={readOnly}
                />
              </Form.Item>
            )}
          </Col>
          <Col span={24}>
            <Form.Item
              label={t('Voucher Number Generation Rule')}
              name="rule"
              rules={[
                {
                  required: true,
                  message: t('Please input'),
                },
                {
                  validator(rule, value: RuleCode = {}, callback) {
                    if (!value.first || !value.second || !value.third) {
                      return callback(t('Please input'));
                    }

                    if (
                      !/[a-zA-Z]/.test(value.first) ||
                      !/[a-zA-Z]/.test(value.second) ||
                      !/[a-zA-Z]/.test(value.third)
                    ) {
                      return callback(t('Please enter English letters'));
                    }
                    return callback();
                  },
                },
              ]}
            >
              <VoucherNumberGenerationRule disabled={readOnly} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label={t('Effective From')}
              name="EffectiveFromGroup"
              rules={[
                {
                  required: true,
                  message: t('Please input'),
                },
              ]}
            >
              <EffectiveFromGroup disabled={readOnly} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label={t('Effective Period')}
              name="EffectivePeriodGroup"
              rules={[
                {
                  required: true,
                  message: t('Please input'),
                },
              ]}
            >
              <EffectivePeriodGroup
                effectiveTime={(effectiveFromGroupValue as EffectiveFromFieldGroup)?.effectiveTime as Moment}
                disabled={readOnly}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </SectionContainer>
  );
};

export default RuleConfig;
