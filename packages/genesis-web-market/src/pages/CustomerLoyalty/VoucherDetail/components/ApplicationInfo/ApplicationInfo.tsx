import { useTranslation } from 'react-i18next';

import { Col, Form, InputNumber, Row, Switch } from 'antd';
import { FormInstance } from 'antd/lib';
import classNames from 'classnames/bind';

import { ApplicableSenario, GoodsInfo, QueryChannelResult, VoucherUsageInfo } from 'genesis-web-service';

import { DetailPageMode } from '@market/common/interface';
import GeneralSelect from '@market/components/GeneralSelect';
import LabelWithTooltip from '@market/components/LabelWithTooltip';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import SectionContainer from '@market/pages/CustomerLoyalty/components/SectionContainer';
import { useRenamedBizTypeOptions } from '@market/pages/CustomerLoyalty/hooks';

import { GoodsPlanTableRow } from '../../interface';
import GoodsPlanEditTable from '../GoodsPlanEditTable';
import styles from './ApplicationInfo.module.scss';

const cx = classNames.bind(styles);

interface Props {
  form: FormInstance;
  mode: DetailPageMode;
  goodsList: GoodsInfo[];
  channelList: QueryChannelResult[];
  associatedGoodsAndPlans: GoodsPlanTableRow[];
  setAssociatedGoodsAndPlans: (data: GoodsPlanTableRow[]) => void;
}

export const ApplicationInfo = ({
  form,
  mode,
  goodsList,
  channelList,
  associatedGoodsAndPlans,
  setAssociatedGoodsAndPlans,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const readOnly = mode === DetailPageMode.view;

  /* ============== 枚举使用start ============== */
  const bizTypeOptions = useBizDictAsOptions('bizType');
  /* ============== 枚举使用end ============== */

  const reNamedBizTypeOptions = useRenamedBizTypeOptions(bizTypeOptions);

  return (
    <SectionContainer className={cx('application-info')} title={t('Application Information')}>
      <GoodsPlanEditTable
        disabled={readOnly}
        goodsList={goodsList}
        associatedGoodsAndPlans={associatedGoodsAndPlans}
        setAssociatedGoodsAndPlans={setAssociatedGoodsAndPlans}
      />
      <Form layout="vertical" form={form}>
        <Row>
          <Col span={24}>
            <Form.Item
              label={t('Voucher Application Channel')}
              name="channelCodes"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <GeneralSelect
                placeholder={t('Please select')}
                style={{ width: 580 }}
                option={channelList.map(channel => ({
                  value: channel.code,
                  label: channel.name,
                }))}
                mode="multiple"
                disabled={readOnly}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label={t('Applicable Scenario')}
              name="applicableBizTypes"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <GeneralSelect
                placeholder={t('Please select')}
                style={{ width: 580 }}
                option={reNamedBizTypeOptions.filter(
                  option => option.value === ApplicableSenario.NB || option.value === ApplicableSenario.RENEWAL
                )}
                mode="multiple"
                allowClear={false}
                disabled={readOnly}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label={
                <LabelWithTooltip
                  title={t('Maximum Voucher Numbers')}
                  tooltip={t('Maximum Voucher numbers can be used in one-time')}
                />
              }
              name="maximumVoucherNumber"
              rules={[
                {
                  required: true,
                  message: t('Please input'),
                },
              ]}
            >
              <InputNumber
                placeholder={t('Please input')}
                style={{ width: 160 }}
                min={1}
                step={1}
                disabled={readOnly}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <div>
              {t('Whether to keep the voucher balance')}
              <Switch style={{ marginLeft: 8 }} size="small" disabled />
            </div>
          </Col>
        </Row>
      </Form>
    </SectionContainer>
  );
};

export default ApplicationInfo;
