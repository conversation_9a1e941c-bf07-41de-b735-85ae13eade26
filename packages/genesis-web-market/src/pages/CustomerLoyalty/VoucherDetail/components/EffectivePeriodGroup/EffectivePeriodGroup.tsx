import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { DatePicker, Input, InputNumber } from 'antd';
import classNames from 'classnames/bind';
import type { Moment } from 'moment';

import { FreelookPeriodUnitType, VoucherExpiryTimeRule } from 'genesis-web-service';

import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';

import styles from './EffectivePeriodGroup.module.scss';

const cx = classNames.bind(styles);

export interface CertainPeriodConfig {
  expiryTimeRule?: VoucherExpiryTimeRule.CertainPeriod;
  effectivePeriod?: number;
  effectivePeriodUnit?: string;
}

export interface FixedDateConfig {
  expiryTimeRule?: VoucherExpiryTimeRule.FixedDate;
  expiryTime?: Moment;
}

export type EffectivePeriodFieldGroup = CertainPeriodConfig | FixedDateConfig;

interface Props {
  value?: EffectivePeriodFieldGroup;
  disabled?: boolean;
  effectiveTime?: Moment;
  onChange?: (val: EffectivePeriodFieldGroup) => void;
}

export const EffectivePeriodGroup = ({ value = {}, effectiveTime, disabled, onChange }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  /* ============== 枚举使用start ============== */
  const voucherExpiryTimeRuleOptions = useBizDictAsOptions('voucherExpiryTimeRule');
  const freelookPeriodUnitOptions = useBizDictAsOptions('freelookPeriodUnit');
  /* ============== 枚举使用end ============== */

  const triggerChange = useCallback(
    (changedValue: any, fieldKey: keyof CertainPeriodConfig | keyof FixedDateConfig) => {
      if (!onChange) {
        return;
      }
      if (fieldKey === 'expiryTimeRule') {
        onChange({
          [fieldKey]: changedValue,
        });
      } else {
        onChange({
          ...value,
          [fieldKey]: changedValue,
        });
      }
    },
    [onChange, value]
  );

  const renderExpiryForm = useCallback(() => {
    if (value?.expiryTimeRule === VoucherExpiryTimeRule.FixedDate) {
      return (
        <DatePicker
          style={{ width: 240, marginLeft: 8 }}
          value={value?.expiryTime}
          onChange={_value => {
            triggerChange(_value, 'expiryTime');
          }}
          disabledDate={current => (current && effectiveTime ? current.isBefore(effectiveTime) : false)}
          disabled={disabled}
          placeholder={t('Please select')}
        />
      );
    }

    return (
      <Input.Group style={{ width: 240, marginLeft: 8 }} compact>
        <InputNumber
          placeholder={t('Please input')}
          onChange={_value => {
            triggerChange(_value, 'effectivePeriod');
          }}
          value={(value as CertainPeriodConfig)?.effectivePeriod}
          style={{ width: 110 }}
          min={1}
          step={1}
          disabled={disabled}
        />
        <GeneralSelect
          option={(freelookPeriodUnitOptions || []).filter(
            optionItem => optionItem.value !== FreelookPeriodUnitType.Hour
          )}
          onChange={_value => {
            triggerChange(_value, 'effectivePeriodUnit');
          }}
          value={(value as CertainPeriodConfig)?.effectivePeriodUnit}
          className={cx('afteron-select')}
          allowClear={false}
          disabled={disabled}
        />
      </Input.Group>
    );
  }, [value, t, disabled, freelookPeriodUnitOptions, triggerChange, effectiveTime]);

  return (
    <div style={{ display: 'flex' }}>
      <GeneralSelect
        option={voucherExpiryTimeRuleOptions}
        value={value?.expiryTimeRule}
        onChange={_value => {
          triggerChange(_value, 'expiryTimeRule');
        }}
        disabled={disabled}
      />
      {renderExpiryForm()}
    </div>
  );
};

export default EffectivePeriodGroup;
