import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { DatePicker } from 'antd';

import type { Moment } from 'moment';

import { VoucherEffectiveTimeRule } from 'genesis-web-service';

import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';

export interface EffectiveFromFieldGroup {
  effectiveTimeRule?: VoucherEffectiveTimeRule;
  effectiveTime?: Moment;
}

interface Props {
  value?: EffectiveFromFieldGroup;
  disabled?: boolean;
  onChange?: (val: EffectiveFromFieldGroup) => void;
}

export const EffectiveFromGroup = ({ value, disabled, onChange }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  /* ============== 枚举使用start ============== */
  const voucherEffectiveTimeRuleOptions = useBizDictAsOptions('voucherEffectiveTimeRule');
  /* ============== 枚举使用end ============== */

  const triggerChange = useCallback(
    (changedValue: any, fieldKey: keyof EffectiveFromFieldGroup) => {
      if (onChange) {
        if (fieldKey === 'effectiveTimeRule') {
          onChange({
            [fieldKey]: changedValue,
          });
        } else {
          onChange({
            ...value,
            [fieldKey]: changedValue,
          });
        }
      }
    },
    [onChange, value]
  );

  return (
    <div>
      <GeneralSelect
        option={voucherEffectiveTimeRuleOptions}
        allowClear={false}
        value={value?.effectiveTimeRule}
        onChange={_value => {
          triggerChange(_value, 'effectiveTimeRule');
        }}
        disabled={disabled}
      />
      <DatePicker
        disabled={value?.effectiveTimeRule === VoucherEffectiveTimeRule.SameAsGenerationDate || disabled}
        style={{ width: 240, marginLeft: 8 }}
        value={value?.effectiveTime}
        onChange={_value => {
          triggerChange(_value, 'effectiveTime');
        }}
        placeholder={t('Please select')}
      />
    </div>
  );
};

export default EffectiveFromGroup;
