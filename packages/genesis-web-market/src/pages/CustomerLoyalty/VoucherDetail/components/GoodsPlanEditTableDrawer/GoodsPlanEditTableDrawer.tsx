import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
import { Checkbox, Input } from 'antd';

import classNames from 'classnames/bind';
import { debounce } from 'lodash-es';

import { Drawer, Icon } from '@zhongan/nagrand-ui';

import { CoveragePlan, GoodsInfo } from 'genesis-web-service';

import noDataImg from '@market/asset/img/customer-loyalty/no_data.png';
import noSearchResultImg from '@market/asset/img/customer-loyalty/no_search_result.png';

import { GoodsPlanTableRow } from '../../interface';
import styles from './GoodsPlanEditTableDrawer.module.scss';

const cx = classNames.bind(styles);

interface Props {
  goodsList: GoodsInfo[];
  visible: boolean;
  onSubmit: (
    selection: Record<
      string,
      {
        selected: boolean;
        planSeletion: Record<string, boolean>;
      }
    >
  ) => void;
  closeDrawer: () => void;
  initValue: GoodsPlanTableRow[];
}

export const GoodsPlanEditTableDrawer = ({
  goodsList,
  visible,
  initValue,
  onSubmit,
  closeDrawer,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [searchInputValue, setSearchInputValue] = useState('');
  const [searchText, setSearchText] = useState('');
  const [searchResult, setSearchResult] = useState<GoodsInfo[]>([]);
  const [goodsExpandedMap, setGoodsExpandedMap] = useState<Record<string, boolean>>({});
  const [selectedGoodsPlan, setSelectedGoodsPlan] = useState<
    Record<
      string,
      {
        selected: boolean;
        planSeletion: Record<string, boolean>;
      }
    >
  >({});

  useEffect(() => {
    if (initValue?.length > 0) {
      const tempSelection: Record<
        string,
        {
          selected: boolean;
          planSeletion: Record<string, boolean>;
        }
      > = {};

      initValue.forEach(row => {
        const planSeletion: Record<string, boolean> = row.planIds.reduce(
          (prev, current) => ({ ...prev, [current]: true }),
          {}
        );
        tempSelection[row.goodsId] = {
          selected: true,
          planSeletion,
        };
      });

      setSelectedGoodsPlan(tempSelection);
    } else {
      setSelectedGoodsPlan({});
    }
  }, [visible]);

  const searchGoods = useCallback(
    (goodsName: string) => {
      if (!goodsName) {
        setSearchResult([]);
      } else {
        setSearchResult(goodsList.filter(goods => goods.goodsBasicInfo.goodsName.includes(goodsName)));
      }
      setSearchText(goodsName);
    },
    [goodsList]
  );

  const onInputChange = debounce((event: React.ChangeEvent<HTMLInputElement>) => {
    searchGoods(event.target.value);
  }, 300);

  const onGoodsChecked = useCallback(
    (checked: boolean, goodsId: number, coveragePlans: CoveragePlan[]) => {
      if (checked) {
        const tempPlanSeletion = coveragePlans
          .map(plan => plan.planId)
          .reduce(
            (previousValue, currentValue) => ({
              ...previousValue,
              [currentValue]: true,
            }),
            {}
          );
        setSelectedGoodsPlan({
          ...selectedGoodsPlan,
          [goodsId]: {
            selected: true,
            planSeletion: tempPlanSeletion,
          },
        });
      } else {
        setSelectedGoodsPlan({
          ...selectedGoodsPlan,
          [goodsId]: undefined,
        });
      }
    },
    [selectedGoodsPlan]
  );

  const onPlanChecked = useCallback(
    (checked: boolean, goodsId: number, planId: number, planSeletion: Record<string, boolean>) => {
      if (checked) {
        setSelectedGoodsPlan({
          ...selectedGoodsPlan,
          [goodsId]: {
            selected: true,
            planSeletion: {
              ...planSeletion,
              [planId]: true,
            },
          },
        });
      } else {
        const planSelectionAfterUnCheck = {
          ...planSeletion,
          [planId]: false,
        };
        if (Object.values(planSelectionAfterUnCheck).filter(Boolean).length === 0) {
          setSelectedGoodsPlan({
            ...selectedGoodsPlan,
            [goodsId]: {
              selected: false,
            },
          });
        } else {
          setSelectedGoodsPlan({
            ...selectedGoodsPlan,
            [goodsId]: {
              selected: true,
              planSeletion: {
                ...planSeletion,
                [planId]: false,
              },
            },
          });
        }
      }
    },
    [selectedGoodsPlan]
  );

  const renderGoodsSection = useCallback(
    (searchResultGoods: GoodsInfo) => {
      const { goodsBasicInfo, coveragePlans } = searchResultGoods;
      const isGoodsExpand = goodsExpandedMap[goodsBasicInfo.goodsId];
      const planSeletion = selectedGoodsPlan[goodsBasicInfo.goodsId]?.planSeletion || {};
      const allPlanLength = coveragePlans?.length || 0;
      const selectedPlanLength = Object.values(planSeletion).filter(Boolean).length;

      return (
        <div>
          <div className={cx('goods-content-list-item')}>
            <div className={cx('checkbox-wrapper-level-1')}>
              {isGoodsExpand ? (
                <CaretUpOutlined
                  onClick={() => {
                    setGoodsExpandedMap({
                      ...goodsExpandedMap,
                      [goodsBasicInfo.goodsId]: false,
                    });
                  }}
                />
              ) : (
                <CaretDownOutlined
                  onClick={() => {
                    setGoodsExpandedMap({
                      ...goodsExpandedMap,
                      [goodsBasicInfo.goodsId]: true,
                    });
                  }}
                />
              )}
              <Checkbox
                indeterminate={selectedPlanLength > 0 && selectedPlanLength !== allPlanLength}
                checked={selectedGoodsPlan[goodsBasicInfo.goodsId]?.selected}
                onChange={event => {
                  onGoodsChecked(event.target.checked, goodsBasicInfo.goodsId, coveragePlans);
                }}
              />
            </div>
            <div className={cx('goods-name')}>{goodsBasicInfo.goodsName}</div>
            <div className={cx('selected-percent')}>
              ({selectedPlanLength} / {allPlanLength})
            </div>
          </div>
          {isGoodsExpand
            ? coveragePlans.map(plan => (
                <div className={cx('plan-content-list-item')}>
                  <div className={cx('checkbox-wrapper-level-2')}>
                    <span />
                    <Checkbox
                      disabled={!selectedGoodsPlan[goodsBasicInfo.goodsId]?.selected}
                      checked={planSeletion?.[plan.planId]}
                      onChange={event => {
                        onPlanChecked(event.target.checked, goodsBasicInfo.goodsId, plan.planId, planSeletion);
                      }}
                    />
                  </div>
                  <div className={cx('plan-name')}>{plan.goodsPlanName}</div>
                </div>
              ))
            : null}
        </div>
      );
    },
    [goodsExpandedMap, onGoodsChecked, onPlanChecked, selectedGoodsPlan]
  );

  const onClose = useCallback(() => {
    setSelectedGoodsPlan({});
    setGoodsExpandedMap({});
    setSearchResult([]);
    setSearchText('');
    setSearchInputValue('');
    closeDrawer();
  }, [closeDrawer]);

  const submit = useCallback(() => {
    onSubmit(selectedGoodsPlan);
    onClose();
  }, [onClose, onSubmit, selectedGoodsPlan]);

  return (
    <Drawer
      title={t('Associated Goods and Plans')}
      open={visible}
      size="small"
      onClose={onClose}
      onSubmit={submit}
    >
      <div className={cx('goods-plan-associate-drawer')}>
        <div className={cx('search-input-wrapper')}>
          <Input
            prefix={<Icon type="search-icon" className={cx('search-icon')} />}
            placeholder={t('Search Goods Name')}
            onPressEnter={event => {
              searchGoods((event.target as HTMLInputElement).value);
            }}
            value={searchInputValue}
            onChange={event => {
              setSearchInputValue(event.target.value);
              onInputChange(event);
            }}
            allowClear
          />
        </div>
        {!searchText && searchResult.length === 0 ? (
          <div className={cx('placeholder-section')}>
            <img style={{ width: '100%' }} src={noDataImg} alt="no data" />
          </div>
        ) : null}
        {searchText && searchResult.length === 0 ? (
          <div className={cx('no-search-result-section')}>
            <div style={{ textAlign: 'center' }}>
              <img style={{ width: 80 }} src={noSearchResultImg} alt="no data" />
              <div className={cx('msg')}>{t('Sorry no goods were found')}</div>
            </div>
          </div>
        ) : null}
        {searchResult.length > 0 ? (
          <div className={cx('goods-section-wrapper')}>
            {searchResult.map(searchResultGoods => renderGoodsSection(searchResultGoods))}
          </div>
        ) : null}
      </div>
    </Drawer>
  );
};

export default GoodsPlanEditTableDrawer;
