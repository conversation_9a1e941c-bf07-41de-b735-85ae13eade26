.goods-plan-associate-drawer {
  .search-input-wrapper {
    border: 1px solid var(--border-light);
    padding: 10px;
  }

  .checkbox-wrapper-level-1 {
    width: 60px;
    padding: 8px 12px;
    line-height: 16px;
    display: flex;
    justify-content: space-between;
  }

  .select-all-section {
    border: 1px solid var(--border-light);
    border-top: none;
    display: flex;
    height: 32px;
    line-height: 32px;

    .text {
      color: var(--text-color);
      font-weight: 700;
      width: 344px;
    }
  }

  .goods-section-wrapper {
    height: 500px;
    overflow: auto;
    border-right: 1px solid var(--border-light);
    border-left: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);

    & > div:first-child > .goods-content-list-item {
      border-top: none;
    }

    .goods-content-list-item {
      border: 1px solid var(--border-light);
      border-left: none;
      border-bottom: none;
      display: flex;
      height: 40px;
      line-height: 40px;

      .checkbox-wrapper-level-1 {
        padding: 12px;
        line-height: 16px;
      }

      .goods-name {
        color: var(--text-color);
        font-weight: 700;
        width: 344px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .selected-percent {
        margin-left: 10px;
        color: var(--table-header-bg);
      }
    }
  }

  .plan-content-list-item {
    border: 1px solid var(--border-light);
    border-top: none;
    display: flex;
    height: 40px;
    line-height: 40px;

    .checkbox-wrapper-level-2 {
      padding: 12px;
      line-height: 16px;
      width: 80px;
      display: flex;
      justify-content: space-between;
    }

    .plan-name {
      color: var(--text-color);
      width: 396px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .placeholder-section {
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .no-search-result-section {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    width: 480px;
    height: 200px;
    background: var(--white);
    box-shadow: 0px 8px 16px var(--disabled-bg);
    border-radius: 8px;

    .msg {
      margin-top: 10px;
      color: var(--text-color-tertiary);
    }
  }
}
:global {
  .anticon.anticon-search.search-icon {
    color: var(--disabled-color) !important;
  }
  .anticon.anticon-caret-up {
    color: var(--text-color-secondary);
    cursor: pointer;
  }
  .anticon.anticon-caret-down {
    color: var(--text-color-secondary);
    cursor: pointer;
  }
}
