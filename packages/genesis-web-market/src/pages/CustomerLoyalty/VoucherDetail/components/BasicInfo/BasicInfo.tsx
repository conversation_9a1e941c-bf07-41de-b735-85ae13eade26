import { useTranslation } from 'react-i18next';

import { Col, Form, Input, Row } from 'antd';
import { FormInstance } from 'antd/lib';

import classNames from 'classnames/bind';

import { DetailPageMode } from '@market/common/interface';
import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import SectionContainer from '@market/pages/CustomerLoyalty/components/SectionContainer';

import styles from './BasicInfo.module.scss';

const cx = classNames.bind(styles);

interface Props {
  form: FormInstance;
  mode: DetailPageMode;
}

export const BasicInfo = ({ form, mode }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  /* ============== 枚举使用start ============== */
  const voucherAmountTypeOptions = useBizDictAsOptions('voucherAmountType');
  /* ============== 枚举使用end ============== */
  const readOnly = mode === DetailPageMode.view;

  return (
    <SectionContainer className={cx('basic-info')} title={t('Basic Information')}>
      <Form layout="vertical" form={form}>
        <Row>
          <Col span={7}>
            <Form.Item
              label={t('Voucher Name')}
              name="voucherName"
              rules={[
                {
                  required: true,
                  message: t('Please input'),
                },
              ]}
            >
              <Input maxLength={50} placeholder={t('Please input')} disabled={readOnly} />
            </Form.Item>
          </Col>
          <Col span={7}>
            <Form.Item
              label={t('Voucher Value Type')}
              name="voucherAmountType"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <GeneralSelect
                placeholder={t('Please select')}
                disabled={readOnly}
                option={voucherAmountTypeOptions}
                allowClear={false}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label={t('Description')} name="description">
              <Input.TextArea
                placeholder={t('Please input')}
                disabled={readOnly}
                maxLength={100}
                rows={4}
                count={{
                  show: mode !== DetailPageMode.view,
                  max: 100,
                }}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </SectionContainer>
  );
};

export default BasicInfo;
