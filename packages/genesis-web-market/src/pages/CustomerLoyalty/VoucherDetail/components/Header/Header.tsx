import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from 'antd';

import classNames from 'classnames/bind';

import { Icon } from '@zhongan/nagrand-ui';

import { useNavigateBack } from 'genesis-web-component/lib/hook/router';
import { VoucherStatus } from 'genesis-web-service';

import { DetailPageMode } from '@market/common/interface';
import VoucherStatusSpan from '@market/pages/CustomerLoyalty/components/VoucherStatus';

import styles from './Header.module.scss';

const cx = classNames.bind(styles);

interface Props {
  mode: DetailPageMode;
  status?: VoucherStatus;
  onSave: () => void;
  onSubmit: () => void;
  backPath: string;
  onEdit: () => void;
}

export const Header = ({
  mode,
  status = VoucherStatus.Draft,
  onEdit,
  onSave,
  onSubmit,
  backPath,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [handleBack] = useNavigateBack(backPath ?? '/');

  const renderTitleByMode = useCallback((): string => {
    switch (mode) {
      case DetailPageMode.add:
        return t('Add New Voucher');
      case DetailPageMode.edit:
        return t('Edit Voucher');
      case DetailPageMode.view:
        return t('View Voucher');
      default:
        return t('Add New Voucher');
    }
  }, [mode, t]);

  return (
    <div className={cx('header-container')}>
      <div>
        <Button onClick={() => handleBack()} icon={<Icon type="left" />}>
          {t('Back to Search')}
        </Button>
        {mode === DetailPageMode.view ? (
          <Button onClick={onEdit} style={{ float: 'right' }}>
            {t('Edit')}
          </Button>
        ) : (
          <React.Fragment>
            <Button onClick={onSubmit} style={{ float: 'right', marginLeft: 16 }} type="primary">
              {t('Submit')}
            </Button>
            <Button onClick={onSave} style={{ float: 'right' }}>
              {t('Save')}
            </Button>
          </React.Fragment>
        )}
      </div>
      <div className={cx('title')}>{renderTitleByMode()}</div>
      <VoucherStatusSpan status={status} />
    </div>
  );
};

export default Header;
