import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Input } from 'antd';

import classNames from 'classnames/bind';

import styles from './VoucherNumberGenerationRule.module.scss';

const cx = classNames.bind(styles);

export interface RuleCode {
  first?: string;
  second?: string;
  third?: string;
}

interface Props {
  value?: RuleCode;
  onChange?: (val: RuleCode) => void;
  disabled?: boolean;
}

export const VoucherNumberGenerationRule = ({ value, onChange, disabled }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  const triggerChange = useCallback(
    (changedValue: string, fieldKey: keyof RuleCode) => {
      if (onChange) {
        onChange({
          ...value,
          [fieldKey]: changedValue,
        });
      }
    },
    [onChange, value]
  );

  return (
    <div className={cx('voucher-number-generation-rule')}>
      <div>
        <div>
          <Input
            className={cx('input-style', {
              valid: value?.first && /[a-zA-Z]/.test(value.first),
              empty: value?.first === undefined,
            })}
            value={value?.first}
            maxLength={1}
            onChange={event => {
              triggerChange(event.target.value, 'first');
            }}
            size="large"
            disabled={disabled}
          />
          <Input
            className={cx('input-style', {
              valid: value?.second && /[a-zA-Z]/.test(value.second),
              empty: value?.second === undefined,
            })}
            value={value?.second}
            maxLength={1}
            onChange={event => {
              triggerChange(event.target.value, 'second');
            }}
            size="large"
            disabled={disabled}
          />
          <Input
            className={cx('input-style', {
              valid: value?.third && /[a-zA-Z]/.test(value.third),
              empty: value?.third === undefined,
            })}
            value={value?.third}
            maxLength={1}
            onChange={event => {
              triggerChange(event.target.value, 'third');
            }}
            size="large"
            disabled={disabled}
          />
        </div>
        <div
          className={cx('bottom-line')}
          style={{
            width: 55,
          }}
        />
        <div className={cx('bottom-tips')}>{t('User Input')}</div>
      </div>
      <div style={{ marginLeft: 24 }}>
        <div>
          <span className={cx('fixed-rule-format')}>Y</span>
          <span className={cx('fixed-rule-format')}>Y</span>
          <span className={cx('fixed-rule-format')}>Y</span>
          <span className={cx('fixed-rule-format')}>Y</span>
          <span className={cx('fixed-rule-format')}>M</span>
          <span className={cx('fixed-rule-format')}>M</span>
          <span className={cx('fixed-rule-format')}>D</span>
          <span className={cx('fixed-rule-format')}>D</span>
          <span className={cx('fixed-rule-format')}>h</span>
          <span className={cx('fixed-rule-format')}>h</span>
          <span className={cx('fixed-rule-format')}>m</span>
          <span className={cx('fixed-rule-format')}>m</span>
          <span className={cx('fixed-rule-format')}>s</span>
          <span className={cx('fixed-rule-format')}>s</span>
        </div>
        <div
          className={cx('bottom-line')}
          style={{
            width: 364,
          }}
        />
        <div className={cx('bottom-tips')}>{t('Date + Time')}</div>
      </div>
      <div style={{ marginLeft: 24 }}>
        <div>
          <span className={cx('fixed-rule-format')}>0</span>
          <span className={cx('fixed-rule-format')}>0</span>
          <span className={cx('fixed-rule-format')}>0</span>
        </div>
        <div
          className={cx('bottom-line')}
          style={{
            width: 63,
          }}
        />
        <div className={cx('bottom-tips')}>{t('Random')}</div>
      </div>
    </div>
  );
};

export default VoucherNumberGenerationRule;
