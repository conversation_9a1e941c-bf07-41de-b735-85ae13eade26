import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, Tooltip } from 'antd';
import type { ColumnProps } from 'antd/es/table';

import classNames from 'classnames/bind';
import { keyBy, remove } from 'lodash-es';

import { DeleteAction, Icon, Table } from '@zhongan/nagrand-ui';

import { GoodsInfo } from 'genesis-web-service';

import { GoodsPlanTableRow } from '../../interface';
import GoodsPlanEditTableDrawer from '../GoodsPlanEditTableDrawer/GoodsPlanEditTableDrawer';
import styles from './GoodsPlanEditTable.module.scss';

const cx = classNames.bind(styles);

interface Props {
  disabled?: boolean;
  goodsList: GoodsInfo[];
  associatedGoodsAndPlans: GoodsPlanTableRow[];
  setAssociatedGoodsAndPlans: (data: GoodsPlanTableRow[]) => void;
}

export const GoodsPlanEditTable = ({
  disabled,
  goodsList,
  associatedGoodsAndPlans,
  setAssociatedGoodsAndPlans,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [drawerVisible, setDrawerVisible] = useState(false);

  const goodsMap = useMemo(() => keyBy(goodsList, goods => goods.goodsBasicInfo.goodsId), [goodsList]);

  const renderPlan = useCallback(
    (text, record: GoodsPlanTableRow) => {
      const planList = goodsMap[record.goodsId]?.coveragePlans || [];
      const planMap = keyBy(planList, 'planId');
      const renderedPlanIds: (number | string)[] = [];
      const popoverIds: (number | string)[] = [];
      let currentLength = 0;
      const maxLength = 500;
      record.planIds.forEach((planId, index) => {
        const planName = planMap[planId]?.goodsPlanName || '';
        const planNameLength = planName.length * 7;
        const spanLength = index === 0 ? planNameLength + 8 * 2 : planNameLength + 8 * 2 + 8;
        if (currentLength + spanLength + 45 <= maxLength) {
          renderedPlanIds.push(planId);
          currentLength += spanLength;
        } else {
          popoverIds.push(planId);
        }
      });

      return (
        <div className={cx('plan-name-wrapper')}>
          {renderedPlanIds.map(planId => (
            <span className={cx('plan-name')}>{planMap[planId]?.goodsPlanName}</span>
          ))}
          {popoverIds.length > 0 ? (
            <Tooltip
              title={
                <div>
                  {popoverIds.map(planId => (
                    <div>{planMap[planId]?.goodsPlanName}</div>
                  ))}
                </div>
              }
            >
              <span className={cx('more-plan', 'plan-name')}>+{popoverIds.length}</span>
            </Tooltip>
          ) : null}
        </div>
      );
    },
    [goodsMap]
  );

  const columns = useMemo(() => {
    const tempColumns: ColumnProps<GoodsPlanTableRow>[] = [
      {
        title: t('Goods Name'),
        dataIndex: 'goodsId',
        render: (text, record: GoodsPlanTableRow) => goodsMap[record.goodsId]?.goodsBasicInfo?.goodsName,
      },
      {
        title: t('Plan'),
        dataIndex: 'planIds',
        width: 660,
        render: renderPlan,
      },
    ];

    if (!disabled) {
      tempColumns.push({
        title: t('Actions'),
        fixed: 'right',
        render: (text, record: GoodsPlanTableRow) => (
          <DeleteAction
            doubleConfirmType="popconfirm"
            onClick={() => {
              const tempList = [...associatedGoodsAndPlans];
              remove(tempList, item => item.goodsId === record.goodsId);
              setAssociatedGoodsAndPlans(tempList);
            }}
            deleteConfirmContent={t('Are you sure to delete this record?')}
          />
        ),
      });
    }

    return tempColumns;
  }, [associatedGoodsAndPlans, disabled, goodsMap, renderPlan, setAssociatedGoodsAndPlans, t]);

  return (
    <Form.Item colon={false} required label={t('Associated Goods and Plans')} layout="vertical">
      <div style={{ height: 32, marginBottom: 8 }}>
        <Button
          onClick={() => {
            setDrawerVisible(true);
          }}
          disabled={disabled}
          icon={<Icon type="edit" />}
        >
          {t('Edit')}
        </Button>
      </div>
      <Table columns={columns} dataSource={associatedGoodsAndPlans} emptyType="text" />
      <GoodsPlanEditTableDrawer
        goodsList={goodsList}
        visible={drawerVisible}
        closeDrawer={() => {
          setDrawerVisible(false);
        }}
        onSubmit={selection => {
          const result: GoodsPlanTableRow[] = [];
          const goodsIds = Object.keys(selection);

          goodsIds.forEach(goodsId => {
            if (selection[goodsId]?.selected) {
              const planIds: (string | number)[] = [];
              const planSelection = selection[goodsId].planSeletion;
              Object.keys(planSelection).forEach(planId => {
                if (planSelection[planId]) {
                  planIds.push(planId);
                }
              });
              result.push({
                goodsId,
                planIds,
              });
            }
          });

          setAssociatedGoodsAndPlans(result);
        }}
        initValue={associatedGoodsAndPlans}
      />
    </Form.Item>
  );
};

export default GoodsPlanEditTable;
