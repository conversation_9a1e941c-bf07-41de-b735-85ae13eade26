import { useTranslation } from 'react-i18next';

import { keyBy } from 'lodash-es';

import { StatusTag } from '@zhongan/nagrand-ui';
import type { StatusType } from '@zhongan/nagrand-ui/dist/components/StatusTag';

import { VoucherStatus } from 'genesis-web-service';

import { useBizDictAsOptions } from '@market/hook/bizDict';

const StatusTagMap = {
  [VoucherStatus.Draft]: 'no-status',
  [VoucherStatus.Effective]: 'success',
};
interface Props {
  status: VoucherStatus;
  display?: 'in-table' | 'normal';
}

export const VoucherStatusComponent = ({ status, display = 'normal' }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  /* ============== 枚举使用start ============== */
  const voucherStatusOptions = useBizDictAsOptions('voucherDefStatus');
  /* ============== 枚举使用end ============== */
  const voucherStatusMap = keyBy(voucherStatusOptions, 'value');

  return (
    <StatusTag
      statusI18n={voucherStatusMap[status]?.label}
      type={StatusTagMap[status] as StatusType}
      needDot={display === 'in-table'}
    />
  );
};

export default VoucherStatusComponent;
