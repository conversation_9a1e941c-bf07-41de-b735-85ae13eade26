import { ReactNode } from 'react';

import classNames from 'classnames/bind';

import styles from './SectionContainer.module.scss';

const cx = classNames.bind(styles);

interface Props {
  title: string;
  children: ReactNode;
  className?: string | undefined;
  id?: string;
}

export const SectionContainer = ({ title, children, className, id }: Props): JSX.Element => (
  <div id={id} className={`${cx('section-container')} ${className || ''}`}>
    {title ? <div className={cx('title')}>{title}</div> : null}
    {children}
  </div>
);

export default SectionContainer;
