import { useEffect, useMemo } from 'react';

import { message } from 'antd';

import { useRequest } from 'ahooks';

import { CustomerLoyaltyService } from 'genesis-web-service/lib/customer-loyalty/customerLoyalty.service';

import { DetailPageMode, SelectOption } from '@market/common/interface';
import { t } from '@market/i18n';

export const useVoucherDetail = (id: number | string, mode: DetailPageMode) => {
  const { data: voucherDetail, runAsync: queryVoucherDetail } = useRequest(
    () => {
      if (mode === DetailPageMode.add || !id) {
        return Promise.resolve(undefined);
      }
      return CustomerLoyaltyService.getVoucher(id);
    },
    {
      onError: error => {
        message.error(error.message);
      },
      refreshDeps: [id, mode],
      manual: true,
    }
  );

  useEffect(() => {
    queryVoucherDetail();
  }, [id, mode]);

  if (!voucherDetail) {
    return {
      reload: queryVoucherDetail,
    };
  }

  return {
    voucherResponse: voucherDetail.voucherResponse,
    voucherGenerateResponse: voucherDetail.voucherGenerateResponse,
    voucherUsageResponse: voucherDetail.voucherUsageResponse,
    reload: queryVoucherDetail,
  };
};

export const useRenamedBizTypeOptions = (bizTypeOptions: SelectOption[]) => {
  const reNamedBizTypeOptions = useMemo(() => {
    const bizTypeI18n: Record<string, string> = {
      '1': t('New Business'),
      '3': t('Renewal'),
    };
    return bizTypeOptions.map(option => {
      if (bizTypeI18n[option.value]) {
        return {
          label: bizTypeI18n[option.value],
          value: option.value,
        };
      }
      return option;
    });
  }, [bizTypeOptions]);

  return reNamedBizTypeOptions;
};
