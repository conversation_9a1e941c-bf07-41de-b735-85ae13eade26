import { RenderMode } from '@zhongan/nagrand-ui';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import { VoucherSearchResult } from 'genesis-web-service';

import { PaginationConfig } from '@market/common/interface';
import { VoucherSearchQuery } from '@market/pages/CustomerLoyalty/VoucherManagement/VoucherManagement';

import SearchResultCardList from '../SearchResultCardList';
import { cardPageSizeOptions } from '../SearchResultCardList/SearchResultCardList';
import SearchResultTable from '../SearchResultTable/SearchResultTable';

interface Props {
  loading: boolean;
  list: VoucherSearchResult[];
  pagination: PaginationConfig;
  onAddNew: () => void;
  onView: (voucher: VoucherSearchResult) => void;
  onEdit: (voucher: VoucherSearchResult) => void;
  onDelete: (voucher: VoucherSearchResult) => void;
  paginationOnChange: (current: number, pageSize: number) => void;
}

export const SearchResultSection = ({
  loading,
  onAddNew,
  onView,
  onEdit,
  onDelete,
  paginationOnChange,
  list,
  pagination,
}: Props): JSX.Element => {
  const [searchQuery, setSearchQuery] = useRouterState<VoucherSearchQuery>();

  const displayType = searchQuery.showCard ? RenderMode.Card : RenderMode.Table;

  const setDisplayType = (_displayType: RenderMode) => {
    setSearchQuery({
      ...searchQuery,
      showCard: _displayType === RenderMode.Card,
      current: 0,
      pageSize: _displayType === RenderMode.Card ? cardPageSizeOptions[0] : 10,
    });
  };

  return searchQuery.showCard ? (
    <SearchResultCardList
      list={list}
      loading={loading}
      pagination={pagination}
      onAddNew={onAddNew}
      onView={onView}
      onEdit={onEdit}
      onDelete={onDelete}
      displayType={displayType}
      setDisplayType={setDisplayType}
      paginationOnChange={paginationOnChange}
    />
  ) : (
    <SearchResultTable
      list={list}
      pagination={pagination}
      loading={loading}
      onAddNew={onAddNew}
      onView={onView}
      onEdit={onEdit}
      onDelete={onDelete}
      displayType={displayType}
      setDisplayType={setDisplayType}
      paginationOnChange={paginationOnChange}
    />
  );
};
