.search-section {
  padding: 16px 16px 32px;
  background-color: var(--white);
}

.title {
  margin-bottom: 30px;
  height: 20px;
  line-height: 20px;
  padding-left: 8px;
  border-left: 4px solid var(--primary-color);
  color: var(--text-color);
  font-weight: 700;
  font-size: 20px;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  border: 1px solid var(--border-light);
  padding: 6px 8px;
  border-radius: 4px;
  width: 560px;
  margin-bottom: 16px;

  :global {
    .anticon-search {
      color: var(--text-color);
    }
  }

  .search-input {
    height: 20px;
    line-height: 20px;
    border: none;
    outline: none;
    box-shadow: none;
    padding-left: 8px;
    width: 400px;
  }

  .filter-btn {
    margin-left: auto;
    padding: 4px 16px;
    line-height: 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}

.type-option-wrapper {
  display: flex;

  .type-option {
    padding: 10px 20px;
    border: 1px solid var(--border-light);
    border-radius: 20px;
    cursor: pointer;
  }

  .active.type-option {
    color: var(--primary-color);
    background-color: var(--primary-color-5-percent);
  }

  .type-option + .type-option {
    margin-left: 8px;
  }
}

.applied-condition-wrapper {
  display: flex;

  .applied-condition {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    margin-bottom: 8px;
    margin-right: 8px;
    line-height: 20px;
    color: var(--text-color);
    border: 1px solid var(--border-light);
    background: var(--disabled-bg);
    border-radius: 24px;

    :global {
      .anticon-close {
        cursor: pointer;
      }
    }
  }

  .applied-condition-text {
    line-height: 20px;
    display: inline-block;
    max-width: 177px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
