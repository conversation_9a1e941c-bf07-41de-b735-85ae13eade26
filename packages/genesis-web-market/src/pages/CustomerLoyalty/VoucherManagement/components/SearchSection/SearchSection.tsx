import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import AntIcon from '@ant-design/icons';
import { Input } from 'antd';

import classNames from 'classnames/bind';
import { keyBy, omit } from 'lodash-es';

import { Icon, SimplePageHeader } from '@zhongan/nagrand-ui';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import { GoodsInfo, QueryChannelResult, VoucherSearchCondition } from 'genesis-web-service';

import filterIcon from '@market/asset/svg/customer-loyalty/filter.svg';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { VoucherSearchQuery } from '@market/pages/CustomerLoyalty/VoucherManagement/VoucherManagement';
import { useRenamedBizTypeOptions } from '@market/pages/CustomerLoyalty/hooks';
import { renderOptionName } from '@market/utils/enum';

import SearchConditionPanel from '../SearchConditionPanel/SearchConditionPanel';
import styles from './SearchSection.module.scss';

const cx = classNames.bind(styles);

interface Props {
  channelList: QueryChannelResult[];
  goodsList: GoodsInfo[];
}

export const SearchSection = ({ goodsList, channelList }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [panelVisible, setPanelVisible] = useState(false);

  const [searchQuery, setSearchQuery] = useRouterState<VoucherSearchQuery>();
  const appliedCondition = omit(searchQuery, 'current', 'pageSize', 'showCard');

  const setAppliedCondition = useCallback(
    (props: Partial<VoucherSearchQuery>) => {
      setSearchQuery({
        ...props,
        current: 0,
      });
    },
    [setSearchQuery]
  );

  /* ============== 枚举使用start ============== */
  const voucherAmountTypeOptions = useBizDictAsOptions('voucherAmountType');
  const bizTypeOptions = useBizDictAsOptions('bizType');
  const voucherStatusOptions = useBizDictAsOptions('voucherDefStatus');
  /* ============== 枚举使用end ============== */
  const reNamedBizTypeOptions = useRenamedBizTypeOptions(bizTypeOptions);

  const showPanel = useCallback(() => {
    setPanelVisible(true);
  }, []);

  const closePanel = useCallback(() => {
    setPanelVisible(false);
  }, []);

  const goodsMap = useMemo(() => keyBy(goodsList, goods => goods.goodsBasicInfo.goodsId), [goodsList]);
  const channelMap = useMemo(() => keyBy(channelList, 'code'), [channelList]);

  const renderAppliedCondition = useCallback(
    (fieldKey: keyof VoucherSearchCondition) => {
      let text: string | number = '';
      switch (fieldKey) {
        case 'voucherName':
          text = appliedCondition[fieldKey] || '';
          break;
        case 'channelCode':
          text = channelMap?.[appliedCondition[fieldKey] as string]?.name || '';
          break;
        case 'goodsId':
          text = goodsMap?.[appliedCondition[fieldKey] as string]?.goodsBasicInfo?.goodsName || '';
          break;
        case 'applicableBizType':
          text = renderOptionName(appliedCondition[fieldKey]!, reNamedBizTypeOptions) || '';
          break;
        case 'status':
          text = renderOptionName(appliedCondition[fieldKey]!, voucherStatusOptions) || '';
          break;
        case 'voucherAmountType':
          text = renderOptionName(appliedCondition[fieldKey]!, voucherAmountTypeOptions) || '';
          break;
        default:
          break;
      }
      return (
        <div className={cx('applied-condition')}>
          <span className={cx('applied-condition-text')}>{text}</span>
          <Icon
            type="close"
            onClick={() => {
              setAppliedCondition({
                ...appliedCondition,
                [fieldKey]: undefined,
              });
            }}
            className="ml-2 hover:cursor-pointer"
          />
        </div>
      );
    },
    [
      appliedCondition,
      reNamedBizTypeOptions,
      channelMap,
      goodsMap,
      setAppliedCondition,
      voucherAmountTypeOptions,
      voucherStatusOptions,
    ]
  );

  return (
    <React.Fragment>
      <SimplePageHeader>{t('Voucher')}</SimplePageHeader>
      <div className={cx('search-section')}>
        <div className={cx('search-input-wrapper')}>
          <Icon type="search-icon" />
          <Input
            onChange={event => {
              setAppliedCondition({
                ...appliedCondition,
                voucherName: event.target.value,
              });
            }}
            value={appliedCondition.voucherName}
            className={cx('search-input')}
            placeholder={t('Search Voucher Name')}
          />
          <div onClick={showPanel} className={cx('filter-btn')}>
            <AntIcon component={filterIcon} style={{ fontSize: 16, height: 16, width: 16, marginRight: 8 }} />
            {t('Filters')}
          </div>
        </div>
        {panelVisible ? (
          <SearchConditionPanel
            appliedCondition={appliedCondition}
            setAppliedCondition={setAppliedCondition}
            closePanel={closePanel}
            goodsList={goodsList}
            channelList={channelList}
          />
        ) : null}
        {Object.keys(appliedCondition).filter(
          fieldKey => !!appliedCondition[fieldKey as keyof VoucherSearchCondition] && fieldKey !== 'voucherName'
        ).length === 0 ? (
          <div className={cx('type-option-wrapper')}>
            {voucherAmountTypeOptions.map(option => (
              <div
                onClick={() => {
                  if (option.value === appliedCondition.voucherAmountType) {
                    return;
                  }
                  setAppliedCondition({
                    ...appliedCondition,
                    voucherAmountType: option.value as string,
                  });
                }}
                className={cx('type-option', {
                  active: appliedCondition.voucherAmountType === option.value,
                })}
              >
                {option.label}
              </div>
            ))}
          </div>
        ) : (
          <div className={cx('applied-condition-wrapper')}>
            {Object.keys(appliedCondition)
              .filter(
                fieldKey => !!appliedCondition[fieldKey as keyof VoucherSearchCondition] && fieldKey !== 'voucherName'
              )
              .map(fieldKey => renderAppliedCondition(fieldKey as keyof VoucherSearchCondition))}
          </div>
        )}
      </div>
    </React.Fragment>
  );
};

export default SearchSection;
