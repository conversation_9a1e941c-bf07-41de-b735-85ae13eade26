import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Input } from 'antd';

import classNames from 'classnames/bind';

import { ApplicableSenario, GoodsInfo, QueryChannelResult, VoucherSearchCondition } from 'genesis-web-service';

import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { useRenamedBizTypeOptions } from '@market/pages/CustomerLoyalty/hooks';

import styles from './SearchConditionPanel.module.scss';

const cx = classNames.bind(styles);

interface Props {
  channelList: QueryChannelResult[];
  goodsList: GoodsInfo[];
  appliedCondition: VoucherSearchCondition;
  setAppliedCondition: (value: VoucherSearchCondition) => void;
  closePanel: () => void;
}

export const SearchConditionPanel = ({
  goodsList,
  channelList,
  appliedCondition,
  setAppliedCondition,
  closePanel,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  /* ============== 枚举使用start ============== */
  const voucherAmountTypeOptions = useBizDictAsOptions('voucherAmountType');
  const voucherStatusOptions = useBizDictAsOptions('voucherDefStatus');
  const bizTypeOptions = useBizDictAsOptions('bizType');
  /* ============== 枚举使用end ============== */
  const [tempCondition, setTempCondition] = useState<VoucherSearchCondition>(appliedCondition);

  const reNamedBizTypeOptions = useRenamedBizTypeOptions(bizTypeOptions);

  const changeCondition = useCallback(
    (key: keyof VoucherSearchCondition, value: string) => {
      setTempCondition({
        ...tempCondition,
        [key]: value,
      });
    },
    [tempCondition]
  );

  const triggerCondition = useCallback(
    (key: keyof VoucherSearchCondition, value: string) => {
      if (tempCondition[key] === value) {
        setTempCondition({
          ...tempCondition,
          [key]: undefined,
        });
      } else {
        setTempCondition({
          ...tempCondition,
          [key]: value,
        });
      }
    },
    [tempCondition]
  );

  const clearConditions = useCallback(() => {
    setTempCondition({});
    closePanel();
  }, [closePanel]);

  const submitConditions = useCallback(() => {
    setAppliedCondition({
      ...tempCondition,
    });
    closePanel();
  }, [closePanel, setAppliedCondition, tempCondition]);

  return (
    <React.Fragment>
      <div className={cx('search-panel')}>
        <div className={cx('search-item')}>
          <div className={cx('label')}>{t('Voucher Name')}</div>
          <Input
            onChange={event => {
              changeCondition('voucherName', event.target.value);
            }}
            value={tempCondition.voucherName}
            placeholder={t('Please input')}
            style={{ width: '100%' }}
          />
        </div>
        <div className={cx('search-item')}>
          <div className={cx('label')}>{t('Voucher Type')}</div>
          <div className={cx('search-option-wrapper')}>
            {voucherAmountTypeOptions.map(option => (
              <div
                onClick={() => {
                  triggerCondition('voucherAmountType', option.value as string);
                }}
                className={cx('search-option', {
                  active: tempCondition.voucherAmountType === option.value,
                })}
              >
                {option.label}
              </div>
            ))}
          </div>
        </div>
        <div className={cx('search-item')}>
          <div className={cx('label')}>{t('Voucher Status')}</div>
          <div className={cx('search-option-wrapper')}>
            {voucherStatusOptions.map(option => (
              <div
                onClick={() => {
                  triggerCondition('status', option.value as string);
                }}
                className={cx('search-option', {
                  active: tempCondition.status === option.value,
                })}
              >
                {option.label}
              </div>
            ))}
          </div>
        </div>
        <div className={cx('search-item')}>
          <div className={cx('label')}>{t('Applicable Scenario')}</div>
          <GeneralSelect
            onChange={value => {
              changeCondition('applicableBizType', value);
            }}
            style={{ width: '100%' }}
            value={tempCondition.applicableBizType}
            option={reNamedBizTypeOptions.filter(
              option => option.value === ApplicableSenario.NB || option.value === ApplicableSenario.RENEWAL
            )}
          />
        </div>
        <div className={cx('search-item')}>
          <div className={cx('label')}>{t('Voucher Channel')}</div>
          <GeneralSelect
            style={{ width: '100%' }}
            option={channelList.map(channel => ({
              label: channel.name,
              value: channel.code,
            }))}
            onChange={value => {
              changeCondition('channelCode', value);
            }}
            value={tempCondition.channelCode}
          />
        </div>
        <div className={cx('search-item')}>
          <div className={cx('label')}>{t('Goods Name')}</div>
          <GeneralSelect
            style={{ width: '100%' }}
            option={goodsList.map(goods => ({
              label: `${goods.goodsBasicInfo.goodsName} - ${goods.goodsBasicInfo.goodsVersion}`,
              value: goods.goodsBasicInfo.goodsId,
            }))}
            onChange={value => {
              changeCondition('goodsId', value);
            }}
            value={tempCondition.goodsId}
          />
        </div>
        <div className={cx('button-wrapper')}>
          <Button onClick={clearConditions} style={{ marginRight: 16 }}>
            {t('Clear')}
          </Button>
          <Button onClick={submitConditions} type="primary">
            {t('Search')}
          </Button>
        </div>
      </div>
      <div onClick={closePanel} className={cx('search-panel-mask')} />
    </React.Fragment>
  );
};

export default SearchConditionPanel;
