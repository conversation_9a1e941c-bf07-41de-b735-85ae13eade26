.search-panel {
  position: absolute;
  padding: 24px 32px;
  width: 600px;
  background-color: var(--white);
  box-shadow: 0px 4px 32px var(--disabled-bg);
  border-radius: 4px;
  margin-top: -11px;
  z-index: 2;
}

.search-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.search-item {
  .label {
    margin-bottom: 4px;
  }

  .search-option-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: -8px;

    .search-option {
      padding: 10px 20px;
      border: 1px solid var(--border-light);
      border-radius: 20px;
      cursor: pointer;
      margin-bottom: 8px;
      margin-right: 8px;
    }

    .active.search-option {
      color: var(--primary-color);
      background-color: var(--primary-color-5-percent);
    }

    .search-option:last-child {
      margin-right: 0;
    }
  }
}

.search-item + .search-item {
  margin-top: 24px;
}

.button-wrapper {
  margin-top: 24px;
  float: right;
}
