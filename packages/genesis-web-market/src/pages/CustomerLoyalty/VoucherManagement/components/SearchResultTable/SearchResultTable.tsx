import { useTranslation } from 'react-i18next';

import { Tooltip } from 'antd';

import {
  AddNewButton,
  DeleteAction,
  EditAction,
  OperationContainer,
  QueryResultContainer,
  RenderMode,
  RenderModeSwitch,
  Table,
  TableActionsContainer,
  ViewAction,
} from '@zhongan/nagrand-ui';

import { VoucherSearchResult, VoucherStatus } from 'genesis-web-service';

import { PaginationConfig } from '@market/common/interface';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import VoucherStatusComponent from '@market/pages/CustomerLoyalty/components/VoucherStatus';
import { renderOptionName } from '@market/utils/enum';

import { renderGoodsName } from '../../utils';

interface Props {
  list: VoucherSearchResult[];
  loading: boolean;
  displayType: RenderMode;
  setDisplayType: (displayType: RenderMode) => void;
  onAddNew: () => void;
  onView: (voucher: VoucherSearchResult) => void;
  onEdit: (voucher: VoucherSearchResult) => void;
  onDelete: (voucher: VoucherSearchResult) => void;
  paginationOnChange: (current: number, pageSize: number) => void;
  pagination: PaginationConfig;
}

export const SearchResultTable = ({
  list,
  pagination,
  loading,
  setDisplayType,
  displayType,
  onAddNew,
  onView,
  onEdit,
  onDelete,
  paginationOnChange,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  /* ============== 枚举使用start ============== */
  const voucherAmountTypeOptions = useBizDictAsOptions('voucherAmountType');
  /* ============== 枚举使用end ============== */

  return (
    <QueryResultContainer>
      <OperationContainer>
        <OperationContainer.Left>
          <AddNewButton type="primary" onClick={onAddNew}>
            {t('Add New')}
          </AddNewButton>
        </OperationContainer.Left>
        <OperationContainer.Right />
        <RenderModeSwitch value={displayType} onChange={setDisplayType} />
      </OperationContainer>
      <Table
        bordered={false}
        dataSource={list}
        pagination={pagination}
        loading={loading}
        scroll={{ x: 'max-content' }}
        columns={[
          {
            title: t('Voucher Name') as string,
            dataIndex: 'voucherName',
          },
          {
            title: t('Voucher Value Type') as string,
            dataIndex: 'voucherAmountType',
            width: 240,
            render: (text: number) => renderOptionName(`${text}`, voucherAmountTypeOptions),
          },
          {
            title: t('Goods Name') as string,
            width: 240,
            render: (text, record) => renderGoodsName(record),
          },
          {
            title: t('Voucher Status') as string,
            dataIndex: 'status',
            width: 240,
            render: (status: VoucherStatus) => <VoucherStatusComponent display="in-table" status={status} />,
          },
          {
            title: t('Description') as string,
            dataIndex: 'description',
            width: 240,
          },
          {
            title: t('Actions') as string,
            fixed: 'right',
            align: 'end',
            render: (text, record) => (
              <TableActionsContainer>
                <ViewAction onClick={() => onView(record)} />
                <EditAction onClick={() => onEdit(record)} />
                <DeleteAction
                  doubleConfirmType="popconfirm"
                  onClick={() => onDelete(record)}
                  deleteConfirmContent={t('Are you sure to delete this record?') as string}
                />
              </TableActionsContainer>
            ),
          },
        ]}
        onChange={({ pageSize, current }) => {
          paginationOnChange(current || 0, pageSize || 10);
        }}
        emptyType="icon"
      />
    </QueryResultContainer>
  );
};

export default SearchResultTable;
