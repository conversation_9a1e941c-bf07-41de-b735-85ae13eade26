/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { keyBy } from 'lodash-es';

import {
  CardActionsContainer,
  CardBodyPrimaryInfo,
  CardFooter,
  CardTagList,
  CardV2,
  DeleteAction,
  EditAction,
  TagType,
  ViewAction,
} from '@zhongan/nagrand-ui';

import { VoucherSearchResult, VoucherStatus } from 'genesis-web-service';

import { useBizDictAsOptions } from '@market/hook/bizDict';
import { renderOptionName } from '@market/utils/enum';

const StatusTagMap = {
  [VoucherStatus.Draft]: 'no-status',
  [VoucherStatus.Effective]: 'success',
};

interface Props {
  voucher: VoucherSearchResult;
  onView: (voucher: VoucherSearchResult) => void;
  onEdit: (voucher: VoucherSearchResult) => void;
  onDelete: (voucher: VoucherSearchResult) => void;
}

export const SearchResultCard = ({ voucher, onView, onEdit, onDelete }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  /* ============== 枚举使用start ============== */
  const voucherAmountTypeOptions = useBizDictAsOptions('voucherAmountType');
  const voucherStatusOptions = useBizDictAsOptions('voucherDefStatus');
  /* ============== 枚举使用end ============== */
  const voucherStatusMap = keyBy(voucherStatusOptions, 'value');

  const handleView = useCallback(() => onView(voucher), [onView, voucher]);
  const handleEdit = useCallback(() => onEdit(voucher), [onEdit, voucher]);
  const handleDelete = useCallback(() => onDelete(voucher), [onDelete, voucher]);

  return (
    <CardV2
      body={
        <React.Fragment>
          <CardBodyPrimaryInfo title={t('Voucher Name') as string} content={voucher.voucherName} />
          <CardTagList
            tagList={[
              {
                type: TagType.Tag,
                tagProps: {
                  statusI18n: voucherStatusMap[voucher.status]?.label?.toUpperCase(),
                  type: StatusTagMap[voucher.status],
                },
              },
            ]}
          />
        </React.Fragment>
      }
      footer={
        <CardFooter
          list={[
            {
              label: t('Voucher Value Type') as string,
              value: renderOptionName(`${voucher.voucherAmountType}`, voucherAmountTypeOptions),
            },
            {
              label: t('Goods Name') as string,
              value: voucher.goodsInfos
                .sort((a, b) => a.goodsName.length - b.goodsName.length)
                .map(goodsInfo => goodsInfo.goodsName),
            },
            {
              label: t('Description') as string,
              value: voucher.description ?? '',
            },
          ]}
        />
      }
      actions={
        <CardActionsContainer>
          <ViewAction onClick={handleView} />
          <EditAction onClick={handleEdit} />
          <DeleteAction
            doubleConfirmType="popconfirm"
            deleteConfirmContent={t('Are you sure to delete this record?') as string}
            onClick={handleDelete}
          />
        </CardActionsContainer>
      }
    />
  );
};

export default SearchResultCard;
