import React from 'react';
import { useTranslation } from 'react-i18next';

import {
  AddNewButton,
  CardLayoutV2,
  OperationContainer,
  Pagination,
  QueryBitMap,
  QueryResultContainer,
  RenderMode,
  RenderModeSwitch,
} from '@zhongan/nagrand-ui';

import { VoucherSearchResult } from 'genesis-web-service';

import { PaginationConfig } from '@market/common/interface';

import SearchResultCard from '../SearchResultCard/SearchResultCard';

export const cardPageSizeOptions = [12, 24, 36, 48];

interface Props {
  loading: boolean;
  displayType: RenderMode;
  list: VoucherSearchResult[];
  pagination: PaginationConfig;
  setDisplayType: (displayType: RenderMode) => void;
  onAddNew: () => void;
  onView: (voucher: VoucherSearchResult) => void;
  onEdit: (voucher: VoucherSearchResult) => void;
  onDelete: (voucher: VoucherSearchResult) => void;
  paginationOnChange: (current: number, pageSize: number) => void;
}

export const SearchResultCardList = ({
  loading,
  list,
  pagination,
  setDisplayType,
  displayType,
  onAddNew,
  onView,
  onEdit,
  onDelete,
  paginationOnChange,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  return (
    <QueryResultContainer>
      <OperationContainer>
        <OperationContainer.Left>
          <AddNewButton type="primary" onClick={onAddNew}>
            {t('Add New')}
          </AddNewButton>
        </OperationContainer.Left>
        <OperationContainer.Right>
          <RenderModeSwitch value={displayType} onChange={setDisplayType} />
        </OperationContainer.Right>
      </OperationContainer>
      {list?.length || loading ? (
        <CardLayoutV2 loading={loading}>
          {list.map((item, index) => (
            <SearchResultCard
              key={item.id?.toString() ?? `voucher-item-${index}`}
              voucher={item}
              onView={onView}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </CardLayoutV2>
      ) : (
        <QueryBitMap queryStatus="NoContent" />
      )}

      {pagination?.total > 0 ? (
        <Pagination
          showQuickJumper
          pageSizeOptions={cardPageSizeOptions}
          current={pagination?.current || 1}
          pageSize={pagination?.pageSize || cardPageSizeOptions[0]}
          total={pagination?.total}
          onChange={(page, pageSize) => {
            paginationOnChange(page || 0, pageSize || cardPageSizeOptions[0]);
          }}
        />
      ) : null}
    </QueryResultContainer>
  );
};

export default SearchResultCardList;
