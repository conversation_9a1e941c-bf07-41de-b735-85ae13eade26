import { message } from 'antd';

import { useRequest } from 'ahooks';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import { CustomerLoyaltyService, VoucherSearchCondition, VoucherSearchResult } from 'genesis-web-service';

import { DetailPageMode } from '@market/common/interface';
import { useChannelList } from '@market/hook/channel.serivce';
import { useGoodsList } from '@market/hook/goods.service';
import { RouteComponentProps } from '@market/router';

import { SearchResultSection } from './components/SearchResultSection';
import SearchSection from './components/SearchSection';

export type VoucherSearchQuery = VoucherSearchCondition & {
  current: number;
  pageSize: number;
  showCard: boolean;
};

export const VoucherManagement = ({ navigate }: RouteComponentProps): JSX.Element => {
  const [searchQuery, setSearchQuery] = useRouterState<VoucherSearchQuery>({
    current: 0,
    pageSize: 12,
    showCard: true,
  });

  const goodsList = useGoodsList();
  const channelList = useChannelList();

  const { runAsync: deleteVoucherById } = useRequest(
    (id: number | string) => CustomerLoyaltyService.deleteVoucher(id),
    {
      onError: error => {
        message.error(error.message);
      },
      manual: true,
    }
  );

  const {
    data: searchResult,
    run: searchResultRun,
    loading,
  } = useRequest(
    () => {
      const { current, pageSize, ...condition } = searchQuery;
      return CustomerLoyaltyService.searchVoucher(condition, current, pageSize).then(res => ({
        list: res.content,
        total: res.totalElements,
      }));
    },
    {
      refreshDeps: [searchQuery],
      debounceWait: 500,
      onError: error => {
        message.error(error.message);
      },
    }
  );

  return (
    <div>
      <SearchSection goodsList={goodsList} channelList={channelList} />
      <SearchResultSection
        onAddNew={() => {
          navigate(`/market/customer-loyalty/voucher-detail`, {
            state: {
              mode: DetailPageMode.add,
            },
          });
        }}
        onView={(voucher: VoucherSearchResult) => {
          navigate(`/market/customer-loyalty/voucher-detail?id=${voucher.voucherDefId}`, {
            state: {
              mode: DetailPageMode.view,
            },
          });
        }}
        onEdit={(voucher: VoucherSearchResult) => {
          navigate(`/market/customer-loyalty/voucher-detail?id=${voucher.voucherDefId}`, {
            state: {
              mode: DetailPageMode.edit,
            },
          });
        }}
        onDelete={(voucher: VoucherSearchResult) => {
          deleteVoucherById(voucher.voucherDefId).then(() => {
            searchResultRun();
          });
        }}
        list={searchResult?.list || []}
        loading={loading}
        paginationOnChange={(page: number, pageSize: number) => {
          setSearchQuery({
            ...searchQuery,
            current: page - 1,
            pageSize,
          });
        }}
        pagination={{
          current: searchQuery.current + 1,
          pageSize: searchQuery.pageSize,
          total: searchResult?.total ?? 0,
        }}
      />
    </div>
  );
};

export default VoucherManagement;
