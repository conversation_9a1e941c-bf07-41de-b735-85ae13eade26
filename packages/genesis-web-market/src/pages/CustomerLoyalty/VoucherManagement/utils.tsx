import { Tooltip } from 'antd';

import { VoucherSearchResult } from 'genesis-web-service';

export const renderGoodsName = (record: Pick<VoucherSearchResult, 'goodsInfos'>, maxWidth = 180) => {
  const sortedGoodsList = record.goodsInfos.sort((a, b) => a.goodsName.length - b.goodsName.length);
  const firstGoodsName = (
    <Tooltip title={sortedGoodsList[0].goodsName}>
      <span
        style={{
          maxWidth,
          display: 'inline-block',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
        }}
      >
        {sortedGoodsList[0].goodsName}
      </span>
    </Tooltip>
  );
  if (sortedGoodsList.length === 1) {
    return firstGoodsName;
  }
  const goodsListExceptFirst = sortedGoodsList.slice(1);
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
      }}
    >
      {firstGoodsName}
      <Tooltip
        title={
          <div>
            {goodsListExceptFirst.map(goods => (
              <div>{goods.goodsName};</div>
            ))}
          </div>
        }
      >
        {`;+${goodsListExceptFirst.length}`}
      </Tooltip>
    </div>
  );
};
