import { FC, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Form } from 'antd';

import { FieldType, QueryForm as NagrandQueryForm } from '@zhongan/nagrand-ui';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import { ProductStatusEnum } from 'genesis-web-service/service-types/product-types/package/index';

import { BizDict } from '@market/common/interface';
import { NewProductService } from '@market/services/product/product.service.new';

interface Option {
  value: string;
  label: string;
}

interface Props {
  productCategoryEnums: BizDict[];
  loading: boolean;
  search: (arg: Record<string, unknown>, arg2?: boolean) => void;
}

const QueryForm: FC<Props> = ({ productCategoryEnums, loading, search }) => {
  const [form] = Form.useForm();
  const [t] = useTranslation(['market', 'common']);
  const [productCodeOptions, setProductCodeOptions] = useState<Option[]>([]);
  const [productVersionOptions, setProductVersionOptions] = useState<Option[]>([]);
  const productCodeValue = Form.useWatch<string>('productCode', form);

  const [searchQuery] = useRouterState<Record<string, unknown>>();

  const queryProductCodeList = () => {
    NewProductService.ProductStructureService.queryProductList({
      status: ProductStatusEnum.SUBMIT,
      code: '',
    }).then(res => {
      const result =
        res?.map(item => ({
          value: item.productCode!,
          label: `${item.productCode!}-${item.productName!}`,
        })) ?? [];
      setProductCodeOptions(result);
    });
  };

  const queryProductVersionList = (productCode: string) => {
    NewProductService.ProductConfigService.queryAllVersion({ productCode }).then(res => {
      const result =
        res?.value?.map(item => ({
          value: item,
          label: item,
        })) ?? [];
      setProductVersionOptions(result);
    });
  };

  useEffect(() => {
    queryProductCodeList();
    form.setFieldsValue(searchQuery);
  }, []);

  return (
    <NagrandQueryForm
      needSearchAfterClear
      title={t('Package')}
      formProps={{ form }}
      loading={loading}
      queryFields={[
        {
          col: 8,
          key: 'packageCode',
          label: t('Package Code'),
          type: FieldType.Input,
        },
        {
          col: 8,
          key: 'packageName',
          label: t('Package Name'),
          type: FieldType.Input,
        },
        {
          col: 8,
          key: 'packageCategory',
          label: t('Package Category'),
          type: FieldType.Select,
          extraProps: {
            options:
              productCategoryEnums?.map(item => ({
                value: item.itemExtend1,
                label: item.itemName,
              })) ?? [],
          },
        },
        {
          col: 8,
          key: 'productCode',
          label: t('Product Code'),
          type: FieldType.Select,
          extraProps: {
            options: productCodeOptions,
            onChange: (value: string) => {
              form.resetFields(['productVersion']);
              if (!value) {
                setProductVersionOptions([]);
              } else {
                queryProductVersionList(value);
              }
            },
          },
        },
        {
          col: 8,
          key: 'productVersion',
          label: t('Product Version'),
          type: FieldType.Select,
          extraProps: {
            disabled: !productCodeValue,
            options: productVersionOptions,
          },
        },
      ]}
      onSearch={search}
    />
  );
};

export default QueryForm;
