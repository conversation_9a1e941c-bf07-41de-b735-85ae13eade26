.table-filter-select {
  height: 0;

  .market-ant4-menu:not(.market-ant4-menu-horizontal) .market-ant4-menu-item-selected {
    background-color: var(--disabled-bg);
  }

  .market-ant4-menu-item:hover,
  .market-ant4-menu-item-active,
  .market-ant4-menu:not(.market-ant4-menu-inline) .market-ant4-menu-submenu-open,
  .market-ant4-menu-submenu-active,
  .market-ant4-menu-submenu-title:hover {
    color: inherit;
  }

  .status-icon {
    display: inline-block;
    vertical-align: middle;
    margin-right: 6px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }

  .market-ant4-menu-item-selected {
    color: inherit;
  }

  .draft {
    @extend .status-icon;
    background-color: $disabled-color;
  }

  .effective {
    @extend .status-icon;
    background-color: var(--success-color);
  }

  .invalid {
    @extend .status-icon;
    background-color: var(--error-color);
  }

  .ready {
    @extend .status-icon;
    background-color: var(--primary-color);
  }

  .pl12 {
    padding-left: 12px;
  }
}
