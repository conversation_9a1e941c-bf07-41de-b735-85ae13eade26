/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, message } from 'antd';

import { cloneDeep, keyBy, omit, uniq } from 'lodash-es';

import { Drawer } from '@zhongan/nagrand-ui';

import { YesOrNo } from 'genesis-web-service/lib/common.interface';
import {
  PackageRiderClosureParam,
  PackageRiderLaunchConfig,
  PackageRiderLaunchUpdateParam,
  RelatedPackageInfoResponse,
  RelatedPackageProductInfo,
} from 'genesis-web-service/lib/market';
import { CollocationBehavior, ProductCategoryDictValue } from 'genesis-web-service/lib/product';
import {
  ProductBasicInfoResponse,
  ProductCollocationResponseDTO,
} from 'genesis-web-service/service-types/product-types/package/index';

import GeneralSteps from '@market/components/GeneralSteps/GeneralSteps';
import { NewMarketService } from '@market/services/market/market.service.new';

import {
  LiabilityConfig,
  PackageConfig,
  RiderActionType,
  RiderPackageConfigInfo,
  RiderStatusType,
} from '../../interface';
import ConfigureBenefitDetailsStep from '../ConfigureBenefitDetailsStep';
import RelationshipConfigurationStep from '../RelationshipConfigurationStep';
import SelectRiderProductStep from '../SelectRiderProductStep';
import WaiverRelationshipConfigurationStep from '../WaiverRelationshipConfigurationStep';

interface Props {
  visible: boolean;
  actionType: RiderActionType;
  setVisible: (visible: boolean) => void;
}

export const RiderLaunchClosureDrawer = ({ visible, setVisible, actionType }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [current, setCurrent] = useState(0);
  const configureBenefitDetailsStepRef = useRef<{
    saveConfigureBenefitDetails: () => void;
    hasChanged: () => boolean;
  }>(null);
  const [selectedMainProducts, setSelectedMainProducts] = useState<ProductCollocationResponseDTO[]>([]);
  const [selectedRiderProduct, setSelectedRiderProduct] = useState<ProductBasicInfoResponse>();
  const [selectedWaiverRiderProductInfo, setSelectedWaiverRiderProductInfo] = useState<RelatedPackageInfoResponse>();
  // 上架时，选中表示上架;下架时，选中表示下架
  const [riderConfigInfo, setRiderConfigInfo] = useState<RiderPackageConfigInfo>({});
  const [waiverRelateConfigInfo, setWaiverRelateConfigInfo] = useState<RiderPackageConfigInfo>({});
  const [ILPPackageList, setILPPackageList] = useState<RelatedPackageInfoResponse[]>([]);
  const [relatedPackageProductInfoList, setRelatedPackageProductInfoList] = useState<RelatedPackageProductInfo[]>([]);

  const [selectPackageAndProductValue, setSelectPackageAndProductValue] = useState<number[]>([]);

  const relatedMainProductMap = useMemo(() => keyBy(selectedMainProducts, 'productId'), [selectedMainProducts]);

  const closeDrawer = useCallback(() => {
    setCurrent(0);
    setSelectedMainProducts([]);
    setSelectedRiderProduct(undefined);
    setVisible(false);
    setILPPackageList([]);
    setWaiverRelateConfigInfo({});
    setRiderConfigInfo({});
    setSelectPackageAndProductValue([]);
  }, [setVisible]);

  // 根据配置回显数据设置前端状态
  const initRiderConfigInfo = useCallback(
    (infoList: RelatedPackageProductInfo[], _actionType: RiderActionType) => {
      const tempConfigInfo: RiderPackageConfigInfo = {};
      const isClosure = _actionType === RiderActionType.Closure;
      infoList.forEach(relatedProduct => {
        // 下架的时候，如果是 rider 跟 主险是必须的关系，下架 功能置灰。
        const isCompulsory =
          +relatedMainProductMap[relatedProduct.mainProductId]?.collocationBehavior === +CollocationBehavior.Compulsory;

        relatedProduct.relatedPackageInfoBizResponseList.forEach(relatedPackage => {
          const liabilityConfig: LiabilityConfig = {};
          relatedPackage.packageLiabilityBizResponseList?.forEach(liability => {
            liabilityConfig[liability.liabilityId] = {
              isMandatory: liability.isMandatory as YesOrNo,
              // 回显的liability都是已经上架的, 下架时初始状态都是未选中的
              isSelect: isClosure ? false : relatedPackage.riderStatusType === +RiderStatusType.Active,
            };
          });

          const packageConfig: PackageConfig = {
            isMandatory: relatedPackage.isMandatory as YesOrNo,
            // 下架时初始状态都是未选中的
            isSelect: isClosure ? false : isCompulsory || relatedPackage.riderStatusType === +RiderStatusType.Active,
            liabilityConfig,
          };

          tempConfigInfo[`${relatedProduct.mainProductId}_${relatedPackage.packageId}`] = packageConfig;
        });
      });

      setRiderConfigInfo(tempConfigInfo);
    },
    [relatedMainProductMap]
  );

  const queryPackageProductsByRider = useCallback(
    (_actionType: RiderActionType) =>
      NewMarketService.PackageProductMgmtV2Service.queryPackageProduct({
        mainProductIdList: selectedMainProducts.map(product => product.productId!),
        riderProductId: selectedRiderProduct!.productId!,
      }).then(res => {
        const avaiableList = (res || [])
          .map(relatedProduct => ({
            ...relatedProduct,
            relatedPackageInfoBizResponseList: relatedProduct.relatedPackageInfoBizResponseList.filter(packageInfo => {
              if (_actionType === RiderActionType.Launch) {
                // 上架时，只展示未上架的数据
                return packageInfo.riderStatusType === +RiderStatusType.Inactive;
              }

              // 下架时，只展示已上架的数据
              return packageInfo.riderStatusType === +RiderStatusType.Active;
            }),
          }))
          .filter(relatedProduct => relatedProduct.relatedPackageInfoBizResponseList.length > 0);

        setRelatedPackageProductInfoList(avaiableList);
        initRiderConfigInfo(avaiableList, _actionType);
      }),
    [initRiderConfigInfo, selectedMainProducts, selectedRiderProduct]
  );

  const queryPackageProductsByWaiverRider = useCallback(
    (_actionType: RiderActionType) =>
      NewMarketService.PackageProductMgmtV2Service.queryAllPackageProduct({
        mainProductIdList: selectedMainProducts.map(product => product.productId!),
        riderProductId: selectedRiderProduct!.productId!,
      }).then(res => {
        const avaiableList = (res || [])
          .map(relatedProduct => ({
            ...relatedProduct,
            relatedPackageInfoBizResponseList: relatedProduct.relatedPackageInfoBizResponseList.filter(productInfo => {
              if (_actionType === RiderActionType.Launch) {
                // 上架时，只展示未上架的数据
                return (
                  !productInfo?.isRelatedWaiver && productInfo.productCategoryCode !== +ProductCategoryDictValue.Waiver
                );
              }

              // 下架时，只展示已上架的数据
              return (
                productInfo?.isRelatedWaiver && productInfo.productCategoryCode !== +ProductCategoryDictValue.Waiver
              );
            }),
          }))
          .filter(relatedProduct => relatedProduct.relatedPackageInfoBizResponseList.length > 0);

        const waiverRiderInfo = res?.[0]?.waiverProductBasicInfoResponse;
        if (waiverRiderInfo) {
          setSelectedWaiverRiderProductInfo({
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            ...waiverRiderInfo.productBasicInfo,
            packageLiabilityUpdateBizRequests: waiverRiderInfo.productLiabilityList,
          });
        }
        setRelatedPackageProductInfoList(avaiableList);
      }),
    [selectedMainProducts, selectedRiderProduct]
  );

  const launchSubmit = useCallback(() => {
    const updateList: PackageRiderLaunchConfig[] = [];
    const updatePackageList: number[] = [];
    relatedPackageProductInfoList.forEach(item => {
      item.relatedPackageInfoBizResponseList.forEach(packageInfo => {
        const packageConfig = riderConfigInfo[`${item.mainProductId}_${packageInfo.packageId}`];
        if (packageConfig.isSelect) {
          updatePackageList.push(packageInfo.packageId);
          const tempPackageInfo: PackageRiderLaunchConfig = {
            ...omit(packageInfo, ['packageLiabilityBizResponseList']),
            parentProductId: item.mainProductId,
            // 只需要提交上架的数据
            riderStatusType: +RiderStatusType.Active,
            packageLiabilityUpdateBizRequests: packageInfo.packageLiabilityBizResponseList.map(liability => ({
              ...liability,
              isMandatory: packageConfig.liabilityConfig[liability.liabilityId].isMandatory,
            })),
          };

          tempPackageInfo.isMandatory = packageConfig.isMandatory;

          updateList.push(tempPackageInfo);
        }
      });
    });
    const param: PackageRiderLaunchUpdateParam = {
      packageIdList: uniq(updatePackageList),
      activeBizRequestList: updateList,
      mainProductIdList: selectedMainProducts.map(product => product.productId!),
      riderProductId: selectedRiderProduct!.productId!,
    };

    NewMarketService.PackageProductMgmtV2Service.activePackageProduct(param).then(() => {
      message.success(t('Submit Successfully'));
      if (ILPPackageList.length > 0) {
        setCurrent(2);
      } else {
        closeDrawer();
      }
    });
  }, [
    ILPPackageList,
    closeDrawer,
    relatedPackageProductInfoList,
    riderConfigInfo,
    t,
    selectedMainProducts,
    selectedRiderProduct,
  ]);

  const closureSubmit = useCallback(() => {
    const packageIdList: number[] = [];

    relatedPackageProductInfoList.forEach(item =>
      item.relatedPackageInfoBizResponseList.forEach(packageInfo => {
        const packageConfig = riderConfigInfo[`${item.mainProductId}_${packageInfo.packageId}`];

        if (packageConfig.isSelect) {
          packageIdList.push(packageInfo.packageId);
        }
      })
    );
    if (packageIdList.length === 0) {
      message.error(t('Please select Related Package'));
      return;
    }
    const param: PackageRiderClosureParam = {
      mainProductIdList: selectedMainProducts.map(product => product.productId!),
      riderProductId: selectedRiderProduct!.productId!,
      packageIdList,
    };

    NewMarketService.PackageProductMgmtV2Service.inactivePackageProduct(param).then(() => {
      message.success(t('Submit Successfully'));
      closeDrawer();
    });
  }, [closeDrawer, relatedPackageProductInfoList, riderConfigInfo, selectedMainProducts, selectedRiderProduct, t]);

  const onSubmit = useCallback(() => {
    if (actionType === RiderActionType.Launch) {
      launchSubmit();
    } else {
      closureSubmit();
    }
  }, [actionType, closureSubmit, launchSubmit]);

  const productInfoDataMapByWaiverRider = useMemo(() => {
    const tempProductInfoMap: Record<number, RelatedPackageInfoResponse[]> = {};
    let tatolProductList: RelatedPackageInfoResponse[] = [];
    const tempRelatedPackageProductInfoList = cloneDeep(relatedPackageProductInfoList);
    tempRelatedPackageProductInfoList.forEach(relatedPackageProduct => {
      tatolProductList = tatolProductList.concat(relatedPackageProduct.relatedPackageInfoBizResponseList);
    });
    tatolProductList.forEach(productItem => {
      if (tempProductInfoMap[productItem.packageId]) {
        tempProductInfoMap[productItem.packageId].push(productItem);
      } else {
        tempProductInfoMap[productItem.packageId] = [productItem];
      }
    });
    return tempProductInfoMap;
  }, [relatedPackageProductInfoList]);

  const launchWaiwerSubmit = useCallback(() => {
    const updateList: PackageRiderLaunchConfig[] = [];
    const updatePackageList: number[] = [];
    selectPackageAndProductValue.forEach(packageId => {
      if (productInfoDataMapByWaiverRider[packageId]) {
        productInfoDataMapByWaiverRider[packageId].forEach(productInfo => {
          const productConfig = waiverRelateConfigInfo[`${productInfo.productId}_${packageId}`];
          if (productConfig.isSelect) {
            updatePackageList.push(packageId);
            const tempPackageInfo: PackageRiderLaunchConfig = {
              ...selectedWaiverRiderProductInfo,
              packageId,
              productType: selectedWaiverRiderProductInfo?.productTypeCode as string,
              attachToProductId: productInfo.productId,
              isMandatory: productConfig.isMandatory,
            };

            updateList.push(tempPackageInfo);
          }
        });
      }
    });
    const param: PackageRiderLaunchUpdateParam = {
      packageIdList: uniq(updatePackageList),
      activeBizRequestList: updateList,
      mainProductIdList: selectedMainProducts.map(product => product.productId!),
      riderProductId: selectedRiderProduct!.productId!,
    };

    NewMarketService.PackageProductMgmtV2Service.activeWaiver(param).then(() => {
      message.success(t('Submit Successfully'));
      if (ILPPackageList.length > 0) {
        setCurrent(2);
      } else {
        closeDrawer();
      }
    });
  }, [
    ILPPackageList,
    closeDrawer,
    productInfoDataMapByWaiverRider,
    selectPackageAndProductValue,
    selectedMainProducts,
    selectedRiderProduct,
    selectedWaiverRiderProductInfo,
    t,
    waiverRelateConfigInfo,
  ]);

  const closureWaiwerSubmit = useCallback(() => {
    const updateList: RelatedPackageInfoResponse[] = [];
    const updatePackageList: number[] = [];
    selectPackageAndProductValue.forEach(packageId => {
      if (productInfoDataMapByWaiverRider[packageId]) {
        productInfoDataMapByWaiverRider[packageId].forEach(productInfo => {
          const productConfig = waiverRelateConfigInfo[`${productInfo.productId}_${packageId}`];
          if (productConfig.isSelect) {
            updatePackageList.push(packageId);
            updateList.push(productInfo);
          }
        });
      }
    });
    const param: PackageRiderClosureParam = {
      mainProductIdList: selectedMainProducts.map(product => product.productId!),
      riderProductId: selectedRiderProduct!.productId!,
      packageIdList: updatePackageList,
      attachToProductIdList: updateList.map(item => item.productId),
    };

    NewMarketService.PackageProductMgmtV2Service.inactiveWaiver(param).then(() => {
      message.success(t('Submit Successfully'));
      closeDrawer();
    });
  }, [
    closeDrawer,
    productInfoDataMapByWaiverRider,
    selectPackageAndProductValue,
    selectedMainProducts,
    selectedRiderProduct,
    t,
    waiverRelateConfigInfo,
  ]);

  const onWaiwerSubmit = useCallback(() => {
    if (actionType === RiderActionType.Launch) {
      launchWaiwerSubmit();
    } else {
      closureWaiwerSubmit();
    }
  }, [actionType, closureWaiwerSubmit, launchWaiwerSubmit]);

  const renderStepsInfo = useCallback(() => {
    const currentSteps = [
      {
        title: t('Select Rider'),
      },
      {
        title: t('Set Relationship with Package'),
      },
    ];
    if (ILPPackageList.length > 0) {
      currentSteps.push({
        title: t('Configure Benefit Details'),
      });
    }
    return currentSteps;
  }, [ILPPackageList, t]);

  return (
    <Drawer
      open={visible}
      title={actionType === RiderActionType.Launch ? t('Launch Rider') : t('Closure Rider')}
      onClose={closeDrawer}
      footer={
        <div className="text-right">
          <Button onClick={closeDrawer} size="large">
            {t('Cancel')}
          </Button>
          {current === 0 && (
            <Button
              onClick={() => {
                if (selectedRiderProduct?.productCategoryCode === ProductCategoryDictValue.Waiver) {
                  queryPackageProductsByWaiverRider(actionType).then(() => {
                    setCurrent(1);
                  });
                } else {
                  queryPackageProductsByRider(actionType).then(() => {
                    setCurrent(1);
                  });
                }
              }}
              disabled={selectedMainProducts.length === 0}
              ghost
              className="ml-md"
              size="large"
              type="primary"
            >
              {t('Next')}
            </Button>
          )}
          {current === 1 && (
            <Button
              onClick={
                selectedRiderProduct?.productCategoryCode === ProductCategoryDictValue.Waiver
                  ? onWaiwerSubmit
                  : onSubmit
              }
              className="ml-md"
              type="primary"
              size="large"
            >
              {ILPPackageList.length > 0 ? t('Next') : t('Submit')}
            </Button>
          )}
          {current === 2 && (
            <Button
              onClick={() => {
                if (configureBenefitDetailsStepRef?.current && configureBenefitDetailsStepRef.current.hasChanged()) {
                  configureBenefitDetailsStepRef?.current?.saveConfigureBenefitDetails();
                  closeDrawer();
                }
              }}
              className="ml-md"
              type="primary"
              size="large"
            >
              {t('Submit')}
            </Button>
          )}
        </div>
      }
    >
      <div>
        <div style={{ width: '90%', marginBottom: 24 }}>
          <GeneralSteps current={current} steps={renderStepsInfo()} />
        </div>
        {current === 0 ? (
          <SelectRiderProductStep
            visible={visible}
            selectedMainProducts={selectedMainProducts}
            setSelectedMainProducts={setSelectedMainProducts}
            setRiderProduct={setSelectedRiderProduct}
          />
        ) : null}
        {current === 1 && selectedRiderProduct?.productCategoryCode !== ProductCategoryDictValue.Waiver ? (
          <RelationshipConfigurationStep
            actionType={actionType}
            selectedRiderProduct={selectedRiderProduct!}
            selectedMainProducts={selectedMainProducts}
            relatedPackageProductInfoList={relatedPackageProductInfoList}
            riderConfigInfo={riderConfigInfo}
            setRiderConfigInfo={setRiderConfigInfo}
            setILPPackageList={setILPPackageList}
          />
        ) : null}
        {current === 1 && selectedRiderProduct?.productCategoryCode === ProductCategoryDictValue.Waiver ? (
          <WaiverRelationshipConfigurationStep
            actionType={actionType}
            productInfoDataMap={productInfoDataMapByWaiverRider}
            selectedRiderProduct={selectedRiderProduct}
            selectPackageAndProductValue={selectPackageAndProductValue}
            selectedMainProducts={selectedMainProducts}
            relatedPackageProductInfoList={relatedPackageProductInfoList}
            waiverRelateConfigInfo={waiverRelateConfigInfo}
            setWaiverRelateConfigInfo={setWaiverRelateConfigInfo}
            setILPPackageList={setILPPackageList}
            setSelectPackageAndProductValue={setSelectPackageAndProductValue}
          />
        ) : null}
        {current === 2 ? (
          <ConfigureBenefitDetailsStep
            actionType={actionType}
            ILPPackageList={ILPPackageList}
            selectedRiderProduct={selectedRiderProduct!}
            refInstance={configureBenefitDetailsStepRef}
          />
        ) : null}
      </div>
    </Drawer>
  );
};

export default RiderLaunchClosureDrawer;
