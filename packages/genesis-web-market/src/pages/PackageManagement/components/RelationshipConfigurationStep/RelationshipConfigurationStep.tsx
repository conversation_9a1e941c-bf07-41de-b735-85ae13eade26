import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import classNames from 'classnames/bind';
import { keyBy } from 'lodash-es';

import { ColumnsType, StatusLabel, StatusTag, Table } from '@zhongan/nagrand-ui';

import { YesOrNo } from 'genesis-web-service';
import type {
  RelatedPackageInfoResponse,
  RelatedPackageLiability,
  RelatedPackageProductInfo,
} from 'genesis-web-service/lib/market';
import { CollocationBehavior, ProductCategoryItemExtend1 } from 'genesis-web-service/lib/product';
import {
  ProductBasicInfoResponse,
  ProductCollocationResponseDTO,
} from 'genesis-web-service/service-types/product-types/package/index';

import GeneralSelect from '@market/components/GeneralSelect/GeneralSelect';
import { useBizDict, useBizDictAsOptions } from '@market/hook/bizDict';
import { renderOptionName } from '@market/utils/enum';

import { RiderActionType, RiderPackageConfigInfo, RiderStatusType } from '../../interface';
import styles from './RelationshipConfigurationStep.module.scss';

const ILPPackageCategoryCodes = [
  ProductCategoryItemExtend1.UnitLinked,
  ProductCategoryItemExtend1.Universal,
  ProductCategoryItemExtend1.VariableAnnuity,
];
const cx = classNames.bind(styles);

interface Props {
  selectedMainProducts: ProductCollocationResponseDTO[];
  selectedRiderProduct: ProductBasicInfoResponse;
  relatedPackageProductInfoList: RelatedPackageProductInfo[];
  riderConfigInfo: RiderPackageConfigInfo;
  setRiderConfigInfo: (config: RiderPackageConfigInfo) => void;
  actionType: RiderActionType;
  setILPPackageList: (arr: RelatedPackageInfoResponse[]) => void;
}

export const RelationshipConfigurationStep = ({
  selectedMainProducts,
  selectedRiderProduct,
  relatedPackageProductInfoList,
  riderConfigInfo,
  setRiderConfigInfo,
  actionType,
  setILPPackageList,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  /* ============== 枚举使用start ============== */
  const collocationBehaviorOptions = useBizDictAsOptions('collocationBehavior');
  const liabilityCategoryOptions = useBizDictAsOptions('liabilityCategory');
  const riderStatusTypeOptions = useBizDictAsOptions('riderStatusType');
  const yesNoBizDicts = useBizDict('yesNo');
  /* ============== 枚举使用end ============== */
  const isClosure = actionType === RiderActionType.Closure;

  const relatedMainProductMap = useMemo(() => keyBy(selectedMainProducts, 'productId'), [selectedMainProducts]);
  const collocationBehaviorMap = useMemo(
    () => keyBy(collocationBehaviorOptions, 'value'),
    [collocationBehaviorOptions]
  );
  const riderStatusTypeMap = useMemo(() => keyBy(riderStatusTypeOptions, 'value'), [riderStatusTypeOptions]);

  useEffect(() => {
    if (!isClosure) {
      let tempPackageList: RelatedPackageInfoResponse[] = [];
      relatedPackageProductInfoList.forEach(relatedPackageProductItem => {
        if (
          Array.isArray(relatedPackageProductItem.relatedPackageInfoBizResponseList) &&
          relatedPackageProductItem.relatedPackageInfoBizResponseList.length > 0
        ) {
          // 根据勾选产品获取package信息
          tempPackageList = tempPackageList.concat(
            relatedPackageProductItem.relatedPackageInfoBizResponseList.filter(item =>
              ILPPackageCategoryCodes.includes(item.packageCategoryId)
            )
          );
        }
      });
      setILPPackageList(tempPackageList);
    }
  }, [isClosure, relatedPackageProductInfoList, setILPPackageList]);

  const expandedRowRender = useCallback(
    (relatedPackageProduct: RelatedPackageProductInfo) => (_package: RelatedPackageInfoResponse) => {
      const liabilityList = _package.packageLiabilityBizResponseList;
      const liabilityConfig =
        riderConfigInfo[`${relatedPackageProduct.mainProductId}_${_package.packageId}`]?.liabilityConfig || {};
      const disabled =
        actionType === RiderActionType.Closure ||
        (actionType === RiderActionType.Launch && _package.riderStatusType === +RiderStatusType.Active);
      const product = relatedMainProductMap[relatedPackageProduct.mainProductId];
      const isNonAttachable = +product.collocationBehavior! === +CollocationBehavior.NonAttachable;

      const columns: ColumnsType<RelatedPackageLiability> = [
        {
          title: t('Rider Liability'),
          dataIndex: 'liabilityName',
        },
        {
          title: t('Liability Category'),
          dataIndex: 'liabilityCategoryCode',
          render: (text: number) => renderOptionName(`${text}`, liabilityCategoryOptions),
        },
        {
          title: t('Mandatory or Not'),
          width: 160,
          dataIndex: 'isMandatory',
          render: (text, record) => (
            <GeneralSelect
              style={{ width: 128 }}
              size="small"
              value={liabilityConfig[record.liabilityId]?.isMandatory}
              onChange={value => {
                liabilityConfig[record.liabilityId].isMandatory = value;
                setRiderConfigInfo({ ...riderConfigInfo });
              }}
              disabled={disabled || isNonAttachable}
              allowClear={false}
              option={yesNoBizDicts.map(item => ({
                label: item.dictValueName,
                value: item.enumItemName,
              }))}
            />
          ),
        },
      ];

      return (
        <Table
          columns={columns}
          dataSource={liabilityList}
          pagination={false}
          size="small"
          rowKey="liabilityId"
          // *****liability的上下架跟随product一起，不做单独操作，代码先不删，万一要加回来*****
          // rowSelection={{
          //   selectedRowKeys: liabilityList.filter(liability => liabilityConfig?.[liability.liabilityId]?.isSelect).map(liability => liability.liabilityId) || [],
          //   onChange: (selectedRowKeys) => {
          //     liabilityList.forEach(liability => {
          //       if (selectedRowKeys.includes(liability.liabilityId)) {
          //         liabilityConfig[liability.liabilityId].isSelect = true;
          //       } else {
          //         liabilityConfig[liability.liabilityId].isSelect = false;
          //       }
          //       setRiderConfigInfo({ ...riderConfigInfo });
          //     })
          //   },
          //   getCheckboxProps: () => ({
          //     disabled: true,
          //   })
          // }}
        />
      );
    },
    [actionType, liabilityCategoryOptions, riderConfigInfo, setRiderConfigInfo, t, yesNoBizDicts]
  );

  const isPackageLevelDisabled = useCallback(
    (relatedPackage: RelatedPackageInfoResponse) => {
      let disabled = false;

      // 不可重复上架
      if (actionType === RiderActionType.Launch && relatedPackage.riderStatusType === +RiderStatusType.Active) {
        disabled = true;
      }

      // 不可重复下架
      if (actionType === RiderActionType.Closure && relatedPackage.riderStatusType === +RiderStatusType.Inactive) {
        disabled = true;
      }

      return disabled;
    },
    [actionType]
  );

  const renderPackageTable = useCallback(
    (relatedPackageProduct: RelatedPackageProductInfo) => {
      const packageList = relatedPackageProduct.relatedPackageInfoBizResponseList;
      const product = relatedMainProductMap[relatedPackageProduct.mainProductId];
      const isNonAttachable = +product.collocationBehavior! === +CollocationBehavior.NonAttachable;

      return (
        <Table
          rowSelection={{
            selectedRowKeys: packageList
              .filter(
                _package => riderConfigInfo[`${relatedPackageProduct.mainProductId}_${_package.packageId}`]?.isSelect
              )
              .map(_package => _package.packageId),
            onChange: selectedRowKeys => {
              packageList.forEach(_package => {
                if (selectedRowKeys.includes(_package.packageId)) {
                  riderConfigInfo[`${relatedPackageProduct.mainProductId}_${_package.packageId}`].isSelect = true;
                } else {
                  riderConfigInfo[`${relatedPackageProduct.mainProductId}_${_package.packageId}`].isSelect = false;
                }
                setRiderConfigInfo({ ...riderConfigInfo });
              });
            },
            getCheckboxProps: (relatedPackage: RelatedPackageInfoResponse) => {
              let checkboxDisabled = isPackageLevelDisabled(relatedPackage);

              if (+product.collocationBehavior! === +CollocationBehavior.Compulsory) {
                checkboxDisabled = true;
              }

              return {
                disabled: checkboxDisabled || isNonAttachable,
              };
            },
          }}
          dataSource={packageList}
          rowKey="packageId"
          columns={[
            {
              title: t('Package Code'),
              dataIndex: 'packageCode',
            },
            {
              title: t('Package Name'),
              dataIndex: 'packageName',
            },
            {
              title: t('Current Rider Status on Package'),
              dataIndex: 'riderStatusType',
              render: (text: number) => (
                <StatusTag
                  statusI18n={riderStatusTypeMap[text]?.label}
                  type={text === +RiderStatusType.Active ? 'success' : 'no-status'}
                  needDot
                />
              ),
            },
            {
              title: t('Mandatory Rider'),
              width: 160,
              dataIndex: 'isMandatory',
              render: (text, record) => (
                <GeneralSelect
                  style={{ width: 128 }}
                  value={
                    riderConfigInfo[`${relatedPackageProduct.mainProductId}_${record.packageId}`]?.isMandatory ||
                    YesOrNo.NO
                  }
                  onChange={value => {
                    riderConfigInfo[`${relatedPackageProduct.mainProductId}_${record.packageId}`].isMandatory = value;
                    setRiderConfigInfo({ ...riderConfigInfo });
                  }}
                  disabled={isPackageLevelDisabled(record) || isNonAttachable}
                  allowClear={false}
                  option={yesNoBizDicts.map(item => ({
                    label: item.dictValueName,
                    value: item.enumItemName,
                  }))}
                />
              ),
            },
          ]}
          pagination={false}
          expandType="nestedTable"
          expandable={{
            defaultExpandAllRows: true,
            expandedRowRender: expandedRowRender(relatedPackageProduct),
          }}
        />
      );
    },
    [
      expandedRowRender,
      isPackageLevelDisabled,
      relatedMainProductMap,
      riderConfigInfo,
      riderStatusTypeMap,
      setRiderConfigInfo,
      t,
      yesNoBizDicts,
    ]
  );

  return (
    <div>
      <div className={cx('title')}>
        {actionType === RiderActionType.Closure ? t('Closure Rider') : t('Launch Rider')}:
        {`${selectedRiderProduct.productName!} (${selectedRiderProduct.insuranceProductCode!})`}
      </div>
      <div className={cx('sub-title-wrapper')}>
        <div className={cx('sub-title')}>{t('Related Main')}</div>
        <div className={cx('related-main-product-list')}>
          {relatedPackageProductInfoList.map(relatedPackageProduct => {
            const product = relatedMainProductMap[relatedPackageProduct.mainProductId];

            return (
              <div className={cx('related-main-product-container')}>
                <div className={cx('related-main-product-upper')}>
                  <div className={cx('related-main-product-title')}>
                    {`${product.productName!} (${product.insuranceProductCode!})`}
                  </div>
                  <StatusLabel
                    statusI18n={collocationBehaviorMap[product.collocationBehavior!]?.label}
                    type={+product.collocationBehavior! === +CollocationBehavior.Compulsory ? 'info' : 'success'}
                    style={{ marginLeft: '16px' }}
                  />
                </div>
                <div className={cx('related-main-product-content')}>
                  <div className={cx('content-title')}>{t('Related Package Info')}</div>
                  {renderPackageTable(relatedPackageProduct)}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default RelationshipConfigurationStep;
