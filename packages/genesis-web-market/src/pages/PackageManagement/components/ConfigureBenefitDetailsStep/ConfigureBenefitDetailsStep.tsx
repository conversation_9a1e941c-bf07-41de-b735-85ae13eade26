import { Ref, useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Col, Form, Radio, Row, Space, message } from 'antd';

import classNames from 'classnames/bind';
import { cloneDeep } from 'lodash-es';

import { Table } from '@zhongan/nagrand-ui';

import {
  RelatedPackageInfoResponse,
  activeInitBenefitDetailsRequestList,
} from 'genesis-web-service/lib/market';
import { ProductBasicInfoResponse } from 'genesis-web-service/service-types/product-types/package/index';

import { PackagePaymentMethodDefine } from '@market/common/enums';
import GeneralSelect from '@market/components/GeneralSelect/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';

import { RiderActionType } from '../../interface';
import styles from './ConfigureBenefitDetailsStep.module.scss';
import { NewMarketService } from '@market/services/market/market.service.new';

const cx = classNames.bind(styles);

interface Props {
  selectedRiderProduct: ProductBasicInfoResponse;
  actionType: RiderActionType;
  refInstance: Ref<unknown>;
  ILPPackageList: RelatedPackageInfoResponse[];
}

export const ConfigureBenefitDetailsStep = ({
  selectedRiderProduct,
  actionType,
  refInstance,
  ILPPackageList,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();

  /* ============== 枚举使用start ============== */
  const ilpPaymentMethodOptions = useBizDictAsOptions('ilpPaymentMethod');
  const packagePaymentMethodDefineOptions = useBizDictAsOptions('packagePaymentMethodDefine');
  /* ============== 枚举使用end ============== */
  const [hasChangedStatus, setHasChangedStatus] = useState<boolean>(false);
  const [currentPackagePaymentMethodDefine, setCurrentPackagePaymentMethodDefine] = useState<string>();
  const [packagePaymentMethodList, setPackagePaymentMethodList] = useState<activeInitBenefitDetailsRequestList[]>([]);

  useEffect(() => {
    setPackagePaymentMethodList(
      ILPPackageList.map(item => ({
        packageId: item.packageId,
        productId: selectedRiderProduct?.productId,
        paymentMethod: undefined,
      }))
    );
    form.setFieldsValue({
      packagePaymentMethodDefine: PackagePaymentMethodDefine.DefineCalculationMethodforEachPackage,
    });
  }, [ILPPackageList]);

  useImperativeHandle(refInstance, () => ({
    saveConfigureBenefitDetails: () =>
      new Promise((resolve, reject) => {
        form
          .validateFields()
          .then((values: { packagePaymentMethodDefine: string; paymentMethodforSelectedPackage: string }) => {
            const tempPackagePaymentMethodList = cloneDeep(packagePaymentMethodList);
            if (
              values.packagePaymentMethodDefine === PackagePaymentMethodDefine.UnifiedPaymentMethodforSelectedPackage
            ) {
              tempPackagePaymentMethodList.forEach(item => {
                item.paymentMethod = values.paymentMethodforSelectedPackage;
              });
            }
            const param = {
              packagePaymentMethodDefine: values.packagePaymentMethodDefine,
              activeBizRequestList: tempPackagePaymentMethodList,
            };
            NewMarketService.PackageProductMgmtV2Service.initBenefitDetails(param).then(res => {
              message.success(t('Submit success'));
              resolve(true);
            });
          });
      }),
    hasChanged: () => hasChangedStatus,
  }));

  return (
    <div>
      <div className={cx('title')}>
        {actionType === RiderActionType.Closure ? t('Closure Rider') : t('Launch Rider')}:
        {`${selectedRiderProduct.productName!} (${selectedRiderProduct.insuranceProductCode!})`}
      </div>
      <Form form={form} layout="vertical">
        <Row>
          <Col span={24}>
            <Form.Item
              label={t('Rider Premium Payment Method')}
              name="packagePaymentMethodDefine"
              rules={[
                {
                  required: true,
                  message: t('Please select'),
                },
              ]}
            >
              <Radio.Group
                onChange={value => {
                  setHasChangedStatus(true);
                  setCurrentPackagePaymentMethodDefine(value.target.value);
                }}
              >
                <Space direction="vertical">
                  {packagePaymentMethodDefineOptions.map(option => (
                    <Radio style={{ marginRight: 5 }} value={option.value}>
                      {currentPackagePaymentMethodDefine ===
                        PackagePaymentMethodDefine.UnifiedPaymentMethodforSelectedPackage &&
                      option.value === PackagePaymentMethodDefine.UnifiedPaymentMethodforSelectedPackage ? (
                        <span style={{ marginBottom: 16 }}>
                          <span style={{ marginRight: 15 }}>{option.label}</span>
                          <Form.Item
                            noStyle
                            name="paymentMethodforSelectedPackage"
                            required
                            rules={[
                              {
                                required: true,
                                message: t('Please input'),
                              },
                            ]}
                          >
                            <GeneralSelect
                              onChange={() => {
                                setHasChangedStatus(true);
                              }}
                              option={ilpPaymentMethodOptions}
                              style={{ width: 240 }}
                            />
                          </Form.Item>
                        </span>
                      ) : (
                        option.label
                      )}
                    </Radio>
                  ))}
                </Space>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      {currentPackagePaymentMethodDefine ===
      PackagePaymentMethodDefine.UnifiedPaymentMethodforSelectedPackage ? null : (
        <Table
          dataSource={ILPPackageList}
          rowKey="productId"
          columns={[
            {
              title: t('Package Code'),
              dataIndex: 'packageCode',
            },
            {
              title: t('Package Name'),
              dataIndex: 'packageName',
            },
            {
              title: t('Rider Premium Payment Method'),
              width: 340,
              dataIndex: 'paymentMethod',
              render: (text, record, index) => (
                <GeneralSelect
                  style={{ width: 240 }}
                  onChange={value => {
                    const tempPackagePaymentMethodList = cloneDeep(packagePaymentMethodList);
                    tempPackagePaymentMethodList[index].paymentMethod = value;
                    setPackagePaymentMethodList(tempPackagePaymentMethodList);
                    setHasChangedStatus(true);
                  }}
                  allowClear={false}
                  option={ilpPaymentMethodOptions}
                />
              ),
            },
          ]}
          pagination={false}
        />
      )}
    </div>
  );
};

export default ConfigureBenefitDetailsStep;
