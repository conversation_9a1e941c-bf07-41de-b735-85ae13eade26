import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Col, Divider, Form, Row } from 'antd';

import { Table } from '@zhongan/nagrand-ui';

import { ProductCategoryDictValue } from 'genesis-web-service/lib/product';
import {
  ProductBasicInfoResponse,
  ProductCollocationResponseDTO,
} from 'genesis-web-service/service-types/product-types/package/index';

import GeneralSelect from '@market/components/GeneralSelect';
import { useBizDictAsOptions } from '@market/hook/bizDict';
import { useRiderProductList } from '@market/hook/product.service';
import { NewProductService } from '@market/services/product/product.service.new';
import { renderOptionName } from '@market/utils/enum';

interface Props {
  visible: boolean;
  selectedMainProducts: ProductCollocationResponseDTO[];
  setSelectedMainProducts: (ids: ProductCollocationResponseDTO[]) => void;
  setRiderProduct: (rider: ProductBasicInfoResponse) => void;
}

export const SelectRiderProductStep = ({
  visible,
  selectedMainProducts,
  setSelectedMainProducts,
  setRiderProduct,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [form] = Form.useForm();
  /* ============== 枚举使用start ============== */
  const productCategoryOptions = useBizDictAsOptions('productCategory');
  /* ============== 枚举使用end ============== */

  const [productCategory, setProductCategory] = useState<string>();
  const [relatedMainProducts, setRelatedMainProducts] = useState<ProductCollocationResponseDTO[]>([]);

  const { data: riderProducts } = useRiderProductList();

  useEffect(() => {
    if (!visible) {
      setRelatedMainProducts([]);
      form.setFieldValue('riderProductId', undefined);
    }
  }, [form, visible]);

  const queryRelatedMainProducts = useCallback(
    (riderProductId: number) => {
      NewProductService.ProductCollocationService.query({
        collocationProductId: riderProductId,
      }).then(res => {
        if (res.success && res.value) {
          setRelatedMainProducts(res.value);
          // 查出来默认全选
          setSelectedMainProducts(res.value);
        } else {
          setRelatedMainProducts([]);
        }
      });
    },
    [setSelectedMainProducts]
  );

  const onSearch = useCallback(() => {
    form.validateFields().then((values: { riderProductId: number }) => {
      setSelectedMainProducts([]);
      queryRelatedMainProducts(values.riderProductId);
      setRiderProduct(riderProducts.find(rider => rider.productId! === values.riderProductId)!);
    });
  }, [form, queryRelatedMainProducts, riderProducts, setRiderProduct, setSelectedMainProducts]);

  const renderRiderInfo = useCallback(
    (rider: ProductBasicInfoResponse) => {
      if (rider.productCategoryCode! === ProductCategoryDictValue.Waiver) {
        return `${rider.productCode!} - ${rider.productVersion!} ${t('Waiver')}`;
      }
      return `${rider.productCode!} - ${rider.productVersion!}`;
    },
    [t]
  );

  return (
    <div>
      <Form layout="vertical" form={form}>
        <Row style={{ alignItems: 'end' }}>
          <Col span={8}>
            <Form.Item label={t('Product Category')}>
              <GeneralSelect
                onChange={value => {
                  form.setFieldValue('riderProductId', undefined);
                  setProductCategory(value);
                  setRelatedMainProducts([]);
                  setSelectedMainProducts([]);
                }}
                option={productCategoryOptions}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              required
              name="riderProductId"
              label={t('Rider Product Code')}
              rules={[
                {
                  message: t('Please select'),
                  required: true,
                },
              ]}
            >
              <GeneralSelect
                option={riderProducts
                  .filter(rider => {
                    if (!productCategory) {
                      return true;
                    }
                    return rider.productCategoryCode === productCategory;
                  })
                  .map(rider => ({
                    label: renderRiderInfo(rider),
                    value: rider.productId!,
                  }))}
                onChange={() => {
                  setRelatedMainProducts([]);
                  setSelectedMainProducts([]);
                }}
              />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item>
              <Button onClick={onSearch} type="primary">
                {t('Search')}
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Divider />
      <div>
        <div>
          <div style={{ marginBottom: 8, fontWeight: 700 }}>{t('Related Main')}</div>
          <Table
            rowKey="productId"
            rowSelection={{
              selectedRowKeys: selectedMainProducts?.map(product => product.productId!),
              onChange: (selectedRowKeys, selectedRows) => {
                setSelectedMainProducts(selectedRows);
              },
            }}
            columns={[
              {
                title: t('Product Code'),
                dataIndex: 'insuranceProductCode',
              },
              {
                title: t('Product Name'),
                dataIndex: 'productName',
              },
              {
                title: t('Product Category'),
                dataIndex: 'productCategoryCode',
                render: (text: number) => renderOptionName(`${text}`, productCategoryOptions),
              },
            ]}
            dataSource={relatedMainProducts}
            pagination={false}
          />
        </div>
      </div>
    </div>
  );
};

export default SelectRiderProductStep;
