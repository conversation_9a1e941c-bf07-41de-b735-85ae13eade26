import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { But<PERSON>, Modal, Tree } from 'antd';

import classNames from 'classnames/bind';
import { cloneDeep, keyBy, uniqBy } from 'lodash-es';

import { ColumnsType, Icon, Table } from '@zhongan/nagrand-ui';

import { YesOrNo } from 'genesis-web-service';
import type {
  RelatedPackageInfoResponse,
  RelatedPackageLiability,
  RelatedPackageProductInfo,
} from 'genesis-web-service/lib/market';
import { ProductCategoryItemExtend1 } from 'genesis-web-service/lib/product';
import {
  ProductBasicInfoResponse,
  ProductCollocationResponseDTO,
} from 'genesis-web-service/service-types/product-types/package/index';

import { TreeDataInfo } from '@market/common/packages.interface';
import GeneralSelect from '@market/components/GeneralSelect/GeneralSelect';
import { useBizDict, useBizDictAsOptions } from '@market/hook/bizDict';
import { renderOptionName } from '@market/utils/enum';

import {
  LiabilityConfig,
  PackageConfig,
  RiderActionType,
  RiderPackageConfigInfo,
  RiderStatusType,
} from '../../interface';
import styles from './WaiverRelationshipConfigurationStep.module.scss';

const cx = classNames.bind(styles);

const ILPPackageCategoryCodes = [
  ProductCategoryItemExtend1.UnitLinked,
  ProductCategoryItemExtend1.Universal,
  ProductCategoryItemExtend1.VariableAnnuity,
];

interface Props {
  selectedMainProducts: ProductCollocationResponseDTO[];
  selectedRiderProduct: ProductBasicInfoResponse;
  relatedPackageProductInfoList: RelatedPackageProductInfo[];
  waiverRelateConfigInfo: RiderPackageConfigInfo;
  setWaiverRelateConfigInfo: (config: RiderPackageConfigInfo) => void;
  actionType: RiderActionType;
  setILPPackageList: (arr: RelatedPackageInfoResponse[]) => void;
  setSelectPackageAndProductValue: (arr: number[]) => void;
  selectPackageAndProductValue: number[];
  productInfoDataMap: Record<number, RelatedPackageInfoResponse[]>;
}

export const WaiverRelationshipConfigurationStep = ({
  actionType,
  productInfoDataMap,
  selectedMainProducts,
  selectedRiderProduct,
  selectPackageAndProductValue,
  relatedPackageProductInfoList,
  waiverRelateConfigInfo,
  setILPPackageList,
  setWaiverRelateConfigInfo,
  setSelectPackageAndProductValue,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  /* ============== 枚举使用start ============== */
  const liabilityCategoryOptions = useBizDictAsOptions('liabilityCategory');
  const yesNoBizDicts = useBizDict('yesNo');
  /* ============== 枚举使用end ============== */
  const isClosure = actionType === RiderActionType.Closure;

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectTempPackageAndProductValue, setSelectTempPackageAndProductValue] = useState<number[]>([]);

  const relatedMainProductMap = useMemo(() => keyBy(selectedMainProducts, 'productId'), [selectedMainProducts]);

  const packageProductInfoTreeData = useMemo(() => {
    const tempPackageProductInfoTreeData: TreeDataInfo[] = [];
    const tempRelatedPackageProductInfoList = cloneDeep(relatedPackageProductInfoList);
    tempRelatedPackageProductInfoList.forEach(relatedPackageProduct => {
      const relatedMainProductObj: TreeDataInfo = {
        title: relatedMainProductMap[relatedPackageProduct.mainProductId].productName!,
        value: relatedPackageProduct.mainProductId,
        key: relatedPackageProduct.mainProductId,
        children: uniqBy(relatedPackageProduct.relatedPackageInfoBizResponseList, 'packageId').map(packageItem => ({
          title: packageItem.packageCode,
          value: packageItem.packageId,
          key: packageItem.packageId,
        })),
      };
      tempPackageProductInfoTreeData.push(relatedMainProductObj);
    });
    return tempPackageProductInfoTreeData;
  }, [relatedMainProductMap, relatedPackageProductInfoList]);

  // 根据勾选产品设置前端状态
  const initWaiverRiderConfigInfo = useCallback(
    (selectValueList: number[]) => {
      const tempConfigInfo: RiderPackageConfigInfo = {};
      const tempPackageList: RelatedPackageInfoResponse[] = [];
      selectValueList.forEach(packageId => {
        if (Array.isArray(productInfoDataMap[packageId]) && productInfoDataMap[packageId].length > 0) {
          // 根据勾选产品获取package信息
          if (ILPPackageCategoryCodes.includes(productInfoDataMap[packageId][0].packageCategoryId)) {
            tempPackageList.push(productInfoDataMap[packageId][0]);
          }

          productInfoDataMap[packageId].forEach(relatedProduct => {
            const liabilityConfig: LiabilityConfig = {};
            relatedProduct.packageLiabilityBizResponseList.forEach(liability => {
              liabilityConfig[liability.liabilityId] = {
                isMandatory: liability.isMandatory as YesOrNo,
                // 回显的liability都是已经上架的, 下架时初始状态都是未选中的
                isSelect: isClosure ? false : relatedProduct.riderStatusType === +RiderStatusType.Active,
              };
            });
            const packageConfig: PackageConfig = {
              isMandatory: relatedProduct.isMandatory as YesOrNo,
              // 临时填充
              isSelect: false,
              liabilityConfig,
            };

            tempConfigInfo[`${relatedProduct.productId}_${relatedProduct.packageId}`] = packageConfig;
          });
        }
      });
      if (!isClosure) {
        setILPPackageList(tempPackageList);
      }
      setWaiverRelateConfigInfo(tempConfigInfo);
    },
    [setILPPackageList, setWaiverRelateConfigInfo, productInfoDataMap, isClosure]
  );

  useEffect(() => {
    if (isClosure) {
      let tempPackageAndProductIdList: number[] = [];
      packageProductInfoTreeData.forEach(packageItem => {
        tempPackageAndProductIdList.push(packageItem.key);
        if (Array.isArray(packageItem.children) && packageItem.children.length > 0) {
          tempPackageAndProductIdList = tempPackageAndProductIdList.concat(
            packageItem.children.map(productItem => productItem.key)
          );
        }
      });
      setSelectPackageAndProductValue(tempPackageAndProductIdList);
      initWaiverRiderConfigInfo(tempPackageAndProductIdList);
    }
  }, []);

  const onCheck = (checkedKeys: number[], info: any) => {
    setSelectTempPackageAndProductValue(checkedKeys);
  };

  const expandedRowRender = useCallback(
    (relatedPackageProduct: RelatedPackageInfoResponse[]) => (_product: RelatedPackageInfoResponse) => {
      const liabilityList = _product.packageLiabilityBizResponseList;
      const liabilityConfig =
        waiverRelateConfigInfo[`${_product.productId}_${_product.packageId}`]?.liabilityConfig || {};

      const columns: ColumnsType<RelatedPackageLiability> = [
        {
          title: t('Waiver Rider Liability'),
          dataIndex: 'liabilityName',
        },
        {
          title: t('Liability Category'),
          dataIndex: 'liabilityCategoryCode',
          render: (text: number) => renderOptionName(`${text}`, liabilityCategoryOptions),
        },
        {
          title: t('Mandatory or Not'),
          width: 160,
          dataIndex: 'isMandatory',
          render: (text, record) => (
            <GeneralSelect
              style={{ width: 128 }}
              size="small"
              value={liabilityConfig[record.liabilityId]?.isMandatory}
              onChange={value => {
                liabilityConfig[record.liabilityId].isMandatory = value;
                setWaiverRelateConfigInfo({ ...waiverRelateConfigInfo });
              }}
              disabled
              allowClear={false}
              option={yesNoBizDicts.map(item => ({
                label: item.dictValueName,
                value: item.enumItemName,
              }))}
            />
          ),
        },
      ];

      return (
        <Table columns={columns} dataSource={liabilityList} pagination={false} size="small" rowKey="liabilityId" />
      );
    },
    [actionType, liabilityCategoryOptions, waiverRelateConfigInfo, setWaiverRelateConfigInfo, t, yesNoBizDicts]
  );

  const renderPackageTable = useCallback(
    (productList: RelatedPackageInfoResponse[], packageId: number) => {
      const product = relatedMainProductMap[packageId];

      return (
        <Table
          rowSelection={{
            selectedRowKeys: productList
              .filter(_product => waiverRelateConfigInfo[`${_product.productId}_${packageId}`]?.isSelect)
              .map(_product => _product.productId),
            onChange: selectedRowKeys => {
              productList.forEach(_product => {
                if (selectedRowKeys.includes(_product.productId)) {
                  waiverRelateConfigInfo[`${_product.productId}_${packageId}`].isSelect = true;
                } else {
                  waiverRelateConfigInfo[`${_product.productId}_${packageId}`].isSelect = false;
                }
                setWaiverRelateConfigInfo({ ...waiverRelateConfigInfo });
              });
            },
          }}
          dataSource={productList}
          rowKey="productId"
          columns={[
            {
              title: t('Product Code'),
              dataIndex: 'productCode',
            },
            {
              title: t('Product Name'),
              dataIndex: 'productName',
            },
            {
              title: t('Mandatory Waiver'),
              width: 160,
              dataIndex: 'isMandatory',
              render: (text, record) => (
                <GeneralSelect
                  style={{ width: 128 }}
                  value={waiverRelateConfigInfo[`${record.productId}_${packageId}`]?.isMandatory || YesOrNo.NO}
                  onChange={value => {
                    waiverRelateConfigInfo[`${record.productId}_${packageId}`].isMandatory = value;
                    setWaiverRelateConfigInfo({ ...waiverRelateConfigInfo });
                  }}
                  disabled={isClosure}
                  allowClear={false}
                  option={yesNoBizDicts.map(item => ({
                    label: item.dictValueName,
                    value: item.enumItemName,
                  }))}
                />
              ),
            },
          ]}
          pagination={false}
          expandType="nestedTable"
          expandable={{
            defaultExpandAllRows: true,
            expandedRowRender: expandedRowRender(productList),
          }}
        />
      );
    },
    [
      relatedMainProductMap,
      t,
      expandedRowRender,
      waiverRelateConfigInfo,
      setWaiverRelateConfigInfo,
      isClosure,
      yesNoBizDicts,
    ]
  );

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
    setSelectPackageAndProductValue(selectTempPackageAndProductValue);
    initWaiverRiderConfigInfo(selectTempPackageAndProductValue);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setSelectTempPackageAndProductValue(selectPackageAndProductValue);
  };

  return (
    <div>
      <div className={cx('title')}>
        {isClosure ? t('Closure Rider') : t('Launch Rider')}:
        {`${selectedRiderProduct.productName!} (${selectedRiderProduct.productCode!})`}
      </div>
      {isClosure ? null : (
        <Button
          icon={<Icon type="add" />}
          onClick={() => {
            showModal();
          }}
          size="large"
        >
          {t('Add Relationship Package')}
        </Button>
      )}
      {selectPackageAndProductValue.length > 0 && !isModalOpen ? (
        <div className={cx('sub-title-wrapper')}>
          <div className={cx('sub-title')}>{t('Related Main Product')}</div>
          <div className={cx('related-main-product-list')}>
            {selectPackageAndProductValue.map(key => {
              if (productInfoDataMap[key]) {
                const packageInfo = productInfoDataMap[key][0];
                const productList = productInfoDataMap[key];
                return (
                  <div className={cx('related-main-product-container')}>
                    <div className={cx('related-main-product-upper')}>
                      <div className={cx('related-main-product-title')}>
                        {`${packageInfo.packageCode} (${packageInfo.packageName})`}
                      </div>
                    </div>
                    <div className={cx('related-main-product-content')}>
                      <div className={cx('content-title')}>{t('Product Info')}</div>
                      {renderPackageTable(productList, key)}
                    </div>
                  </div>
                );
              }
            })}
          </div>
        </div>
      ) : null}
      <Modal
        title={t('Select Relationship Package')}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        style={{ height: 550 }}
      >
        <Tree
          height={300}
          checkable
          defaultSelectedKeys={selectPackageAndProductValue}
          onCheck={onCheck}
          treeData={packageProductInfoTreeData}
        />
      </Modal>
    </div>
  );
};

export default WaiverRelationshipConfigurationStep;
