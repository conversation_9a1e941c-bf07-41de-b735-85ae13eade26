.title {
  margin-bottom: 40px;
  font-weight: 700;
  font-size: 16px;
  line-height: 24px;
  color: $label;
}

.sub-title {
  margin-bottom: 16px;
  color: var(--text-color);
  font-weight: 700;
  font-size: 14px;
  line-height: 22px;
  margin-top: $gap-md;
}

.related-main-product-list {
  .related-main-product-container {
    border: 1px solid var(--disabled-color);
    margin-bottom: 24px;
    border-radius: 8px;
  }

  .related-main-product-upper {
    border-bottom: 1px solid var(--disabled-color);
    padding: 16px 24px;
    display: flex;

    .related-main-product-title {
      color: $label;
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;
    }
  }

  .related-main-product-content {
    padding: 16px 24px 32px;

    .content-title {
      margin-bottom: 16px;
      color: $label;
      font-size: 14px;
      line-height: 22px;
      font-weight: 700;
    }
  }
}
