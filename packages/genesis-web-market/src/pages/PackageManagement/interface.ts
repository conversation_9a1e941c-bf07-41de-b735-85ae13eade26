import { YesOrNo } from 'genesis-web-service/lib/common.interface';

export enum RiderActionType {
  Launch = 'Launch',
  Closure = 'Closure',
}

export interface PackageConfig {
  isMandatory: YesOrNo;
  isSelect: boolean;
  liabilityConfig: LiabilityConfig;
}

export type LiabilityConfig = Record<
  number,
  {
    isMandatory: YesOrNo;
    isSelect: boolean;
  }
>;

// key: MainProductId_PackageId
export type RiderPackageConfigInfo = Record<string, PackageConfig>;

export enum RiderStatusType {
  Active = '1',
  Inactive = '2',
}
