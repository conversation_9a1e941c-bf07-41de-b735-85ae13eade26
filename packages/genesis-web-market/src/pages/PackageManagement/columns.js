import React from 'react';

import { Dropdown, Menu, Space, Tooltip, message } from 'antd';

import {
  CommonIconAction,
  DeleteAction,
  EditAction,
  Icon,
  StatusTag,
  TableActionsContainer,
  ViewAction,
} from '@zhongan/nagrand-ui';

import { StatusEnum } from '@market/common/enums';
import I18nInstance from '@market/i18n.ts';
import { isDrivenPackage } from '@market/marketService/package.service';
import { NewMarketService } from '@market/services/market/market.service.new';

import './index.scss';

const StatusTagMap = {
  [StatusEnum.Draft]: 'no-status',
  [StatusEnum.Effective]: 'success',
  [StatusEnum.Ready]: 'info',
  [StatusEnum.Invalid]: 'info',
};

const columnsGenerator = (
  enums = {},
  initialValue = {},
  envConfig,
  navigate,
  search,
  statusVisible,
  changeStatusVisible,
  deletePackage,
  userInfo,
  getActionDom,
  permissionMap,
  setModalOpen,
  setCopiedRecord
) => {
  const getStatusClassName = text => {
    switch (text) {
      case StatusEnum.Draft:
        return 'draft';
      case StatusEnum.Effective:
        return 'effective';
      case StatusEnum.Invalid:
        return 'invalid';
      case StatusEnum.Ready:
        return 'ready';
      default:
        return 'pl12';
    }
  };

  const getCopyPackageId = async record => {
    if (!record?.packageId) {
      message.error(I18nInstance.t('PackageId is required', { ns: 'market' }));
      return;
    }

    const isDriven = isDrivenPackage(record.packageProductResponseDTOList || []);
    if (!isDriven) {
      message.warning(
        I18nInstance.t(
          'This record was configured based on old version. Currently, viewing and editing are not supported.',
          { ns: ['market', 'common'] }
        ),
        7
      );
      return;
    }

    const packageId = Number(record.packageId);
    const res = await NewMarketService.PackageQueryMgmtService.copyPackage({
      packageId,
    });
    if (res.success && res.value) {
      const newPackageId = res.value.packageId;
      navigate(`/market/package/basic?packageId=${newPackageId}`, {
        state: {
          mode: 'copy',
        },
      });
    }
  };

  const clickPackageDetail = async record => {
    const isDriven = isDrivenPackage(record.packageProductResponseDTOList || []);
    if (!isDriven) {
      message.warning(
        I18nInstance.t(
          'This record was configured based on old version. Currently, viewing and editing are not supported.',
          { ns: ['market', 'common'] }
        ),
        7
      );
      return;
    }

    navigate(`/market/package/basic?packageId=${record.packageId}`, {
      state: {
        mode: 'edit',
      },
    });
  };
  const getColumnSearchProps = (dataIndex, selectData) => ({
    filteredValue: dataIndex === 'packageStatus' && initialValue.packageStatus,
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm }) => {
      return (
        <div className="table-filter-select">
          <Dropdown
            key={Math.random()}
            open={statusVisible}
            getPopupContainer={() => document.getElementById('management-container-table-id')}
            dropdownRender={() => (
              <Menu
                style={{ width: 120 }}
                selectedKeys={selectedKeys}
                onClick={e => {
                  setSelectedKeys(e.keyPath);
                  confirm();
                }}
              >
                {Array.isArray(selectData) &&
                  [
                    {
                      dictValue: '0',
                      dictValueName: I18nInstance.t('ALL', {
                        ns: 'market',
                      }),
                    },
                    ...selectData,
                  ].map(k1512 => (
                    <Menu.Item key={k1512.dictValue}>
                      <span className={getStatusClassName(Number(k1512.dictValue))} />
                      <span>{k1512.dictValueName}</span>
                    </Menu.Item>
                  ))}
              </Menu>
            )}
          >
            <div style={{ height: 1 }} />
          </Dropdown>
        </div>
      );
    },
    onFilterDropdownVisibleChange: visible => {
      changeStatusVisible(visible);
    },
  });

  const getViewDom = record => {
    return (
      <ViewAction
        onClick={() => {
          const isDriven = isDrivenPackage(record.packageProductResponseDTOList || []);
          if (!isDriven) {
            message.warning(
              I18nInstance.t(
                'This record was configured based on old version. Currently, viewing and editing are not supported.',
                { ns: ['market', 'common'] }
              ),
              7
            );
            return;
          }
          navigate(`/market/package/basic?packageId=${record.packageId}`, {
            state: {
              mode: 'view',
            },
          });
        }}
      />
    );
  };

  // 编辑权限
  const hasEditAuth = !!permissionMap['market.edit'];
  const isSuperUser = !!permissionMap['market.edit-all'];

  const getDom = record => {
    if (!record) {
      return;
    }
    const draftFlag = record.packageStatus === 1;
    const currentUserIsCreator = `${userInfo.userId}` === record.creator;
    const canEdit = (currentUserIsCreator && hasEditAuth) || isSuperUser;
    const noCreatorJudgeEditAuth = hasEditAuth || isSuperUser;

    const editDom = !canEdit ? (
      <EditAction disabled />
    ) : (
      <EditAction onClick={() => clickPackageDetail(record)} tooltipTitle={I18nInstance.t('Edit')} />
    );

    const copyDom = (
      <React.Fragment>
        {noCreatorJudgeEditAuth ? (
          <CommonIconAction
            onClick={() => {
              setModalOpen(true);
              setCopiedRecord(record);
            }}
            tooltipTitle={I18nInstance.t('Copy')}
            icon={<Icon type="copy" />}
          />
        ) : null}
      </React.Fragment>
    );

    const deleteDom = (
      <DeleteAction
        disabled={!(draftFlag && canEdit)}
        onClick={() => deletePackage(record.packageId)}
        doubleConfirmType="modal"
        tooltipTitle={I18nInstance.t('Delete')}
      />
    );

    return (
      <TableActionsContainer>
        {envConfig && envConfig.env !== 'prd' ? (
          <>
            {getViewDom(record)}
            {editDom}
            {copyDom}
            {deleteDom}
          </>
        ) : (
          getViewDom(record)
        )}
      </TableActionsContainer>
    );
  };

  if (getActionDom) {
    return getDom;
  }

  return [
    {
      title: I18nInstance.t('Package Code', { ns: 'market' }),
      dataIndex: 'packageCode',
      sorter: true,
      className: 'market-table-column-goods-code',
      sortOrder: initialValue.sortField === 'packageCode' ? initialValue.sortOrder : undefined,
      width: 182,
      render: (text, record) => {
        if (text) {
          return (
            <Tooltip placement="topLeft" title={text}>
              <div
                style={{
                  width: 150,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {text}
              </div>
            </Tooltip>
          );
        }
        return <React.Fragment>— —</React.Fragment>;
      },
    },
    {
      title: I18nInstance.t('Package Name', { ns: 'market' }),
      dataIndex: 'packageName',
      width: 222,
      render: text => {
        if (text) {
          return (
            <Tooltip placement="topLeft" title={text}>
              <div
                style={{
                  width: 190,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {text}
              </div>
            </Tooltip>
          );
        }
        return I18nInstance.t('--');
      },
    },
    {
      title: I18nInstance.t('Product Code - Version', { ns: 'market' }),
      dataIndex: 'packageProductResponseDTOList',
      width: 300,
      render: text => {
        if (Array.isArray(text) && text.length) {
          return (
            <Tooltip
              placement="topLeft"
              title={text.map(item => (
                <div key={item.insuranceProductCode}>
                  {`${item.insuranceProductCode}-${item.productVersion || ' '};`}
                </div>
              ))}
              overlayStyle={{ maxWidth: 'initial' }}
            >
              {text.map((item, index) => {
                if (index < 2) {
                  return (
                    <div
                      key={item.insuranceProductCode}
                      style={{
                        width: 268,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      <span className={(index === 1 && text.length > 2 && 'more') || ''}>
                        {`${item.insuranceProductCode}-${item.productVersion || ' '}${
                          (text.length > 1 && (index === 0 || (index === 1 && text.length === 2)) && ';') || ''
                        }`}
                      </span>
                    </div>
                  );
                }
              })}
            </Tooltip>
          );
        }
        return I18nInstance.t('--');
      },
    },
    {
      title: I18nInstance.t('status', { ns: 'market' }),
      dataIndex: 'packageStatus',
      ...getColumnSearchProps('packageStatus', enums.packageStatus),
      width: 120,
      render: text => {
        if (
          text != null &&
          Array.isArray(enums.packageStatus) &&
          !!enums.packageStatus.filter(item => Number(item.dictValue) === text).length
        ) {
          return (
            <StatusTag
              statusI18n={enums.packageStatus.filter(item => Number(item.dictValue) === text)[0].dictValueName}
              needDot
              type={StatusTagMap[text]}
            />
          );
        }
      },
    },
    {
      title: I18nInstance.t('Package Category', { ns: 'market' }),
      dataIndex: 'packageCategory',
      width: 200,
      render: text =>
        text != null &&
        Array.isArray(enums.productCategory) &&
        !!enums.productCategory.filter(item => Number(item.itemExtend1) === text).length && (
          <Tooltip
            placement="topLeft"
            title={enums.productCategory.filter(item => Number(item.itemExtend1) === text)[0].dictValueName}
          >
            <div
              style={{
                width: 128,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {enums.productCategory.filter(item => Number(item.itemExtend1) === text)[0].dictValueName}
            </div>
          </Tooltip>
        ),
    },
    {
      title: I18nInstance.t('Summary', { ns: 'market' }),
      dataIndex: 'packageIntro',
      width: 250,
      render: text => (
        <Tooltip placement="topLeft" title={text}>
          <div
            style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
            }}
          >
            {text}
          </div>
        </Tooltip>
      ),
    },
    {
      title: I18nInstance.t('service_page_creator', { ns: 'market' }),
      dataIndex: 'creatorName',
      width: 170,
      render: (text, record) => {
        return text || record.creator;
      },
    },
    {
      title: I18nInstance.t('Last Modifier', { ns: 'market' }),
      width: 128,
      dataIndex: 'modifierName',
    },
    {
      title: I18nInstance.t('Actions', { ns: 'common' }),
      dataIndex: 'action',
      width: 120,
      fixed: 'right',
      align: 'right',
      render: (text, record) => getDom(record),
    },
  ];
};

export { columnsGenerator };
