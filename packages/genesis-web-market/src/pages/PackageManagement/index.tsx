import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { Button, Divider, Form, Input, Skeleton } from 'antd';
import { TablePaginationConfig } from 'antd/es/table';
import { FilterValue, SorterResult } from 'antd/es/table/interface';

import {
  AddNewButton,
  CardActionsContainer,
  CardBodyHeader,
  CardBodyPrimaryInfo,
  CardFooter,
  CardLayoutV2,
  CardTagList,
  CardV2,
  CopyAction,
  DeleteAction,
  EditAction,
  Modal,
  OperationContainer,
  Pagination,
  ProfilePhoto,
  QueryBitMap,
  QueryOperationSelect,
  QueryResultContainer,
  RenderMode,
  RenderModeSwitch,
  Table,
  TagType,
  ViewAction,
  message,
} from '@zhongan/nagrand-ui';

import { useRouterState } from 'genesis-web-component/lib/hook/router';
import { YesOrNo } from 'genesis-web-service';
import { PackageQueryResponseDTO } from 'genesis-web-service/service-types/market-types/package';
import { l10n } from 'genesis-web-shared/lib';

import { BizDict } from '@market/common/interface';
import { useBizDict } from '@market/hook/bizDict';
import { usePermission } from '@market/hook/permission';
import { t } from '@market/i18n';
import { isDrivenPackage } from '@market/marketService/package.service';
import { selectEnvConfig, selectUserState } from '@market/redux/selector';
import { NewMarketService } from '@market/services/market/market.service.new';

import { PackageCodeValidator } from '../PackageBasicConfig/validateConfig';
import QueryForm from './QueryForm';
import { columnsGenerator } from './columns';
import RiderLaunchClosureDrawer from './components/RiderLaunchClosureDrawer/RiderLaunchClosureDrawer';
import './index.scss';
import { RiderActionType } from './interface';

const statusMap: Record<string, string> = {
  1: 'no-status',
  2: 'error',
  3: 'success',
};

const sortedMap: Record<string, string> = {
  ascend: 'ASC',
  descend: 'DESC',
};

const showStatus = (status: number, dict: BizDict[]) => {
  const selectedItem = (dict ?? []).find(item => Number(item.itemExtend1) === status);

  return {
    statusI18n: selectedItem?.itemName?.toUpperCase(),
    type: statusMap[`${status}`],
  };
};

type TableData = PackageQueryResponseDTO & {
  index: number;
};

const NewPackageManagement: React.FC = () => {
  const [form] = Form.useForm();
  const hasEditAuth = !!usePermission('market.edit');
  const isSuperUser = !!usePermission('market.edit-all');
  const envConfig = useSelector(selectEnvConfig) as {
    env: string;
  };
  const userInfo = useSelector(selectUserState) as Record<string, number>;
  const productCategoryEnums = useBizDict('productCategory');
  const packageStatusBizDicts = useBizDict('packageStatus');

  const [skeletonVisible, setSkeletonVisible] = useState(true);
  const [actionType, setActionType] = useState<undefined | RiderActionType>();
  const [riderLaunchDrawerVisible, setRiderLaunchDrawerVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<TableData[]>([]);
  const [total, setTotal] = useState(0);
  const [packageStatus, setPackageStatus] = useState<string[]>(['0']);
  const [statusVisible, setStatusVisible] = useState(false);
  const navigate = useNavigate();

  const [searchQuery, setSearchQuery] = useRouterState<{
    [key: string]: any;
    showSelf: boolean;
    current: number;
    pageSize: number;
    sortField?: string;
    sortOrder?: string;
    showCard: boolean;
  }>({
    showSelf: false,
    current: 0,
    pageSize: 12,
    showCard: true,
  });

  const [modalOpen, setModalOpen] = useState(false);
  const [copiedRecord, setCopiedRecord] = useState<TableData>();
  const hasPermission = hasEditAuth || isSuperUser;

  const search = () => {
    const { current, pageSize, showSelf, sortOrder, sortField, showCard, ...condition } = searchQuery ?? {};

    Object.keys(condition).forEach(conditionKey => {
      if (condition[conditionKey] === '0') {
        condition[conditionKey] = undefined;
      }

      if (conditionKey === 'packageCategory' && condition[conditionKey]) {
        condition[conditionKey] = Number(condition[conditionKey]);
      }
    });

    setLoading(true);

    NewMarketService.PackageQueryMgmtService.query$POST$mgmt_package_query({
      packageQueryRequestDTOPage: {
        pageIndex: current + 1,
        limit: pageSize,
        condition: {
          sorted: sortedMap[sortOrder!],
          packageStatus: packageStatus.toString() === '0' ? undefined : (packageStatus.toString() as unknown as number),
          ...condition,
        },
      },
      showSelfPackage: showSelf,
    })
      .then(res => {
        if (Array.isArray(res?.value?.packageQueryResponseDTOPage?.results)) {
          setTotal(res.value.packageQueryResponseDTOPage.total!);
          setData(
            res.value.packageQueryResponseDTOPage.results.map((item, i) => ({
              ...item,
              index: i + 1 + current * pageSize,
            }))
          );
        } else {
          setTotal(0);
          setData([]);
        }
      })
      .finally(() => {
        setLoading(false);
        setSkeletonVisible(false);
      });
  };

  useEffect(() => {
    search();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery]);

  const deletePackage = (packageId: number) => {
    NewMarketService.PackageQueryMgmtService.delete({
      packageId,
    }).then(() => {
      search();
    });
  };

  const handleTableChange = (
    pagination: TablePaginationConfig,
    filters?: Record<string, FilterValue | null>,
    sorter?: SorterResult<any> | SorterResult<any>[]
  ) => {
    if (searchQuery.showCard) {
      setSearchQuery({
        ...searchQuery,
        pageSize: pagination.pageSize ?? 12,
        current: (pagination.current ?? 1) - 1,
      });
    } else {
      setSearchQuery({
        ...searchQuery,
        pageSize: pagination.pageSize ?? 10,
        current: (pagination.current ?? 1) - 1,
        sortField: (sorter as Record<string, string>)?.field,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        sortOrder: (sorter as Record<string, string>)?.order,
      });

      setPackageStatus(filters?.packageStatus as string[]);
    }
  };

  const handleViewClick = (record: TableData) => {
    const isDriven = isDrivenPackage(record.packageProductResponseDTOList || []);
    if (!isDriven) {
      message.warning(
        t('This record was configured based on old version. Currently, viewing and editing are not supported.'),
        7
      );
      return;
    }
    navigate(`/market/package/basic?packageId=${record.packageId!}`, {
      state: {
        mode: 'view',
      },
    });
  };

  const handleEditClick = (record: TableData) => {
    const isDriven = isDrivenPackage(record.packageProductResponseDTOList || []);
    if (!isDriven) {
      message.warning(
        t('This record was configured based on old version. Currently, viewing and editing are not supported.'),
        7
      );
      return;
    }

    navigate(`/market/package/basic?packageId=${record.packageId!}`, {
      state: {
        mode: 'edit',
      },
    });
  };

  const handleCopyClick = useCallback((record: TableData) => {
    setModalOpen(true);
    setCopiedRecord(record);
  }, []);

  const getCardItem = (tableData: TableData[]) =>
    tableData.map(item => {
      const _createDate = l10n.dateFormatInstance.getDateString(item.gmtModified);
      const { statusI18n, type } = showStatus(item.packageStatus!, packageStatusBizDicts);
      const currentUserIsCreator = `${userInfo.userId}` === item.creator;
      const canEdit = (currentUserIsCreator && hasEditAuth) || isSuperUser;
      const draftFlag = item.packageStatus === 1;

      return (
        <CardV2
          key={item.packageId}
          body={
            <React.Fragment>
              <CardBodyHeader leftContent={_createDate} leftContentTooltip={`${t('Update Date')}: ${_createDate!}`} />
              <CardBodyPrimaryInfo
                title={t('Package Name')}
                content={item.packageName!}
                right={<ProfilePhoto users={item.modifierName!} tooltipLabel={t('Modifier')} />}
              />
              <CardTagList
                tagList={[
                  {
                    type: TagType.Tag,
                    tagProps: {
                      statusI18n,
                      type,
                    },
                  },
                ]}
              />
            </React.Fragment>
          }
          footer={
            <CardFooter
              list={[
                {
                  label: t('Package Code'),
                  value: item.packageCode!,
                },
                {
                  label: t('Product Code'),
                  value:
                    item.packageProductResponseDTOList
                      ?.filter(packageProductResponseDTO => packageProductResponseDTO?.insuranceProductCode)
                      ?.map(packageProductResponseDTO => packageProductResponseDTO.insuranceProductCode) ?? [],
                },
                {
                  label: t('Package Category'),
                  value: productCategoryEnums?.find(i => i.itemExtend1?.toString() === item.packageCategory?.toString())
                    ?.dictValueName as string,
                },
              ]}
            />
          }
          actions={
            envConfig.env === 'prd' ? (
              <CardActionsContainer>
                <ViewAction onClick={() => handleViewClick(item)} />
              </CardActionsContainer>
            ) : (
              <CardActionsContainer>
                <ViewAction onClick={() => handleViewClick(item)} />
                <EditAction disabled={!canEdit} onClick={canEdit ? () => handleEditClick(item) : undefined} />
                {hasPermission ? <CopyAction onClick={() => handleCopyClick(item)} /> : null}
                <DeleteAction
                  doubleConfirmType="popconfirm"
                  deleteConfirmContent={t('Are you sure to delete this record?')}
                  disabled={!(draftFlag && canEdit)}
                  onClick={() => deletePackage(item.packageId!)}
                />
              </CardActionsContainer>
            )
          }
        />
      );
    });

  const handleMenuClick = (innerShowSelf: boolean) => {
    setSearchQuery({
      ...searchQuery,
      showSelf: innerShowSelf,
      current: 0,
    });
  };

  // 处理卡片/表格切换事件
  const handleCardTableSwitch = (value: RenderMode) => {
    const showCard = value === RenderMode.Card;

    setTotal(0);
    setLoading(false);
    setData([]);

    setSearchQuery({
      ...searchQuery,
      showCard,
      pageSize: showCard ? 12 : 10,
      sortField: undefined,
      sortOrder: undefined,
      current: 0,
    });
  };

  // 重置复制模态框
  const resetCopyModal = () => {
    setCopiedRecord(undefined);
    setModalOpen(false);
    form.resetFields();
  };

  // 复制确认处理函数
  const onCopyConfirm = () => {
    form.validateFields().then((values: any) => {
      if (!copiedRecord?.packageId) {
        message.error(t('PackageId is required'));
        return;
      }
      const isDriven = isDrivenPackage(copiedRecord.packageProductResponseDTOList || []);
      if (!isDriven) {
        message.warning(
          t('This record was configured based on old version. Currently, viewing and editing are not supported.'),
          7
        );
        return;
      }
      NewMarketService.PackageQueryMgmtService.copyPackage({
        packageId: Number(copiedRecord.packageId),
        ...values,
      }).then(res => {
        if (res.success && res.value) {
          const newPackageId = res.value.packageId;
          navigate(`/market/package/basic?packageId=${newPackageId!}&isCopyFirstEnter=${YesOrNo.YES}`, {
            state: {
              mode: 'edit',
            },
          });
          resetCopyModal();
        }
      });
    });
  };

  const handleOperationLaunchAndClosureBtnClick = useCallback((value: RiderActionType) => {
    setActionType(value);
    setRiderLaunchDrawerVisible(true);
  }, []);

  return (
    <div className="management-container" id="management-container-id">
      <Skeleton active loading={skeletonVisible}>
        <QueryForm
          productCategoryEnums={productCategoryEnums}
          loading={loading}
          search={values => {
            setSearchQuery({
              ...searchQuery,
              ...values,
              current: 0,
            });
          }}
        />

        <QueryResultContainer>
          <OperationContainer>
            <OperationContainer.Left>
              {envConfig.env !== 'prd' ? (
                <AddNewButton
                  disabled={!hasPermission}
                  type="primary"
                  ghost
                  onClick={() => {
                    navigate('/market/package/basic', {
                      state: {
                        mode: 'add',
                      },
                    });
                  }}
                >
                  {t('Add New')}
                </AddNewButton>
              ) : null}
            </OperationContainer.Left>

            <OperationContainer.Right>
              <Button
                onClick={() => handleOperationLaunchAndClosureBtnClick(RiderActionType.Launch)}
                disabled={!hasPermission}
              >
                {t('Launch & Next')}
              </Button>
              <Button
                onClick={() => handleOperationLaunchAndClosureBtnClick(RiderActionType.Closure)}
                disabled={!hasPermission}
              >
                {t('Closure & Next')}
              </Button>
              <QueryOperationSelect
                options={[
                  {
                    label: t('My Creations'),
                    value: true,
                  },
                  {
                    label: t('All Creations'),
                    value: false,
                  },
                ]}
                value={!!searchQuery?.showSelf}
                onChange={handleMenuClick}
              />
              <Divider type="vertical" />
              <RenderModeSwitch
                value={searchQuery.showCard ? RenderMode.Card : RenderMode.Table}
                onChange={handleCardTableSwitch}
              />
            </OperationContainer.Right>
          </OperationContainer>

          {!searchQuery.showCard ? (
            <Table
              loading={loading}
              dataSource={data}
              rowKey="packageId"
              pagination={{
                total,
                pageSize: searchQuery.pageSize,
                current: searchQuery.current + 1,
              }}
              onChange={handleTableChange}
              scroll={{ x: 'max-content' }}
              emptyType="icon"
              columns={columnsGenerator(
                {
                  productCategory: productCategoryEnums,
                  packageStatus: packageStatusBizDicts,
                },
                {
                  sortField: searchQuery?.sortField,
                  sortOrder: searchQuery?.sortOrder,
                  packageStatus,
                },
                envConfig,
                navigate,
                undefined,
                statusVisible,
                setStatusVisible,
                deletePackage,
                userInfo,
                false,
                {
                  'market.edit': hasEditAuth,
                  'market.edit-all': isSuperUser,
                },
                setModalOpen,
                setCopiedRecord
              )}
            />
          ) : data.length || loading ? (
            <React.Fragment>
              <CardLayoutV2 loading={loading}>{getCardItem(data)}</CardLayoutV2>
              <Pagination
                current={searchQuery.current + 1}
                pageSize={searchQuery?.pageSize}
                total={total}
                showSizeChanger
                showQuickJumper
                pageSizeOptions={['12', '24', '36', '48']}
                onChange={(current, pageSize) => handleTableChange({ current, pageSize })}
                onShowSizeChange={(current, pageSize) => handleTableChange({ current, pageSize })}
              />
            </React.Fragment>
          ) : (
            <QueryBitMap />
          )}
        </QueryResultContainer>

        <Modal open={modalOpen} title={t('Copy Reminder')} onCancel={resetCopyModal} onOk={onCopyConfirm}>
          <Form form={form} name="copyModalForm" layout="vertical">
            <Form.Item
              name="packageName"
              label={t('Package Name')}
              rules={[{ required: true, message: 'Please input' }]}
            >
              <Input placeholder={t('Please input')} />
            </Form.Item>
            <Form.Item name="packageCode" label={t('Package Code')} rules={PackageCodeValidator}>
              <Input placeholder={t('Please input')} />
            </Form.Item>
          </Form>
        </Modal>

        <RiderLaunchClosureDrawer
          actionType={actionType!}
          visible={riderLaunchDrawerVisible}
          setVisible={visible => {
            setRiderLaunchDrawerVisible(visible);
            setActionType(undefined);
          }}
        />
      </Skeleton>
    </div>
  );
};

export default NewPackageManagement;
