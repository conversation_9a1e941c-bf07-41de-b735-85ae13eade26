import { ApplicationElementCustomerType, ApplicationElementTopic } from 'genesis-web-service/lib/market';

export interface AppElementEnum {
  key: string;
  no: number;
  code: string;
  name: string;
  fullPathKey?: string;
  dictKey?: string;
  childList?: AppElementEnum[];
  itemExtend1?: string;
  dictValue: string;
}

export interface AppElementLinkageEnum extends AppElementEnum {
  parentValue: string;
  childList?: AppElementLinkageEnum[];
  extraInfo?: string;
}

export type MetadataLinkageLevel = 1 | 2 | 3 | 4 | 5;

export interface LinkageEnumLevelDetail {
  entryBizDictKey: string;
  targetLevel: MetadataLinkageLevel;
  level1?: string[];
  level2?: string[];
  level3?: string[];
  level4?: string[];
  level5?: string[];
}

// TODO 待优化
export interface ConfigedDataByTopicAndCustomerType {
  [ApplicationElementTopic.policyHolder]: ConfigedDataByCustomerType;
  [ApplicationElementTopic.insured]: ConfigedDataByCustomerType;
  [ApplicationElementTopic.beneficiary]: ConfigedDataByCustomerType;
  [ApplicationElementTopic.beneficialOwner]: ConfigedDataByCustomerType;
  [ApplicationElementTopic.object]: ConfigedDataByCustomerType;
  [ApplicationElementTopic.nominee]: ConfigedDataByCustomerType;
  [ApplicationElementTopic.consentee]: ConfigedDataByCustomerType;
  [ApplicationElementTopic.secondaryLifeInsured]: ConfigedDataByCustomerType;
  [ApplicationElementTopic.trustee]: ConfigedDataByCustomerType;
  [ApplicationElementTopic.contactPerson]: ConfigedDataByCustomerType;
}

export interface ConfigedDataByCustomerType {
  [ApplicationElementCustomerType.Person]: Record<string, string[]>;
  [ApplicationElementCustomerType.Company]: Record<string, string[]>;
}

export interface ConfigedDataByObjectSubCategory {
  [index: string]: Record<string, string[]>;
}
