import {
  SalesPartnerAgency,
  SalesPartnerChannel,
  SalesPartnerFormula,
  SalesPartnerServiceCompany,
  ServiceFeeFormula,
} from 'genesis-web-service';

import { BizTopic } from './enums';
import { EditTableProps } from './interface';

// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace SalesAttributesNS {
  export type FESalesPartnerChannel = SalesPartnerChannel & EditTableProps;

  export type FESalesPartnerAgency = SalesPartnerAgency & EditTableProps;

  export type FESalesPartnerServiceCompany = SalesPartnerServiceCompany &
    EditTableProps & {
      formulaCategory: BizTopic.Commission | BizTopic.CommissionClawback;
      formulaCode: string;
      clawbackFormulaCode: string;
    };

  export type FESalesPartnerFormula = SalesPartnerFormula & EditTableProps;
}

export type TableType =
  | 'AgencyPartner'
  | 'ChannePartnerl'
  | 'AgentPartnerl'
  | 'LeasingChannel'
  | 'Bank'
  | 'BrokerCompany'
  | 'agentCommission'
  | 'agentClawbackCommission'
  | 'tiedAgentChannel';

export enum PosPremiumRefundType {
  InCash = '1',
  InVoucher = '2',
}

export interface EditTableProps {
  editing?: boolean;
  key?: number;
}

export enum AgentCategoryType {
  BranchManager = '1',
  Agent = '2',
}

export enum SalesDistrictType {
  Nationwide = '1',
}
