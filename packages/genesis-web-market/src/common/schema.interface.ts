/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import {
  ClaimIncidentSchemaDef,
  CustomerRole,
  CustomerSchemaDef,
  CustomerSubType,
  CustomerType,
  LiabilitySchemaDef,
  ObjectSchemaDef,
  POSCategory,
  POSSchemaDef,
  ProductSchemaDef,
  SchemaDef,
  SchemaDefField,
  SchemaDefType,
} from 'genesis-web-service';

export enum SchemaDefParamType {
  insured = 'insured',
  liability = 'liability',
  object = 'object',
  product = 'product',
  coverage = 'coverage',
  policy = 'policy',
  pos = 'pos',
  policyHolder = 'policyHolder',
  claim = 'claim',
  channel = 'channel',
  collectionPayment = 'collectionPayment',
  lossParty = 'lossParty',
  incident = 'incident',
  incidentSection = 'incidentSection',
  statistic = 'statistic',
  claimStack = 'claimStack',
}
export interface SchemaField extends SchemaDefField {
  key: string;
  showText: string;
  schemaDefType: string;
  schemaDefRefId: number;
  liabilityId?: number;
  customerRole?: CustomerRole;
  posCategory?: string;
  objectSubCategory?: string;
  schemaDefParamType: SchemaDefParamType;
}

export interface SchemaDefBaseProps extends SchemaDef<unknown> {
  schemaDefType: SchemaDefType;
  schemaDefRefId: number;
  fields: SchemaField[];
}

export interface PolicySchema {
  insured?: CustomerSchemaDef[];
  policyHolder?: CustomerSchemaDef[];
  liability?: LiabilitySchemaDef[];
  object?: ObjectSchemaDef[];
  product?: ProductSchemaDef[];
  coverage?: ProductSchemaDef[];
  policy?: SchemaDef<unknown>[];
  pos?: POSSchemaDef[];
  channel?: SchemaDef<unknown>[];
  claimStack?: SchemaDef<unknown>[];
  collectionPayment?: SchemaDef<unknown>[];
  claimIncident?: SchemaDef<unknown>[];
  claimIncidentSection?: ClaimIncidentSchemaDef[];
  claimStatistic?: SchemaDef<unknown>[];
}

// TODO 优化成联合类型
export interface PrdSchemaDef extends SchemaDefBaseProps {
  customerType?: CustomerType;
  customerSubType?: CustomerSubType;
  liabilityCategory?: string;
  objectCategory?: string;
  objectSubCategory?: string;
  productSubCategoryId?: string;
  posCategory?: POSCategory;
  claimCategoryType?: string;
  claimSectionType?: string;
}
