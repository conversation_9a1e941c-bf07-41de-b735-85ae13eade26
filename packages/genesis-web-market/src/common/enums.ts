/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { t } from '@market/i18n';

export const YesNoOptions = [
  { label: t('Yes'), value: true },
  { label: t('No'), value: false },
];

export enum YesOrNo {
  YES = 'YES',
  NO = 'NO',
}

export enum ObjectSubCategoryType {
  CarOwner = 10,
  BusDelay = 11,
  FoodDeliveryDelay = 12,
  Trip = 13,
  FellowTraveller = 14,
  Hotel = 15,
  Ticket = 16,
  Parcel = 17,
  Device = 1,
  FlightDelay = 2,
  Travel = 3,
  Order = 4,
  Building = 5,
  Pet = 6,
  Vehicle = 7,
  ClaimExperience = 8,
  Driver = 9,
  VehicleLoan = 31,
  IndividualRenter = 37,
  OrgRenter = 38,
}

export enum ObjectCategoryType {
  Parcel = 10,
  Device = 1,
  FlightDelay = 2,
  Travel = 3,
  Order = 4,
  Building = 5,
  Pet = 6,
  Auto = 7,
  BusDelay = 8,
  FoodDeliveryDelay = 9,
}

export enum PackagePeriodRule {
  UnifiedRules = 1,
  FollowEachProduct = 2,
}

/**
 * title: formula Category
 *
 * metadata: bizTopic.dictValue
 */
export enum BizTopic {
  Premium = '1',
  Claim = '2',
  Risk = '4',
  CashBonus = '6',
  SurvivalBenefit = '8',
  SumAssured = '13',
  Commission = '14',
  PremiumDiscount = '19',
  POS = '21',
  BenefitIllustration = '22',
  ClaimReserve = '23',
  ServiceFee = '25',
  MaturityBenefit = '26',
  Annuity = '27',
  CashValue = '28',
  PlannedPremium = '29',
  PeriodLimitMatrix = '30',
  AnnuityPayment = '31',
  Tax = '32',
  ClaimStack = '33',
  CommissionClawback = '34',
  ExtraPremium = '35',
  Campaign = '36',
  DeferredInterest = '37',
  ProductCorrelationMatrix = '42',
  VehicleMarketValue = '43',
  VehicleElementsRelationshipMatrix = '51',
  CustomerElementsRelationshipMatrix = '52',
  DriverElementsRelationshipMatrix = '53',
  QuickQuotationMatrix = '60',
}

export enum CommissionSubCategory {
  GrossCommission = '9',
  NetCommission = '10',
  CommissionGST = '11',
  CommissionVAT = '12',
  CommissionWithholdingTax = '13',
}

export enum IssuingModelType {
  IndividualPolicyIssuedForEachInsuredPerson = '1',
  OnePolicyIssuedForAllInsuredPerson = '2',
}

export enum PackageEffectiveDateRuleEnhancementType {
  BasedonTimePointofPolicyInsureDate = '1',
  Basedon00_00_00ofPolicyInsureDate = '2',
}

export enum FileType {
  excel = 'excel',
  image = 'image',
  pdf = 'pdf',
  word = 'word',
  zip = 'zip',
}

export enum UsageScenario {
  LoanCompany = '1',
  PaymentBank = '2',
}

/**
 * title: Premium Calculation Method
 * metadata: calculationRule.dictValue
 */
export enum CalculationRuleType {
  UnifiedCalculationMethodForAllProducts = '1',
  DefineCalculationMethodForEachProduct = '2',
}

/**
 * title: Premium Calculation Method
 * metadata: agreementBasis.dictValue
 */
export enum AgreementBasisType {
  CalculatePremium = '1',
  CalculateSA = '2',
}

/**
 * title: Policy Effective Without Collection (NB)
 * metadata: policyEffectiveWithoutCollection.dictValue
 */
export enum PolicyEffectiveWithoutCollection {
  YES = 1,
  NO = 2,
  ByRule = 3,
}

/**
 * title: PackagePaymentMethodDefine
 * metadata: packagePaymentMethodDefine.dictValue
 */
export enum PackagePaymentMethodDefine {
  UnifiedPaymentMethodforSelectedPackage = '1',
  DefineCalculationMethodforEachPackage = '2',
}

export enum GoodsCategoryItemExtend1 {
  'TermLife' = 11,
  'WholeLife' = 12,
  'Endowment' = 13,
  'Annuity' = 14,
  'Waiver' = 15,
  'CriticalIllness' = 16,
  'HospitalMedical' = 17,
  'Accident' = 18,
  'UnitLinked' = 19,
  'Universal' = 20,
  'VariableAnnuity' = 21,
  'Auto' = 22,
  'Liability' = 23,
  'Property' = 24,
  'Travel' = 26,
  'ExpenseCompensation' = 27,
  'GroupEmployeeBenefit' = 33,
  'ProductBundle' = 80,
}

export enum StatusEnum {
  Draft = 1,
  Invalid = 2,
  Effective = 3,
  Ready = 4,
}

export enum CustomerServiceDictValue {
  Freelook = '1',
  InsureObject = '11',
  PolicyholderBasicInformationChange = '13',
  InsuredBasicInformationChange = '15',
  PolicyCancellationorSurrender = '101',
  PolicyCoverageDateChange = '102',
  BeneficiaryChange = '104',
  FullSurrender = '105',
  ILPSingleTopUp = '106',
  BonusAllocation = '107',
  PartialWithdraw = '108',
  Seizure = '111',
  PolicyPaymentInformationChange = '112',
  SumAssuredChange = '113',
  EventPolicyTermination = '116',
  PlanChange = '117',
  EventPolicyIssueSwitchChange = '118',
  EventPolicyDriverChange = '119',
  SmokeStatusChange = '120',
  Reinstatement = '122',
  AddorDeleteRider = '125',
  AutoRenewalSwitchChange = '129',
  ILPWithdrawal = '133',
  ILPCouponAllocation = '135',
  AddorDeleteInsuredPerson = '138',
  PolicyholderIdentificationDetailChange = '140',
  InsuredIdentificationDetailChange = '141',
  PolicyholderChange = '142',
  MasterPolicyAddorDeleteInsuredPerson = '143',
  PremiumFrequencyChange = '148',
  PolicyAccountWithdrawal = '149',
  PolicyLoan = '150',
  TrusteeChange = '153',
  PolicyLoanRepayment = '156',
  BenefitBonusOptionAccountChange = '158',
  ILPPremiumHoliday = '160',
  ILPFundSwitch = '161',
  ILPSumAssuredChange = '162',
  MasterPolicyAddOrDeleteInsuredObject = '164',
  ILPFundAppointmentChange = '166',
  ILPRegularTopUpChange = '167',
  PaidUp = '168',
  ILPPlannedPremiumChange = '169',
  ExtendedTerm = '170',
  PolicyBasicInformationChange = '173',
  ILPAddDeleteRider = '176',
  PolicyAssignment = '182',
  HomeProtectionSchemeExemption = '191',
  PolicyReprint = '192',
  PolicyVestingOption = '194',
  SecondaryLifeInsuredOption = '195',
  MasterPolicyBasicInfoChange = '196',
  MasterPolicyCancellation = '197',
  ILPModelPortfolioChange = '198',
  ILPPolicyVoidance = '201',
  OpenEndPolicyAutoReview = '205',
  AddorDeleteVehicle = '218',
  PolicyInformationChange = '219',
}

export enum CoverageDateChangeType {
  'CoveragePeriodRevision' = '1',
  'CoveragePeriodShift' = '2',
}
