/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { PackageProduct, ProductInfoValue } from 'genesis-web-service';

export enum LanguageType {
  English = '1',
}
export interface ProductTableData extends ProductInfoValue {
  key: string;
  parentProductId?: string;
  canNotSelect?: boolean;
}

export interface ProductsLiabilityListTable extends PackageProduct {
  key: string;
}

export interface ProductsItemInfo extends ProductInfoValue {
  collocationBehavior: number;
  key: string;
  parentProductId: number;
}

export interface WaiverProductLiabilityList {
  key: string;
  liabilityId: number;
  liabilityCode: string;
  liabilityName: string;
  liabilityCategoryCode: string;
}

export interface WaiverProductRelationTableData {
  key?: string;
  attachToIsOptional: string;
  productId?: number;
  productName: string;
  attachToProductName: string;
  attachToProductId: number;
  addStu?: boolean;
  waiverLiabilityList?: WaiverProductLiabilityList[];
}

export interface ProductEchoHaveLiabilityList {
  benefitLevel: string;
  id: number;
  collocationBehavior?: number;
  insuranceProductCode: string;
  canEdit: boolean;
  canVirtual: boolean;
  isRequired: number;
  isVirtual: number;
  isSelect: boolean;
  multiCurrency: string;
  key: string;
  productCode: string;
  productId: number;
  productName: string;
  productSubCategoryId: number;
  productType: number;
  productVersion: string;
  productTypeCode: number;
  isOptional: number;
  unitType: string;
  packageLiabilityList: LiabilityListEchoInfo[];
}

export interface LiabilityListEchoInfo {
  isOptional: number;
  liabilityAmount: string;
  liabilityCode: string;
  liabilityId: number;
  liabilityLimitAmount: string;
  liabilityName: string;
  liabilityPremium: string;
  liabilityType: number;
  liabilityTypeName: string;
  packageId: number;
  packageLiabilityId: number;
  productBy: string;
  productByName: string;
  productCode: string;
  productId: number;
  productName: string;
}

export interface TreeDataInfo {
  title: string;
  value: number;
  key: number;
  children?: TreeDataInfo[];
}
