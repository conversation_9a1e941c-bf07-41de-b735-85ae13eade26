/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { createI18nInstance } from 'genesis-web-shared/lib/i18n';

import market_en_US from '../locales/en_US.json';
import market_ja_JP from '../locales/ja_JP.json';
import market_tkf_en from '../locales/tkf_en.json';
import market_zh_CN from '../locales/zh_CN.json';

const i18n = createI18nInstance(
  ['market'],
  {
    'en-US': {
      market: market_en_US,
    },
    'ja-JP': {
      market: market_ja_JP,
    },
    'zh-CN': {
      market: market_zh_CN,
    },
    'tkf-EN': {
      market: market_tkf_en,
    },
  },
  'genesis-web-market'
);

export const t = (val: string): string => i18n.t(val, { ns: ['market', 'common'] });

export default i18n;
