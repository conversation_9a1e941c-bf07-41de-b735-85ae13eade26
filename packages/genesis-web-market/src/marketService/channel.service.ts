import { MarketService } from 'genesis-web-service/lib/market/market.service';

import { FEChannel } from '../common/interface';
import { NewChannelService } from '@market/services/channel/channel.service.new';
import { ChannelResponse } from 'genesis-web-service/service-types/channel-types/package';

/**
 * 查询所有channel列表，列表中包含subChannel
 */
export const queryChannelList = (): Promise<FEChannel[]> =>
  MarketService.queryChannel().then(res => {
    if (res.success && res.value) {
      const resultsList = res.value.results || [];
      const totalChannelList: FEChannel[] = [];
      resultsList.forEach(parentItem => {
        totalChannelList.push(parentItem);
        if (Array.isArray(parentItem.childChannelList)) {
          totalChannelList.push(
            ...parentItem.childChannelList.map((childItem: FEChannel) => {
              childItem.parentCode = parentItem.code;
              childItem.parentName = parentItem.name;
              return childItem;
            })
          );
        }
      });
      return totalChannelList;
    }
    return [];
  });

export const queryTotalChannelList = (): Promise<Omit<ChannelResponse, 'channelId'>[]> =>
  Promise.all([
    queryChannelList(),
    NewChannelService.ChannelService.pageQueryChannel({
      queryParams: {
        pageIndex: 0,
        pageSize: 10000,
        type: 'AGENCY',
      }
    }),
    NewChannelService.ChannelService.pageQueryChannel({
      queryParams: {
        pageIndex: 0,
        pageSize: 10000,
        type: 'SALE_CHANNEL',
        salesChannelType: 'TIED_AGENT'
      }
    }),
    MarketService.queryServiceCompanyV2(),
    NewChannelService.ChannelService.pageQueryChannel({
      queryParams: {
        pageIndex: 0,
        pageSize: 10000,
        type: 'LEASE_CHANNEL',
      }
    }),
    NewChannelService.ChannelService.pageQueryChannel({
      queryParams: {
        pageIndex: 0,
        pageSize: 10000,
        type: 'BANK',
      }
    }),
    NewChannelService.ChannelService.pageQueryChannel({
      queryParams: {
        pageIndex: 0,
        pageSize: 10000,
        type: 'BROKER_COMPANY',
      }
    }),
    NewChannelService.ChannelService.pageQueryChannel({
      queryParams: {
        pageIndex: 0,
        pageSize: 10000,
        type: 'FRONT_LINE_CHANNEL',
      }
    }),
    NewChannelService.ChannelService.pageQueryChannel({
      queryParams: {
        pageIndex: 0,
        pageSize: 10000,
        type: 'KEY_ACCOUNT_CHANNEL',
      }
    }),
  ]).then(
    ([
      channelRes,
      agencyRes,
      tiedAgentRes,
      serviceCompanyRes,
      leasingRes,
      bankRes,
      brokerCompanyRes,
      frontLineRes,
      keyAccountRes,
    ]) => {
      const result: Omit<ChannelResponse, 'channelId'>[] = [];
      result.push(...channelRes);
      result.push(...(agencyRes.data || []));
      result.push(...tiedAgentRes.data || []);
      result.push(...(serviceCompanyRes.results?.map(item => ({
        ...item,
        name: item.instituteName,
        code: item.instituteCode,
      })) || []));
      result.push(...(leasingRes.data || []));
      result.push(...(bankRes.data || []));
      result.push(...(brokerCompanyRes.data || []));
      result.push(...(frontLineRes.data || []));
      result.push(...(keyAccountRes.data || []));

      return result;
    }
  );
