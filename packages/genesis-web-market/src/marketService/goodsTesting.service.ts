import { NewMarketService } from '@market/services/market/market.service.new';
import { DataPreConfigurationService } from 'genesis-web-service/lib/data-pre-configuration/dataPreConfiguration.service';

/**
 * goods testing出单成功之后publish goods
 *
 * 修改goods状态并且生成一个默认的DataPreConfiguiration
 */
export const publishGoods = (goodsId: string) =>
  NewMarketService.GoodsMgmtControllerService.launchGoods(+goodsId, {
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(() => DataPreConfigurationService.createDefaultSchema(goodsId));

/**
 * 判断Notification Configuration是否展示
 */
export const isShowNotification = async (goodsId: number) => {
  let hasNotificationList = false;

  const res = await NewMarketService.GoodsNotificationConfigMgmtService.queryNotification({ goodsId });

  if (res.success && res.value && res.value.length > 0) {
    hasNotificationList = true;
  }
  return hasNotificationList;
};
