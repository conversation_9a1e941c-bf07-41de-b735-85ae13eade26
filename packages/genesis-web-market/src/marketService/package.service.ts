/* eslint-disable @typescript-eslint/no-unsafe-assignment */

/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { NewMarketService } from '@market/services/market/market.service.new';
import { MetadataService } from 'genesis-web-service';

// 如果Package下有任意一个产品是非driven，即认为是非driven package
export const isDrivenPackage = (
  packageProducts: {
    productSubCategoryId?: number;
  }[]
) => {
  let isDriven = true;
  packageProducts.forEach(item => {
    if (!item.productSubCategoryId) {
      isDriven = false;
    }
  });
  return isDriven;
};

export const getPackageType = (packageId: string) => {
  let withSchema = true;
  return NewMarketService.PackageProductMgmtService.queryConfigProducts({ packageId: +packageId })
    .then(packageConfigWithProducts => {
      if (packageConfigWithProducts?.products?.length > 0) {
        withSchema = isDrivenPackage(packageConfigWithProducts?.products || []);
      }
      return withSchema;
    })
    .catch(() => withSchema);
};

/**
 * 根据bizDictGroupCode获取动态级联关系的枚举
 * @example vehicleDatabase
 */
export const getGroupCascadingEnums = (
  params: {
    groupCode: string;
    subGroupCode: string | null;
  }[]
) => MetadataService.queryCustomizedBizDictByGroupCode(params);
