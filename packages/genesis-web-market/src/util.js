import { Modal, message } from 'antd';

import { Icon } from '@zhongan/nagrand-ui';

import ApiClient from 'genesis-web-service/ApiClient';
import { ProductCategoryItemExtend1 } from 'genesis-web-service/lib/product/enums';

import I18nInstance from '@market/i18n.ts';

import ModalTitle from './components/ModalTitle/ModalTitle';

const client = new ApiClient({
  observe: 'response',
});

export function combineColumns(columns, enhanceColumns) {
  for (let index = 0; index < columns.length; index++) {
    const element = columns[index];
    const renderKey = element.renderKey;
    if (renderKey && enhanceColumns.hasOwnProperty(renderKey) && !element.hasOwnProperty('render')) {
      element.render = enhanceColumns[renderKey];
    }
  }
}

/**
 * Helper function for class component
 * @param permissionSet
 * @param oneOf
 * @param allOf
 * @return {boolean}
 */
export const canActivateHelper = (permissionSet, { oneOf = undefined, allOf = undefined }) => {
  if (allOf && allOf.length > 0) {
    return allOf.every(one => !!permissionSet[one]);
  }

  if (oneOf && oneOf.length > 0) {
    return oneOf.some(one => !!permissionSet[one]);
  }

  return true;
};

export const moduleId = (curr, parent = undefined) => (parent ? `${parent}.${curr}` : curr);

export function getFileFromDownloadResp(response) {
  const { data, headers } = response;
  const alink = document.createElement('a');

  const blob = new Blob([data], {
    type: 'application/octet-stream;charset=utf-8',
  });
  alink.href = URL.createObjectURL(blob);
  const fileName = window.decodeURI(headers['content-disposition'] && headers['content-disposition'].split('=')[1]);

  if (fileName) {
    alink.download = fileName;
  }
  document.body.appendChild(alink);
  alink.click();
  setTimeout(() => {
    document.body.removeChild(alink);
  }, 500);
}

export function urlQuery(key) {
  const parsedUrl = new URL(window.location.href);
  return parsedUrl.searchParams.get(key);
}

/**
 // * @deprecated 废弃
 * url：文件下载地址，data：下载方法为post时候的入参，method：请求方法
 * 注意点：mimeTypeMapping是一个文件类型和mime类型的映射，如果有不支持的文件类型，需要添加映射关系
 */
export function downloadFile(url, data = {}, method = 'get', callback) {
  const options = {
    responseType: 'blob',
  };
  let requestMethod;
  if (method.toLowerCase() === 'get') {
    requestMethod = client.get(url, options);
  } else {
    requestMethod = client.post(url, data, options);
  }
  requestMethod
    .then(response => {
      // TODO: 此处沿用之前的逻辑，之后是否考虑统一处理？
      // 修正了campaign 模板下载的bug 正常返回状态码为201 之前!==200 将正常返回情况变为异常情况了
      if (response.status < 200 || response.status >= 300) {
        const errData = JSON.parse(String.fromCharCode.apply(null, new Uint8Array(response.data)));
        message.error(errData.message);
        if (typeof callback === 'function') {
          callback('error', errData.message);
        }
        return false;
      }

      const headers = response.headers;
      const pattern = new RegExp(/filename="?([^"]*)/i);
      let fileName = decodeURIComponent(pattern.exec(headers?.['content-disposition'])?.[1] || '');
      if (fileName === 'undefined' || fileName === null || fileName === '') {
        // 特殊处理，status code=200的情况下，有错误的message
        let data = {};
        try {
          data = JSON.parse(String.fromCharCode.apply(null, new Uint8Array(response.data)));
        } catch (error) {
          console.log(error);
        }
        const errorMsg = response.headers.msg || data.message;
        if (errorMsg) {
          message.error(errorMsg);
        }
        if (typeof callback === 'function') {
          callback('error', errorMsg);
        }
        return false;
      }
      if (fileName.startsWith('"') && fileName.endsWith('"')) {
        fileName = fileName.slice(1, -1);
      }
      const link = document.createElement('a');
      // const contentType = response.headers['content-type'];
      console.log('response.data', response.data);
      // const blob = new Blob([response.data], { type: contentType || 'application/octet-stream' });
      link.href = window.URL.createObjectURL(response.data);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      if (typeof callback === 'function') {
        callback('success');
      }
      document.body.removeChild(link);
    })
    .catch(() => {
      if (typeof callback === 'function') {
        callback('error');
      }
    });
}

export const generalConfirm = ({ title, content, onOk, okText = I18nInstance.t('Next') }) => {
  Modal.confirm({
    width: 480,
    title,
    icon: <Icon type="exclamation-circle" />,
    content,
    onOk() {
      if (typeof onOk === 'function') {
        onOk();
      }
    },
    okText,
    okButtonProps: {
      style: onOk
        ? {}
        : {
            display: 'none',
          },
    },
    cancelText: I18nInstance.t('Cancel'),
    className: ModalTitle.ModalClassName,
  });
};

export const isILPProduct = productCategory => {
  const ILPCategoryCodes = [
    ProductCategoryItemExtend1.UnitLinked,
    ProductCategoryItemExtend1.Universal,
    ProductCategoryItemExtend1.VariableAnnuity,
  ];

  return ILPCategoryCodes.includes(+productCategory);
};
