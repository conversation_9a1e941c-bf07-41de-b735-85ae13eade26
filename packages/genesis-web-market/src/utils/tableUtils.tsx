import { Tooltip } from 'antd';

export const renderPackagesNameInTable = (
  packages: {
    packageName: string;
    packageCode: string;
  }[],
  showCount = 2, // 直接展示的数量，其余的在tooltip中展示
  maxWidth = 180
) => {
  const showList = packages.slice(0, showCount);
  const showText = showList.map(_package => `${_package.packageCode}_${_package.packageName}`).join(';');
  const showDom = (
    <Tooltip title={showText}>
      <span
        style={{
          maxWidth,
          display: 'inline-block',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
        }}
      >
        {showText}
      </span>
    </Tooltip>
  );
  if (packages.length <= showCount) {
    return showDom;
  }
  const packageListExceptFirst = packages.slice(showCount);
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
      }}
    >
      {showDom}
      <Tooltip
        title={
          <div>
            {packageListExceptFirst.map(_package => (
              <div>{`${_package.packageCode}_${_package.packageName}`}; </div>
            ))}
          </div>
        }
      >
        <span style={{ fontWeight: 700 }}>{`;+${packageListExceptFirst.length}`}</span>
      </Tooltip>
    </div>
  );
};
