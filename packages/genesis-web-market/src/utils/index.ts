import { divide } from 'lodash-es';

import { t } from '@market/i18n';

export const scrollToHashTag = (hash: string) => {
  window.location.hash = hash;
  setTimeout(() => {
    window.location.hash = '';
  }, 1000);
};

/**
 * 去掉单体对象中素有属性值(String类型)中的首尾空格, 单体对象属性值中不能有function
 * 表单值 表单结果 提交 submit trim 去空格
 * @param {Object} obj
 * @returns {Object}
 */
export function trimObjectStringValue(obj: unknown): unknown {
  if (typeof obj !== 'object') return obj;
  return JSON.parse(JSON.stringify(obj, (key, value: unknown) => (typeof value === 'string' ? value.trim() : value)));
}

/**
 * 渲染普通文本，如果没有值，显示占位符 - -
 */
export function textRender(text?: string | number): string | number {
  return text || t('- -');
}

/**
 * 负数渲染函数，比如取值是10，要渲染成 -10
 */
export const negativeNumberRender = (text?: string | number): string => {
  if (text) {
    // 负数要渲染成正数
    if (typeof text === 'string' && text?.startsWith('-')) {
      return text.substring(1);
    }
    return `-${text}`;
  }
  return t('- -');
};

/**
 * 为对象数组增加一个index属性，主要用于显示表格中数据的序号
 */
export function attachIndexForList<T>(array: T[]) {
  return array.map((item, index) => ({
    ...item,
    index: index + 1,
  }));
}

/**
 * 为对象增加一个key属性，赋值一个随机数，主要用于可编辑表格
 */
export function attachRandomKeyForObj<T extends Record<string, any>>(obj: T) {
  return {
    ...obj,
    key: Math.random(),
  };
}

/**
 * 判断对象里面是不是存在非空值
 */
export function isNotEmptyObject(obj: Record<string, any> = {}) {
  return Object.values(obj).filter(item => item !== null && item !== undefined).length > 0;
}

/**
 * 安全的加法，防止浮点数精度问题
 * 注意大数溢出问题
 */
export function safeAdd(augend: number, addend: number): number {
  return (10000 * augend + 10000 * addend) / 10000;
}

/**
 * 浮点数除法，防止浮点数精度问题
 * 注意大数溢出问题
 */
export function floatDivide(dividend: number, divisor: number): number {
  return (10000 * dividend) / divisor / 10000;
}

/**
 * 浮点数乘法，防止浮点数精度问题
 * 注意大数溢出问题
 *
 * @example 0.0157 * 100
 */
export function floatMultiplication(number1: number, number2: number): number {
  return (1000 * number1 * number2) / 1000;
}

/**
 * 被除数的0判断
 */
export function safeDivide(dividend: number, divisor: number): number {
  if (divisor === 0) {
    return 0;
  }
  return divide(dividend, divisor);
}
