export class CurrencyUtils {
  /*
        function：将数字转换为以逗号间隔的字符串, eg. 112233 => 1,112,233
        param：amount: number
        return：string
    */
  static getStringifyAmount = (amount: string | number) => amount.toLocaleString();

  /*
        function：获取小数点后两位数字，不进行四舍五入
        param：amount: number | string
        return：string
    */
  static getTwoDigitsAfterDecimalPoint = (amount: string | number) => {
    const strAmount = this.getStringifyAmount(amount);
    if (strAmount.includes('.')) {
      return strAmount.substring(0, strAmount.indexOf('.') + 3);
    }
    return strAmount;
  };

  /*
       function：获取小数点后两位数字，四舍五入，如果小数点后为00则舍弃, eg. 100.00 => 100
       param：amount: number | string
       return：string
    */
  static getFixedTwoDigits = (amount: string | number) => {
    const fixedAmount = Number(amount).toFixed(2);
    const arrAmount = fixedAmount.split('.');
    if (arrAmount[1] === '00') {
      return arrAmount[0];
    }
    return fixedAmount;
  };

  /*
       function：将两个字符串拼接起来，eg. ('12', '33') => '12.33'
       param：beforeStr: string / afterStr: string
       return：string
    */
  static getConcatProcessAmount = (beforeStr: string | number, afterStr: string | number) => {
    const processAmount = `${beforeStr}.${afterStr}`;
    return this.getFixedTwoDigits(processAmount);
  };

  /*
      function：获取最终数字
      param：amount: string | number
      return：string
    */
  static getShortedAmount = (amount: string | number) => {
    const strAmount = this.getStringifyAmount(amount);
    const arrAmount = strAmount.split(',');
    if (arrAmount.length === 1) {
      return this.getTwoDigitsAfterDecimalPoint(arrAmount[0]);
    }
    if (arrAmount.length === 2) {
      const afterDecimalAmount = arrAmount[1].split('.')[0];
      return this.getConcatProcessAmount(arrAmount[0], afterDecimalAmount);
    }
    if (arrAmount.length > 2 && arrAmount.length < 6) {
      return this.getConcatProcessAmount(arrAmount[0], arrAmount[1]);
    }
    if (arrAmount.length >= 6) {
      return 'Amount exceeds range.';
    }
  };

  /*
      function：获取金额的单位 ( K: thousand / M: million / B: billion / T: trillion )
      param：amount: string | number
      return：string
    */
  static getAmountSymbol = (amount: string | number) => {
    const strAmount = CurrencyUtils.getStringifyAmount(amount);
    const arrAmount = strAmount.split(',');
    switch (arrAmount.length) {
      case 2:
        return 'K';
      case 3:
        return 'M';
      case 4:
        return 'B';
      case 5:
        return 'T';
      default:
        return '';
    }
  };
}
