/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { cloneDeep } from 'lodash-es';
import { v4 as uuidv4 } from 'uuid';

import {
  CustomerRole,
  DrivenSchemaDef,
  FactorSourceType,
  ObjectSchemaDef,
  SchemaCalcParam,
  SchemaParam,
} from 'genesis-web-service';

import { PolicySchema, PrdSchemaDef, SchemaDefParamType, SchemaField } from '@market/common/schema.interface';

export const generateParameterPath = (
  schemaDefType: string,
  schemaDefRefId: string | number,
  code: string,
  posFlag: 'BEFORE' | 'AFTER' = 'AFTER'
): string => `${schemaDefType}/${schemaDefRefId}/${code}/${posFlag}`;

export const praseParameterPath = (
  parameterPath: string
): {
  schemaDefType: string;
  schemaDefRefId: string;
  code: string;
  posFlag?: string;
} => {
  const [schemaDefType, schemaDefRefId, code, posFlag] = parameterPath.split('/');

  return {
    schemaDefType,
    schemaDefRefId,
    code,
    posFlag,
  };
};

export const getJsonPath = (parameterMap: Record<string, string>, schemaParam: SchemaCalcParam) => {
  /**
   * insured和policyHolder的schemaDefType、schemaDefRefId、factorCode，完全一样，所以需要依赖jsonPath；
   * 不同责任上面的schemaDefType、schemaDefRefId、factorCode完全一样，所以需要依赖jsonPath
   * 所以insured和policyHolder的路径不支持国际化
   */
  if (schemaParam.schemaDefType === 'CUSTOMER' || schemaParam.schemaDefType === 'LIABILITY') {
    return schemaParam.jsonPath;
  }
  const key = generateParameterPath(schemaParam.schemaDefType, schemaParam.schemaDefRefId, schemaParam.factorCode);
  return parameterMap[key] || schemaParam.jsonPath;
};

interface collectSchemaParamsFromStepsOption {
  isPOS: boolean;
  isClaim?: boolean;
  isGeneral?: boolean;
  objectListParam?: Record<string, string[]>;
  subFormulaObjectFactorShowTextList?: string[];
}

export const collectSchemaParams = (
  cleanUsedParams: string[],
  parameterMap: Record<string, string>,
  parameterList: SchemaField[],
  option: collectSchemaParamsFromStepsOption
): SchemaParam[] => {
  const schemaParams: SchemaParam[] = [];

  cleanUsedParams.forEach((cleanUsedParam, cleanUsedParamIndex) => {
    const uuid = uuidv4().substr(0, 8);
    const { isPOS = false, isGeneral = false } = option;

    const paramPath = parameterMap[cleanUsedParam];
    const { schemaDefType, schemaDefRefId, code } = praseParameterPath(paramPath);

    const currentParameter = parameterList.find(parameter => parameter.showText === cleanUsedParam);

    if (currentParameter) {
      schemaParams.push({
        factorCode: code,
        schemaDefType,
        schemaDefRefId,
        jsonPath: cleanUsedParam, // ratetable里面的jsonPath存showText
        customerRole: currentParameter.customerRole,
        liabilityId: isGeneral ? undefined : currentParameter.liabilityId,
        factorUuid: uuid,
        isExtension: currentParameter.isExtension,
        factorSourceType: FactorSourceType.schema,
      });
    }
  });

  return schemaParams;
};

export const convertSchemaParamsToJSONPath = (parameterMap: Record<string, string>, schemaParams: SchemaParam[]) => {
  let factorShowtextList: string[] = [];

  if (Array.isArray(schemaParams)) {
    factorShowtextList = schemaParams.map((schemaParam: SchemaParam) => {
      const jsonPath = getJsonPath(parameterMap, schemaParam);

      return jsonPath;
    });
  }

  return factorShowtextList;
};

/**
 * 生成因子的展示文案
 */
export const generateGeneralShowText = (
  field: {
    code: string;
  },
  type: SchemaDefParamType
): string => `${type}_${field.code}`;

/**
 * 生成标的类型因子的展示文案
 */
export const generateObjectShowText = (
  schemaDef: Pick<PrdSchemaDef, 'objectSubCategory'>,
  field: {
    code: string;
  }
): string => `${schemaDef.objectSubCategory!.toLowerCase()}_${field.code}`;

export const generateShowText = (
  field: {
    code: string;
  },
  type: SchemaDefParamType,
  schemaDef: DrivenSchemaDef
): string => {
  if (type === SchemaDefParamType.object) {
    return generateObjectShowText(schemaDef as ObjectSchemaDef, field);
  }

  return generateGeneralShowText(field, type);
};

/**
 * 在后端返回的对象上面增加前端需要用的属性，并且修改interface
 *
 * fillDataInFields 方法的优化版
 */
export const attachPropsToFields = (schemaDef: DrivenSchemaDef, paramType: SchemaDefParamType): PrdSchemaDef => {
  const fields: SchemaField[] = schemaDef.fields.map(field => {
    const tempField: SchemaField = {
      ...field,
      schemaDefType: schemaDef.schemaDefType,
      schemaDefRefId: schemaDef.schemaDefRefId,
      schemaDefParamType: paramType,
      key: generateParameterPath(schemaDef.schemaDefType, schemaDef.schemaDefRefId, field.code),
      showText: generateShowText(field, paramType, schemaDef),
    };

    if (paramType === SchemaDefParamType.object) {
      tempField.objectSubCategory = (schemaDef as ObjectSchemaDef).objectSubCategory;
    } else if (paramType === SchemaDefParamType.insured) {
      tempField.customerRole = CustomerRole.insured;
    } else if (paramType === SchemaDefParamType.policyHolder) {
      tempField.customerRole = CustomerRole.policyHolder;
    }

    return tempField;
  });

  const prdSchemaDef: PrdSchemaDef = {
    ...schemaDef,
    fields,
  };

  return prdSchemaDef;
};

// 生成 showText 与 key 的映射关系，方便快速获取
export const generateParameterMap = (parameterList: SchemaField[]) => {
  const parameterMap: {
    [index: string]: string;
  } = {};

  parameterList.forEach(parameter => {
    parameterMap[parameter.key] = parameter.showText;
    parameterMap[parameter.showText] = parameter.key;
  });

  return parameterMap;
};

export interface TransformSchemaOptions {
  isGeneral?: boolean;
}

const firstLevelTreeKey: (keyof PolicySchema)[] = ['policy', 'product', 'channel', 'coverage'];

const schemaKeyAndParamTypeMap: Record<string, SchemaDefParamType> = {
  product: SchemaDefParamType.product,
  coverage: SchemaDefParamType.coverage,
  channel: SchemaDefParamType.channel,
  policy: SchemaDefParamType.policy,
};

// 这种方法用于不需要收集SchemaDef的情况
const collectSchemaFields = (schemaDefs: PrdSchemaDef[]) => {
  const totalFields: SchemaField[] = [];
  schemaDefs.forEach(schemaDef => {
    totalFields.push(...schemaDef.fields);
  });

  return totalFields;
};

/**
 * 处理schema对象，获取因子列表、因子路径映射Map、因子层级树
 */
export const transformSchema = (originSchema: PolicySchema) => {
  const schema = cloneDeep(originSchema);
  const updatedSchema: Record<string, PrdSchemaDef[]> = {};
  const parameterList: SchemaField[] = [];
  let totalInsuredList: SchemaField[] = [];
  let totalPolicyHolderList: SchemaField[] = [];

  // 动态处理只有一级结构的schema因子
  firstLevelTreeKey.forEach(key => {
    if (schema[key]) {
      updatedSchema[key] = schema[key]!.map(schemaDef => attachPropsToFields(schemaDef, schemaKeyAndParamTypeMap[key]));
      const tempList = collectSchemaFields(updatedSchema[key]);
      parameterList.push(...tempList);
    }
  });

  if (schema.object) {
    updatedSchema.object = schema.object.map(schemaDef => attachPropsToFields(schemaDef, SchemaDefParamType.object));
    totalInsuredList = collectSchemaFields(updatedSchema.object);
    parameterList.push(...totalInsuredList);
  }

  if (schema.insured) {
    updatedSchema.insured = schema.insured.map(schemaDef => attachPropsToFields(schemaDef, SchemaDefParamType.insured));
    totalInsuredList = collectSchemaFields(updatedSchema.insured);
    parameterList.push(...totalInsuredList);
  }

  if (schema.policyHolder) {
    updatedSchema.policyHolder = schema.policyHolder.map(schemaDef =>
      attachPropsToFields(schemaDef, SchemaDefParamType.policyHolder)
    );
    totalPolicyHolderList = collectSchemaFields(updatedSchema.policyHolder);
    parameterList.push(...totalPolicyHolderList);
  }

  const parameterMap = generateParameterMap(parameterList);

  return {
    parameterList,
    parameterMap,
  };
};
