import { FormInstance } from 'antd';

interface ValidateErrorEntity {
  errorFields: { name: string[]; errors: string[] }[];
}

// 滚动至form第一个validate Error位置
export const formErrorHandler = (form: FormInstance) => (errorInfo?: ValidateErrorEntity) => {
  const firstErrorName = errorInfo?.errorFields?.[0]?.name;
  if (!firstErrorName) {
    return Promise.reject(errorInfo);
  }
  form.scrollToField(firstErrorName, {
    // 该配置使得页面滚动到表单的label位置，默认是滚动到控件位置
    behavior: actions => {
      actions.forEach(({ el, top, left }) => {
        el.scrollTop = top - 60;
        el.scrollLeft = left;
      });
    },
  });

  return Promise.reject({
    validated: false,
    ...errorInfo,
  });
};
