export const textEllipsis = (text: string, maxLength: number) => {
  if (text.length > maxLength) {
    return `${text.substring(0, maxLength)} ...`;
  }
  return text;
};

export const getMultiLineEllipsisConfig = (line: number): any => ({
  textOverflow: 'ellipsis',
  overflow: 'hidden',
  display: '-webkit-box',
  WebkitLineClamp: line,
  lineClamp: line,
  WebkitBoxOrient: 'vertical',
  wordBreak: 'break-all',
});
