/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { rootReducer } from 'genesis-web-shared/lib/redux';

import {
  FILL_CLIENT_CONFIG,
  FILL_LOCALE,
  FILL_MIXED_USER_INFO,
  FILL_MODULE_PERMISSIONS,
  GET_ENUMS,
  GET_ENVCONFIG,
  GET_TENANT_TIME_FORMAT_BY_ZEUS,
  LOCAL_CONFIG,
  SAVE_BIZ_DICTS,
  SAVE_ENUM,
  TENANT_INFO,
  UPDATE_CURRENT_MENU,
  UPDATE_DOMAIN,
  UPDATE_GLOBALCONFIG,
  UPDATE_OFFSET_TIME,
  UPDATE_PATH,
  UPDATE_USER_INFO,
} from './action';

/**
 * @typedef {object} UserState
 * @param {string} loginName
 * @param {number | null} userId
 * @param {string} userRealName
 * @param {string} tenant
 * @param {ModulePermission[]} modulePermissions
 */

/**
 * @type {UserState}
 */
const initialUserState = {
  loginName: '',
  userId: null,
  userRealName: '',
  countyCode: '',
  mobile: '',
  tenant: '',
  region: '',
  email: '',
  offsetTime: 0,
  modulePermissions: [],
  basicPackageInfo: {},
};

/**
 *
 * @param {UserState} prev
 * @param {OAuthUserInfo} next
 *
 * @returns {UserState}
 */
const fillMixedUserInfo = (prev, next) => ({
  ...prev,
  loginName: next.username,
  userId: next.id,
  userRealName: next.fullName,
  countryCode: next.countryCode,
  mobile: next.mobile,
  tenant: next.tenant,
  region: next.region,
  email: next.email,
  offsetTime: next.offsetTime,
});

const updateUserInfo = (prev, next) => ({
  ...prev,
  loginName: next.username,
  userId: next.id,
  userRealName: next.fullName,
  countryCode: next.countryCode,
  mobile: next.mobile,
  email: next.email,
});

const userStateReducer = (state = initialUserState, action) => {
  switch (action.type) {
    case FILL_MIXED_USER_INFO:
      return fillMixedUserInfo(state, action.obj);
    case UPDATE_USER_INFO:
      return updateUserInfo(state, action.payload);
    case FILL_MODULE_PERMISSIONS:
      return {
        ...state,
        modulePermissions: action.payload,
      };
    case UPDATE_OFFSET_TIME:
      return {
        ...state,
        offsetTime: action.payload,
      };
    default:
      return state;
  }
};

const initialLocaleState = {
  lang: 'en_US',
};

const localeReducer = (state = initialLocaleState, action) => {
  switch (action.type) {
    case FILL_LOCALE:
      return {
        ...state,
        lang: action.payload,
      };
    default:
      return state;
  }
};

const initialClientConfig = {
  nav: true,
  head: true,
  embed: false,
  security: true,
};

const clientConfigReducer = (state = initialClientConfig, action) => {
  switch (action.type) {
    case FILL_CLIENT_CONFIG:
      return {
        ...state,
        ...action.payload,
      };
    default:
      return state;
  }
};

function StoreEnum(state = {}, action) {
  switch (action.type) {
    case GET_ENUMS:
    case SAVE_ENUM:
    case SAVE_BIZ_DICTS:
      return {
        ...state,
        ...action.data,
      };
    default:
      return state;
  }
}

// 更新全局配置
function globalConfig(state = {}, action) {
  switch (action.type) {
    case UPDATE_GLOBALCONFIG:
      return { ...action.obj };
    default:
      return state;
  }
}

// 根据路由选择菜单
function currentPath(state = '', action) {
  switch (action.type) {
    case UPDATE_PATH:
      return action.str;
    default:
      return state;
  }
}

// 返回所有的系统url
function Domain(state = '', action) {
  switch (action.type) {
    case UPDATE_DOMAIN:
      return { ...action.obj };
    default:
      return state;
  }
}
// 返回系统当前的菜单
function currentMenu(state = '', action) {
  switch (action.type) {
    case UPDATE_CURRENT_MENU:
      return action.str;
    default:
      return state;
  }
}

// 返回时区列表
function productConfig(state = {}, action) {
  switch (action.type) {
    case LOCAL_CONFIG:
      return { ...action.obj };
    default:
      return state;
  }
}

// 返回环境信息
function envConfig(state = {}, action) {
  switch (action.type) {
    case GET_ENVCONFIG:
      return { ...action.obj };
    default:
      return state;
  }
}
// 返回环境信息
function tenantInfo(state = { orgType: 'default' }, action) {
  switch (action.type) {
    case TENANT_INFO:
      return { ...action.obj };
    default:
      return state;
  }
}

function tenantTimeFormatByZeusReducer(state = null, action) {
  switch (action.type) {
    case GET_TENANT_TIME_FORMAT_BY_ZEUS:
      return { ...action.obj };
    default:
      return state;
  }
}

const appReducer = {
  userInfo: userStateReducer,
  locale: localeReducer,
  clientConfig: clientConfigReducer,
  currentPath,
  Domain,
  enums: StoreEnum,
  globalConfig,
  timeZone: rootReducer.timeZone,
  currentMenu,
  productConfig,
  envConfig,
  tenantInfo,
  tenantTimeFormatByZeus: tenantTimeFormatByZeusReducer,
};

export default appReducer;
