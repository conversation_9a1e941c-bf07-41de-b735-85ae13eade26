/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import moment from 'moment-timezone';

import { MetadataService } from 'genesis-web-service/lib/metadata/metadata.service';
import { SystemService } from 'genesis-web-service/lib/system/system.service';

import { NewProductService } from '@market/services/product/product.service.new';

/*
 * action 类型
 */

/**
 * Mixed user info, except origin user info, it contains tenant and region info;
 * @type {string}
 */
export const FILL_MIXED_USER_INFO = 'FILL_MIXED_USER_INFO';
/**
 * Pure user info
 * @type {string}
 */
export const UPDATE_USER_INFO = 'UPDATE_USER_INFO';
export const UPDATE_PATH = 'UPDATE_PATH';
export const UPDATE_DOMAIN = 'UPDATE_DOMAIN';
export const GET_ENUMS = 'GET_ENUMS';
export const SAVE_ENUM = 'SAVE_ENUM';
export const SAVE_BIZ_DICTS = 'SAVE_BIZ_DICTS';
export const UPDATE_GLOBALCONFIG = 'UPDATE_GLOBALCONFIG';
export const UPDATE_CURRENT_MENU = 'UPDATE_CURRENT_MENU';
export const LOCAL_CONFIG = 'LOCAL_CONFIG';
export const GET_ENVCONFIG = 'GET_ENVCONFIG';
export const TENANT_INFO = 'TENANT_INFO';
export const FILL_MODULE_PERMISSIONS = 'FILL_MODULE_PERMISSIONS';
export const FILL_LOCALE = 'FILL_LOCALE';
export const FILL_CLIENT_CONFIG = 'FILL_CLIENT_CONFIG';
export const UPDATE_OFFSET_TIME = 'UPDATE_OFFSET_TIME';
export const SAVE_PACKAGE_BASIC = 'SAVE_PACKAGE_BASIC';
export const GET_TENANT_TIME_FORMAT_BY_ZEUS = 'GET_TENANT_TIME_FORMAT_BY_ZEUS';

const bizDictKeys = ['triggerPointType', 'collocationBehavior'];

const tenantDictKeys = {
  dictKeys: [
    'channelType',
    'reDefineEffectiveDateRule',
    'packagePaymentMethodDefine',
    'riderStatusType',
    'uploadApplicationElementsType',
    'policyEffectiveWithoutCollection',
    'calculationRule',
    'goodsLabel',
    'policyIDTypeList',
    'documentTypeSubCategory',
    'agentCategory',
    'ilpPaymentMethod',
    'ncbIssueType',
    'targetUserOfVoucher',
    'posPremiumRefundType',
    'packageEffectiveDateRuleEnhancement',
    'goodsType',
    'questionnaireTrigger',
    'customerDrivenSwitch',
    'typeOfCI',
    'coverageDateChangeType',
    'placeOfIncident',
    'certiType',
    'dateFormat',
    'nameFormat',
    'country',
    'customerServiceItem',
    'productCategory',
    'goodsCategory',
    'baseCurrency',
    'documentType',
    'payMethod',
    'posReason',
    'fundTypes',
    'currencys',
    'region',
    'cancellationReason',
    'race',
    'objectCategory',
    'objectSubCategory',
    'customerType',
    'customerSubType',
    'liabilityCategory',
    'waiverInsuredPerson',
    'loadingMethod',
    'overdueStatus',
    'LoadingMethodEnum',
    'cashValueType',
    'calculationMethod',
    'premiumType',
    'serviceType',
    'coveragePeriodType',
    'premiumPeriodType',
    'questionnairePurpose',
    'premiumFrequencyType',
    'declarationCategory',
    'serviceFreeCharge',
    'servicePaymentFrequency',
    'serviceCalculationItem',
    'yesNo',
    'bizTopic',
    'packageTaxType',
    'packageTaxCalculateType',
    'effectiveDateRule',
    'techProductEffectiveDateRule',
    'techProductExpiryDateRule',
    'policyProductStatusChangeCause',
    'packageStatus',
    'agreementBasis',
    'isOptional',
    'goodsStatus',
    'answerType',
    'informGroupType',
    'packagePeriodRule',
    'dateUnit',
    'issuingModel',
    'coverageTimeAgreementTypeBase',
    'goodsRuleTriggerPoint',
    'language',
    'applicationElementType',
    'newSubjectMatterType',
    'serviceCampaignFeeType',
    'salesType',
    'relationshipType',
    'enableRenewalType',
    'paymentType',
    'triggerPointType',
    'fieldValidationRule',
    'partnerType',
    'partnerFeeType',
    'uwIssuePlatform',
    'survivalPaymentDrawType',
    'paymentFrequencyBase',
    'paymentDeferPeriodType',
    'paymentPeriodType',
    'countryCodeIsoRule',
    'settlementRule',
    'ageCalcBasic',
    'voucherAmountType',
    'voucherStatus',
    'voucherDefStatus',
    'voucherEffectiveTimeRule',
    'voucherExpiryTimeRule',
    'freelookPeriodUnit',
    'bizType',
    'stackType',
    'stackUnit',
    'claimStackValueType',
    'claimTimesType',
    'mainConditionType',
    'claimBillObjectType',
    'claimPeriodType',
    'claimDaysType',
    'numberItems',
    'appliedUnit',
    'billConditions',
    'claimIncidentType',
    'dueDateCompare',
    'dateCompare',
    'overdueStatus',
    'renewalPolicyEffectiveDateRule',
    'supportBeneficiaryType',
    'goodsDocType',
    'assigneeType',
    'salesDistrict',
    'premiumBreakdownItem',
    'splittingRatioInputMethod',
    'pricingCurrency',
    'policyScenarioType',
    'coveragePeriodBasedOnApplicationElements',
    'expiryDateRule',
    'expiryDateCalMethod',
    'coveragePeriodValueType',
    'expiryDateAdjustmentType',
    'withoutPayOverdueStatus',
    'gracePeriodUnit',
    'calculatePremiumForInstallmentCategory',
    'installmentPremiumCalculationBasis',
    'dueDateRule',
    'overdueHanding',
    'dunningRule',
    'premiumFrequencyLapseDateType',
    'extractionMethod',
    'autoDeductionDate',
    'premiumEndDateCalculationRule',
    'claimStackValueType',
    'claimStackValueRule',
    'manualAnnualPremiumInputMethod',
    'planPremiumModel',
    'cancellationType',
    'coveragePeriodRevision',
  ],
};

/*
 * action 创建函数
 */
export const savePackageBasic = data => ({ type: SAVE_PACKAGE_BASIC, data });

export const getEnums = data => ({ type: GET_ENUMS, data });

export const saveEnum = data => ({ type: SAVE_ENUM, data });

// 批量保存
export const saveBizDicts = data => ({ type: SAVE_BIZ_DICTS, data });

export const fillMixedUserInfo = obj => ({ type: FILL_MIXED_USER_INFO, obj });

export const updatePath = str => ({ type: UPDATE_PATH, str });

export const updateDomain = obj => ({ type: UPDATE_DOMAIN, obj });

export const updateGlobalConfig = obj => ({ type: UPDATE_GLOBALCONFIG, obj });

export const saveLocalConfig = obj => ({ type: LOCAL_CONFIG, obj });
export const getEnvConfig = obj => ({ type: GET_ENVCONFIG, obj });
export const saveTenantInfo = obj => ({ type: TENANT_INFO, obj });

export const updateUserInfo = ({ payload }) => ({
  type: UPDATE_USER_INFO,
  payload,
});
export const fillModulePermissions = ({ payload }) => ({
  type: FILL_MODULE_PERMISSIONS,
  payload,
});
export const fillLocale = ({ payload }) => ({ type: FILL_LOCALE, payload });
export const fillClientConfig = ({ payload }) => ({
  type: FILL_CLIENT_CONFIG,
  payload,
});
export const updateOffsetTime = ({ payload }) => ({
  type: UPDATE_OFFSET_TIME,
  payload,
});

export const updateCurrentMenu = str => ({ type: UPDATE_CURRENT_MENU, str });

export const getTenantTimeFormatByZeus = obj => ({
  type: GET_TENANT_TIME_FORMAT_BY_ZEUS,
  obj,
});

const generateOption = i => ({
  itemExtend1: i.dictValue,
  itemName: i.dictValueName,
  dictKeyName: i.dictKeyName,
  dictValue: i.dictValue,
  dictValueName: i.dictValueName,
  dictDesc: i.dictDesc,
  child: i.child,
});
const transOption = i => {
  if (i.child && i.child.length > 0) {
    i.child = i.child.map(j => generateOption(j));
  }
  return generateOption(i);
};

export const getBizDictsPromise = () => {
  return dispatch => {
    return NewProductService.ProductConfigService.bizDict({
      dictKeys: bizDictKeys,
    }).then(res => {
      const { success } = res;
      if (success) {
        const value = res.value.bizDictListResponseDTO;
        const result = {};
        for (let index = 0; index < value.length; index++) {
          const element = value[index];
          if (element.length > 0) {
            const array = element.map(transOption);
            const key = element[0].dictKey;
            result[key] = array;
          }
        }
        dispatch(saveBizDicts(result));
      }
    });
  };
};

export const getTenantDictsPromise = () => {
  return dispatch => {
    return MetadataService.queryBizDict(tenantDictKeys).then(data => {
      if (Array.isArray(data)) {
        const value = data;
        const result = {};
        for (let index = 0; index < value.length; index++) {
          const element = value[index];
          if (!result[element.dictKey]) result[element.dictKey] = [];
          // 延续旧枚举的结构，加入itemExtend1，itemName, itemCode
          const item = {
            ...element,
            originItemExtend1: element.itemExtend1,
            itemExtend1: element.dictValue || element.enumItemName,
            itemName: element.dictValueName || element.dictDesc,
            itemCode: element.dictValue,
          };
          // customerServiceItem 原来枚举取值对应 dictValue
          // 产品中心agreement中用到的枚举，dictValue作为key
          // itemExtend1为组件中默认的key的字段名
          if (element.dictKey === 'premiumFrequencyType') {
            item.itemExtend1 = item.dictValue;
          }
          if (element.dictKey === 'effectiveDateRule') {
            item.itemExtend1 = item.dictValue;
          }
          if (element.dictKey === 'language') {
            item.itemExtend1 = item.originItemExtend1;
          }
          // productCategory 原来枚举itemExtend1 值为数字类型
          if (element.dictKey === 'productCategory' || element.dictKey === 'goodsCategory') {
            item.itemExtend1 = parseInt(item.originItemExtend1);
          }
          result[element.dictKey].push(item);
        }
        // 后端国家国籍枚举统一为 country 方便前端显示处理为 国籍 country.nationalityEnum 国家 country.countryEnum 结构与原来枚举相同
        const nationalityEnum = [];
        const countryEnum = [];
        result.country &&
          result.country.forEach(item => {
            const temp = {
              itemCode: item.dictValue,
              itemExtend1: item.enumItemName,
              itemExtend4: item.itemExtend2,
              itemExtend5: item.itemExtend1,
            };
            const nationalityObj = { ...temp };
            nationalityObj.itemName = item.itemExtend3Desc;
            const countryObj = { ...temp };
            countryObj.itemName = item.dictValueName;
            // itemExtend3Desc 为国籍名，不存在时，不存储
            item.itemExtend3Desc && nationalityEnum.push(nationalityObj);
            countryEnum.push(countryObj);
          });
        result['country.nationalityEnum'] = nationalityEnum;
        result['country.countryEnum'] = countryEnum;

        const baseCurrency = result.baseCurrency && result.baseCurrency[0] ? { ...result.baseCurrency[0] } : {};
        delete result.baseCurrency;

        /*
         * nameFormat 统一处理规则
         * 当只有Full Name字段时，Full Name可编辑
         * 当First Name，Middle Name，Last Name存在时，Full Name不可编辑，只改为由First Name，Middle Name，Last Name字段拼接
         * 这里统一处理好Full Name是否可以编辑的状态
         */
        let fullNameSub = 0;
        let fullName2Sub = 0;
        let fullName3Sub = 0;
        let localFullNameSub = 0;
        result.nameFormat.forEach(item => {
          if (!item.dictValue) return;
          if (!item.dictValue.toLowerCase().includes('fullname')) {
            (item.dictValue === 'firstName' || item.dictValue === 'middleName' || item.dictValue === 'lastName') &&
              (fullNameSub += 1);
            item.dictValue.includes('2') && (fullName2Sub += 1);
            item.dictValue.includes('3') && (fullName3Sub += 1);
            item.dictValue.includes('local') && (localFullNameSub += 1);
          }
        });
        result.nameFormat.forEach(item => {
          if (item.dictValue === 'fullName') item.editable = fullNameSub === 0;
          if (item.dictValue === 'fullName2') {
            item.editable = fullName2Sub === 0;
          }
          if (item.dictValue === 'fullName3') {
            item.editable = fullName3Sub === 0;
          }
          if (item.dictValue === 'localFullName') {
            item.editable = localFullNameSub === 0;
          }
        });
        dispatch(saveBizDicts(result));

        // 多租户日期格式
        // 将后端格式转换为前端格式
        const format =
          result.dateFormat && result.dateFormat[0]
            ? result.dateFormat[0].dictDesc
                .replace('yyyy', 'YYYY')
                .replace('dd', 'DD')
                .replace(':MM', ':mm')
                .replace(':SS', ':ss')
            : 'YYYY-MM-DD HH:mm:ss';
        // 多租户日期格式配置
        const dateString = (_date, _timezone, _format) => {
          let temp;
          try {
            if (_timezone) {
              const initTime = moment(_date).format();
              const zone = moment.tz(_timezone).format('Z');
              const time = initTime.slice(0, -6) + zone;
              temp = moment.tz(time, _timezone).format(_format);
            } else {
              temp = moment(_date).format(_format);
            }
          } catch (error) {
            temp = _date;
          }
          return temp;
        };
        const globalConfig = {
          dateFormat: format.split(' ')[0],
          dateTimeFormat: format,
          dateTimeFormatNoSecond: format.slice(0, format.length - 3),
          timeFormat: format.split(' ')[1],
          timeFormatNoSecond: format.split(' ')[1].slice(0, format.split(' ')[1].length - 3),
          getDateString: (date, timezone) => dateString(date, timezone, format.split(' ')[0]),
          getDateTimeString: (date, timezone) => dateString(date, timezone, format),
          getDateTimeStringNoSecond: (date, timezone) => dateString(date, timezone, format.slice(0, format.length - 3)),
        };

        // baseCurrency
        globalConfig.baseCurrency = baseCurrency;
        globalConfig.currency = baseCurrency.itemExtend1;

        // updateGlobalConfig
        dispatch(updateGlobalConfig(globalConfig));
      }
    });
  };
};

export const saveTenantTimeFormatByZeus = tenantCode => {
  return dispatch => {
    return SystemService.getZeusTenantConfigs(tenantCode).then(res => {
      if (res.additionalConfigurations) {
        dispatch(getTenantTimeFormatByZeus(res.additionalConfigurations));
      }
    });
  };
};
