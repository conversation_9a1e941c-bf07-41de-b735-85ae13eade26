/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 *
 */
import { ModulePermission } from '@market/model/common';

export interface UserState {
  loginName: string;
  userId: number;
  userRealName: string;
  countyCode: string;
  mobile: string;
  tenant: string;
  region: string;
  email: string;
  offsetTime: number;
  modulePermissions: ModulePermission[];
}

export interface GlobalConfig {
  dateFormat: string;
  dateTimeFormat: string;
  dateTimeFormatNoSecond: string;
  timeFormat: string;
  timeFormatNoSecond: string;
  currency: string;
  timeZone: string;
}
