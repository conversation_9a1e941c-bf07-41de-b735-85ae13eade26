/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { createSelector } from '@reduxjs/toolkit';

import { moduleId } from '@market/util';

/**
 * @return {UserState}
 */
export const selectUserState = state => state.userInfo;
export const selectGlobalConfig = state => state.globalConfig;
export const selectLocale = state => state.locale;
export const selectClientConfig = state => state.clientConfig;
export const selectEnvConfig = state => state.envConfig;
export const selectTimeZone = state => state.timeZone;
export const selectTenantTimeFormatByZeus = state => state.tenantTimeFormatByZeus;

export const selectShowHeader = createSelector(selectClientConfig, clientConfig => clientConfig.head);
export const selectShowNav = createSelector(selectClientConfig, clientConfig => clientConfig.nav);
export const selectEnableSecurity = createSelector(selectClientConfig, clientConfig => clientConfig.security);

export const selectUsername = createSelector(selectUserState, userState => userState.userRealName);
export const selectUserId = createSelector(selectUserState, userState => userState.userId);
export const selectTenant = createSelector(selectUserState, userState => userState.tenant);
export const selectUserEmail = createSelector(selectUserState, userState => userState.email);
export const selectModulePermissions = createSelector(selectUserState, userState => userState.modulePermissions);
export const selectDateTimeFormat = createSelector(selectGlobalConfig, globalState => globalState.dateTimeFormat);
export const selectDateFormat = createSelector(selectGlobalConfig, globalState => globalState.dateFormat);

/**
 * Select all available module IDs from user module permission data,
 * concat as {main module ID}.{sub module ID} and flat them as a string array
 */
export const selectUserModules = createSelector(selectModulePermissions, permissions => {
  return permissions
    .map(main => [main.module, ...main.subModules.map(sub => moduleId(sub.module, main.module))])
    .reduce((out, curr) => out.concat(curr), []);
});

export const selectUserModuleCount = createSelector(selectUserModules, modules => modules.length);

/**
 * Collect all permissions from user module permission data
 */
export const selectUserPermissions = createSelector(selectModulePermissions, permissions =>
  permissions
    .map(main =>
      (main.subModules || [])
        .map(sub => sub.permissions)
        .reduce((outPerms, currPerms) => outPerms.concat(currPerms), main.permissions || [])
    )
    .reduce((out, subPerms) => out.concat(subPerms), [])
);

export const selectGrantedPermissionMap = createSelector(selectUserPermissions, permissions =>
  permissions.reduce((out, curr) => ({ ...out, [curr.id]: curr }), {})
);

export const selectPermissionCheckMap = createSelector(
  [selectGrantedPermissionMap, selectEnableSecurity],
  (permissionMap, security) => {
    const checkMap = Object.keys(permissionMap).reduce((out, key) => ({ ...out, [key]: true }), {});

    if (!security) {
      return new Proxy(checkMap, {
        get: () => true,
      });
    }

    return checkMap;
  }
);

export const selectEnums = state => state.enums;

export const selectLegacyLocale = createSelector(selectLocale, localeState => localeState.lang || 'en-US');
export const selectSystemLocale = createSelector(selectLocale, localeState => localeState.lang);

const getPermissionSelectorFactory = permission => createSelector(selectGrantedPermissionMap, map => map[permission]);

export const selectQuickSearchRankingPermission = getPermissionSelectorFactory('quick.search');
