import { redux } from 'genesis-web-shared';
import { getZoneInfoPromise } from 'genesis-web-shared/lib/redux';

import { getEnvConfig, saveLocalConfig, saveTenantInfo, updateCurrentMenu, updateDomain, updatePath } from './action';
import {
  selectLegacyLocale,
  selectPermissionCheckMap,
  selectQuickSearchRankingPermission,
  selectShowHeader,
  selectShowNav,
  selectTenant,
  selectUserModuleCount,
  selectUsername,
} from './selector';

/*
 * Redux
 */
// 哪些 Redux 全局的 state 是我们组件想要通过 props 获取的？
export function mapStateToProps(state) {
  return {
    state,
    enums: { ...state.enums },
    locale: selectLegacyLocale(state),
    defaultTimeZone: Object.values(state.timeZone.defaultZoneInfo || {})[0],
    defaultTimeZoneKey: Object.keys(state.timeZone.defaultZoneInfo || {})[0],
    defaultTimeStamp: state.timeZone.defaultTimeStamp,
    dateTimeFormat: state.globalConfig.dateTimeFormat,
    /**
     * @deprecated
     */
    userInfo: state.userInfo,
    username: selectUsername(state),
    tenant: selectTenant(state),
    permissions: selectPermissionCheckMap(state),
    menuCount: selectUserModuleCount(state),
    showNav: selectShowNav(state),
    showHeader: selectShowHeader(state),
    quickSearchPermission: selectQuickSearchRankingPermission(state),
    clientConfig: state.clientConfig,
    imageVersion: redux.selectImageVersion(state),
  };
}

// 获取 action 创建函数
export function mapDispatchToProps(dispatch) {
  return {
    updateCurrentPath: async data => {
      dispatch(updatePath(data));
    },
    // updateLanguage: async currentLang => {
    //   const data = await request.getlLang(currentLang);
    //   dispatch(updateLang(data));
    // },
    updateDomain: async domain => {
      dispatch(updateDomain(domain));
    },
    getTimeZoneFun: async (tenantLocalConfigEnable = false, multiTimeZoneEnable = true, defaultZoneInfo = {}) => {
      dispatch(getZoneInfoPromise(tenantLocalConfigEnable, multiTimeZoneEnable, defaultZoneInfo));
    },
    getEnvConfig: async res => {
      dispatch(getEnvConfig(res));
    },
    updateCurrentMenu: async data => {
      dispatch(updateCurrentMenu(data));
    },
    saveLocalConfig: async data => {
      dispatch(saveLocalConfig(data));
    },
    saveTenantInfo: async data => {
      dispatch(saveTenantInfo(data || {}));
    },
  };
}
