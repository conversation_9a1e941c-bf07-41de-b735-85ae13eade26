import { CSSProperties, PropsWithChildren, ReactNode } from 'react';

import cls from 'classnames';

import { SimpleSectionHeader } from '@zhongan/nagrand-ui';

interface IProps {
  title: string | ReactNode;
  className?: string;
  style?: CSSProperties;
  id?: string;
  titleClassName?: string;
}

export const CardWithTitle = ({ children, title, className, style, titleClassName, id }: PropsWithChildren<IProps>) => (
  <div className={cls('py-6 px-4 bg-white rounded-lg mb-[10px] mx-2', className)} style={style} id={id}>
    <SimpleSectionHeader className={cls('mb-6 !text-[20px] !leading-[28px]', titleClassName)}>
      {title}
    </SimpleSectionHeader>
    {children}
  </div>
);
