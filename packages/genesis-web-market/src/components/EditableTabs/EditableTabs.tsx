import { useCallback, useState } from 'react';

import { Tabs } from 'antd';
import { TabPaneProps } from 'antd/lib/tabs';

const { TabPane } = Tabs;

interface Props {
  tabTitlePrefix: string;
  tabPaneRenderProps: (key: number, index: number) => JSX.Element;
  hideAdd?: boolean;
}

export const EditableTabs = ({ tabTitlePrefix, tabPaneRenderProps, hideAdd = false }: Props) => {
  const [panes, setPanes] = useState<TabPaneProps[]>([
    {
      tab: `${tabTitlePrefix} 1`,
      key: '0',
    },
  ]);
  const [newTabIndex, setNewTabIndex] = useState(1);
  const [activeKey, setActiveKey] = useState(panes[0].key);

  const add = useCallback(() => {
    const _activeKey = `${newTabIndex}`;
    panes.push({
      tab: `${tabTitlePrefix} ${newTabIndex + 1}`,
      key: _activeKey,
    });
    setNewTabIndex(newTabIndex + 1);
    setPanes([...panes]);
  }, [panes, newTabIndex]);

  const remove = useCallback(
    (targetKey: string) => {
      let lastIndex: number | undefined;
      let newActiveKey = activeKey;
      panes.forEach((pane, i) => {
        if (pane.key === targetKey) {
          lastIndex = i - 1;
        }
      });
      const newPanes = panes.filter(pane => pane.key !== targetKey);
      if (newPanes.length && activeKey === targetKey) {
        if (lastIndex && lastIndex >= 0) {
          newActiveKey = newPanes[lastIndex].key;
        } else {
          newActiveKey = newPanes[0].key;
        }
      }
      setPanes(newPanes);
      setActiveKey(newActiveKey);
    },
    [panes, activeKey]
  );

  const onChange = useCallback(_activeKey => {
    setActiveKey(_activeKey);
  }, []);

  const onEdit = useCallback(
    (targetKey, action: any) => {
      if (action === 'add') {
        add();
      } else if (action === 'remove') {
        remove(targetKey);
      }
    },
    [add]
  );

  return (
    <div>
      <Tabs
        type="editable-card"
        onEdit={onEdit}
        activeKey={activeKey}
        onChange={onChange}
        // 去掉hideAdd需要放开scss中 flex-flow: row-reverse; 的注释
        hideAdd={hideAdd}
      >
        {panes.map((pane, index) => (
          <TabPane tab={pane.tab} key={pane.key} closable={panes.length > 1}>
            {tabPaneRenderProps(+pane.key!, index)}
          </TabPane>
        ))}
      </Tabs>
    </div>
  );
};

export default EditableTabs;
