.config-section {
  min-width: 40%;
  max-width: 50%;

  &.col-1 {
    min-width: 100%;
    max-width: 100%;
    width: 100%;
  }
  &.col-2 {
    min-width: 50%;
    max-width: 50%;
    width: 50%;
  }
  .title {
    height: 52px;
    padding-left: 16px;
    font-weight: bold;
    line-height: 52px;
    background: var(--border-light);
  }
  .config-section-bottom {
    height: 400px;
    overflow: auto;
    border: 1px solid var(--border-light);

    .config-section-list-item {
      display: flex;
      align-items: center;
      height: 40px;
      padding: 12px 16px;
      line-height: 16px;

      .name {
        display: inline-block;
        width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .right-icon {
        margin-left: auto;
      }
    }

    .config-section-list-item.active {
      background-color: var(--info-color-bg);
    }
  }
}
