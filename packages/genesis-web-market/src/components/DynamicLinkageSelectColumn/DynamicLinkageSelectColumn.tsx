import { useCallback, useMemo, useState } from 'react';

import { Checkbox, Tooltip } from 'antd';

import classNames from 'classnames/bind';
import { difference } from 'lodash-es';

import { Icon } from '@zhongan/nagrand-ui';

import { LinkedNode } from '@market/shared/dataStructure/DoublyLinkedList';

import styles from './DynamicLinkageSelectColumn.module.scss';

const cx = classNames.bind(styles);

export interface ColumnOption {
  key: string; // 唯一标识
  code: string;
  name: string; // 显示文案
  columnKey: string; // 每一列option的唯一表示
  childList: ColumnOption[];
  extraInfo?: string; // 扩展字段，纯展示信息
}

export interface ColumnInfo {
  dictKey: string;
  key: string;
  selectedEnum?: ColumnOption;
  enumsMap: Record<string, ColumnOption>;
}

interface Props {
  hideCheckbox?: boolean;
  hideUnSelected?: boolean;
  columnInfo: LinkedNode<ColumnInfo>;
  options: ColumnOption[];
  title: string;
  countOfColumns: number;
  selectedKeys: string[];
  firstLevel: boolean;
  extraConfig?: {
    key: string;
    title: string;
  };
  onSelect: (key: string) => void;
  onUnSelect: (key: string) => void;
  onSelectAll: (checked: boolean, changedkeys: string[]) => void;
  setRenderKey: (value: number) => void; // 用来触发页面更新
}

export const DynamicLinkageSelectColumn = ({
  hideCheckbox,
  hideUnSelected,
  columnInfo,
  title,
  countOfColumns,
  options,
  selectedKeys,
  firstLevel,
  extraConfig,
  onSelect,
  onUnSelect,
  onSelectAll,
  setRenderKey,
}: Props): JSX.Element => {
  const [selectedKey, setSelectedKey] = useState<string>();

  const parentSelected = columnInfo?.previous?.element?.selectedEnum;
  const readOnly = !parentSelected && !firstLevel;

  const optionsDisplay = useMemo(() => {
    if (hideUnSelected) {
      return options.filter(option => selectedKeys.includes(option.key));
    }
    return options;
  }, [hideUnSelected, options, selectedKeys]);

  // 如果父级切换选中的item，但是子级还停留在上一个item的选中状态，需要更新修复一下数据状态
  if (
    selectedKey &&
    !firstLevel &&
    !columnInfo?.previous?.element?.selectedEnum?.childList?.find(item => item.key === selectedKey)
  ) {
    columnInfo.element.selectedEnum = undefined;
    setSelectedKey(undefined);
    setRenderKey(Math.random());
  }

  const onRowSelectInner = useCallback(
    (item: ColumnOption) => {
      // 已经选中的话就不需要执行回调函数了
      if (columnInfo.element.selectedEnum !== item) {
        columnInfo.element.selectedEnum = item;
      }
      setSelectedKey(item.key);
      setRenderKey(Math.random());
    },
    [columnInfo, setRenderKey]
  );

  const onChange = useCallback(
    (checked: boolean, key: string) => {
      if (checked) {
        onSelect(key);
      } else {
        onUnSelect(key);
      }
    },
    [onSelect, onUnSelect]
  );

  const toogleSelectAll = useCallback(() => {
    const totalOptionsCodes = optionsDisplay.map(option => option.key);
    if (firstLevel) {
      if (selectedKeys?.length === optionsDisplay.length) {
        onSelectAll(false, totalOptionsCodes);
      } else {
        onSelectAll(true, totalOptionsCodes);
      }
    } else {
      const diffKeys = difference(totalOptionsCodes, selectedKeys);
      if (diffKeys.length === 0) {
        // 全部取消
        onSelectAll(false, totalOptionsCodes);
      } else {
        // 全部选中
        onSelectAll(true, totalOptionsCodes);
      }
    }
  }, [firstLevel, onSelectAll, optionsDisplay, selectedKeys]);

  return (
    <div className={cx('config-section', `col-${countOfColumns}`)}>
      <div className={cx('title')}>
        {hideCheckbox ? null : (
          <Checkbox disabled={readOnly} onClick={toogleSelectAll} indeterminate style={{ marginRight: 8 }} />
        )}
        {title}
      </div>
      <div className={cx('config-section-bottom')}>
        {optionsDisplay.map((appEnum, secondLevelIndex) => {
          const focused = selectedKey === appEnum.key;
          const key = appEnum.key;

          return (
            <div
              onClick={() => {
                onRowSelectInner(appEnum);
              }}
              key={key}
              className={`${cx('config-section-list-item')} ${focused ? cx('active') : ''}`}
            >
              {hideCheckbox ? null : (
                <Checkbox
                  disabled={readOnly}
                  onChange={event => onChange(event.target.checked, key)}
                  checked={selectedKeys?.includes(key)}
                  style={{ marginRight: 16 }}
                />
              )}
              <span className={cx('name')}>
                <Tooltip title={appEnum.name}>{appEnum.name}</Tooltip>
              </span>
              <span style={{ marginLeft: 6 }} className={cx('name')}>
                <Tooltip title={appEnum.code}>{appEnum.code}</Tooltip>
              </span>
              {extraConfig ? (
                <span style={{ marginLeft: 6 }} className={cx('name')}>
                  <Tooltip title={appEnum.extraInfo}>{appEnum.extraInfo}</Tooltip>
                </span>
              ) : null}
              <Icon type="right" className={cx('right-icon')} />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default DynamicLinkageSelectColumn;
