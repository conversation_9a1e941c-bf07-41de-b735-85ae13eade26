import React, { createRef } from 'react';

import { ArrowDownOutlined, ArrowUpOutlined, VerticalAlignTopOutlined } from '@ant-design/icons';
import { Button, DatePicker, Form, Input, InputNumber, Popconfirm, Switch } from 'antd';

import { array, bool, func, node, object, oneOfType } from 'prop-types';

import { DeleteAction, EditAction, Icon, Table } from '@zhongan/nagrand-ui';

import GeneralSelect from '@market/components/GeneralSelect';

import I18nInstance from '../../i18n.ts';
import './index.scss';

class EditableCell extends React.Component {
  getInput = () => {
    const { selectOptions = [], inputType, controllerprops = {} } = this.props;
    if (inputType === 'number' || inputType === 'numberRange') {
      return <InputNumber {...controllerprops} />;
    }
    if (inputType === 'date') {
      return <DatePicker format="YYYY-MM-DD" {...controllerprops} />;
    }
    if (inputType === 'select') {
      return (
        <GeneralSelect
          {...controllerprops}
          option={
            Array.isArray(selectOptions)
              ? selectOptions.map(i => ({
                  value: i.itemExtend1,
                  label: i.itemName,
                  disabled: i.disabled,
                }))
              : []
          }
        />
      );
    }
    if (inputType === 'switch') {
      return <Switch defaultChecked={controllerprops.status} />;
    }
    if (inputType === 'txt') {
      return <span>{controllerprops.value}</span>;
    }
    return <Input maxLength={64} {...controllerprops} />;
  };

  getNumberRange = (idx, record) => {
    const { dataIndex, initialValue = [], normalize } = this.props;
    return (
      <Form.Item
        style={{ margin: '0 2px', display: 'inline-block', width: '104px' }}
        name={`${dataIndex}_${idx}`}
        initialValue={
          record.key === 'add' ? initialValue[idx] : record[dataIndex] ? record[dataIndex][idx] : initialValue[idx]
        }
        normalize={normalize}
      >
        {this.getInput()}
      </Form.Item>
    );
  };

  getInputRange = record => {
    return (
      <React.Fragment>
        {this.getNumberRange(0, record)}
        <span style={{ fontSize: 14 }}>-</span>
        {this.getNumberRange(1, record)}
      </React.Fragment>
    );
  };

  render() {
    const {
      editing,
      dataIndex,
      title,
      inputType,
      record,
      index,
      children,
      validate,
      initialValue,
      t,
      normalize,
      form,
      selectKey,
      selectOption,
      selectAttr,
      selectList,
      inputKey,
      countryCodeProps,
      inputOptions,
      numberOverlap,
      ...restProps
    } = this.props;

    const restPropsCopy = { ...restProps };
    // 去掉react报错的属性
    delete restPropsCopy.selectOptions;
    if (dataIndex === 'action-operation') {
      return (
        <td {...restPropsCopy} className={editing ? ` ${restPropsCopy.className}` : restPropsCopy.className}>
          {children}
        </td>
      );
    }

    let validateOption;
    if (validate) {
      validateOption = validate;
    } else if (inputType === 'txt') {
      validateOption = [];
    } else if (inputType === 'numberRange') {
      const validateRule = {
        validator: (rule, value, callback) => {
          if (!form.getFieldValue(`${dataIndex}_0`) || !form.getFieldValue(`${dataIndex}_1`)) {
            callback(I18nInstance.t('Please input'));
          } else if (form.getFieldValue(`${dataIndex}_0`) >= form.getFieldValue(`${dataIndex}_1`)) {
            callback(numberOverlap);
          } else {
            callback();
          }
        },
      };
      validateOption = [
        {
          trigger: 'onBlur',
          rules: [
            {
              ...validateRule,
            },
          ],
        },
      ];
    } else {
      let errorMsg = I18nInstance.t('Please input');
      if (inputType === 'select' || inputType === 'date') {
        errorMsg = I18nInstance.t('Please select');
      }
      validateOption = [
        {
          trigger: 'onChange',
          rules: [
            {
              required: restPropsCopy.controllerprops?.required ?? true,
              message: errorMsg,
            },
          ],
        },
      ];
    }

    return (
      <td {...restPropsCopy} className={editing ? 'edit-td-highLight' : undefined}>
        {editing ? (
          <Form.Item
            style={{ margin: 0 }}
            name={dataIndex}
            validate={inputType === 'select' ? [] : validateOption}
            initialValue={(`${record[dataIndex]}` === '- -' ? '' : record[dataIndex]) || initialValue}
            normalize={normalize}
          >
            {this.getInput()}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  }
}
class EditableFormTable extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      editingKey: '',
      newData: undefined,
      sortMode: false,
      current: 1, // 表格内部维护一下页码，在有分页情况下控制表格的页数
    };
    this.table_state_editing = 'Editing';
    this.table_state_normal = 'Normal';
    this.attachOperationCol();
    this.formRef = createRef();
  }

  attachOperationCol = () => {
    const { disabledRecordKey = 'disabled' } = this.props;
    this.operationCol = {
      title: I18nInstance.t('Actions'),
      dataIndex: 'action-operation',
      width: 96,
      fixed: 'right',
      align: 'center',
      editable: true,
      render: (text, record, index) => {
        const { editingKey, sortMode } = this.state;
        const {
          onEditRow,
          newAndEditControl,
          newAndEditFunction,
          hideEdit,
          hideDelete,
          renderOperationCol,
          deleteIconProps,
        } = this.props;
        let editable = this.isEditing(record);
        const disabled =
          (editingKey !== '' && editingKey !== record.key) || this.props.disabled || record[disabledRecordKey];
        const extraIcons = renderOperationCol ? renderOperationCol(record, disabled) : null;
        if (disabled) {
          return (
            <span disabled={disabled}>
              {!hideEdit && <EditAction disabled={disabled} />}
              {!hideDelete && <DeleteAction disabled={disabled} />}
              {extraIcons}
            </span>
          );
        }

        if (onEditRow && typeof onEditRow === 'function') {
          /* 行内编辑动作交给父组件时候，新增行，不显示正在编辑状态 */
          editable = false;
        }
        if (sortMode) {
          return (
            <span>
              <VerticalAlignTopOutlined
                disabled={index === 0}
                onClick={() => {
                  if (index !== 0 && typeof this.props.onSortTop === 'function') {
                    const array_1 = [...this.props.data];
                    // 待置顶的元素
                    const temp = array_1[index];
                    temp.index = 1;
                    for (let i = index; i > 0; i--) {
                      array_1[i] = array_1[i - 1];
                      array_1[i].index = i + 1;
                    }
                    array_1[0] = temp;
                    this.props.onSortTop(record, index, array_1);
                  }
                }}
              />
              <ArrowUpOutlined
                disabled={index === 0}
                onClick={() => {
                  if (index !== 0 && typeof this.props.onSortUp === 'function') {
                    const array_1 = [...this.props.data];
                    const temp = array_1[index];
                    // 数组从0开始，表格从1开始
                    temp.index = index - 1 + 1;
                    array_1[index - 1].index = index + 1;
                    array_1[index] = array_1[index - 1];
                    array_1[index - 1] = temp;
                    this.props.onSortUp(record, index, array_1);
                  }
                }}
                style={{ marginLeft: 16 }}
              />
              <ArrowDownOutlined
                disabled={index === this.props.data.length - 1}
                onClick={() => {
                  if (index !== this.props.data.length - 1 && typeof this.props.onSortDown === 'function') {
                    const array_1 = [...this.props.data];
                    const temp = this.props.data[index];
                    temp.index = index + 1 + 1;
                    array_1[index + 1].index = index + 1;
                    array_1[index] = array_1[index + 1];
                    array_1[index + 1] = temp;
                    this.props.onSortDown(record, index, array_1);
                  }
                }}
                style={{ marginLeft: 16 }}
              />
            </span>
          );
        }
        if (editable) {
          // 编辑状态显示打勾和打叉Icon
          return (
            <span>
              <Icon
                type="check-icon"
                className="text-xl cursor-pointer"
                onClick={() => this.save(this.formRef.current, record.key, index)}
              />
              <Icon className="cursor-pointer ml-4" type="close" onClick={() => this.cancel(record.key, index)} />
            </span>
          );
        }
        return (
          <span>
            {!hideEdit &&
              (this.props.actionDisabled({ actionName: 'edit', ...record }) ? (
                <EditAction disabled />
              ) : (
                <EditAction onClick={newAndEditControl ? () => newAndEditFunction(record) : () => this.edit(record)} />
              ))}
            {!hideDelete &&
              (this.props.actionDisabled({
                actionName: 'delete',
                ...record,
              }) ? (
                <DeleteAction disabled />
              ) : (
                <Popconfirm
                  title={I18nInstance.t('Are you sure to delete this record?')}
                  onConfirm={() => {
                    this.delete(record.key, index, record);
                  }}
                  placement="topLeft"
                  okText={I18nInstance.t('yes')}
                  cancelText={I18nInstance.t('no')}
                  {...deleteIconProps}
                >
                  <DeleteAction />
                </Popconfirm>
              ))}
            {extraIcons}
          </span>
        );
      },
    };
  };

  componentDidMount = () => {
    const { defaultAdd, onStateChange } = this.props;
    // 默认新增一行进入编辑状态
    defaultAdd && this.add();
    if (typeof onStateChange === 'function') {
      onStateChange(this.table_state_normal);
    }
    if (typeof this.props.myRef === 'function') {
      this.props.myRef(this);
    }
  };

  componentDidUpdate(prevProps) {
    const { defaultAdd } = this.props;
    const { editingKey } = this.state;
    // props改变时更新Function列
    this.attachOperationCol();

    // 默认新增一行进入编辑状态
    if (defaultAdd && prevProps.defaultAdd !== defaultAdd) {
      !editingKey && this.add();
    }
  }

  isEditing = record => record.key === this.state.editingKey;

  cancel = (key, index) => {
    const { onStateChange } = this.props;
    this.setState({
      editingKey: '',
      newData: undefined,
    });
    if (typeof onStateChange === 'function') {
      onStateChange(this.table_state_normal);
    }
    if (typeof this.props.onCancel === 'function') {
      this.props.onCancel(key, index);
    }
  };

  save(form, key, index) {
    const { onStateChange } = this.props;
    form.validateFields().then(row => {
      if (typeof this.props.onSubmit === 'function') {
        this.props.onSubmit(row, key, index).then(() => {
          this.setState({ editingKey: '', newData: undefined });
          if (typeof onStateChange === 'function') {
            onStateChange(this.table_state_normal);
          }
        });
      }
    });
  }

  delete(key, index, record) {
    if (typeof this.props.onDelete === 'function') {
      this.props.onDelete(key, index, record);
    }
  }

  edit(record) {
    const { onEditRow, onEditBefore, onStateChange } = this.props;
    if (typeof onStateChange === 'function') {
      onStateChange(this.table_state_editing);
    }
    if (onEditBefore && typeof onEditBefore === 'function') {
      // 点击编辑按钮暴露出record给父组件
      // 点击编辑按钮暴露出form给父组件 编辑时下拉联动清除数据使用
      onEditBefore(record, this.formRef.current);
    }
    if (onEditRow && typeof onEditRow === 'function') {
      /* 父组件接手编辑行的动作 */
      return onEditRow(record);
    }
    if (this.state.editingKey) return;
    this.formRef.current.resetFields();
    this.setState({ editingKey: record.key });
  }

  add = () => {
    const { onAddNew, data, onAddBefore, onStateChange, newAddTop } = this.props;
    if (typeof onStateChange === 'function') {
      onStateChange(this.table_state_editing);
    }
    if (onAddBefore && typeof onAddBefore === 'function') {
      // 点击新增按钮暴露出form给父组件 编辑时下拉联动清除数据使用
      onAddBefore(this.formRef.current);
    }
    if (onAddNew && typeof onAddNew === 'function') {
      /* 父组件接手新增行的动作 */
      return onAddNew();
    }
    const newData = {};
    this.props.columns.forEach(i => {
      // 图片类型不需要 - - 占位
      if (i.editable || i.inputType === 'image') {
        newData[i.dataIndex] = undefined;
      } else {
        newData[i.dataIndex] = '- -';
      }
    });
    this.formRef.current.resetFields();
    newData.key = 'add';
    newData.index = data.length + 1;
    this.setState({
      newData,
      editingKey: 'add',
      current: newAddTop ? 1 : Math.ceil(data.length / 10), // 加在第一行跳到第一页，否则跳到最后一页
    });
  };

  sort = () => {
    const { onStateChange } = this.props;
    this.setState(
      {
        sortMode: true,
      },
      () => {
        if (typeof this.props.onStartSort === 'function') {
          this.props.onStartSort();
        }
        if (typeof onStateChange === 'function') {
          onStateChange(this.table_state_editing);
        }
      }
    );
  };

  saveSort = () => {
    const { onStateChange } = this.props;
    this.setState(
      {
        sortMode: false,
      },
      () => {
        if (typeof this.props.onSaveSort === 'function') {
          this.props.onSaveSort();
        }
        if (typeof onStateChange === 'function') {
          onStateChange(this.table_state_normal);
        }
      }
    );
  };

  onTableChange = (pagination, filters, sorter) => {
    const { onChange } = this.props;
    this.setState({
      current: pagination.current,
    });
    if (typeof onChange === 'function') {
      onChange(pagination, filters, sorter);
    }
  };

  render() {
    const { sortMode, newData, editingKey, current } = this.state;
    const {
      disableAdd,
      t,
      sortable,
      disabled,
      newAndEditControl,
      newAndEditFunction,
      newAddTop,
      loading,
      pagination,
      rowKey,
      rightBtn,
    } = this.props;
    const combinedColumns = [...this.props.columns, this.operationCol];

    const components = {
      body: {
        cell: EditableCell,
      },
    };

    const columns = combinedColumns.map(col => {
      // 图片类型走EditableCell中的特殊逻辑
      if (!col.editable && col.inputType !== 'image') {
        return col;
      }
      return {
        ...col,
        onCell: record => ({
          record,
          // 只是为了number类型使用，当前单元不是新增或者编辑状态的时候，inputType取record自己带过来的inputType，否则取columns的
          inputType:
            rowKey !== 'add' &&
            !this.isEditing(record) &&
            (col.inputType === 'number' || col.inputType === 'numberRange')
              ? record.inputType || record[`inputType${col.dataIndex}`]
              : col.inputType,
          controllerprops: col.controllerprops,
          dataIndex: col.dataIndex,
          maxLength: col.maxLength,
          selectOptions: col.selectOptions,
          validate: typeof col.validate === 'function' ? col.validate(record, this.formRef.current) : col.validate,
          title: col.title,
          editing: this.isEditing(record),
          normalize: col.normalize, // formitem的值用户自定义格式化
          initialValue: col.initialValue, // 初始值
          t,
          form: this.formRef.current,
          selectKey: col.selectKey,
          selectOption: col.selectOption,
          selectAttr: col.selectAttr,
          selectList: col.selectList,
          inputKey: col.inputKey,
          inputOptions: col.inputOptions,
          numberOverlap: col.numberOverlap,
          countryCodeProps: col.countryCodeProps,
        }),
      };
    });
    return (
      <Form ref={this.formRef}>
        {this.formRef.current ? (
          <div className="base-editable-table">
            {sortable ? (
              <div className="button-group btn-group-above-table">
                <Button
                  onClick={newAndEditControl ? () => newAndEditFunction() : this.add}
                  disabled={newData || sortMode || editingKey || disabled || disableAdd}
                  icon={<Icon type="add" />}
                >
                  {I18nInstance.t('Add')}
                </Button>
                {sortMode ? (
                  <Button
                    className="sort-btn active"
                    onClick={this.saveSort}
                    disabled={newData || editingKey || disabled}
                  >
                    <Icon type="sort" />
                    {I18nInstance.t('Save Sort')}
                  </Button>
                ) : rightBtn ? (
                  <div className="sort-btn">{rightBtn}</div>
                ) : (
                  <Button
                    className="sort-btn"
                    onClick={this.sort}
                    disabled={newData || editingKey || disabled || this.props.data.length < 2}
                  >
                    <Icon type="sort" /> {I18nInstance.t('Sort')}
                  </Button>
                )}
              </div>
            ) : (
              <Button
                onClick={newAndEditControl ? () => newAndEditFunction() : this.add}
                disabled={
                  newData || editingKey || disabled || this.props.actionDisabled({ actionName: 'add' }) || disableAdd
                }
                className="long-add-btn btn-group-above-table"
                style={{
                  lineHeight: '24px',
                }}
                icon={<Icon type="add" />}
              >
                {I18nInstance.t('Add')}
              </Button>
            )}
            <Table
              rowKey={rowKey}
              loading={loading}
              components={components}
              dataSource={
                newData ? (newAddTop ? [newData, ...this.props.data] : [...this.props.data, newData]) : this.props.data
              }
              columns={columns}
              rowClassName="editable-row"
              pagination={pagination ? { current, ...pagination, disabled: !!editingKey } : false}
              onChange={this.onTableChange}
              emptyType="text"
              scroll={this.props.scroll || {}}
            />
          </div>
        ) : null}
      </Form>
    );
  }
}

EditableFormTable.propTypes = {
  newAndEditControl: bool, // 编辑和新增的权限是否放到组件外面
  newAndEditFunction: func, // newAndEditControl为true的时候，需要提供编辑和新增的处理函数
  disabled: bool, // 按钮全部展示为disable
  disableAdd: bool,
  columns: array, // 表格中展示列的配置项（不需要传action那一列）
  data: array, // 表格中的数据
  sortable: bool, // 是否显示排序的功能
  scroll: object, // table 的滚动设置，原封不动传给antd的table
  onSubmit: func, // 在编辑状态下点击打勾icon的回调函数
  onCancel: func, // 在编辑状态下点击打叉icon的回调函数
  onAddNew: func, // 点击add按钮的回调函数，屏幕组件默认行内新增的行为
  onAddAfter: func, // 点击add按钮后的回调函数，将newData传给父组件处理
  onEditRow: func, // 点击edit按钮的回调函数，屏幕组件默认行内编辑的行为
  onDelete: func, // 确认删除的回调函数
  onStartSort: func, // 点击sort按钮的回调函数
  onSaveSort: func, // 点击save sort按钮的回调函数
  onSortTop: func, // 排序状态下点击向上置顶的回调函数
  onSortUp: func, // 排序状态下点击向上的回调函数
  onSortDown: func, // 排序状态下点击向下的回调函数
  newAddTop: bool, // 新增的编辑行在最上还是最下
  onEditBefore: func, // 点击edit按钮，暴露出record给父组件；点击编辑按钮暴露出form给父组件 编辑时联动清除数据使用
  onAddBefore: func, // 点击新增按钮暴露出form给父组件 编辑时联动清除数据使用
  defaultAdd: bool, // defaultAdd为true时，调用新增逻辑，默认新增一行进入编辑状态
  hideEdit: bool, // hideEdit为true时，隐藏表格编辑按钮
  hideDelete: bool, // hideDelete为true时，隐藏表格删除按钮
  loading: bool, // 表格加载状态
  onStateChange: func,
  pagination: oneOfType([object, bool]), // table 的分页器
  onChange: func, // 分页、排序、筛选变化时触发
  rowKey: func,
  renderOperationCol: func, // render 自定义icons
  deleteIconProps: object,
  rightBtn: node,
};
EditableFormTable.defaultProps = {
  newAndEditControl: false,
  newAndEditFunction: () => {},
  disabled: false,
  disableAdd: false,
  columns: [], // 表格中展示列的配置项（不需要传action那一列）
  data: [], // 表格中的数据
  sortable: false, // 是否显示排序的功能
  onSubmit: () => {}, // 在编辑状态下点击打勾icon的回调函数
  onCancel: () => {}, // 在编辑状态下点击打叉icon的回调函数
  onDelete: () => {}, // 确认删除的回调函数
  onStartSort: () => {}, // 点击sort按钮的回调函数
  onSaveSort: () => {}, // 点击save sort按钮的回调函数
  onSortTop: () => {}, // 排序状态下点击向上置顶的回调函数
  onSortUp: () => {}, // 排序状态下点击向上的回调函数
  onSortDown: () => {}, // 排序状态下点击向下的回调函数
  onStateChange: () => {}, // 表格状态变更的回调 'Normal','Editing'
  actionDisabled: () => {
    return false;
  }, // edit和add操作的disabled控制
  onChange: () => {}, // 分页、排序、筛选变化时触发 和 pagination一起使用
  newAddTop: false, // 新增的编辑行在最上还是最下
  loading: false, // 表格加载状态
  pagination: false, // 默认关闭分页
  rowKey: () => {},
  renderOperationCol: () => {}, // render额外icons
  deleteIconProps: {},
  disabledRecordKey: 'disabled',
};

export default EditableFormTable;
