import React from 'react';

import type { FormComponentProps } from 'antd/es/form';
import type { PopconfirmProps } from 'antd/es/popconfirm';
import type { ColumnProps, TableProps } from 'antd/es/table';

export interface EditableColumnProps<T = any> extends ColumnProps<T> {
  editable?: boolean;
  inputType?: 'number' | 'date' | 'select' | 'txt' | 'numberRange' | 'input';
  controllerprops?: any;
  selectOptions?: any;
  validate?: any[];
  numberOverlap?: string; // numberRange,min大于max时的提示语
}

interface EditableFormTableProps<T = any> extends TableProps {
  newAndEditControl?: boolean; // 编辑和新增的权限是否放到组件外面
  newAndEditFunction?: (record?: T) => void; // newAndEditControl为true的时候，需要提供编辑和新增的处理函数
  disableAdd?: boolean;
  disabled?: boolean; // 按钮全部展示为disable
  data?: T[]; // 表格中的数据
  sortable?: boolean; // 是否显示排序的功能
  columns?: EditableColumnProps<T>[];
  onSubmit?: (value: any, key: any, index: any) => void; // 在编辑状态下点击打勾icon的回调函数
  onCancel?: () => void; // 在编辑状态下点击打叉icon的回调函数
  onAddNew?: () => void; // 点击add按钮的回调函数，屏幕组件默认行内新增的行为
  onAddAfter?: () => void; // 点击add按钮后的回调函数，将newData传给父组件处理
  onEditRow?: (record: any) => any; // 点击edit按钮的回调函数，屏幕组件默认行内编辑的行为
  onDelete?: (key: string, index: number, record?: any) => any; // 确认删除的回调函数
  onStartSort?: () => void; // 点击sort按钮的回调函数
  onSaveSort?: (record: any, index: number, array: any[]) => void; // 点击save sort按钮的回调函数
  onSortTop?: (record: any, index: number, array: any[]) => void; // 排序状态下点击向上置顶的回调函数
  onSortUp?: (record: any, index: number, array: any[]) => void; // 排序状态下点击向上的回调函数
  onSortDown?: (record: any, index: number, array: any[]) => void; // 排序状态下点击向下的回调函数
  newAddTop?: boolean; // 新增的编辑行在最上还是最下
  onEditBefore?: (record: any, form: FormComponentProps) => void; // 点击edit按钮，暴露出record给父组件；点击编辑按钮暴露出form给父组件 编辑时联动清除数据使用
  onAddBefore?: (form: FormComponentProps) => void; // 点击新增按钮暴露出form给父组件 编辑时联动清除数据使用
  defaultAdd?: boolean; // defaultAdd为true时，调用新增逻辑，默认新增一行进入编辑状态
  hideEdit?: boolean; // hideEdit为true时，隐藏表格编辑按钮
  hideDelete?: boolean; // hideDelete为true时，隐藏表格删除按钮
  loading?: boolean; // 表格加载状态
  onStateChange?: (state: string) => void;
  renderOperationCol?: (record: any, disabled: boolean) => void; // render 自定义icons
  deleteIconProps?: Partial<PopconfirmProps>;
  customNoDataFlag?: boolean; // 是否使用自定义nodata
  rightBtn?: React.ReactNode;
  rowKey?: string | ((record: any) => string | undefined);
  scroll?: Record<string, any>;
  pagination?: any;
  myRef?: () => any;
  disabledRecordKey?: string; // 当数据上有这个属性的值为true的时候，禁用编辑
}

declare class EditableFormTable extends React.Component<EditableFormTableProps> {}

export default EditableFormTable;
