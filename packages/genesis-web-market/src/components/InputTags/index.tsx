import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { PlusOutlined } from '@ant-design/icons';
import type { InputRef } from 'antd';
import { Flex, Input, Tag } from 'antd';

import clsx from 'classnames';
import { cloneDeep } from 'lodash-es';

interface InputTagsProps {
  value?: string[];
  max?: number;
  disabled?: boolean;
  onChange?: (value: string[]) => void;
}

const InputTags = ({ value = [], max, disabled = false, onChange }: InputTagsProps) => {
  const [t] = useTranslation(['market', 'common']);
  const [inputVisible, setInputVisible] = useState(false);
  const [editInputIndex, setEditInputIndex] = useState(-1);
  const inputRef = useRef<InputRef>(null);
  const editInputRef = useRef<InputRef>(null);

  // Edit state input autofocus
  useEffect(() => {
    if (inputVisible) {
      inputRef.current?.focus();
    }
  }, [inputVisible]);

  useEffect(() => {
    if (editInputIndex >= 0) {
      editInputRef.current?.focus();
    }
  }, [editInputIndex]);

  const removeTag = (removedTag: string) => {
    const newTags = value?.filter(tag => tag !== removedTag);
    onChange?.(newTags);
  };

  const handleInputConfirm = () => {
    const newValue = inputRef?.current?.input?.value;
    if (newValue && !value.includes(newValue)) {
      onChange?.([...value, newValue]);
    }
    setInputVisible(false);
  };

  const handleEditInputConfirm = () => {
    const newTags = cloneDeep(value);
    const newValue = editInputRef?.current?.input?.value;
    if (newValue && !value.includes(newValue)) {
      newTags[editInputIndex] = newValue || '';
      onChange?.(newTags);
    }
    setEditInputIndex(-1);
  };

  return (
    <Flex gap="6px 0" wrap>
      {value.map((tag, index) => {
        if (editInputIndex === index) {
          return (
            <Input
              ref={editInputRef}
              key={tag}
              size="small"
              className="w-24 mr-2"
              defaultValue={value?.[editInputIndex]}
              onBlur={handleEditInputConfirm}
              onPressEnter={handleEditInputConfirm}
            />
          );
        }
        return (
          <Tag key={tag} onClose={() => removeTag(tag)} closable={!disabled}>
            <span
              onDoubleClick={e => {
                if (disabled) return;
                setEditInputIndex(index);
                e.preventDefault();
              }}
            >
              {tag}
            </span>
          </Tag>
        );
      })}
      {(!max || value.length < max) && (
        <>
          {inputVisible ? (
            <Input
              ref={inputRef}
              type="text"
              size="small"
              className="w-24"
              onBlur={handleInputConfirm}
              onPressEnter={handleInputConfirm}
            />
          ) : (
            <Tag
              icon={<PlusOutlined className="text-xs" />}
              onClick={() => {
                if (disabled) return;
                setInputVisible(true);
              }}
              className={clsx('border-dashed', { 'cursor-not-allowed': disabled, 'bg-white': !disabled })}
            >
              {t('New Tag')}
            </Tag>
          )}
        </>
      )}
    </Flex>
  );
};

export default InputTags;
