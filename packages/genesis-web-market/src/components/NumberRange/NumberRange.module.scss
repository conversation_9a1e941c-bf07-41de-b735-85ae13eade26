.number-range-wrapper .number-range-split {
  background-color: var(--white);
}

.number-range-wrapper .number-range-right {
  border-left-width: 0;
}

.number-range-wrapper .number-range-right:hover,
.number-range-wrapper .number-range-right:focus {
  border-left-width: 1px;
}

:global {
  .market-ant4-input-group.market-ant4-input-group-compact > *:not(:last-child) {
    margin-inline-end: 0;
  }

  .has-error {
    .market-ant4-input-disabled {
      border-color: var(--error-color);
    }
  }
}
