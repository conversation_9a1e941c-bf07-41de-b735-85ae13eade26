import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Input, InputNumber } from 'antd';

import classNames from 'classnames/bind';

import styles from './NumberRange.module.scss';

const cx = classNames.bind(styles);

interface Props {
  value?: [number | undefined, number | undefined];
  onChange?: (value: [number | undefined, number | undefined]) => void;
  disabled?: boolean;
  extraProps?: Record<string, any>[];
  className?: string;
  autoChangeMinMax?: boolean;
}

export const NumberRange = ({
  value,
  onChange,
  disabled,
  extraProps = [],
  className,
  autoChangeMinMax = true,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [minValue, maxValue] = value || [undefined, undefined];

  const onMinValueChange = (_minValue: number | undefined) => {
    onChange?.([_minValue, maxValue]);
  };

  const onMaxValueChange = useCallback(
    (_maxValue: number | undefined) => {
      onChange?.([minValue, _maxValue]);
    },
    [minValue, onChange]
  );

  const onBlur = useCallback(() => {
    if (!autoChangeMinMax) {
      return;
    }
    if (minValue && maxValue && +minValue > +maxValue) {
      onChange?.([maxValue, minValue]);
    }
  }, [maxValue, minValue, onChange]);

  return (
    <Input.Group compact className={cx('number-range-wrapper', className)}>
      <InputNumber
        style={{ width: 105, textAlign: 'center' }}
        value={minValue}
        onChange={onMinValueChange}
        disabled={disabled}
        onBlur={onBlur}
        stringMode
        placeholder={t('Min')}
        {...(extraProps?.[0] || {})}
      />
      <Input
        className={cx('number-range-split')}
        style={{
          width: 30,
          borderRight: '0',
          borderLeft: '0',
          pointerEvents: 'none',
        }}
        placeholder="~"
        readOnly
      />
      <InputNumber
        style={{ width: 105, textAlign: 'center' }}
        className={cx('number-range-right')}
        value={maxValue}
        onChange={onMaxValueChange}
        disabled={disabled}
        onBlur={onBlur}
        stringMode
        placeholder={t('Max')}
        {...(extraProps?.[1] || {})}
      />
    </Input.Group>
  );
};

export default NumberRange;
