.anchor-container {
  padding: 24px;
  background-color: var(--white);

  :global {
    .market-ant4-anchor-wrapper {
      margin-top: -8px;
      margin-bottom: -8px;
      margin-left: -4px;
      padding-left: 4px;
      overflow: auto;
      background-color: transparent;
    }

    .market-ant4-anchor-ink {
      height: 95%;
    }

    .market-ant4-anchor-link {
      margin-left: -7px;
      position: relative;
      padding: 8px 0 8px 0 !important;
    }

    .market-ant4-anchor-link-title {
      background: var(--white);
      padding-left: 25px;
      z-index: 2;
      position: relative;
      font-size: 14px;
      line-height: 16px;
      color: var(--text-color-secondary);
    }

    .market-ant4-anchor-link-title:before {
      content: ' ';
      width: 6px;
      height: 6px;
      position: absolute;
      left: 2.5px;
      top: 5px;
      background-color: var(--border-light);
      border-radius: 4px;
    }

    .market-ant4-anchor-link-title-active {
      color: var(--text-color);
      font-weight: 700;
    }

    .market-ant4-anchor-link-title-active::before {
      background-color: var(--primary-color);
      width: 8px;
      height: 8px;
      left: 1.5px;
      top: 4px;
    }

    .market-ant4-anchor-wrapper:not(.market-ant4-anchor-wrapper-horizontal) .market-ant4-anchor::before {
      border-inline-start: 1px solid var(--border-light);
    }
  }
}
