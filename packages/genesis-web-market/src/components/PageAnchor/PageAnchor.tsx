import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Anchor } from 'antd';

import classNames from 'classnames/bind';

import styles from './PageAnchor.module.scss';

const { Link } = Anchor;
const cx = classNames.bind(styles);

interface Props {
  scrollContainerElement?: HTMLDivElement | null;
  links: {
    href: string;
    title: string;
  }[];
  affix?: boolean;
  offsetTop?: number;
}

export const PageAnchor = ({ scrollContainerElement, links = [], affix, offsetTop }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  return (
    <div className={cx('anchor-container')}>
      <Anchor offsetTop={offsetTop} getContainer={() => scrollContainerElement || window} affix={affix}>
        {links.map(link => (
          <Link href={link.href} title={link.title} />
        ))}
      </Anchor>
    </div>
  );
};

export default PageAnchor;
