/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import React, { Component } from 'react';

import { Input } from 'antd';

import { t } from '@market/i18n';

class InputEnhance extends Component {
  state = {};

  render() {
    const props = { ...this.props };
    const placeholder = t('pleaseInput');
    delete props.pleaseInput;
    delete props.dispatch;
    return <Input placeholder={placeholder} maxLength={250} {...props} />;
  }
}

export const InputGroup = Input.Group;

export const TextArea = Input.TextArea;

export default InputEnhance;
