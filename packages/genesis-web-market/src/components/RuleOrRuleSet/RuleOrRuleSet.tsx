import { useTranslation } from 'react-i18next';

import { Form, FormInstance, Space } from 'antd';

import { SelectOption } from '@market/common/interface';

import GeneralSelect from '../GeneralSelect';

interface RuleOrRuleSetProps {
  form: FormInstance;
  disabled: boolean;
  ruleCategoryFieldName: string;
  ruleCodeFieldName: string;
  ruleCategoryInitialValue: string;
  ruleCodeInitialValue: string;
  ruleCategoryOptions: SelectOption[];
  ruleList: SelectOption[];
}

export const RuleOrRuleSet = ({
  form,
  disabled,
  ruleCategoryFieldName,
  ruleCodeFieldName,
  ruleCategoryInitialValue,
  ruleCodeInitialValue,
  ruleCategoryOptions,
  ruleList,
}: RuleOrRuleSetProps) => {
  const [t] = useTranslation(['market', 'common']);

  return (
    <Space.Compact>
      <Form.Item
        label={t('Rule/Rule Set')}
        required
        name={ruleCategoryFieldName}
        initialValue={ruleCategoryInitialValue}
        rules={[
          {
            required: true,
            message: t('Please select'),
          },
        ]}
      >
        <GeneralSelect disabled option={ruleCategoryOptions} />
      </Form.Item>
      <Form.Item
        label=" "
        required={false}
        name={ruleCodeFieldName}
        initialValue={ruleCodeInitialValue}
        rules={[
          {
            required: true,
            message: t('Please select'),
          },
        ]}
      >
        <GeneralSelect style={{ width: 240 }} placeholder={t('Please select')} disabled={disabled} option={ruleList} />
      </Form.Item>
    </Space.Compact>
  );
};
