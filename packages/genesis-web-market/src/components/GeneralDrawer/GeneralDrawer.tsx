import { ReactNode } from 'react';

import { Drawer } from 'antd';
import { DrawerProps } from 'antd/lib/drawer';

import styles from './GeneralDrawer.module.scss';

interface Props {
  drawerProps: DrawerProps;
  content: ReactNode;
  footer: ReactNode;
}

export const GeneralDrawer = ({ drawerProps, content, footer }: Props): JSX.Element => (
  <Drawer {...drawerProps} rootClassName={`${styles['market-general-drawer']} ${drawerProps?.className || ''}`}>
    <div>
      <div>{content}</div>
      <div style={drawerProps.width ? { width: drawerProps.width } : {}} className={styles['drawer-foot']}>
        {footer}
      </div>
    </div>
  </Drawer>
);

export default GeneralDrawer;
