.market-general-drawer {
  padding-bottom: 180px;

  :global(.market-ant4-drawer-header) {
    border-bottom-style: dashed;
    padding-left: 0;
    margin-left: 32px;
    margin-right: 32px;
  }

  :global(.market-ant4-drawer-title) {
    font-weight: 700;
  }

  :global(.market-ant4-drawer-body) {
    min-height: 90%;
    padding-top: 16px;
    padding-left: 32px;
    padding-bottom: 88px;
    overflow-y: auto;
  }

  :global {
    .market-ant4-drawer-header-title {
      flex-direction: row-reverse;
    }

    .market-ant4-drawer-close {
      margin-right: 0;
    }
  }

  .drawer-foot {
    position: fixed;
    z-index: 10;
    right: 0;
    bottom: 0;
    width: 1024px;
    padding: 16px 32px;
    background: var(--white);
    text-align: right;
    height: 72px;
    box-shadow: 0 -0.5px 0 0 var(--border-default);
  }
}
