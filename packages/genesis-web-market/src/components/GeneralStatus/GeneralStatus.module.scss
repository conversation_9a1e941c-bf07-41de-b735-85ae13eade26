.general-status {
  border-radius: 20px;
  padding: 4px 8px;
  line-height: 16px;
}

.general-status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
  vertical-align: 1px;
}

.general-status-text {
  font-size: 12px;
}

.general-status.success {
  background-color: var(--success-color-bg);
  .general-status-dot {
    background-color: var(--success-color);
  }
  .general-status-text {
    color: var(--success-color-text-dark);
  }
}

.general-status.info {
  background-color: var(--info-color-bg);
  .general-status-dot {
    background-color: var(--info-color);
  }
  .general-status-text {
    color: var(--info-color-text-dark);
  }
}

.general-status.warning {
  background-color: var(--warning-color-bg);
  .general-status-dot {
    background-color: $warning-color;
  }
  .general-status-text {
    color: var(--warning-color-text-dark);
  }
}

.general-status.error {
  background-color: var(--error-color-bg);
  .general-status-dot {
    background-color: var(--error-color);
  }
  .general-status-text {
    color: var(--error-color-text-dark);
  }
}

.general-status.normal {
  background-color: var(--disabled-bg);
  .general-status-dot {
    background-color: var(--border-light);
  }
  .general-status-text {
    color: var(--primary-light);
  }
}
