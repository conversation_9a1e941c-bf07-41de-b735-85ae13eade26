import { useCallback, useMemo } from 'react';

import { CampaignStatus } from 'genesis-web-service/lib/campaign';

import { CampaignFEStatus } from '@market/common/interface';
import { t } from '@market/i18n';

import styles from './GeneralStatus.module.scss';

const map = {
  info: 'info',
  warning: 'warning',
  error: 'error',
  success: 'success',
  normal: 'normal',
};

interface Props {
  status: 'info' | 'warning' | 'error' | 'success' | 'normal';
  text: string;
  location: 'detail' | 'card' | 'table';
  startTime?: string;
  expireTime?: string;
  configStatus?: CampaignStatus;
}

// TODO: REMOVE
export const calcCampaignFEStatus = (
  status: CampaignStatus | undefined,
  startTime: string | undefined,
  expireTime: string | undefined
): CampaignFEStatus | undefined => {
  if (!status || !startTime || !expireTime) {
    return undefined;
  }
  let campaignFEStatus;
  if (status === CampaignStatus.Approved) {
    const currentDateTime = new Date();
    const expireTimeObj = new Date(expireTime);
    const startTimeObj = new Date(startTime);
    if (currentDateTime < startTimeObj) {
      campaignFEStatus = CampaignFEStatus.Ready;
    } else if (currentDateTime > expireTimeObj) {
      campaignFEStatus = CampaignFEStatus.Expired;
    } else {
      campaignFEStatus = CampaignFEStatus.Effective;
    }
  }
  return campaignFEStatus;
};

export const calcStatusSuffix = (status: CampaignFEStatus | undefined): string => {
  let suffix = '';
  if (status === CampaignFEStatus.Ready) {
    suffix = ` / ${t('Ready')}`;
  } else if (status === CampaignFEStatus.Expired) {
    suffix = ` / ${t('Expired')}`;
  } else if (status === CampaignFEStatus.Effective) {
    suffix = ` / ${t('Effective')}`;
  }
  return suffix;
};

export const GeneralStatus = ({ status, text, location, startTime, expireTime, configStatus }: Props): JSX.Element => {
  const statusClassName = useMemo(() => map[status], [status]);

  const getStatusClass = useCallback(
    (className: string) => {
      if (configStatus && expireTime && configStatus === CampaignStatus.Approved) {
        // Approved 判断是否失效
        const approvedType = calcCampaignFEStatus(configStatus, startTime, expireTime);

        if (approvedType === CampaignFEStatus.Ready) {
          return 'info';
        }
        if (approvedType === CampaignFEStatus.Expired) {
          return 'warning';
        }
        if (approvedType === CampaignFEStatus.Effective) {
          return 'success';
        }
      }
      return className;
    },
    [configStatus, expireTime, startTime]
  );

  if (location === 'table') {
    return (
      <span className={`${styles['general-status']} ${styles[statusClassName]}`}>
        <span className={styles['general-status-dot']} />
        <span className={styles['general-status-text']}>{text}</span>
      </span>
    );
  }
  if (location === 'card') {
    return (
      <span className={`${styles['general-status']} ${styles[getStatusClass(statusClassName)]}`}>
        <span
          style={{
            fontWeight: 'bold',
          }}
          className={styles['general-status-text']}
        >
          {text.toUpperCase()}
          {calcStatusSuffix(calcCampaignFEStatus(configStatus, startTime, expireTime))}
        </span>
      </span>
    );
  }
  return (
    <span className={`${styles['general-status']} ${styles[statusClassName]}`}>
      <span className={styles['general-status-text']}>{text}</span>
    </span>
  );
};

export default GeneralStatus;
