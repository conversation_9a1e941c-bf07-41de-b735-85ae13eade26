import React, { ReactElement, ReactNode, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Divider, Popconfirm } from 'antd';

import { Icon, SimpleSectionHeader } from '@zhongan/nagrand-ui';

import styles from './AgreementContainerLayout.module.scss';

interface Props {
  agreementCode: string;
  title?: string | ReactElement;
  children: ReactNode;
  onClear?: () => void;
  showClearBtn?: boolean;
  className?: string;
  showHint?: boolean;
  hideDivider?: boolean;
  renderRightCustomBtn?: () => React.ReactNode;
  showExpandBtn?: boolean;
}

export const AgreementContainerLayout = ({
  agreementCode,
  title,
  children,
  onClear,
  showClearBtn,
  className,
  showHint = true,
  hideDivider = false,
  renderRightCustomBtn,
  showExpandBtn = false,
}: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [hiddenStatus, setHiddenStatus] = useState(true);

  const renderClearBtn = useCallback(() => {
    if (showClearBtn) {
      return (
        <Popconfirm
          placement="left"
          title={t('Are you sure to clear this section?')}
          okText={t('yes')}
          cancelText={t('no')}
          onConfirm={onClear}
        >
          <a href="#" className={styles.clear}>
            {t('Clear')}
          </a>
        </Popconfirm>
      );
    }
    return null;
  }, [onClear, showClearBtn, t]);

  return (
    <React.Fragment>
      {!hideDivider && (
        <Divider
          dashed
          style={{
            margin: '24px 0',
          }}
        />
      )}
      <div className={`${styles['agreement-block']} ${className || ''}`} id={agreementCode}>
        <div className={`${styles['block-title']}`}>
          {title && showHint && <SimpleSectionHeader className="!font-bold">{title}</SimpleSectionHeader>}
          {title && !showHint && <div>{title}</div>}
          {renderClearBtn()}
          {showExpandBtn ? (
            <div onClick={() => setHiddenStatus(!hiddenStatus)} className="cursor-pointer">
              <span className="mr-[10px]">{t('Expand')}</span>
              {hiddenStatus ? <Icon type="down" /> : <Icon type="up" />}
            </div>
          ) : null}
          {renderRightCustomBtn && renderRightCustomBtn()}
        </div>
        <div className={`${hiddenStatus && showExpandBtn ? 'hidden' : 'visible'} ${styles['agreement-detail-block']}`}>
          {children}
        </div>
      </div>
    </React.Fragment>
  );
};

export default AgreementContainerLayout;
