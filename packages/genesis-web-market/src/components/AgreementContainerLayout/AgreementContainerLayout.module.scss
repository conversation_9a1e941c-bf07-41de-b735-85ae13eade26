.agreement-block {
  background: var(--white);
  margin-bottom: 16px;

  // padding: 0 24px;
  // 存在父子约定的情况下，子约定容器不需要padding
  .agreement-block {
    padding: 0;
  }

  .block-title {
    align-items: center;
    color: var(--text-color);
    display: flex;
    font-weight: 700;
    justify-content: space-between;
    margin-bottom: 8px;
    margin-right: 30px;
    line-height: 24px;
    height: 24px;
    align-items: center;

    .block-title-hint {
      width: 4px;
      height: 21px;
      background-color: var(--primary-color);
      border-radius: 12px;
      margin-right: 4px;
    }

    .title {
      line-height: 24px;
      font-size: 16px;
    }
  }

  .clear {
    float: right;
    font-weight: 400;
    font-size: 14px;
    color: var(--primary-color);
  }
}
