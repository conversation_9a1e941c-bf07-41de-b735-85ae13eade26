import { Tooltip } from 'antd';

import classNames from 'classnames/bind';

import { Icon } from '@zhongan/nagrand-ui';

import styles from './LabelWithTooltip.module.scss';

const cx = classNames.bind(styles);

interface Props {
  title: string;
  tooltip: string;
  className?: string;
}

export const LabelWithTooltip = ({ title, tooltip, className }: Props): JSX.Element => (
  <span className={cx('label-with-tooltip', className)}>
    {title}
    &nbsp;
    <Tooltip title={tooltip} overlayStyle={{ width: 350 }}>
      <Icon type="info-circle" style={{ marginLeft: 5, marginTop: '-3px', fontSize: 14 }} />
    </Tooltip>
  </span>
);

export default LabelWithTooltip;
