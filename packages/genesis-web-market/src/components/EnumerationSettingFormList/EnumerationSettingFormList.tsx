import React from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, Input } from 'antd';

import { CommonIconAction, Icon } from '@zhongan/nagrand-ui';

interface Props {
  label: string;
  name: string | number | (string | number)[];
  disabled?: boolean;
}

function hasDuplicates(arr: string[]): boolean {
  const set = new Set(arr);
  return set.size !== arr.length;
}

export const EnumerationSettingFormList = ({ label, name, disabled }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  return (
    <Form.Item required label={label}>
      <Form.List
        name={name}
        rules={[
          {
            validator: async (_, names: string[]) => {
              if (!names || names.length === 0) {
                return Promise.reject(new Error(t('Please input')));
              }
              if (hasDuplicates(names.filter(item => !!item))) {
                return Promise.reject(new Error(t('Exists duplicate value')));
              }
            },
          },
        ]}
      >
        {(fields, { add, remove }, { errors }) => (
          <React.Fragment>
            {fields.map((field, index) => (
              <div className="mb-2 flex items-center">
                <Form.Item
                  {...field}
                  validateTrigger={['onChange', 'onBlur']}
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: t('Please input'),
                    },
                  ]}
                  noStyle
                >
                  <Input disabled={disabled} placeholder={t('Please input')} className="w-[240px]" />
                </Form.Item>
                {fields.length > 1 && !disabled ? (
                  <CommonIconAction icon={<Icon type="delete" />} onClick={() => remove(field.name)} className="ml-4" />
                ) : null}
              </div>
            ))}
            {!disabled && (
              <div>
                <Button onClick={() => add()} icon={<Icon type="add" />}>
                  {t('Add')}
                </Button>
                <Form.ErrorList errors={errors} />
              </div>
            )}
          </React.Fragment>
        )}
      </Form.List>
    </Form.Item>
  );
};

export default EnumerationSettingFormList;
