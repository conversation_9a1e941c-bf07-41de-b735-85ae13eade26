import { useTranslation } from 'react-i18next';

import classNames from 'classnames/bind';

import styles from './ColumnRequiredTitle.module.scss';

const cx = classNames.bind(styles);

interface Props {
  title: string;
}

export const ColumnRequiredTitle = ({ title }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  return <span className={cx('title')}>{title}</span>;
};

export default ColumnRequiredTitle;
