import React from 'react';
import { connect } from 'react-redux';

import { Menu, message } from 'antd';

import { cloneDeep, isEmpty } from 'lodash-es';
import PropTypes from 'prop-types';

import { StatusLabel, TextEllipsisDetect } from '@zhongan/nagrand-ui';

import { t } from '@market/i18n';
import { isShowNotification } from '@market/marketService/goodsTesting.service';
import { NewMarketService } from '@market/services/market/market.service.new';

import { getPackageType } from '../../marketService/package.service';
import { urlQuery } from '../../util';
import './index.scss';

const MenuItem = Menu.Item;

class FMarketMenu extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      menuList: [],
      packageBasicInfo: {},
      goodsDetail: {},
    };
  }

  packageMenuList = () => {
    // 如果有值，传具体menu的id，比如传2，就disable 3、4、5 这三个菜单
    const { disableAfterPages } = this.props;
    const packageMenuList = [
      {
        id: '1',
        menuName: t('Basic Information'),
        url: '/market/package/basic',
      },
      {
        id: '2',
        menuName: t('Benefit Configuration'),
        url: '/market/package/benefit-configuration',
      },
      {
        id: '3',
        menuName: t('Package Agreement'),
        url: '/market/package/agreement',
      },
      {
        id: '4',
        menuName: t('Application Elements'),
        url: '/market/package/application-elements',
      },
      {
        id: '5',
        menuName: t('Declaration Configuration'),
        url: '/market/package/declaration',
      },
      // {
      //   id: '6',
      //   menuName: t('Policy Change'),
      //   url: '/market/package/policy-change',
      // },
    ];
    if (disableAfterPages) {
      const startIndex = packageMenuList.findIndex(menu => menu.id === disableAfterPages);
      return packageMenuList.map((menu, index) => ({
        ...menu,
        disabled: index > startIndex,
      }));
    }
    return packageMenuList;
  };

  goodsMenuList = ({ showNotificationConfig, disableAfterPages }) => {
    const baseMenu = [
      {
        id: '1',
        menuName: `${t('Basic Information')}`,
        url: '/market/goods/basic',
      },
      {
        id: '2',
        menuName: t('Sales Attributes'),
        url: '/market/goods/salesAttributes',
      },
      {
        id: '3',
        menuName: t('Plan Configuration'),
        url: '/market/goods/coverage-plan',
      },
      {
        id: '4',
        menuName: t('Documents Display'),
        url: '/market/goods/doc-display',
      },
    ];
    if (showNotificationConfig) {
      baseMenu.push({
        id: '5',
        menuName: t('Notification Configuration'),
        url: '/market/goods/notification-config',
      });
    }
    if (disableAfterPages) {
      const startIndex = baseMenu.findIndex(menu => menu.id === disableAfterPages);
      return baseMenu.map((menu, index) => ({
        ...menu,
        disabled: index > startIndex,
      }));
    }
    return baseMenu;
  };

  serviceMenuList = () => [
    {
      id: '1',
      menuName: t('Basic Configuration'),
      url: '/market/service/basic',
    },
    {
      id: '2',
      menuName: t('Upload Files'),
      url: '/market/service/upload-file',
    },
  ];

  getMenuList = async goodsId => {
    const { category, envConfig, checkIsBothProductEmptyOnGoodsPage } = this.props;
    // 创建的时候没有goodsId，所以不需要判断是否显示通知
    const showNotification = goodsId ? await isShowNotification(goodsId) : false;

    const showNotificationConfig = envConfig.marketNotificationConfigEnable && showNotification;
    switch (category) {
      case 1:
        return cloneDeep(this.packageMenuList());
      case 2:
        if (goodsId && checkIsBothProductEmptyOnGoodsPage) {
          const isProhibitNextStepOnGoodsPage = await checkIsBothProductEmptyOnGoodsPage(goodsId);
          return cloneDeep(
            this.goodsMenuList({
              showNotificationConfig,
              disableAfterPages: isProhibitNextStepOnGoodsPage ? '1' : '',
            })
          );
        }
        return cloneDeep(this.goodsMenuList({ showNotificationConfig }));

      case 3:
        return cloneDeep(this.serviceMenuList());
    }
  };

  async setMenu() {
    const { type } = this.props;
    const goodsId = urlQuery('goodsId');
    const packageId = urlQuery('packageId');
    const serviceId = urlQuery('serviceId');

    const menuList = await this.getMenuList(goodsId);
    if (!type) {
      this.setState({ menuList });
    } else if ((!goodsId && !packageId && !serviceId) || type === 'copy') {
      // 在没有id信息（add状态）或者为copy状态时，从第一步开始初始化
      menuList.map((item, index) => {
        if (index > 0) {
          item.disabled = true;
        }
      });
      this.setState({ menuList });
    } else {
      this.setState({ menuList });
    }
  }

  componentDidUpdate(prevProps) {
    const packageId = urlQuery('packageId');
    if (prevProps.type !== this.props.type || prevProps.disableAfterPages !== this.props.disableAfterPages) {
      if (this.props.category === 1 && packageId) {
        getPackageType(packageId).then(() => {
          this.setMenu();
        });
      } else {
        this.setMenu();
      }
    }
  }

  componentDidMount() {
    const packageId = urlQuery('packageId');
    if (this.props.page === 'PACKAGE' && packageId) {
      NewMarketService.MarketStructureMgmtService.queryPackageStructure({
        packageId: +packageId,
        queryBasic: true,
      }).then(res => {
        this.setState({
          packageBasicInfo: res.packageBasicInfo,
        });
      });
    }
    const goodsId = urlQuery('goodsId');
    if (this.props.page === 'GOODS' && goodsId) {
      NewMarketService.GoodMgmtService.find({
        goodsId: +goodsId,
      }).then(res => {
        if (res?.value?.basicProp) {
          this.setState({
            goodsDetail: res.value.basicProp,
          });
        }
      });
    }
    if (this.props.category === 1 && packageId) {
      getPackageType(packageId).then(withSchema => {
        if (!withSchema) {
          message.warning(
            t('This record was configured based on old version. Currently, viewing and editing are not supported.'),
            7
          );
        }
        this.setMenu();
      });
    } else {
      this.setMenu();
    }
  }

  clickMenu = async item => {
    if (this.props.onClick) {
      this.props.onClick();
      return;
    }
    const { category, navigate, queryMode, type, checkIsBothProductEmptyOnGoodsPage } = this.props;
    const { menuList } = this.state;
    if (!item.key) return;
    const goodsId = urlQuery('goodsId');
    const packageId = urlQuery('packageId');
    const layoutId = urlQuery('layoutId');
    const serviceId = urlQuery('serviceId');
    let search = '';
    switch (category) {
      case 1: {
        search = `packageId=${packageId}`;
        break;
      }
      case 2: {
        const _search = (search = `goodsId=${goodsId}&layoutId=${layoutId}`);

        if (goodsId && checkIsBothProductEmptyOnGoodsPage) {
          const isProhibitNextStepOnGoodsPage = await checkIsBothProductEmptyOnGoodsPage(goodsId);
          search = isProhibitNextStepOnGoodsPage ? '' : _search;
        } else {
          search = _search;
        }
        break;
      }
      case 3: {
        search = `serviceId=${serviceId}`;
        break;
      }
    }

    if (!search) return;

    // add 和 copy的时候其他步骤永远不可以点击
    navigate(
      {
        pathname: menuList[parseInt(item.key) - 1].url,
        search,
      },
      {
        state: {
          mode: type,
          queryMode,
        },
      }
    );
  };

  render() {
    const { defaultSelectedKeys, type, page } = this.props;
    const { menuList, packageBasicInfo } = this.state;
    menuList.map(item => {
      item.menuNameList = item.menuName.split(',');
    });

    let goodsDetail = {};
    if (!isEmpty(this.state.goodsDetail)) {
      goodsDetail = this.state.goodsDetail;
    }
    if (!isEmpty(this.props.goodsDetail)) {
      goodsDetail = this.props.goodsDetail;
    }

    const pageIsPackage = page === 'PACKAGE';
    const packageTitle = packageBasicInfo?.packageName;
    const goodsTitle = isEmpty(goodsDetail) ? '' : `${goodsDetail.goodsName}-${goodsDetail.version}`;
    const title = pageIsPackage ? packageTitle : goodsTitle;

    const calGoodsStatusI18n = () => {
      if (!goodsDetail.goodStatus || goodsDetail.goodStatus === 1 || !urlQuery('goodsId')) return t('EDITING');
      if (goodsDetail.goodStatus === 2) return t('EFFECTIVE');
      return t('LAUNCHED');
    };

    const calId = () => {
      if (pageIsPackage) return packageBasicInfo?.packageId;
      if (urlQuery('goodsId')) return urlQuery('goodsId');
      return '--';
    };

    const calCode = () => {
      if (pageIsPackage) return packageBasicInfo?.packageCode;
      if (!isEmpty(goodsDetail)) return goodsDetail.code;
      return '--';
    };

    return (
      <React.Fragment>
        <div className="market-menu">
          <div className="text-xs font-bold text-@border-light m-4 mb-[9px]">{t('MENU')}</div>
          <Menu
            theme="light"
            mode="inline"
            defaultSelectedKeys={defaultSelectedKeys}
            onClick={item => this.clickMenu(item)}
          >
            {menuList.map(item => {
              return (
                <MenuItem key={item.id} title={item.menuName} disabled={item.disabled}>
                  {item.menuNameList.map((item_1, index) => {
                    return <span key={index}>{item_1}</span>;
                  })}
                </MenuItem>
              );
            })}
          </Menu>
        </div>
        {page !== 'SERVICE' && (
          <div className="m-[8px] p-[16px] rounded-[8px] bg-[var(--table-body-hover-bg)]">
            {title && <div className="break-all w-[160px] text-root font-bold mb-[8px]">{title}</div>}
            <div className="text-[var(--text-color-quaternary)] mb-[8px]">
              <TextEllipsisDetect text={`ID: ${calId()}`} maxWidth={160} />
              <br />
              <TextEllipsisDetect text={`${t('Code')}: ${calCode()}`} maxWidth={160} />
            </div>
            {page === 'GOODS' && (
              <StatusLabel
                style={{ fontWeight: 600 }}
                needBgColor
                type={goodsDetail.goodStatus === 1 ? 'no-status' : 'success'}
                statusI18n={calGoodsStatusI18n()}
              />
            )}
            {page === 'PACKAGE' && (
              <StatusLabel
                style={{ fontWeight: 600 }}
                needBgColor
                type={packageBasicInfo?.packageStatus === 3 ? 'success' : 'no-status'}
                statusI18n={packageBasicInfo?.packageStatus === 3 ? t('EFFECTIVE') : t('EDITING')}
              />
            )}
          </div>
        )}
      </React.Fragment>
    );
  }
}

FMarketMenu.propTypes = {
  page: PropTypes.string,
  // menuList: PropTypes.array, // 菜单项
  onClick: PropTypes.func, // 菜单点击事件
  defaultSelectedKeys: PropTypes.array, // 默认选中的菜单
  type: PropTypes.string, // 类型：add-新增，edit-编辑，view-查看, copy-复制
  menuMainTitle: PropTypes.string, // 标题
  category: PropTypes.number, // 菜单类型：1-产品组合菜单；2-商品菜单
  onBack: PropTypes.func, // 返回搜索页的操作函数
  queryMode: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

export default connect(state => {
  return {
    envConfig: state.envConfig,
  };
})(FMarketMenu);
