.market-menu {
  width: 208px;
  background: var(--white);
  .market-ant4-menu-inline {
    padding: 0;
    list-style-type: none;
    padding: 0 8px;

    li {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 9px 8px !important;
      color: var(--text-color);
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;
      height: auto;
      &:hover:not(.market-ant4-menu-item-selected) {
        background: #f2f4f8;
        color: var(--text-color);
      }
      .market-ant4-menu-title-content {
        span {
          display: block;
          word-break: break-word;
          white-space: normal;
        }
      }
      &.market-ant4-menu-item-disabled {
        cursor: not-allowed;
      }
    }
    .market-ant4-menu-item-selected {
      color: var(--info-color);
      background: var(--info-color-bg);
      border-radius: 4px;
      &::after {
        display: none;
      }
    }
  }
}
