import { useMemo, useState } from 'react';

import cls from 'classnames';

import { Icon } from '@zhongan/nagrand-ui';

interface CollapseProps {
  children: React.ReactNode;
  visible?: boolean;
  title?: React.ReactNode | string;
  isVisibleIcon?: boolean;
  onTitleClick?: () => void;
  subTitle?: React.ReactNode | string;
  destroyOnClose?: boolean;
  setVisible?: (visible: boolean) => void;
}

export const Collapse = (props: CollapseProps) => {
  const {
    children,
    visible,
    title,
    onTitleClick,
    subTitle,
    isVisibleIcon = true,
    destroyOnClose = false,
    setVisible,
  } = props;
  const [internalVisible, setInternalVisible] = useState(visible ?? false);
  // 有setVisible认为是受控组件
  const controlled = !!setVisible;
  const currentVisible = useMemo(() => (controlled ? visible : internalVisible), [visible, internalVisible]);

  const renderChildren = () => {
    if (destroyOnClose) {
      return currentVisible ? children : null;
    }
    return children;
  };

  const handleToggle = () => {
    if (controlled) {
      setVisible(!currentVisible);
    } else {
      setInternalVisible(!currentVisible);
    }
  };

  return (
    <div>
      {subTitle ? (
        <div className="rounded-md overflow-hidden mb-6">
          <div
            className="bg-@collapse-header-bg px-4 py-1 cursor-pointer"
            onClick={() => {
              if (onTitleClick) {
                onTitleClick?.();
              } else {
                handleToggle();
              }
            }}
          >
            {subTitle}
            <Icon type="up" className={cls(!currentVisible && 'rotate-180', 'ml-1')} />
          </div>
          <div className={cls('bg-@white', currentVisible ? 'visible' : 'hidden')}>{renderChildren()}</div>
        </div>
      ) : (
        <div
          className={cls(
            'rounded-xl py-[16px] mt-4 font-medium',
            currentVisible ? 'bg-@input-addon-bg' : 'bg-white border-@border-color-base border-solid border-[1px]'
          )}
        >
          <div
            onClick={() => {
              if (onTitleClick) {
                onTitleClick?.();
              } else {
                handleToggle();
              }
            }}
            className="flex items-center gap-2 justify-between px-6 cursor-pointer"
          >
            <span className="font-medium mr-2 w-11/12">{title}</span>
            {isVisibleIcon && (
              <span
                className={cls(
                  'w-8 h-8 rounded-[32px] border-solid border-[1px] border-@primary-light flex justify-center items-center',
                  !currentVisible && 'rotate-180'
                )}
              >
                <Icon type="up" className="text-xl flex" />
              </span>
            )}
          </div>
          <div className={cls(currentVisible ? 'visible' : 'hidden')}>
            <div className="px-6 mt-4">{renderChildren()}</div>
          </div>
        </div>
      )}
    </div>
  );
};
export default Collapse;
