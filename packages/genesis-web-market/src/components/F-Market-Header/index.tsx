import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { useNavigateBack } from 'genesis-web-component/lib/hook/router';
import { ProductStructureStyle } from 'genesis-web-service';
import { ProductLineI18n } from 'genesis-web-shared/lib/i18n/product-line-i18n';

import LeftArrowIcon from '@market/asset/svg/left-arrow.svg';

type SubMenuType = 'Package' | 'Application_Elements_Group' | 'Marketing_Goods' | 'Quick_Quotation';

interface FMarketHeaderProps {
  onBack?: () => void;
  subMenu: SubMenuType;
  backPath?: string;
}

export const FMarketHeader = ({ onBack, subMenu, backPath }: FMarketHeaderProps) => {
  const [t] = useTranslation(['market', 'common']);

  const [handleBack] = useNavigateBack(backPath ?? '/');

  const title: string = useMemo(() => {
    const shouldFollowProductLineInMenu: SubMenuType[] = ['Package', 'Application_Elements_Group'];
    if (
      ProductLineI18n.getInstance()?.productLineLable?.style === ProductStructureStyle.Coverage &&
      shouldFollowProductLineInMenu.includes(subMenu)
    ) {
      return t('Product Center');
    }
    return t('Marketing Center');
  }, [subMenu]);

  const subTitle: string = useMemo(() => {
    switch (subMenu) {
      case 'Application_Elements_Group':
        return t('Application Elements Group');
      case 'Marketing_Goods':
        return t('Marketing Goods');

      case 'Package':
        if (ProductLineI18n.getInstance()?.productLineLable?.style === ProductStructureStyle.Coverage) {
          return t('Package Management');
        }
        return t('Package');
      case 'Quick_Quotation':
        return t('Quick Quotation');
      default:
        return '';
    }
  }, [subMenu]);

  return (
    <div className="h-12 flex gap-4 px-4 py-[10px] items-center font-medium text-@text-color border-solid border-b-@table-header-select-bg bg-white border-0 border-b-[1px]">
      <div
        onClick={() => {
          if (onBack) {
            onBack();
          } else {
            handleBack();
          }
        }}
        className="flex items-center gap-1 bg-@form-addon-bg px-2 py-[1px] rounded cursor-pointer"
      >
        <LeftArrowIcon />
        <span>{t('Back to Search')}</span>
      </div>
      <span className="text-@disabled-color">|</span>
      <span>{`${title} - ${subTitle}`}</span>
    </div>
  );
};
