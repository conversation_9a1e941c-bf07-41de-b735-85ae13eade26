import { useTranslation } from 'react-i18next';

import classNames from 'classnames/bind';

import styles from './ProductTagLabel.module.scss';

const cx = classNames.bind(styles);

interface Props {
  type?: 'main' | 'rider';
  text?: string;
  className?: string;
}

export const ProductTagLabel = ({ type, text, className = '' }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  if (type === 'main') {
    return <span className={cx('product-main', className)}>{t('Main')}</span>;
  }
  if (type === 'rider') {
    return <span className={cx('product-rider', className)}>{t('Rider')}</span>;
  }
  return <span className={`px-2 text-@label font-medium bg-@label-bg rounded inline-block ${className}`}>{text}</span>;
};

export default ProductTagLabel;
