import { useTranslation } from 'react-i18next';

import { DefaultOptionType, SelectProps } from 'antd/lib/select';

import { Select } from '@zhongan/nagrand-ui';

import empty from '@market/asset/img/empty.png';
import { SelectOption } from '@market/common/interface';

interface Props extends SelectProps {
  option: SelectOption[]; // option 兼容老组件
  selectAll?: boolean;
}

export const GeneralSelect = ({ option, options, selectAll = false, ...resetProps }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  if (!resetProps.style) {
    // 默认宽度240
    resetProps.style = { width: 240 };
  }

  return (
    <Select
      tooltipOnEditMode
      options={option || options}
      // 默认宽度240
      style={{ width: 240 }}
      // 默认提示语
      placeholder={t('Please select')}
      // 默认自带搜索功能
      filterOption={(input: string, _option: DefaultOptionType | undefined) =>
        `${_option?.label as string}`.toLowerCase().includes(input.toLowerCase())
      }
      getPopupContainer={(triggerNode: HTMLElement) => triggerNode.parentElement || document.body}
      notFoundContent={
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          <div>
            <img src={empty} />
          </div>
          <div style={{ marginBottom: 16, marginTop: 8 }}>{t('No Data')}</div>
        </div>
      }
      showSearch
      // 默认允许clear
      allowClear
      selectAll={selectAll}
      {...resetProps}
    />
  );
};

export default GeneralSelect;
