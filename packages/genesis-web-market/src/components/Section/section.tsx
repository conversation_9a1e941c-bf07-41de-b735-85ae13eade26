/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import React, { CSSProperties, useState } from 'react';
import { useTranslation } from 'react-i18next';

import cls from 'classnames';

import { Icon } from '@zhongan/nagrand-ui';

import './index.scss';

interface Props {
  className?: string;
  style?: CSSProperties;
  title?: string | React.ReactElement;
  subTitle?: string | React.ReactElement;
  children?: React.ReactElement | (React.ReactElement | null)[] | Element | null;
  showExpandBtn?: boolean;
  thirdLevelTitle?: string | React.ReactElement;
  destroyOnClose?: boolean;
}

const Section = (props: Props) => {
  const {
    className = '',
    style,
    title,
    subTitle,
    children,
    thirdLevelTitle,
    showExpandBtn = false,
    destroyOnClose = false,
  } = props;

  const [hiddenStatus, setHiddenStatus] = useState(true);
  const [t] = useTranslation(['market', 'common']);

  const renderChildren = () => {
    if (destroyOnClose) {
      return hiddenStatus ? null : children;
    }
    return children;
  };

  return (
    <div className={`${className} market-section-container`} style={style || {}}>
      {title ? <div className="title">{title}</div> : null}
      {subTitle || thirdLevelTitle ? (
        <div
          className={cls(
            'font-bold text-root mb-4',
            showExpandBtn ? 'flex justify-between items-center ' : '',
            subTitle ? 'text-@label-color' : 'text-@text-color-secondary'
          )}
        >
          <span className="flex items-center">
            <span
              className={cls(
                'w-1 h-[21px] rounded-xl mr-1 inline-block',
                subTitle ? 'bg-@primary-color' : 'bg-@border-default'
              )}
            />
            {subTitle || thirdLevelTitle}
          </span>
          {showExpandBtn ? (
            <div onClick={() => setHiddenStatus(!hiddenStatus)} className="cursor-pointer">
              <span className="mr-[10px]">{t('Expand')}</span>
              {hiddenStatus ? <Icon type="down" /> : <Icon type="up" />}
            </div>
          ) : null}
        </div>
      ) : null}
      <div className={cls(title && 'mx-6', hiddenStatus && showExpandBtn ? 'hidden' : 'visible')}>
        {renderChildren()}
      </div>
    </div>
  );
};

export default Section;
