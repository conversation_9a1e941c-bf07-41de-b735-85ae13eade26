import { Component } from 'react';

import { Icon } from '@zhongan/nagrand-ui';

import './index.scss';

class TwoItemSort extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  swap = () => {
    if (typeof this.props.onChange === 'function') {
      const { value } = this.props;
      this.props.onChange([value[1], value[0]]);
    }
  };

  findOption = key => {
    const { options = [] } = this.props;

    return (
      options.find(option => {
        return option.value === key;
      }) || {
        label: key,
      }
    );
  };

  render() {
    const { value = [] } = this.props;

    return (
      <div className="market-tow-item-sort">
        <div>
          {value.map((key, index) => (
            <div key={key} className="sort-item">
              <span className="order-number">{index + 1}</span>
              {this.findOption(key).label}
            </div>
          ))}
        </div>
        {value.length >= 2 ? <Icon type="sort" onClick={this.swap} style={{ marginLeft: 13 }} /> : null}
      </div>
    );
  }
}

export default TwoItemSort;
