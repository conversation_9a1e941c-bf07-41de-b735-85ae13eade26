import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Drawer, Input, message } from 'antd';

import { cloneDeep } from 'lodash-es';

import { hasDuplicates } from 'genesis-web-component/lib/util/array';
import { I18nLanguageInfo } from 'genesis-web-service';

import GeneralSelect from '../GeneralSelect/GeneralSelect';
import styles from './I18nLanguageDrawer.module.scss';

const InputGroup = Input.Group;
interface SelectOption {
  label: string;
  value: string | number;
}

interface Props {
  visible: boolean;
  options: SelectOption[];
  initData: I18nLanguageInfo[];
  setVisible: (arg: boolean) => void;
  saveLanguageInfo: (arr: I18nLanguageInfo[]) => void;
}
export const I18nLanguageDrawer: React.FC<Props> = ({
  visible = false,
  options,
  initData,
  setVisible,
  saveLanguageInfo,
}) => {
  const [t] = useTranslation(['query', 'common']);
  const [i18nLanguageList, setI18nLanguageList] = useState<I18nLanguageInfo[]>([]);

  useEffect(() => {
    const list = cloneDeep(options).map(({ value }) => ({
      lang: value?.toString(),
      i18nContent: initData.find(inner => inner.lang === value)?.i18nContent || '',
    }));
    setI18nLanguageList(list || []);
  }, [initData, options]);

  const onClose = useCallback(() => {
    setVisible(false);
    setI18nLanguageList([]);
  }, [setVisible]);

  const onConfirmSubmit = useCallback(() => {
    if (hasDuplicates(i18nLanguageList, 'lang')) {
      return message.error(t('Same language should only have one remark'));
    }
    saveLanguageInfo(i18nLanguageList.filter(item => !!item.i18nContent));
    onClose();
  }, [i18nLanguageList, onClose, saveLanguageInfo, t]);

  const changeLanguageItem = useCallback(
    (values, index: number, key: string) => {
      const tempI18nLanguageList = cloneDeep(i18nLanguageList);
      tempI18nLanguageList[index][`${key}` as keyof I18nLanguageInfo] = values;
      setI18nLanguageList(tempI18nLanguageList);
    },
    [i18nLanguageList]
  );

  return (
    <Drawer
      title={t('Multilingual')}
      width={720}
      onClose={onClose}
      closable={false}
      open={visible}
      bodyStyle={{ paddingBottom: 80 }}
      rootClassName={styles['i18nLanguage-drawer']}
    >
      <div>
        <div>{t('Language')}</div>
        {i18nLanguageList.map((item, index) => (
          <InputGroup className={styles['inputGroup-item']} compact>
            <GeneralSelect
              style={{ width: 232 }}
              value={item.lang}
              placeholder={t('Please select')}
              option={options}
              disabled
            />
            <Input
              style={{ width: 328 }}
              value={item.i18nContent}
              placeholder={t('Please input')}
              onChange={event => changeLanguageItem(event.target.value, index, 'i18nContent')}
            />
          </InputGroup>
        ))}
      </div>
      <div
        style={{
          position: 'absolute',
          right: 0,
          bottom: 0,
          zIndex: 1,
          width: '100%',
          borderTop: '1px solid #e9e9e9',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
        }}
      >
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          {t('Cancel')}
        </Button>
        <Button onClick={onConfirmSubmit} type="primary" style={{ marginRight: 8 }}>
          {t('Confirm')}
        </Button>
      </div>
    </Drawer>
  );
};
