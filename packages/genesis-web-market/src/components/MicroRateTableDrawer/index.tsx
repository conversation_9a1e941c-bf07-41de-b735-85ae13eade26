import React from 'react';

// eslint-disable-next-line import/no-extraneous-dependencies
import { MicroApp, initGlobalState, loadMicroApp } from 'qiankun';

interface Props {
  options: any;
  onlyIcon: boolean;
  rateTableCode: string;
}

class MicroRateTableDrawer extends React.Component<Props> {
  containerRef = React.createRef<HTMLDivElement>();

  microApp: MicroApp = null!;

  actions = initGlobalState({
    visible: null,
    status: null,
    mode: null,
    data: null,
  });

  componentDidMount() {
    this.microApp = loadMicroApp({
      name: 'genesis-web-product',
      entry: '/children/genesis-web-product/index.html',
      container: this.containerRef.current as HTMLElement,
      props: {
        microSource: 'market',
      },
    });
  }

  componentWillUnmount() {
    this.actions.offGlobalStateChange();
    this.microApp.unmount();
  }

  render() {
    // this.actions.onGlobalStateChange((state, prev) => {
    //   if (state.visible === false) {
    //     this.props.changeVisible(false);
    //   }
    //   if (state.status === 'published' && this.props.currentRuleCode === null) {
    //     this.props.addAgeValidation(state.data);
    //   }
    //   if (state.status === 'published' && this.props.currentRuleCode !== null) {
    //     this.props.editAgeValidation(state.data);
    //   }
    // });
    return <div ref={this.containerRef} />;
  }
}

export default MicroRateTableDrawer;
