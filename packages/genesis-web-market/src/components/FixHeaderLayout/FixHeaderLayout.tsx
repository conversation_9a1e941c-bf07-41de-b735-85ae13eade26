/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import React, { useRef } from 'react';

import { useSize } from 'ahooks';

interface Props {
  header: React.ReactNode;
  content: React.ReactNode;
  outOffsetHeight?: number; // 如果这个layout占满整个屏幕的高度，传0；要留多少px显示别的内容，就传多少px；
}

/**
 * header头部固定，content内容区滑动
 */
export const FixHeaderLayout = ({ header, content, outOffsetHeight = 50 }: Props): JSX.Element => {
  const headerDom = useRef<HTMLDivElement>(null);
  const headerSize = useSize(headerDom);

  return (
    <div
      style={{
        height: `calc(100vh - ${outOffsetHeight}px)`, // 全局的header头部高度为50px
        position: 'relative',
        overflow: 'hidden',
        paddingTop: headerSize?.height,
      }}
    >
      <div
        ref={headerDom}
        style={{
          position: 'absolute',
          top: 0,
          width: '100%',
        }}
      >
        {header}
      </div>
      <div
        style={{
          overflow: 'auto',
          height: '100%',
          paddingBottom: 60,
        }}
      >
        {content}
      </div>
    </div>
  );
};

export default FixHeaderLayout;
