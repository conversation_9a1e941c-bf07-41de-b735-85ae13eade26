/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';

import { DatePicker, Form, FormInstance, Input } from 'antd';

import clsx from 'classnames';
import moment from 'moment-timezone';

import { useL10n } from 'genesis-web-shared/lib/l10n';

import { TimeZoneProps } from '@market/common/interface';
import { selectDateFormat, selectTimeZone } from '@market/redux/selector';

import GeneralSelect from '../GeneralSelect/GeneralSelect';

const { RangePicker } = DatePicker;

const InputGroup = Input.Group;
export interface ZoneOption {
  key?: string;
  name: string;
  value?: string;
}

interface SelectItem {
  key?: string | number;
  initialValue?: string;
}
interface SelectObj {
  allowClear?: boolean;
  disabled?: boolean;
  dropdownMatchSelectWidth?: boolean;
  list?: ZoneOption[];
  placeholder?: string;
  handleChange?: (value: any) => void;
}

interface Rules {
  required: boolean;
  message: string;
}

interface Validate extends Rules {
  trigger: string;
  rules: Rules[];
}
interface PickerItem {
  key?: string;
  initialValue?: moment.Moment | string;
  rules?: Rules[];
  validate?: Validate;
  normalize?: (value: any) => void;
}

enum PickerTypeEnums {
  DATE = 'date',
  RANGE = 'range',
}

interface Props {
  pickerType?: PickerTypeEnums;
  showTime?: boolean | Record<string, any>;
  onChange?: (value: moment.Moment | undefined, dateString: string | string[]) => void;
  format: string;
  selectObj?: SelectObj;
  disabled?: boolean;
  selectItem?: SelectItem;
  pickerItem?: PickerItem;
  customizeSize?: number[];
  onlyZone?: boolean;
  form: FormInstance;
  placeholder: [string, string] | undefined;
  className?: string;
  showError?: boolean;
}

const FTimezonePicker: React.FC<Props> = props => {
  const {
    form,
    showTime,
    placeholder,
    format,
    selectObj,
    disabled,
    selectItem,
    pickerItem,
    customizeSize = [],
    pickerType,
    className,
    showError = false,
    onlyZone,
    ...rest
  } = props;

  const { l10n } = useL10n();
  const [t] = useTranslation(['common']);
  const timeZone = useSelector(selectTimeZone) as TimeZoneProps;
  const dateFormat = useSelector(selectDateFormat);
  const [dateText, setDateText] = useState<moment.Moment | string>('');

  useEffect(() => {
    const zoneId = selectItem?.initialValue ? selectItem.initialValue.trim().split(' ')[0] : '';
    if (pickerType && pickerType === PickerTypeEnums.RANGE) {
      setDateText(l10n.dateFormat.l10nMoment(new Date(), zoneId));
    } else if (pickerItem && pickerItem.key) {
      setDateText(form?.getFieldValue(pickerItem?.key) ?? l10n.dateFormat.l10nMoment(new Date(), zoneId));
    }
  }, [pickerType, pickerItem?.key, form]);

  const selectComponent = useMemo(() => {
    const { ...restOfSelect } = selectObj;

    const zoneOptions = (selectObj?.list || timeZone?.zoneOptionList || []).map((item: ZoneOption) => {
      const [zone] = item.name.split(' ');
      return {
        key: item.key,
        value: item.value as string,
        label: `${l10n.dateFormat.getZoneUTC(zone, dateText) ?? ''} ${zone}`,
      };
    });

    if (selectItem && selectItem?.key) {
      return (
        <Form.Item className={clsx('select-form-item')} name={selectItem.key} {...selectItem} noStyle>
          <GeneralSelect
            allowClear={selectObj?.allowClear}
            style={{ width: customizeSize.length > 0 ? customizeSize[0] : '45%' }}
            disabled={disabled}
            dropdownMatchSelectWidth={selectObj?.dropdownMatchSelectWidth}
            onChange={val => {
              selectObj?.handleChange?.(val);
            }}
            {...restOfSelect}
            option={zoneOptions}
          />
        </Form.Item>
      );
    }
  }, [selectObj, form, dateText]);

  const dateComponent = useMemo(() => {
    if (pickerItem?.key) {
      return (
        <Form.Item name={pickerItem?.key} {...pickerItem} noStyle>
          <DatePicker
            style={{ width: customizeSize.length > 1 ? customizeSize[1] : '55%' }}
            showTime={showTime}
            format={format || dateFormat}
            placeholder={placeholder?.[0] ?? t('Please select')}
            disabled={disabled}
            {...rest}
          />
        </Form.Item>
      );
    }
  }, [selectObj, form, dateText]);

  const rangeComponent = useMemo(() => {
    if (pickerItem?.key) {
      return (
        <Form.Item name={pickerItem?.key} {...pickerItem} noStyle>
          <RangePicker
            style={{ width: customizeSize.length > 1 ? customizeSize[1] : '55%' }}
            showTime={showTime}
            format={format || dateFormat}
            placeholder={placeholder}
            disabled={disabled}
            {...rest}
          />
        </Form.Item>
      );
    }
  }, [selectObj, form, dateText]);

  return (
    <InputGroup compact className={className}>
      {selectComponent}
      {!onlyZone && (!pickerType || pickerType === PickerTypeEnums.DATE) && dateComponent}
      {pickerType === PickerTypeEnums.RANGE && rangeComponent}
    </InputGroup>
  );
};

export default FTimezonePicker;
