import React from 'react';

import { Button } from 'antd';

import styles from './MoreButton.module.scss';

interface MoreButtonProps {
  onClick: () => void;
  disabled: boolean;
  style?: React.CSSProperties;
  className?: string;
}

const MoreButton = ({ onClick, disabled, style }: MoreButtonProps) => (
  <Button className={styles.moreButton} onClick={onClick} disabled={disabled} style={style}>
    . . .
  </Button>
);

export default MoreButton;
