import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Button, DatePicker, Form, Input, InputNumber, Popconfirm } from 'antd';

import { array, bool, func, object, string } from 'prop-types';

import { Icon, Table } from '@zhongan/nagrand-ui';

import GeneralSelect from '@market/components/GeneralSelect';
import I18nInstance from '@market/i18n.ts';

import './index.scss';

const EditableCell = ({
  editing,
  dataIndex,
  title,
  inputType,
  record,
  index,
  children,
  validate,
  normalize,
  formItemProps,
  dateFormat,
  selectOptions = [],
  controllerprops = {},
  ...restProps
}) => {
  const getInput = dateFormat => {
    if (inputType === 'number') {
      return <InputNumber {...controllerprops} />;
    }
    if (inputType === 'date') {
      return <DatePicker format={dateFormat || 'YYYY-MM-DD'} {...controllerprops} />;
    }
    if (inputType === 'select') {
      return (
        <GeneralSelect
          {...controllerprops}
          option={
            Array.isArray(selectOptions)
              ? selectOptions.map(i => ({
                  value: i.itemExtend1,
                  label: i.itemName,
                }))
              : []
          }
        />
      );
    }
    return <Input maxLength={64} {...controllerprops} />;
  };

  if (dataIndex === 'action-operation' || dataIndex === 'index') {
    return (
      <td {...restProps} className={editing ? `edit-td-highLight ${restProps.className}` : restProps.className}>
        {children}
      </td>
    );
  }
  let validateOption;
  if (validate) {
    validateOption = validate;
  } else {
    let errorMsg = I18nInstance.t('Please input', {
      ns: ['market', 'common'],
    });
    if (inputType === 'select' || inputType === 'date' || inputType === 'mutiSelect') {
      errorMsg = I18nInstance.t('Please select', { ns: ['market', 'common'] });
    }
    validateOption = [
      {
        trigger: 'onChange',
        rules: [{ required: true, message: errorMsg }],
      },
    ];
  }

  // 图片特殊处理，非编辑状态展示占位符，有值展示图片，没值展示上传
  if (inputType === 'image') {
    if (editing) {
      return (
        <td {...restProps} className={editing ? `edit-td-highLight ${restProps.className}` : restProps.className}>
          {children}
        </td>
      );
    }
    if (record[dataIndex]) {
      return (
        <td {...restProps} className={editing ? `edit-td-highLight ${restProps.className}` : restProps.className}>
          <img style={{ width: 48, height: 48 }} src={record[dataIndex]} />
        </td>
      );
    }
    return (
      <td {...restProps} className={editing ? `edit-td-highLight ${restProps.className}` : restProps.className}>
        <div className="image-place-holder">
          <Icon type="add" />
        </div>
      </td>
    );
  }

  return (
    <td {...restProps} className={editing ? 'edit-td-highLight' : undefined}>
      {editing ? (
        <Form.Item
          style={{ margin: 0 }}
          name={dataIndex}
          validate={inputType === 'select' ? [] : validateOption}
          normalize={normalize}
          {...formItemProps}
          initialValue={record[dataIndex] || (formItemProps ? formItemProps.initialValue : undefined)}
        >
          {getInput(dateFormat)}
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};
const EditableTable = ({
  disabled,
  columns,
  data,
  sortable,
  scroll,
  onSubmit,
  onCancel,
  onAddNew,
  onEditRow,
  onDelete,
  onStartSort,
  onSaveSort,
  onSortChange,
  wrapperClassName,
  onEditingRecord,
  onStateChange,
  getPopupContainer,
  rowKey,
  loading,
  renderRandom,
  customTableTopElement,
  hideAdd,
}) => {
  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState('');
  const [newData, setNewData] = useState(undefined);
  const [sortMode, setSortMode] = useState(false);

  const table_state_editing = 'Editing';
  const table_state_normal = 'Normal';
  const dateFormat = useSelector(state => state.globalConfig.dateFormat);

  useEffect(() => {
    form.resetFields();
  }, [editingKey]);

  const operationCol = {
    title: I18nInstance.t('Actions', { ns: ['market', 'common'] }),
    dataIndex: 'action-operation',
    width: 128,
    fixed: 'right',
    align: 'right',
    editable: true,
    render: (text, record, index) => {
      const disabled = (editingKey !== '' && editingKey !== record.key) || disabled || sortMode;
      let editable = record.key === editingKey;

      if (onEditRow && typeof onEditRow === 'function') {
        /* 行内编辑动作交给父组件时候，新增行，不显示正在编辑状态 */
        editable = false;
      }
      if (disabled) {
        return (
          <span disabled={disabled}>
            <Icon type="edit" disabled={disabled} />
            <Icon type="delete" disabled={disabled} style={{ marginLeft: 16 }} />
          </span>
        );
      }
      if (editable) {
        // 编辑状态显示打勾和打叉Icon
        return (
          <span>
            <Icon type="check-icon" onClick={() => save(record.key, index)} />
            <Icon type="close" style={{ marginLeft: 16 }} onClick={() => cancel(record.key, index)} />
          </span>
        );
      }
      return (
        <span>
          {record.disableEdit ? (
            <Icon type="edit" disabled={disabled} />
          ) : (
            <Icon type="edit" onClick={() => edit(record)} />
          )}
          <Popconfirm
            title={I18nInstance.t('Are you sure to delete this record?', {
              ns: ['market', 'common'],
            })}
            onConfirm={() => {
              deleteRecord(record.key, index);
            }}
            okText={I18nInstance.t('yes', { ns: ['market', 'common'] })}
            cancelText={I18nInstance.t('no', { ns: ['market', 'common'] })}
            getPopupContainer={getPopupContainer}
          >
            <Icon type="delete" style={{ marginLeft: 16 }} />
          </Popconfirm>
        </span>
      );
    },
  };

  const isEditing = record => record.key === editingKey;

  const cancel = (key, index) => {
    setEditingKey('');
    setNewData(undefined);
    if (typeof onStateChange === 'function') {
      onStateChange(table_state_normal);
    }
    if (typeof onCancel === 'function') {
      onCancel(key, index);
    }
  };

  const save = (key, index) => {
    form
      .validateFields()
      .then(row => {
        if (typeof onSubmit === 'function') {
          onSubmit(row, key, index).then(() => {
            setEditingKey('');
            setNewData(undefined);
            if (typeof onStateChange === 'function') {
              onStateChange(table_state_normal);
            }
          });
        }
      })
      .catch(error => {
        console.log(error);
      });
  };

  const deleteRecord = (key, index) => {
    if (typeof onDelete === 'function') {
      onDelete(key, index);
    }
  };

  const edit = record => {
    if (typeof onStateChange === 'function') {
      onStateChange(table_state_editing);
    }
    if (onEditRow && typeof onEditRow === 'function') {
      return onEditRow(record);
    }
    if (onEditingRecord) {
      onEditingRecord(record);
    }
    if (editingKey) return;
    setEditingKey(record.key);
  };

  const add = () => {
    if (typeof onStateChange === 'function') {
      onStateChange(table_state_editing);
    }
    if (onAddNew && typeof onAddNew === 'function') {
      /* 父组件接手新增行的动作 */
      return onAddNew();
    }
    if (onEditingRecord) {
      onEditingRecord();
    }
    const newDataObj = {};
    columns.forEach(i => {
      if (i.editable || i.inputType === 'image') {
        newDataObj[i.dataIndex] = undefined;
      } else {
        newDataObj[i.dataIndex] = I18nInstance.t('--');
      }
    });
    newDataObj.key = 'add';
    newDataObj.index = data.length + 1;
    setNewData(newDataObj);
    setEditingKey('add');
    form.resetFields();
  };

  const sort = () => {
    setSortMode(true);
    if (typeof onStartSort === 'function') {
      onStartSort();
    }
    if (typeof onStateChange === 'function') {
      onStateChange(table_state_editing);
    }
  };

  const saveSort = () => {
    setSortMode(false);
    if (typeof onSaveSort === 'function') {
      onSaveSort();
    }
    if (typeof onStateChange === 'function') {
      onStateChange(table_state_normal);
    }
  };

  const renderAddBtn = disabled => {
    if (hideAdd) {
      return null;
    }
    return (
      <Button onClick={add} disabled={disabled} className="btn-group-above-table" type="dashed">
        + {I18nInstance.t('Add', { ns: ['market', 'common'] })}
      </Button>
    );
  };

  const renderTableTopSection = () => {
    if (customTableTopElement) {
      return (
        <div>
          {renderAddBtn(newData || editingKey || disabled)}
          {customTableTopElement}
        </div>
      );
    }
    return sortable ? (
      <div style={{ overflow: 'hidden' }} className="button-group btn-group-above-table">
        {renderAddBtn(newData || sortMode || editingKey || disabled)}
        {data.length > 1 &&
          (sortMode ? (
            <Button className="sort-btn active" onClick={saveSort} disabled={newData || editingKey || disabled}>
              <Icon type="sort" /> {I18nInstance.t('Save Sort', { ns: ['market', 'common'] })}
            </Button>
          ) : (
            <Button className="sort-btn" onClick={sort} disabled={newData || editingKey || disabled || data.length < 2}>
              <Icon type="sort" /> {I18nInstance.t('Sort', { ns: ['market', 'common'] })}
            </Button>
          ))}
      </div>
    ) : (
      renderAddBtn(newData || editingKey || disabled)
    );
  };

  useEffect(() => {
    if (typeof onStateChange === 'function') {
      onStateChange(table_state_normal);
    }
  }, []);

  const combinedColumns = [...columns];
  if (!disabled) {
    combinedColumns.push(operationCol);
  }

  const components = {
    body: {
      cell: EditableCell,
    },
  };

  const columnsWithProps = combinedColumns.map(col => {
    if (!col.editable && col.inputType !== 'image') {
      return col;
    }
    return {
      ...col,
      onCell: record => ({
        record,
        inputType: col.inputType,
        controllerprops: col.controllerprops,
        formItemProps: col.formItemProps,
        dataIndex: col.dataIndex,
        maxLength: col.maxLength,
        selectOptions: col.selectOptions,
        validate: col.validate,
        title: col.title,
        editing: isEditing(record),
        normalize: col.normalize,
        dateFormat,
      }),
    };
  });

  const tableCommonProps = {
    className: 'table_loading_position',
    loading,
    draggable: sortMode,
    rowKey,
    setDataSource: onSortChange,
    dataSource: newData ? [...data, newData] : data,
    columns: columnsWithProps,
    rowClassName: 'editable-row',
    pagination: false,
    renderRandom, // 触发渲染解决样式错位bug
    emptyType: 'text',
    scroll: scroll || {},
  };

  return (
    <Form form={form}>
      <div className={`base-editable-table ${wrapperClassName}`}>
        {renderTableTopSection()}
        {sortMode ? <Table {...tableCommonProps} /> : <Table {...tableCommonProps} components={components} />}
      </div>
    </Form>
  );
};

EditableTable.propTypes = {
  disabled: bool, // 按钮全部展示为disable
  columns: array, // 表格中展示列的配置项（不需要传action那一列）
  data: array, // 表格中的数据
  sortable: bool, // 是否显示排序的功能
  scroll: object, // table 的滚动设置，原封不动传给antd的table
  onSubmit: func, // 在编辑状态下点击打勾icon的回调函数
  onCancel: func, // 在编辑状态下点击打叉icon的回调函数
  onAddNew: func, // 点击add按钮的回调函数，屏幕组件默认行内新增的行为
  onEditRow: func, // 点击edit按钮的回调函数，屏幕组件默认行内编辑的行为
  onDelete: func, // 确认删除的回调函数
  onStartSort: func, // 点击sort按钮的回调函数
  onSaveSort: func, // 点击save sort按钮的回调函数
  onSortChange: func, // 拖拽排序结束回调
  wrapperClassName: string, // 附加的包装类
  onEditingRecord: func, // 在编辑前的状态抛出
  onStateChange: func,
  getPopupContainer: func,
  rowKey: string,
};
EditableTable.defaultProps = {
  disabled: false,
  columns: [], // 表格中展示列的配置项（不需要传action那一列）
  data: [], // 表格中的数据
  sortable: false, // 是否显示排序的功能
  onSubmit: () => {}, // 在编辑状态下点击打勾icon的回调函数
  onCancel: () => {}, // 在编辑状态下点击打叉icon的回调函数
  onDelete: () => {}, // 确认删除的回调函数
  onStartSort: () => {}, // 点击sort按钮的回调函数
  onSaveSort: () => {}, // 点击save sort按钮的回调函数
  onSortChange: () => {}, // 拖拽排序结束回调
  onStateChange: () => {}, // 表格状态变更的回调 'Normal','Editing'
  wrapperClassName: '', // 默认没有附加的包装类
  loading: false,
  rowKey: 'id',
};

export default EditableTable;
