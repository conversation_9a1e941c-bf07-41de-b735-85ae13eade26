import { Component } from 'react';
import Highlighter from 'react-highlight-words';

import { Button, Input } from 'antd';

import { Icon } from '@zhongan/nagrand-ui';

import I18nInstance from '@market/i18n.ts';

import styles from './columnSearch.module.scss';

class columnSearch extends Component {
  state = {
    searchText: '', // 表头筛选需要
    searchedColumn: '', // 表头筛选需要
  };

  getColumnSearchProps = dataIndex => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => {
      return (
        <div style={{ padding: 8 }}>
          <Input
            ref={node => {
              this.searchInput = node;
            }}
            // placeholder={`Search ${dataIndex}`}
            value={selectedKeys[0]}
            onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => this.handleSearch(selectedKeys, confirm, dataIndex)}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Button
            type="primary"
            onClick={() => this.handleSearch(selectedKeys, confirm, dataIndex)}
            icon={<Icon type="search-icon" />}
            size="small"
            style={{ width: 90, marginRight: 8 }}
          >
            {I18nInstance.t('Search', { ns: 'common' }) || 'Search'}
          </Button>
          <Button onClick={() => this.handleReset(clearFilters, confirm)} size="small" style={{ width: 90 }}>
            {I18nInstance.t('Clear', { ns: 'common' }) || 'Clear'}
          </Button>
        </div>
      );
    },
    filterIcon: filtered => <Icon type="search-icon" style={{ color: filtered ? styles.primaryColor : undefined }} />,
    onFilter: (value, record) => record[dataIndex].toString().toLowerCase().includes(value.toLowerCase()),
    onFilterDropdownVisibleChange: visible => {
      if (visible) {
        setTimeout(() => this.searchInput.select());
      }
    },
    render: text => {
      return this.state.searchedColumn === dataIndex ? (
        <Highlighter
          highlightStyle={{
            backgroundColor: styles.warningColorBg,
            padding: 0,
          }}
          searchWords={[this.state.searchText]}
          autoEscape
          textToHighlight={text.toString()}
        />
      ) : (
        text
      );
    },
  });

  handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    this.state = {
      searchText: selectedKeys[0],
      searchedColumn: dataIndex,
    };
  };

  handleReset = (clearFilters, confirm) => {
    clearFilters();
    this.state = { searchText: '' };
    confirm?.();
  };
}

export default columnSearch;
