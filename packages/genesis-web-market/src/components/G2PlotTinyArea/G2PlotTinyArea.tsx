import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { TinyArea } from '@antv/g2plot';

import styles from './G2PlotTinyArea.module.scss';

interface Props {
  data: number[];
}

export const G2PlotTinyArea = ({ data }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const containerRef = useRef<HTMLDivElement>(null);
  const tinyArea = useRef<TinyArea>(null);

  useEffect(() => {
    if (!containerRef.current) {
      return;
    }
    if (tinyArea?.current) {
      tinyArea.current.update({
        data,
      });
    } else {
      tinyArea.current = new TinyArea(containerRef.current, {
        height: 40,
        width: 128,
        autoFit: false,
        data,
        smooth: true,
        color: styles.successColor,
        line: {
          color: styles.successColorTextHover,
        },
      });

      tinyArea.current.render();
    }
  }, []);

  return <div ref={containerRef} />;
};

export default G2PlotTinyArea;
