import { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Anchor, Tooltip } from 'antd';

import cls from 'classnames';

import { TextEllipsisDetect } from '@zhongan/nagrand-ui';

import ReactDraggable from 'genesis-web-component/lib/components/ReactDraggable';

import DragIcon from '@market/asset/svg/drag.svg';
import PinIcon from '@market/asset/svg/pin.svg';

import styles from './ProductAnchor.module.scss';

const { Link } = Anchor;

interface Props {
  style?: { [key: string]: string };
  navList: {
    href: string;
    title: string;
  }[];
  container?: () => HTMLElement;
}

export const ProductAnchor = ({ style, navList = [], container = () => window }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);
  const [visible, setVisible] = useState(false);
  const [hasPin, setHasPin] = useState(false);

  return (
    <ReactDraggable window={window} list={navList}>
      <div
        className="absolute right-0 z-10 top-[96px]"
        onMouseEnter={() => setVisible(true)}
        onMouseLeave={() => setVisible(false)}
      >
        <div
          className={cls(
            `${
              !hasPin && !visible ? 'visible' : 'hidden'
            } pt-[15.5px] pb-[14.5px] pl-[20px] pr-[14px] rounded-l-[100px] border-solid border-[1px] border-@default-color-bg bg-@white shadow-lg cursor-pointer flex items-center`
          )}
        >
          <DragIcon />
        </div>
        <div
          className={cls(
            `${
              hasPin || visible ? 'visible' : 'hidden'
            } overflow-auto rounded-l-xl border-solid border-[1px] border-@default-color-bg bg-@white shadow-lg`
          )}
        >
          <div className="flex items-center justify-between py-3 px-4 border-@disabled-color border-solid border-b-[0.5px] border-0 sticky top-0 z-10 bg-@white font-medium">
            <span>{t('Quick Menu')}</span>
            <Tooltip title={hasPin ? t('Cancel pin') : t('Pin')} placement="topRight">
              <div className={styles.productDetailAnchorTitlePin} onClick={() => setHasPin(!hasPin)}>
                <PinIcon className={hasPin && 'rotate-45'} />
              </div>
            </Tooltip>
          </div>
          <Anchor
            affix={false}
            showInkInFixed
            className={styles.productDetailAnchor}
            getContainer={container}
            style={style}
          >
            {navList.map(nav => (
              <Link key={nav.href} href={`#${nav.href}`} title={<TextEllipsisDetect text={nav.title} />} />
            ))}
          </Anchor>
        </div>
      </div>
    </ReactDraggable>
  );
};

export default ProductAnchor;
