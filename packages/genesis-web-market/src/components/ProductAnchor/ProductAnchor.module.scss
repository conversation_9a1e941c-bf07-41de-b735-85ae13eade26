.product-detail-anchor {
  overflow: auto;
  width: 260px;
  background-color: var(--white);
  margin-top: 16px;
  margin-bottom: 16px;
  padding: 0 16px;
  max-height: 60vh !important;

  :global {
    .#{$market-prefix}-anchor-ink {
      &::before {
        width: 1px;
      }
    }
    .#{$market-prefix}-anchor-ink-ball {
      width: 1px;
      height: 36px;
      border-width: 0.5px;
      margin-top: -14px;
    }

    .#{$market-prefix}-anchor-link {
      padding: 8px 16px;

      .#{$market-prefix}-anchor-link-title {
        line-height: 20px;
        white-space: normal;
        font-weight: 500;
      }
    }
  }
}
.product-detail-anchor-title-pin {
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover {
    border-radius: 2px;
    background: $form-addon-bg;
    svg {
      path {
        stroke: #08f;
      }
    }
  }
}
