export interface InputConditionFieldsConfig {
  type: 'input';
  label: string;
  fieldKey: string;
}

export interface SelectConditionFieldsConfig {
  type: 'select';
  fieldKey: string;
  label: string;
  options: {
    label: string;
    value: number | string;
  }[];
}

export interface RadioConditionFieldsConfig {
  type: 'radio';
  fieldKey: string;
  label: string;
  options: {
    label: string;
    value: number | string;
  }[];
}

export interface RangepickerConditionFieldsConfig {
  type: 'rangepicker';
  fieldKey: string;
  label: string;
}
export interface InputgroupConditionFieldsConfig {
  type: 'inputgroup';
  fieldKey: string;
  label: string;
  groupList: ConditionFieldsConfig[];
}

export type OutConditionFieldsConfig = Omit<RadioConditionFieldsConfig, 'label'>;

export type ConditionFieldsConfig =
  | InputConditionFieldsConfig
  | SelectConditionFieldsConfig
  | RadioConditionFieldsConfig
  | RangepickerConditionFieldsConfig
  | InputgroupConditionFieldsConfig;
