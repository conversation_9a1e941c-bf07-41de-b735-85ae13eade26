import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, DatePicker, Input, Select } from 'antd';

import classNames from 'classnames/bind';
import { Moment } from 'moment';

import styles from './ConditionSearchPanel.module.scss';
import { ConditionFieldsConfig } from './interface';

const cx = classNames.bind(styles);
const { RangePicker } = DatePicker;

interface Props<T extends Record<string, unknown>> {
  panelConditions: ConditionFieldsConfig[];
  appliedCondition: T;
  onChange: (value: T) => void;
  closePannel: () => void;
  onBeforeSearch?: (value: T) => boolean;
  options?: {
    dateTimeRender?: (date: Moment) => string;
    format?: string;
  };
}

export function ConditionSearchPanel<T extends Record<string, unknown>>({
  panelConditions,
  appliedCondition,
  onChange,
  closePannel,
  onBeforeSearch,
  options,
}: Props<T>): JSX.Element {
  const [t] = useTranslation(['market', 'common']);
  const [panelConditionState, setPanelConditionState] = useState<T>(appliedCondition);

  const changeCondition = useCallback(
    (key: keyof T, value: string | string[] | Moment[]) => {
      setPanelConditionState({
        ...panelConditionState,
        [key]: value,
      });
    },
    [panelConditionState]
  );

  const triggerCondition = useCallback(
    (key: keyof T, value: string) => {
      if (panelConditionState[key] === value) {
        setPanelConditionState({
          ...panelConditionState,
          [key]: undefined,
        });
      } else {
        setPanelConditionState({
          ...panelConditionState,
          [key]: value,
        });
      }
    },
    [panelConditionState]
  );

  const clearConditions = useCallback(() => {
    setPanelConditionState({} as T);
    closePannel();
  }, [closePannel]);

  const submitConditions = useCallback(() => {
    if (typeof onBeforeSearch !== 'function') {
      onChange({
        ...panelConditionState,
      });
      closePannel();
      return;
    }

    const isValid = onBeforeSearch({
      ...panelConditionState,
    });

    if (isValid) {
      onChange({
        ...panelConditionState,
      });
      closePannel();
    }
  }, [onBeforeSearch, closePannel, onChange, panelConditionState]);

  const renderFormItem = useCallback(
    (conditionConfig: ConditionFieldsConfig) => {
      switch (conditionConfig.type) {
        case 'input':
          return (
            <div className={cx('search-item')}>
              <div className={cx('label')}>{conditionConfig.label}</div>
              <Input
                onChange={event => {
                  changeCondition(conditionConfig.fieldKey, event.target.value);
                }}
                value={panelConditionState[conditionConfig.fieldKey] as number | string}
                placeholder={t('Please input')}
                style={{ width: '100%' }}
              />
            </div>
          );
        case 'radio':
          return (
            <div className={cx('search-item')}>
              <div className={cx('label')}>{conditionConfig.label}</div>
              <div className={cx('search-option-wrapper')}>
                {conditionConfig.options?.map((option, index) => (
                  <div
                    key={index}
                    onClick={() => {
                      triggerCondition(conditionConfig.fieldKey, option.value as string);
                    }}
                    className={cx('search-option', {
                      active: panelConditionState[conditionConfig.fieldKey] === option.value,
                    })}
                  >
                    {option.label}
                  </div>
                ))}
              </div>
            </div>
          );
        case 'select':
          return (
            <div className={cx('search-item')}>
              <div className={cx('label')}>{conditionConfig.label}</div>
              <Select
                style={{ width: '100%' }}
                value={panelConditionState[conditionConfig.fieldKey] as string}
                options={conditionConfig.options}
                onChange={(value: string) => {
                  changeCondition(conditionConfig.fieldKey, value);
                }}
                placeholder={t('Please select')}
              />
            </div>
          );
        case 'rangepicker':
          return (
            <div className={cx('search-item')}>
              <div className={cx('label')}>{conditionConfig.label}</div>
              <div>
                <RangePicker
                  onChange={value => changeCondition(conditionConfig.fieldKey, value as Moment[])}
                  value={(panelConditionState?.[conditionConfig.fieldKey] as [Moment, Moment]) || []}
                  style={{ width: '100%' }}
                  format={options?.format || 'YYYY-MM-DD'}
                  placeholder={[t('Start date'), t('End date')]}
                />
              </div>
            </div>
          );
        case 'inputgroup':
          return (
            <div className={cx('search-item')}>
              <div className={cx('label')}>{conditionConfig.label}</div>
              <div className={cx('search-group-box')}>
                {(conditionConfig.groupList || []).map(groupItem => renderFormItem(groupItem))}
              </div>
            </div>
          );
        default:
          return null;
      }
    },
    [changeCondition, options, panelConditionState, t, triggerCondition]
  );

  return (
    <React.Fragment>
      <div className={cx('search-pannel')}>
        {panelConditions?.map(conditionConfig => renderFormItem(conditionConfig))}
        <div className={cx('button-wrapper')}>
          <Button onClick={clearConditions} style={{ marginRight: 16 }}>
            {t('Clear')}
          </Button>
          <Button onClick={submitConditions} type="primary">
            {t('Search')}
          </Button>
        </div>
      </div>
      <div onClick={closePannel} className={cx('search-pannel-mask')} />
    </React.Fragment>
  );
}

export default ConditionSearchPanel;
