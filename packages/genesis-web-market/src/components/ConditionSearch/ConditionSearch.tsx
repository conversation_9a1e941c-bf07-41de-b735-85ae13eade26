import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import AntIcon from '@ant-design/icons';
import { Input, Tooltip } from 'antd';

import classNames from 'classnames/bind';
import { keyBy } from 'lodash-es';
import { Moment } from 'moment';

import { Icon } from '@zhongan/nagrand-ui';

import filterIcon from '@market/asset/svg/customer-loyalty/filter.svg';

import styles from './ConditionSearch.module.scss';
import ConditionSearchPanel from './ConditionSearchPanel';
import {
  ConditionFieldsConfig,
  InputgroupConditionFieldsConfig,
  OutConditionFieldsConfig,
  RadioConditionFieldsConfig,
  SelectConditionFieldsConfig,
} from './interface';
import { findOptionNameFromList } from './util';

const cx = classNames.bind(styles);

interface Props<T extends Record<string, any>> {
  outInputKey: keyof T; // 外部输入框查询条件对应的字段key
  outInputPlaceholder: string; // 外部输入框默认文案
  panelConditions: ConditionFieldsConfig[]; // pannel里面的查询条件
  appliedCondition: T; // 已经选择的搜索条件
  outCondition?: OutConditionFieldsConfig; // 输入框下面的查询条件
  onBeforeSearch?: (changedCondition: T) => boolean; // Search之前的校验函数
  onChange: (changedCondition: T) => void; // 打开pannel点Search的回调函数
  options?: {
    dateTimeRender?: (date: Moment) => string; // 日期格式化函数
    format?: string; // 日期控件中的格式配置
  };
}

export function ConditionSearch<T extends Record<string, any>>({
  outInputKey,
  outInputPlaceholder,
  outCondition,
  panelConditions,
  appliedCondition,
  onChange,
  onBeforeSearch,
  options,
}: Props<T>): JSX.Element {
  const [t] = useTranslation(['market', 'common']);
  const [pannelVisible, setPannelVisible] = useState(false);

  const selectedFieldsKeys = Object.keys(appliedCondition).filter(
    fieldKey => !!appliedCondition[fieldKey as keyof T] && fieldKey !== outInputKey
  );

  const hasPanelConditionChosed = useMemo(() => {
    const panelKeys = panelConditions.map(condition => condition.fieldKey);
    return Object.keys(appliedCondition)
      .filter(key => panelKeys.includes(key))
      .some(key => appliedCondition[key]);
  }, [appliedCondition, panelConditions]);

  const showPannel = useCallback(() => {
    setPannelVisible(true);
  }, []);

  const closePannel = useCallback(() => {
    setPannelVisible(false);
  }, []);

  const totalConditionMap = useMemo(() => {
    const enumConditionWithoutLabel = panelConditions.map(config => {
      if (config.type === 'input') {
        return {
          fieldKey: config.fieldKey,
          type: config.type,
        };
      }
      if (config.type === 'inputgroup') {
        return {
          fieldKey: config.fieldKey,
          type: config.type,
          groupList: config.groupList,
        };
      }
      return {
        fieldKey: config.fieldKey,
        type: config.type,
        options: (config as SelectConditionFieldsConfig)?.options,
      };
    });
    if (outCondition) {
      enumConditionWithoutLabel.push(outCondition);
    }

    return keyBy(enumConditionWithoutLabel, 'fieldKey') as Record<keyof T, ConditionFieldsConfig>;
  }, [outCondition, panelConditions]);

  const renderConditionValue = useCallback(
    (type: string, fieldKey: keyof T) => {
      if (!type || !appliedCondition) {
        return '';
      }

      switch (type) {
        case 'input':
          return (appliedCondition[fieldKey] as string) || '';
        case 'radio':
        case 'select':
          return findOptionNameFromList(
            appliedCondition[fieldKey],
            (totalConditionMap[fieldKey] as RadioConditionFieldsConfig).options
          );
        case 'rangepicker':
          return Array.isArray(appliedCondition[fieldKey])
            ? (appliedCondition[fieldKey] as Moment[])
                .map(item => options?.dateTimeRender?.(item) || item.format())
                .join('~')
            : '';
        case 'inputgroup':
          return (totalConditionMap[fieldKey] as InputgroupConditionFieldsConfig).groupList
            .map(groupItem => {
              if (groupItem.type === 'select') {
                return findOptionNameFromList(appliedCondition[groupItem.fieldKey], groupItem.options);
              }
              if (groupItem.type === 'input') {
                return (appliedCondition[groupItem.fieldKey] as string) || '';
              }
            })
            .join(' ');

        default:
          return '';
      }
    },
    [appliedCondition, options, totalConditionMap]
  );

  const renderAppliedCondition = useCallback(
    (fieldKey: keyof T) => {
      const type = totalConditionMap[fieldKey]?.type || undefined;
      const text: string | number = renderConditionValue(type, fieldKey);

      return (
        <div className={cx('applied-condition')}>
          <Tooltip title={text !== '' ? text : null}>
            <span className={cx('applied-condition-text')}>{text}</span>
          </Tooltip>
          <Icon
            type="close"
            onClick={() => {
              if (type === 'inputgroup') {
                const tempAppliedCondition = {
                  ...appliedCondition,
                };

                (totalConditionMap[fieldKey] as InputgroupConditionFieldsConfig).groupList.forEach(groupItem => {
                  (tempAppliedCondition as Record<string, any>)[groupItem.fieldKey] = undefined;
                });
                onChange(tempAppliedCondition);
                return;
              }
              onChange({
                ...appliedCondition,
                [fieldKey]: undefined,
              });
            }}
            style={{ marginLeft: 8 }}
          />
        </div>
      );
    },
    [totalConditionMap, renderConditionValue, onChange, appliedCondition]
  );

  const renderOutConditions = useCallback(() => {
    if (!outCondition) {
      return null;
    }
    const fieldKey = outCondition.fieldKey;
    return (
      <div className={cx('type-option-wrapper')}>
        {outCondition.options?.map(option => (
          <div
            key={option.value}
            onClick={() => {
              if (option.value === appliedCondition[fieldKey]) {
                return;
              }
              onChange({
                ...appliedCondition,
                [fieldKey]: option.value as string,
              });
            }}
            className={cx('type-option', {
              active: appliedCondition[fieldKey] === option.value,
            })}
          >
            {option.label}
          </div>
        ))}
      </div>
    );
  }, [appliedCondition, outCondition, onChange]);

  const filterConfigedKeys = useCallback(
    (fieldItem: ConditionFieldsConfig) => {
      if (selectedFieldsKeys.includes(fieldItem.fieldKey)) {
        return true;
      }
      if (
        (fieldItem as InputgroupConditionFieldsConfig)?.groupList?.find(groupItem =>
          selectedFieldsKeys.includes(groupItem.fieldKey)
        )
      ) {
        return true;
      }
      return false;
    },
    [selectedFieldsKeys]
  );

  return (
    <div className={cx('search-section')}>
      <div className={cx('search-input-wrapper')}>
        <Icon type="search" />
        <Input
          onChange={event => {
            onChange({
              ...appliedCondition,
              [outInputKey]: event.target.value,
            });
          }}
          value={appliedCondition[outInputKey]}
          className={cx('search-input')}
          placeholder={outInputPlaceholder}
        />
        <div
          onClick={showPannel}
          className={cx('filter-btn', {
            'pannel-opend': pannelVisible,
          })}
        >
          <AntIcon component={filterIcon} style={{ fontSize: 16, height: 16, width: 16, marginRight: 8 }} />
          {t('Filters')}
          <span
            className={cx({
              'filter-dot': hasPanelConditionChosed,
            })}
          />
        </div>
      </div>
      {pannelVisible ? (
        <ConditionSearchPanel
          panelConditions={panelConditions}
          appliedCondition={appliedCondition}
          onChange={onChange}
          closePannel={closePannel}
          onBeforeSearch={onBeforeSearch}
          options={options}
        />
      ) : null}
      {selectedFieldsKeys.length === 0 ? (
        renderOutConditions()
      ) : (
        <div className={cx('applied-condition-wrapper')}>
          {panelConditions
            .filter(filterConfigedKeys)
            .map(conditionItem => renderAppliedCondition(conditionItem.fieldKey as keyof T))}
        </div>
      )}
    </div>
  );
}

export default ConditionSearch;
