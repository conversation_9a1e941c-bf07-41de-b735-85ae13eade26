import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import classNames from 'classnames/bind';

import { Icon } from '@zhongan/nagrand-ui';

import styles from './ModalTitle.module.scss';

const cx = classNames.bind(styles);

interface Props {
  text: string;
  type: 'warning';
}

export const ModalTitle = ({ text, type }: Props): JSX.Element => {
  const [t] = useTranslation(['market', 'common']);

  const icon = useMemo(() => {
    switch (type) {
      case 'warning':
        return <Icon type="exclamation-circle" />;
      default:
        return null;
    }
  }, [type]);

  return (
    <div className={cx('modal-title')}>
      {icon}
      <span className="ml-3">{text}</span>
    </div>
  );
};

// 找个地方放一下Modal的样式
ModalTitle.ModalClassName = styles['shared-modal-confirm'];

export default ModalTitle;
