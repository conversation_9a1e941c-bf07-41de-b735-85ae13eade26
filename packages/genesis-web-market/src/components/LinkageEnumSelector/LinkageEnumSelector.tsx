import { useCallback, useEffect, useState } from 'react';

import { keyBy, remove, union } from 'lodash-es';

import { DoublyLinkedList } from '@market/shared/dataStructure/DoublyLinkedList';

import DynamicLinkageSelectColumn from '../DynamicLinkageSelectColumn';
import { ColumnInfo, ColumnOption } from '../DynamicLinkageSelectColumn/DynamicLinkageSelectColumn';

interface Props {
  linkageConfigs: {
    key: string;
    title: string;
    disabled?: boolean;
  }[];
  extraConfig?: {
    key: string;
    title: string;
  };
  linkageDictkeys: string[];
  options: ColumnOption[];
  selectedEnumsByDictKey: Record<string, string[]>;
  defaultSelectAll?: boolean;
  /**
   * @param keys 以columnKey为key，选中的code数组为值组成的map
   */
  setSelectedEnumsByDictKey: (keys: Record<string, string[]>) => void;
}

function collectChildsCode(list: ColumnOption[], map: Record<string, string[]>) {
  list.forEach((currentValue: ColumnOption) => {
    if (map[currentValue.columnKey]) {
      map[currentValue.columnKey].push(currentValue.key);
    } else {
      map[currentValue.columnKey] = [currentValue.key];
    }
    return collectChildsCode(currentValue?.childList || [], map);
  });
}

function convertBizDictToMap(enumsList: ColumnOption[], map: Record<string, Record<string, ColumnOption>>) {
  if (enumsList?.length === 0) {
    return;
  }
  const list = enumsList.reduce(
    (previous: ColumnOption[], currentValue: ColumnOption) => previous.concat(currentValue.childList || []),
    [] as ColumnOption[]
  );
  if (list?.[0]?.columnKey) {
    map[list?.[0]?.columnKey] = keyBy(list, 'key') as any;
  }
  convertBizDictToMap(list, map);
}

export const LinkageEnumSelector = ({
  options,
  linkageConfigs,
  extraConfig,
  linkageDictkeys,
  selectedEnumsByDictKey,
  setSelectedEnumsByDictKey,
  defaultSelectAll = true,
}: Props): JSX.Element => {
  const [linkedList, setLinkedList] = useState<DoublyLinkedList<ColumnInfo>>();
  const [renderKey, setRenderKey] = useState(Math.random());

  // 根据父级的值，选中子级的值
  const getChildValuesMap = useCallback((parentColumnInfo: ColumnInfo, selectedKey: string) => {
    const selectedEnum = parentColumnInfo.enumsMap[selectedKey];

    const result: Record<string, string[]> = {};
    collectChildsCode(selectedEnum.childList || [], result);

    return result;
  }, []);

  const onSelectKey = useCallback(
    (_columnInfo: ColumnInfo) => (selectedKey: string) => {
      const childValues = getChildValuesMap(_columnInfo, selectedKey);
      const selectedValues = {
        [_columnInfo.dictKey]: [selectedKey],
        ...childValues,
      };

      linkageDictkeys.forEach(key => {
        selectedEnumsByDictKey[key] = union(selectedEnumsByDictKey[key], selectedValues[key]);
      });
      setSelectedEnumsByDictKey({
        ...selectedEnumsByDictKey,
      });
    },
    [linkageDictkeys, getChildValuesMap, selectedEnumsByDictKey, setSelectedEnumsByDictKey]
  );

  const onUnSelect = useCallback(
    (_columnInfo: ColumnInfo) => (unselectedKey: string) => {
      const childValues = getChildValuesMap(_columnInfo, unselectedKey);
      const unselectedValues = {
        [_columnInfo.dictKey]: [unselectedKey],
        ...childValues,
      };

      linkageDictkeys.forEach(key => {
        remove(selectedEnumsByDictKey[key], item => unselectedValues[key]?.includes(item));
      });

      setSelectedEnumsByDictKey({
        ...selectedEnumsByDictKey,
      });
    },
    [getChildValuesMap, linkageDictkeys, selectedEnumsByDictKey, setSelectedEnumsByDictKey]
  );

  const onSelectAll = useCallback(
    (_columnInfo: ColumnInfo) => (checked: boolean, changedkeys: string[]) => {
      const selectFunc = onSelectKey(_columnInfo);
      const unselectFunc = onUnSelect(_columnInfo);
      if (checked) {
        // 全部选中
        changedkeys.forEach(key => {
          selectFunc(key);
        });
      } else {
        // 全部取消
        changedkeys.forEach(key => {
          unselectFunc(key);
        });
      }
    },
    [onSelectKey, onUnSelect]
  );

  useEffect(() => {
    if (linkageConfigs.length > 0 && linkageDictkeys.length > 0 && options.length > 0) {
      const enumsMapByBizDictKey: Record<string, Record<string, ColumnOption>> = {};
      if (options?.[0]) {
        const firstLevelMap: Record<string, ColumnOption> = keyBy(options, 'key') as any;
        enumsMapByBizDictKey[options[0]!.columnKey] = firstLevelMap;
      }
      /**
       * 将结构化拍平成一个map
       * 索引是bizdictKey
       * 值是该bizdict对应的所有枚举,以code为索引组成的map
       *
       * 主要用于处理级联选中和级联取消选中，可以快速定位到操作的元素
       */
      convertBizDictToMap(options, enumsMapByBizDictKey);

      const initLinkedList = new DoublyLinkedList<ColumnInfo>({
        dictKey: linkageDictkeys[0],
        key: linkageConfigs[0].key,
        enumsMap: enumsMapByBizDictKey[linkageDictkeys[0]],
      });

      let currentNode = initLinkedList.head!;
      for (let index = 1; index < linkageConfigs.length; index++) {
        const newNode = initLinkedList.insert(
          {
            dictKey: linkageDictkeys[index],
            key: linkageConfigs[index].key,
            enumsMap: enumsMapByBizDictKey[linkageDictkeys[index]],
          },
          currentNode
        );
        currentNode = newNode!;
      }

      setLinkedList(initLinkedList);
      // 初始化默认选择全部
      if (Object.keys(selectedEnumsByDictKey).length === 0 && defaultSelectAll) {
        onSelectAll(initLinkedList.head!.element)(
          true,
          options.map(enumItem => enumItem.key)
        );
      }
    }
  }, [options]);

  const renderDynamicColumns = useCallback(() => {
    if (!linkedList || linkageConfigs.length === 0) {
      return null;
    }
    const renderArr = [
      <DynamicLinkageSelectColumn
        firstLevel
        options={options}
        columnInfo={linkedList.head!}
        countOfColumns={linkageConfigs.length}
        title={linkageConfigs[0].title}
        hideCheckbox={linkageConfigs[0].disabled}
        hideUnSelected={linkageConfigs[0].disabled}
        selectedKeys={selectedEnumsByDictKey?.[linkedList.head!.element.dictKey] || []}
        onSelect={onSelectKey(linkedList.head!.element)}
        onUnSelect={onUnSelect(linkedList.head!.element)}
        onSelectAll={onSelectAll(linkedList.head!.element)}
        setRenderKey={setRenderKey}
        extraConfig={extraConfig}
      />,
    ];

    let index = 1;
    let currentColumn = linkedList.head!.next;
    while (currentColumn !== linkedList.head && renderArr.length + 1 <= linkageConfigs.length) {
      renderArr.push(
        <DynamicLinkageSelectColumn
          firstLevel={false}
          columnInfo={currentColumn!}
          countOfColumns={linkageConfigs.length}
          title={linkageConfigs[index].title}
          hideCheckbox={linkageConfigs[index].disabled}
          hideUnSelected={linkageConfigs[index].disabled}
          options={currentColumn?.previous?.element?.selectedEnum?.childList || []}
          selectedKeys={selectedEnumsByDictKey?.[currentColumn!.element.dictKey] || []}
          onSelect={onSelectKey(currentColumn!.element)}
          onUnSelect={onUnSelect(currentColumn!.element)}
          onSelectAll={onSelectAll(currentColumn!.element)}
          setRenderKey={setRenderKey}
          extraConfig={extraConfig}
        />
      );
      currentColumn = currentColumn!.next;
      index += 1;
    }
    return renderArr;
  }, [options, extraConfig, linkageConfigs, linkedList, onSelectAll, onSelectKey, onUnSelect, selectedEnumsByDictKey]);

  return <div style={{ display: 'flex', flexWrap: 'nowrap' }}>{renderDynamicColumns()}</div>;
};

export default LinkageEnumSelector;
