.general-steps {
  .process-icon {
    box-shadow: 0 0 0 4px var(--primary-color-20-percent);
    border-radius: 50%;
  }

  :global {
    // filter icon图标的颜色不知道被哪个模块覆盖了，重置一下
    svg {
      fill: currentColor;
    }

    .#{$market-prefix}-steps-item-container {
      display: flex;
      align-items: center;
    }

    .#{$market-prefix}-steps-item-active {
      .#{$market-prefix}-steps-item-title {
        color: var(--text-color);
        font-weight: 700;
      }
    }

    .#{$market-prefix}-steps-item-wait {
      .#{$market-prefix}-steps-item-title {
        color: var(--text-color-tertiary);
      }
    }

    .#{$market-prefix}-steps-icon {
      display: inline-block;
      color: transparent !important;
    }

    .#{$market-prefix}-steps-icon {
      .anticon {
        font-size: 24px;
        line-height: 24px;
        margin-top: 4px;
      }
    }

    .#{$market-prefix}-steps-small
      .#{$market-prefix}-steps-item-custom
      .#{$market-prefix}-steps-item-icon
      .#{$market-prefix}-steps-icon {
      width: 24px;
      height: 24px;
    }
  }
}
