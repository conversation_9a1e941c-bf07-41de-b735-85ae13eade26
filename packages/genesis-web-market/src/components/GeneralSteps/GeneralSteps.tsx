import { ComponentType, SVGProps, useCallback } from 'react';

import Icon from '@ant-design/icons';
import { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';
import { Steps, StepsProps } from 'antd';

import classNames from 'classnames/bind';

import FinishOutlined from '@market/asset/svg/finish.svg';
import ProcessOutlined from '@market/asset/svg/process.svg';
import WaitOutlined from '@market/asset/svg/wait.svg';

import styles from './GeneralSteps.module.scss';

const cx = classNames.bind(styles);

interface Props extends StepsProps {
  current: number;
  steps: {
    title: string;
  }[];
}

export const GeneralSteps = ({ current = 0, steps = [], size }: Props): JSX.Element => {
  const renderStepIcon = useCallback(
    (currentIndex, stepIndex): ComponentType<CustomIconComponentProps | SVGProps<SVGSVGElement>> => {
      if (currentIndex < stepIndex) {
        return WaitOutlined as ComponentType<CustomIconComponentProps | SVGProps<SVGSVGElement>>;
      }
      if (currentIndex > stepIndex) {
        return FinishOutlined as ComponentType<CustomIconComponentProps | SVGProps<SVGSVGElement>>;
      }
      return ProcessOutlined as ComponentType<CustomIconComponentProps | SVGProps<SVGSVGElement>>;
    },
    []
  );

  return (
    <div className={cx('general-steps')}>
      <Steps size={size} current={current}>
        {steps.map((step, stepIndex) => (
          <Steps.Step
            title={step.title}
            icon={
              <Icon
                component={renderStepIcon(current, stepIndex)}
                className={cx({
                  'process-icon': current === stepIndex && size !== 'small',
                })}
              />
            }
          />
        ))}
      </Steps>
    </div>
  );
};

export default GeneralSteps;
