import { ReactNode, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Dropdown, Input, InputNumber, Menu, Tooltip, message } from 'antd';
import type { ColumnProps } from 'antd/es/table';

import classNames from 'classnames/bind';
import { groupBy } from 'lodash-es';

import { DeleteAction, EditAction, Icon, Table } from '@zhongan/nagrand-ui';

import GeneralSelect from '@market/components/GeneralSelect';

import styles from './FundAllocationTable.module.scss';

const cx = classNames.bind(styles);
interface Option {
  label: string;
  value: number;
}

interface FundAllocationBase {
  premiumType: number;
  fundCode: string;
  fundAllocation: number;
  amount?: number;
  key: number;
}

export enum PremiumType {
  PlannedPremium = 1,
  RegularTopUp = 2,
  SingleTopUp = 3,
}

type FundAllocationRow =
  | (FundAllocationBase & {
      isNew?: false;
      rowSpan?: number;
      totalAllocation: number;
    })
  | (FundAllocationBase & {
      isNew: true;
      rowSpan?: number;
    });

interface Props {
  premiumTypeOptions: Option[];
  fundOptions: {
    label: string;
    value: string;
  }[];
  list: FundAllocationBase[];
  onChange: (list: FundAllocationBase[]) => void;
  readOnly?: boolean;
  fixedAllocation?: boolean;
  canConfigAmount?: (record: FundAllocationBase) => boolean;
  onAddPackageDefinitions: (premiumType: PremiumType) => void;
  onDeletePackageDefinitions: (premiumType: PremiumType) => void;
}

export const FundAllocationTable = ({
  premiumTypeOptions,
  fundOptions,
  list,
  readOnly,
  fixedAllocation,
  onAddPackageDefinitions,
  onDeletePackageDefinitions,
  onChange,
  canConfigAmount,
}: Props): JSX.Element => {
  const [t] = useTranslation(['common']);
  const [newObj, setNewObj] = useState<FundAllocationRow>();
  const [editFormRecord, setEditFormRecord] = useState<Record<string, any>>({});
  const [editingKey, setEditingKey] = useState<number>();

  const isEditStatus = Boolean(newObj || editingKey);

  const combinedList = useMemo(() => {
    const orderedList: FundAllocationRow[] = [];
    const listmapBypremiumType = groupBy(newObj ? [...list, newObj] : list, 'premiumType');
    const totalAllocationMap: Record<string, number> = {};

    Object.keys(listmapBypremiumType).forEach(type => {
      totalAllocationMap[type] = listmapBypremiumType[type].reduce(
        (previous, current) => (10000 * previous + 10000 * current.fundAllocation) / 10000,
        0
      );
    });

    Object.keys(listmapBypremiumType).forEach(type => {
      orderedList.push(
        ...(listmapBypremiumType[type]?.map((item: FundAllocationBase & { isNew?: boolean }, index) => ({
          ...item,
          isNew: item.isNew,
          totalAllocation: totalAllocationMap[type],
          rowSpan: index === 0 ? listmapBypremiumType[type]?.length : undefined,
        })) || [])
      );
    });

    return orderedList;
  }, [list, newObj]);

  const premiumTypeRender = useCallback(
    (text: number, record: FundAllocationRow): ReactNode => {
      const name = premiumTypeOptions.find(option => option.value === text)?.label;

      let icon = (
        <Icon
          type="info-circle"
          style={{
            fontSize: 14,
            verticalAlign: '-2px',
            marginRight: 10,
            color: styles.textColorTertiary,
          }}
        />
      );
      if (!record.isNew) {
        if (record.totalAllocation === 100) {
          icon = (
            <Icon
              type="check-circle"
              style={{
                fontSize: 14,
                verticalAlign: '-2px',
                marginRight: 10,
                color: styles.successColor,
              }}
            />
          );
        } else {
          icon = (
            <Tooltip title={t('Fund allocation percent summation must be 100%. ')}>
              <Icon
                type="info-circle"
                style={{
                  fontSize: 14,
                  verticalAlign: '-2px',
                  marginRight: 12,
                  color: styles.warningColor,
                }}
              />
            </Tooltip>
          );
        }
      }
      if (record.rowSpan) {
        return {
          children: readOnly ? (
            name
          ) : (
            <span>
              {icon} {name}
            </span>
          ),
          props: {
            rowSpan: record.rowSpan,
            style: {
              borderRight: `1px solid ${styles.borderDefault}`,
            },
          },
        };
      }
      return {
        children: '',
        props: {
          rowSpan: 0,
        },
      };
    },
    [premiumTypeOptions, readOnly, t]
  );

  const deleteByKey = useCallback(
    (key: number) => {
      const tempList = [...list];
      tempList.splice(
        tempList.findIndex(item => item.key === key),
        1
      );
      onChange([...tempList]);
    },
    [list, onChange]
  );

  const renderDeleteIcon = useCallback(
    (record: FundAllocationRow) =>
      isEditStatus ? (
        <DeleteAction
          style={{
            color: styles.textColorSecondary,
            cursor: 'not-allowed',
          }}
        />
      ) : (
        <DeleteAction
          doubleConfirmType="popconfirm"
          onClick={() => {
            if (fixedAllocation) {
              onDeletePackageDefinitions(record.premiumType);
              return;
            }
            deleteByKey(record.key);
          }}
          deleteConfirmContent={t('Are you sure to delete this record?')}
        />
      ),
    [deleteByKey, isEditStatus, t, onDeletePackageDefinitions, fixedAllocation]
  );

  const actionColumnConfig = useMemo(
    () => ({
      title: t('Actions'),
      width: 88,
      render: (text: number, record: FundAllocationRow, index: number) => {
        if (record.isNew || record.key === editingKey) {
          return (
            <span>
              <Icon
                type="check-icon"
                onClick={() => {
                  if (!editFormRecord.fundCode) {
                    message.error(t('Please select Fund'));
                    return;
                  }
                  if (!editFormRecord.fundAllocation) {
                    message.error(t('Please input Premium Allocation'));
                    return;
                  }
                  if (
                    list.find(
                      item =>
                        item.fundCode === editFormRecord.fundCode &&
                        item.premiumType === record.premiumType &&
                        record.key !== editingKey
                    )
                  ) {
                    message.error(t('Please do not select the same fund for the same Premium Type'));
                    return;
                  }
                  if (record.isNew) {
                    setNewObj(undefined);
                    setEditFormRecord({});
                    onChange([
                      ...list,
                      {
                        premiumType: record.premiumType,
                        fundCode: editFormRecord.fundCode,
                        fundAllocation: editFormRecord.fundAllocation,
                        amount: record.amount,
                        key: Math.random(),
                      },
                    ]);
                  }
                  if (record.key === editingKey) {
                    setEditingKey(undefined);
                    setEditFormRecord({});
                    const editIndex = list.findIndex(item => item.key === editingKey);
                    list[editIndex] = {
                      premiumType: record.premiumType,
                      fundCode: editFormRecord.fundCode,
                      fundAllocation: editFormRecord.fundAllocation,
                      amount: record.amount,
                      key: record.key,
                    };

                    onChange([...list]);
                  }
                }}
              />

              <Icon
                type="close"
                style={{ marginLeft: 16 }}
                onClick={() => {
                  setNewObj(undefined);
                  setEditingKey(undefined);
                  setEditFormRecord({});
                }}
              />
            </span>
          );
        }
        if (fixedAllocation) {
          return renderDeleteIcon(record);
        }
        return (
          <span>
            {isEditStatus ? (
              <EditAction
                style={{
                  color: styles.textColorSecondary,
                  cursor: 'not-allowed',
                }}
              />
            ) : (
              <EditAction
                onClick={() => {
                  setEditingKey(record.key);
                  setEditFormRecord({ ...record });
                }}
              />
            )}
            {renderDeleteIcon(record)}
          </span>
        );
      },
    }),
    [editFormRecord, editingKey, isEditStatus, list, fixedAllocation, onChange, renderDeleteIcon, t]
  );

  const columns = useMemo(() => {
    const tempColumns: ColumnProps<FundAllocationRow>[] = [
      {
        dataIndex: 'premiumType',
        title: t('Premium Type'),
        width: 175,
        render: premiumTypeRender,
      },
      {
        dataIndex: 'amount',
        title: t('Amount'),
        width: 120,
        className: cx('amount-column'),
        render: (text: number, record: FundAllocationRow) => {
          if (canConfigAmount?.(record)) {
            if (record.rowSpan) {
              // 新增的时候不可以编辑amount，数据流向有问题
              if (record.isNew) {
                return null;
              }
              return {
                children: (
                  <InputNumber
                    value={record.amount}
                    onChange={value => {
                      const editIndex = list.findIndex(item => item.key === record.key);
                      list[editIndex] = {
                        premiumType: record.premiumType,
                        fundCode: record.fundCode,
                        fundAllocation: record.fundAllocation,
                        amount: value!,
                        key: record.key,
                      };

                      onChange([...list]);
                    }}
                    min={0}
                    placeholder={t('Please input')}
                    style={{ width: 88 }}
                  />
                ),
                props: {
                  rowSpan: record.rowSpan,
                },
              };
            }
            return {
              children: '',
              props: {
                rowSpan: 0,
              },
            };
          }

          return '';
        },
      },
      {
        dataIndex: 'fundCode',
        title: t('Fund'),
        width: '250px',
        render: (text: string, record: FundAllocationRow) => {
          if (record.isNew || record.key === editingKey) {
            return (
              <GeneralSelect
                option={fundOptions}
                style={{ width: 210 }}
                value={editFormRecord.fundCode}
                onChange={value => {
                  setEditFormRecord({
                    ...editFormRecord,
                    fundCode: value,
                  });
                }}
              />
            );
          }
          const fundName = fundOptions.find(option => option.value === text)?.label;
          return fundName || text;
        },
      },
      {
        dataIndex: 'fundAllocation',
        title: t('Premium Allocation'),
        width: '167px',
        render: (text: number, record: FundAllocationRow) => {
          if (record.isNew || record.key === editingKey) {
            return (
              <Input.Group compact>
                <InputNumber
                  value={editFormRecord.fundAllocation}
                  onChange={value => {
                    setEditFormRecord({
                      ...editFormRecord,
                      fundAllocation: value,
                    });
                  }}
                  max={100}
                  min={0}
                  placeholder={t('Please input')}
                  style={{ width: 87 }}
                />
                <span
                  style={{
                    width: 48,
                    background: styles.formAddonBg,
                    textAlign: 'center',
                    lineHeight: '32px',
                    fontWeight: 700,
                  }}
                >
                  %
                </span>
              </Input.Group>
            );
          }

          return `${text} %`;
        },
      },
    ];
    if (!readOnly) {
      tempColumns.push(actionColumnConfig);
    }

    return tempColumns;
  }, [
    t,
    premiumTypeRender,
    readOnly,
    canConfigAmount,
    list,
    onChange,
    editingKey,
    fundOptions,
    editFormRecord,
    actionColumnConfig,
  ]);

  const premiumTypeMenus = useCallback(
    () => (
      <Menu>
        {premiumTypeOptions.map(option => (
          <Menu.Item
            onClick={() => {
              if (fixedAllocation) {
                onAddPackageDefinitions(option.value);
                return;
              }
              setNewObj({
                isNew: true,
                premiumType: option.value,
                fundCode: '',
                fundAllocation: 0,
                rowSpan: 1,
                amount: undefined,
                key: Math.random(),
              });
            }}
          >
            {option.label}
          </Menu.Item>
        ))}
      </Menu>
    ),
    [premiumTypeOptions, onAddPackageDefinitions, fixedAllocation]
  );

  const renderAddBtn = useCallback(() => {
    if (readOnly) {
      return null;
    }
    if (isEditStatus) {
      return (
        <Button disabled style={{ marginBottom: 8, width: '100%' }} icon={<Icon type="add" />}>
          {t('Add')}
        </Button>
      );
    }
    return (
      <Dropdown dropdownRender={premiumTypeMenus}>
        <Button style={{ marginBottom: 8, width: '100%' }} icon={<Icon type="add" />}>
          {t('Add')}
        </Button>
      </Dropdown>
    );
  }, [isEditStatus, premiumTypeMenus, readOnly, t]);

  return (
    <div>
      {renderAddBtn()}
      <Table columns={columns} dataSource={combinedList} pagination={false} />
    </div>
  );
};

export default FundAllocationTable;
