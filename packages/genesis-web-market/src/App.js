import { Suspense, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Route, Routes, useLocation, useNavigate } from 'react-router-dom';

import { Spin } from 'antd';

import { Theme } from '@zhongan/nagrand-ui';
import '@zhongan/nagrand-ui/dist/antd-reset.scss';
import '@zhongan/nagrand-ui/dist/resources/theme/variables.css';

import { CommonAntdConfigProvider, CommonNagrandConfigProvider, Copyright } from 'genesis-web-component/lib/components';
import { LazySuspense } from 'genesis-web-component/lib/components/Lazy';
import copyrightWhiteList from 'genesis-web-shared/lib/constant/copyrightWhiteList';
import { L10nProvider } from 'genesis-web-shared/lib/l10n';
import {
  getZoneInfoPromise,
  selectClientConfig,
  selectGlobalConfig,
  selectImageVersion,
  selectTenant,
  selectUserState,
} from 'genesis-web-shared/lib/redux';

import { saveTenantTimeFormatByZeus } from './redux/action';
import {
  selectEnableSecurity,
  selectEnums,
  selectLegacyLocale,
  selectPermissionCheckMap,
  selectTenantTimeFormatByZeus,
  selectTimeZone,
} from './redux/selector';
import { useActivateRoutes } from './router';
import './style/antd-reset.scss';
import './style/index.scss';
import './style/reset.css';
import './tailwind.scss';

const AntdPrefix = 'market-ant4';

const PageWrapper = ({ route, tenant, enums, setGlobalSpin }) => {
  if (window.__POWERED_BY_QIANKUN__) {
    window.fixStyleBugProduct();
  }
  const location = useLocation();
  const navigate = useNavigate();

  const RouteComponent = route.component;
  return (
    <RouteComponent
      clearSpin={() => setGlobalSpin(false)}
      loadSpin={() => setGlobalSpin(true)}
      tenant={tenant}
      enums={enums}
      navigate={navigate}
      location={location}
    />
  );
};

const App2 = props => {
  const [globalSpin, setGlobalSpin] = useState(false);
  const dispatch = useDispatch();

  const globalConfig = useSelector(selectGlobalConfig);
  const userInfo = useSelector(selectUserState);
  const locale = useSelector(selectLegacyLocale);
  const tenant = useSelector(selectTenant);
  const imageVersion = useSelector(selectImageVersion);
  const theme = props?.theme || Theme.Default;
  const messageDuration = props?.messageDuration;
  const clientConfig = useSelector(selectClientConfig);
  const tenantTimeFormatByZeus = useSelector(selectTenantTimeFormatByZeus);
  const permissionMap = useSelector(selectPermissionCheckMap);
  const security = useSelector(selectEnableSecurity);
  const enums = useSelector(selectEnums);
  const timeZone = useSelector(selectTimeZone);

  useEffect(() => {
    dispatch(getZoneInfoPromise(clientConfig.tenantLocalConfigEnable));
  }, [clientConfig]);

  useEffect(() => {
    document.getElementById('micro').className = `${locale}`;
  }, [locale]);

  useEffect(() => {
    if (tenant) {
      dispatch(saveTenantTimeFormatByZeus(tenant));
    }
  }, [dispatch, tenant]);

  const routes = useActivateRoutes({
    permissions: permissionMap,
    security,
  });

  return (
    <L10nProvider
      tenantDateTimeFormat={globalConfig.dateTimeFormat || ''}
      offsetTime={userInfo?.offsetTime || 0}
      timeFormat={tenantTimeFormatByZeus?.timeFormat}
      timeRangeFormat={tenantTimeFormatByZeus?.timeRangeFormat}
      zoneInfoList={timeZone?.zoneOptionList || []}
    >
      <CommonAntdConfigProvider
        currentLang={locale}
        prefixCls={AntdPrefix}
        currentTheme={theme}
        messageDuration={messageDuration}
      >
        <CommonNagrandConfigProvider currentLang={locale} prefixCls={AntdPrefix}>
          <Suspense fallback={<LazySuspense />}>
            <Spin size="large" spinning={globalSpin} wrapperClassName="market-global-loading-container">
              <div className="content market">
                <Routes>
                  {routes.map(route => (
                    <Route
                      path={route.path}
                      element={
                        <PageWrapper route={route} tenant={tenant} enums={enums} setGlobalSpin={setGlobalSpin} />
                      }
                    />
                  ))}
                </Routes>
                <Copyright whiteList={copyrightWhiteList} version={imageVersion} />
              </div>
            </Spin>
          </Suspense>
        </CommonNagrandConfigProvider>
      </CommonAntdConfigProvider>
    </L10nProvider>
  );
};

export default App2;
