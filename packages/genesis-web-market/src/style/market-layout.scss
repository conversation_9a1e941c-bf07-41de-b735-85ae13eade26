#subapp-container {
  height: 100%;

  > div {
    height: 100% !important;
  }
}

#micro {
  height: 100%;
}

.market {
  color: var(--text-color);

  .market-layout {
    position: relative;
    display: block;
    height: 100%;
    overflow: hidden;

    .market-sider {
      border-right: 1px solid var(--table-header-select-bg);
      background: var(--white);
      height: calc(100% - 50px);
      min-height: 100%;
      overflow: hidden;
    }

    .market-content.market-ant4-layout-content {
      overflow: auto;
      background-color: $white;
      margin-bottom: 71px;

      // 如果页面上右边有锚点时，为了修正定位，把margin改成透明填充
      .anchor-top-fix {
        height: 16px;
        background: transparent;
      }

      .right-content-wrapper {
        background: $white;

        // 右边有锚点的时候需要加一层这个容器
        .right-content {
          min-width: 792px;
          margin-right: 206px;
          flex-grow: 1;
          background-color: var(--white);
        }
      }

      .anchor-top-fix + .right-content-wrapper {
        margin-top: 0;
      }
    }

    .nagrand-editing-row {
      .market-ant4-table-cell {
        .market-ant4-row.market-ant4-form-item-row {
          display: inline-block;
        }
      }
    }
  }

  // section 组件在market中间距修改
  .base-section .base-section-title {
    padding-bottom: 16px;
    margin-bottom: 16px;
  }

  .base-section + .base-section {
    margin-top: 16px;
  }
}

// goods-validation页面隐藏header和菜单
.system-layout.goods.validation {
  .sider.market-ant4-layout-sider {
    display: none;
  }

  .market-ant4-layout-header {
    display: none;
  }

  .content-layout .mataince-content .content {
    margin: 0;
  }

  .content-layout.market-ant4-layout {
    padding-top: 0;
  }
}

.market-global-loading-container {
  height: 100%;
}

.market-field-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 40px;

  // antd4-form
  .market-ant4-form-item-control-input-content,
   // antd-compatible-form
   .market-ant4-legacy-form-item-children {
    >.market-ant4-input-group-wrapper,
     // Select
     >.market-ant4-select,
     // Input
     >.market-ant4-input:not(.market-ant4-input-group),
     // InputNumber
     >.market-ant4-input-number:not(.market-ant4-input-group),
     // InputNumber with addon
     >.market-ant4-input-number-group-wrapper {
      width: 100% !important;
    }

    // NumberRange
    > .market-ant4-input-group {
      display: flex;

      > .market-ant4-input-number,
      > .market-ant4-input-group-wrapper {
        width: calc((100% - 30px) / 2) !important;
        flex: 1;
      }

      > .market-ant4-input-number-group-wrapper {
        flex: 1;

        .market-ant4-input-number {
          border-radius: 0;
        }
      }

      .market-ant4-input-group-addon {
        padding: 0 18px;
      }

      width: 100% !important;

      // NumberRange with addonAfter
      &[class*='inputGroupRange'] {
        > .market-ant4-input-number {
          width: calc((100% - 95px) / 2) !important;
          flex: none;
        }
      }
    }
  }

  .form-item-horizontal {
    grid-column-start: 1;
    grid-column-end: -1;

    .market-ant4-form-item-row {
      flex-direction: row;
      align-items: center;
      gap: 8px;

      .market-ant4-form-item-control {
        width: auto;
      }

      .market-ant4-form-item-label {
        margin-bottom: 0;
      }
    }
  }

  .form-item-column-2 {
    grid-column: span 2;
  }

  .form-item-row-full {
    grid-column: span 3;
  }
}
