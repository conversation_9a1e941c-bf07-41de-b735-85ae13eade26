//  在全局的样式文件中
.#{$market-prefix}-spin-container {
  .nagrand-header-container {
    border-bottom: 1px solid var(--table-header-select-bg);
  }
  .nagrand-back-btn {
    padding: var(--gap-xss) !important;
  }
}
// 如果用到nagrand Modal, 那antd5 modal样式问题覆盖
.#{$market-prefix}-modal.nagrand-modal {
  // 表单label颜色
  .#{$market-prefix}-form-item .#{$market-prefix}-form-item-label > label {
    color: var(--text-color);
  }
}
.#{$market-prefix}-radio-checked:not(.#{$market-prefix}-radio-disabled) {
  .#{$market-prefix}-radio-inner {
    background-color: white;
    &::after {
      background-color: var(--primary-color);
      transform: scale(0.5);
    }
  }
}

.#{$market-prefix}-modal-confirm .#{$market-prefix}-modal-confirm-btns {
  margin-top: 24px;
}

.horizontal-radio-group {
  margin-bottom: 0px;

  .#{$market-prefix}-row {
    flex-direction: row !important;
    flex-wrap: nowrap;
    align-items: baseline;
  }

  .#{$market-prefix}-col {
    margin-right: 16px !important;
  }

  .#{$market-prefix}-form-item-label {
    padding-right: 24px !important;
    white-space: nowrap !important;
  }

  .#{$market-prefix}-form-item-control {
    width: auto !important;
  }
}

.#{$market-prefix}-form-horizontal .#{$market-prefix}-form-item-vertical .#{$market-prefix}-form-item-control {
  flex: auto;
}
