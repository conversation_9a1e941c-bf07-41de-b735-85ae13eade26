@import '@zhongan/nagrand-ui/dist/resources/styles/fonts/font.scss';
@import 'market-layout.scss';

// @legacy: antd form compatible v3 to v4 style -----------------------------------
.market-ant4-legacy-form-item {
  margin-bottom: 24px;
  display: block !important;
}

/* 修改滚动条样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.market-ant4-legacy-form-item-required::before {
  color: $error-color;
  content: '*';
  display: inline-block;
  font-family: SimSun, sans-serif;
  font-size: 14px;
  line-height: 1;
  margin-right: 4px;
}

.market-ant4-legacy-form-item-with-help {
  .market-ant4-select-selector {
    border-color: $error-color !important;
  }

  .market-ant4-legacy-form-explain {
    color: $error-color;
  }
}

// --------------------------------------------------------------------------------

// ----------拖拽样式----------
.row-dragging {
  display: flex;
  align-items: center;
  background: var(--table-body-hover-bg) !important;
  border: 1px solid var(--border-color-base);
}

.row-dragging td {
  padding: 0 var(--gap-md);
  background: inherit;
}

// ----------拖拽样式 end----------

.market {
  height: 100%;

  &.system-layout .content-layout .mataince-content .content {
    margin: 0 !important;
    color: var(--text-color);
  }

  .table-record-counter {
    margin-top: -43px;
    color: var(--text-color-tertiary);
  }

  .bottom-copyright {
    color: var(--text-color-tertiary);
    text-align: center;
    margin-top: 240px;
    margin-bottom: 160px;
  }

  .market-ant4-select.market-ant4-select-in-form-item,
  .market-ant4-input:not(textarea) {
    width: 240px;
  }

  .nagrand-query-form {
    .market-ant4-select.market-ant4-select-in-form-item,
    .market-ant4-input {
      // 上方样式影响到了组件库，覆盖一下
      width: 100%;
    }
  }

  .long-add-btn,
  .long-add-btn:hover {
    width: 100%;
    border-radius: 4px;
    border: 1px dashed var(--border-light);
    text-align: center;
    font-size: 14px;
    color: var(--text-color);
    margin: 0px 0px 8px 0px;
    cursor: pointer;

    &:hover {
      color: var(--primary-color);
      border-color: var(--primary-color);
    }
  }

  .long-add-btn[disabled],
  .long-add-btn[disabled]:hover {
    color: var(--text-disabled-color);
    border: 1px dashed var(--border-light);
    background-color: var(--disabled-bg);
  }

  .market-drawer {
    color: var(--text-color);

    .drawer-title {
      margin-bottom: 32px;
      font-weight: bold;
      padding-bottom: 20px;
      border-bottom: 1px dashed var(--border-light);
    }

    .drawer-product-info-section {
      color: var(--text-color);
      margin: 24px 0 32px;
      font-weight: bold;
      height: 20px;
      line-height: 20px;

      span + span {
        margin-left: 56px;
      }
    }

    .drawer-content {
      display: flex;
      margin-bottom: 24px;

      & > div {
        width: 240px;

        .label-name {
          margin-bottom: 8px;
        }
      }

      & > div + div {
        margin-left: 20px;
      }
    }
  }

  .drawer-foot {
    z-index: 30;
    position: fixed;
    right: 0;
    bottom: 0;
    width: 1024px;
    border-top: 1px solid var(--border-light);
    background: var(--white);
    text-align: right;
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 32px;

    button + button {
      margin-left: 24px;
    }

    button {
      padding: 0 16px;
      height: 40px;
    }
  }

  .single-word-confirm-btn {
    cursor: pointer;
    display: inline-block;
    width: 80px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    border-radius: 4px;
    color: var(--white);
    background-color: var(--primary-color);
  }

  .confirm-btn-disabled {
    opacity: 0.5;
  }

  .bottom-action-bar {
    position: absolute;
    left: 208px;
    bottom: 0;
    right: 0;
    padding: 0 32px;
    background-color: var(--white);
    border-top: 1px solid var(--table-header-select-bg);
    height: 72px;
    line-height: 72px;
    color: var(--text-color);
    z-index: 99;

    & > span {
      cursor: pointer;
    }

    button {
      width: 80px;
      float: right;
      margin-top: 16px;
      margin-left: 16px;
    }
  }

  .market-ant4-layout-sider-children {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .layout-copy-right {
    padding: 28px 0 16px;
    text-align: center;
    font-size: 12px;
    color: var(--text-color-tertiary);
    background: transparent;

    .version {
      margin-top: 12px;
      color: $disabled-color;
    }
  }
}

button.enum-configure-btn {
  background-color: transparent;
  border: 0;
  border-image-width: 0;
  border-radius: 0;
  box-shadow: none;
  color: var(--primary-color);
}

.market-ant4-spin-nested-loading,
.market-ant4-spin-container {
  height: 100%;
}

.market-ant4-table {
  // 组件库设置了scrollbar属性，导致 ::-webkit-scrollbar 被覆盖，需要改为 inherit
  scrollbar-color: inherit !important;

  // 子表格不需要边框
  .market-ant4-table-wrapper {
    border: none;
  }
}

.market-ant4-table-tbody > tr.market-ant4-table-row > td {
  border-bottom: 1px solid $disabled-color;
}

.market-ant4-table-tbody > tr.market-ant4-table-row:last-child > td {
  border-bottom: none;
}

.btn-group-above-table {
  margin-bottom: var(--gap-xs);
}

.query-form-container .bottom-section .market-ant4-col-8 {
  margin-right: 20px;
  max-width: 360px;
  min-width: 240px;
  width: 25%;
}
