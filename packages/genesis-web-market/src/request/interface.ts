import { Ref } from 'react';

import { FormInstance } from 'antd';

export enum PaymentFrequencyBaseDict {
  SinglePremium = '1',
  Yearly = '2',
  SemiYearly = '3',
  Quarterly = '4',
  Monthly = '5',
  Daily = '6',
  EveryNYears = '7',
}

export interface BizDict {
  pureName?: string;
  itemName: string;
  itemExtend1: string;
  dictValueName: string;
  dictValue: string;
  dictKeyName: string;
  dictDesc?: string;
  enumItemName: string;
  childList?: BizDict[];
}

export interface ProductProps {
  id: number;
}

export interface ParticipatingProductProps<T> {
  disabled: boolean;
  editStatus: boolean;
  myRef: any;
  participatingProductData: T;
  saveParticipatingProduct: (participatingData: T[]) => void;
  refInstance: Ref<{ form: FormInstance }>;
}

export interface AllocationTable {
  [index: string]: number | string | undefined | boolean;
  id?: number;
  packageId?: number | string;
  premiumType?: number | string;
  fundCode?: number | string;
  premiumAllocationByPercent?: number | string;
  key?: number | string;
  editing?: boolean;
  length?: number;
}

export interface PremiumAllocationData {
  [index: string]: number | string | undefined | AllocationTable[];
  packageId: number;
  plannedPremiumRule: number;
  plannedPremiumCalculationMethod?: number;
  investmentPremiumAllocationList?: AllocationTable[];
}
export interface Fund {
  code: string;
  name: string;
}
export interface InvestmentProductProps<T> {
  disabled: boolean;
  editStatus: boolean;
  packageId: string | number;
  investmentPremiumAllocationData: T;
  saveInvestmentData: (data: AllocationTable[]) => void;
  refInstance: Ref<{ form: FormInstance }>;
}

export enum PaymentDeferPeriodTypeEnum {
  Immediate = '1',
  DeferredFixedPolicyYear = '2',
}

export enum PaymentPeriodTypeEnum {
  WholeLife = '1',
  ForCertainYear = '2',
  UpToCertainAge = '3',
}

export interface Relation {
  type: RelationTypeEnum;
  relatedItem: string;
  relatedItemValue: string[];
}

export enum RelationTypeEnum {
  Show = 'show',
}

export enum PremiumTypeEnum {
  Protection = '1',
  Saving = '2',
  SingleTopUp = '3',
  RegularTopUp = '4',
  All = '5',
  PlannedPremium = '6',
}

export enum OptEnum {
  Add = 'add',
  Edit = 'edit',
  View = 'view',
}

export enum Mode {
  Add = 'add',
  Edit = 'edit',
  View = 'view',
}

export interface DrawerRefProps {
  isOpen?: boolean;
  setOpen: () => void;
}
