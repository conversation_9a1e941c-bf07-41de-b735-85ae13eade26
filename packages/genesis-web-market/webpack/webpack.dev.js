const path = require('path');
const fs = require('fs');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');
const { lessVars, prependDataNagrand } = require('@zhongan/nagrand-ui');
const AntdDayjsWebpackPlugin = require('@ant-design/moment-webpack-plugin');

const { setupProxyList, proxyTarget, proxyRouter } = require('../../../config/proxy.config');
const { headScripts } = require('../../../config/proxy.script');

const template = path.resolve(__dirname, '../../../public/index.html');
const templateContent = fs.readFileSync(template, { encoding: 'utf-8' });

const marketLessVars = { ...lessVars };
delete marketLessVars['@ant-prefix'];

module.exports = {
  mode: 'development',
  // devtool: 'cheap-module-eval-source-map',
  devtool: 'eval-cheap-source-map',
  // devtool: false,
  amd: false,
  // node: false,
  entry: path.resolve(__dirname, '../src/index.js'),
  output: {
    path: path.resolve(__dirname, 'dist'),
    publicPath: '/',
    filename: '[name].bundle.js',
    chunkFilename: '[id].chunk.js',
    // multiple asset name conflict
    assetModuleFilename: '[name].[contenthash:8][ext]',
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      '@market': path.resolve(__dirname, '../src'),
    },
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx|mjs|mjsx|ts|tsx)$/,
        include: [
          path.resolve(__dirname, './src'),
          /genesis-web-/,
          /product-types/, // 处理后端接口类型文件
          /market-types/,
          /calculator-types/,
          /metadata-types/,
        ],
        resolve: {
          fullySpecified: false,
        },
        use: [
          {
            loader: require.resolve('babel-loader'),
            options: {
              cacheDirectory: true,
              cacheCompression: false,
              plugins: ['react-refresh/babel'],
            },
          },
        ],
      },
      // handle dependency css import
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
      // handle source code, support postcss, css
      // maybe it's better use postcss
      {
        test: /\.s[ac]ss$/i,
        include: [path.resolve(__dirname, '../src'), /genesis-web-/, /node_modules\/@zhongan/],
        use: [
          {
            loader: 'style-loader',
            options: {
              insert: 'body', // 从 body 底部插入
              injectType: 'singletonStyleTag',
            },
          },
          {
            loader: 'css-loader',
            options: {
              import: false,
              modules: {
                auto: true,
                exportLocalsConvention: 'camelCase',
                localIdentName: '[path][name]__[local]--[hash:base64:5]',
              },
            },
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: {
                  tailwindcss: {},
                  autoprefixer: {},
                },
              },
            },
          },
          {
            loader: 'sass-loader',
            options: {
              additionalData: `${prependDataNagrand};$market-prefix: market-ant4;$ant-prefix: market-ant4;`,
            },
          },
        ],
      },
      // keep less original format
      {
        test: /\.less$/,
        use: [
          'style-loader',
          'css-loader',
          {
            loader: 'less-loader',
            options: {
              additionalData: `${prependDataNagrand.replace(/\$/g, '@')};@ant-prefix: market-ant4;`,
              lessOptions: {
                modifyVars: {
                  ...marketLessVars,
                  'ant-prefix': 'market-ant4',
                },
                javascriptEnabled: true,
                math: 'always',
              },
            },
          },
        ],
      },
      // webpack asset module, use resource instead of url for convenience
      {
        test: /\.(woff|woff2|eot|ttf|otf|png|jpe?g|gif|ico)$/i,
        type: 'asset/resource',
      },
      {
        test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
        oneOf: [
          // use svg as component within js, jsx, ts, tsx, as url within css, scss, less
          {
            type: 'asset/resource',
            issuer: /\.(css|pcss|less|sass|scss)$/,
          },
          // use resource query to detect url mode or component mode, default component mode
          {
            type: 'asset/resource',
            resourceQuery: /assets/,
          },
          // ContextModule has no issuer
          {
            use: [
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                },
              },
              {
                loader: '@svgr/webpack',
                options: {
                  babel: false,
                },
              },
            ],
          },
        ],
      },
    ],
  },
  devServer: {
    historyApiFallback: true,
    hot: true,
    inline: true,
    host: '0.0.0.0',
    port: 8080,
    injectHot: true,
    injectClient: true,
    watchContentBase: true,
    contentBase: [path.resolve(__dirname, '../../../dist')],
    proxy: [
      ...setupProxyList,
      {
        context: ['/api', '/n/', '/bff-api', '/oauth', '/ext'],
        target: proxyTarget,
        router: proxyRouter,
        changeOrigin: true,
        logLevel: 'debug',
      },
    ],
  },
  plugins: [
    new AntdDayjsWebpackPlugin({}),
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify('development'),
    }),
    new HtmlWebpackPlugin({
      hash: true,
      title: 'Graphene',
      filename: 'index.html',
      // 注入 proxy script 到 HTML 中
      templateContent: templateContent.replace('</head>', `${headScripts}</head>`),
      favicon: path.resolve(__dirname, '../../../public/favicon.ico'),
      inject: 'body',
    }),
    new CleanWebpackPlugin(),
    new ReactRefreshWebpackPlugin({
      overlay: false,
    }),
    // new BundleAnalyzerPlugin({
    //   analyzerMode: 'static',
    //   openAnalyzer: false,
    //   reportFilename: '../analyzer/analyzer.html',
    // }),
  ],
  optimization: {
    runtimeChunk: {
      name: 'runtime',
    },
  },
};
