const path = require('path');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
const pkgName = require('../package.json').name;
const { lessVars, prependDataNagrand } = require('@zhongan/nagrand-ui');
const AntdDayjsWebpackPlugin = require('@ant-design/moment-webpack-plugin');
const { uploadBundleData } = require('../../../scripts/bundle');
const BuildBundlePlugin = require('../../../scripts/build-bundle-plugin');

// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

const marketLessVars = { ...lessVars };
delete marketLessVars['@ant-prefix'];

const { generateSMPData, uploadData } = require('../../../scripts/buildProfiling');

// const ignoreReg = new RegExp(`(packages/(?!${pkgName}))|(/?!packages)|base|webpack`);

const prdConfig = {
  mode: 'production',
  // devtool: 'cheap-module-eval-source-map',
  devtool: 'eval-cheap-source-map',
  // devtool: false,
  amd: false,
  node: false,
  entry: path.resolve(__dirname, '../src/index.js'),
  output: {
    path: path.resolve(__dirname, '../../../dist/children', pkgName),
    library: pkgName,
    publicPath: `/children/${pkgName}/`,
    filename: '[name].bundle.[contenthash:8].js',
    chunkFilename: '[name].[contenthash:8].js',
    assetModuleFilename: '[name].[contenthash:8][ext]',
    libraryTarget: 'umd',
  },
  cache: {
    type: 'filesystem',
    cacheLocation: path.resolve(__dirname, '.appcache'),
    buildDependencies: {
      config: [__filename],
    },
  },
  watchOptions: {
    followSymlinks: true,
    aggregateTimeout: 100,
    // ignored: ignoreReg,
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      '@market': path.resolve(__dirname, '../src'),
    },
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx|mjs|mjsx|ts|tsx)$/,
        include: [
          path.resolve(__dirname, './src'),
          /genesis-web-/,
          /product-types/, // 处理后端接口类型文件
          /market-types/,
          /calculator-types/,
          /metadata-types/,
        ],
        resolve: {
          fullySpecified: false,
        },
        use: [
          {
            loader: require.resolve('babel-loader'),
            options: {
              cacheDirectory: true,
              cacheCompression: false,
            },
          },
        ],
      },
      // handle dependency css import
      {
        test: /\.css$/,
        use: [MiniCssExtractPlugin.loader, 'css-loader'],
      },
      // handle source code, support postcss, css
      // maybe it's better use postcss
      {
        test: /\.s[ac]ss$/i,
        include: [path.resolve(__dirname, '../src'), /genesis-web-/, /node_modules\/@zhongan/],
        use: [
          {
            loader: 'style-loader',
            options: {
              insert: 'body', // 从 body 底部插入
              injectType: 'singletonStyleTag',
            },
          },
          {
            loader: 'css-loader',
            options: {
              import: false,
              modules: {
                auto: true,
                exportLocalsConvention: 'camelCase',
                localIdentName: '[path][name]__[local]--[contenthash:base64:5]',
              },
            },
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: {
                  tailwindcss: {},
                  autoprefixer: {},
                },
              },
            },
          },
          {
            loader: 'sass-loader',
            options: {
              additionalData: `${prependDataNagrand};$market-prefix: market-ant4;$ant-prefix: market-ant4;`,
            },
          },
        ],
      },
      // keep less original format
      {
        test: /\.less$/,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          {
            loader: 'less-loader',
            options: {
              additionalData: `${prependDataNagrand.replace(/\$/g, '@')};@ant-prefix: market-ant4;`,
              lessOptions: {
                modifyVars: {
                  ...marketLessVars,
                  'ant-prefix': 'market-ant4',
                },
                javascriptEnabled: true,
                math: 'always',
                sourceMap: false,
              },
            },
          },
        ],
      },
      // webpack asset module, use resource instead of url for convenience
      {
        test: /\.(woff|woff2|eot|ttf|otf|png|jpe?g|gif|ico)$/i,
        type: 'asset/resource',
      },
      {
        test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
        oneOf: [
          // use svg as component within js, jsx, ts, tsx, as url within css, scss, less
          {
            type: 'asset/resource',
            issuer: /\.(css|pcss|less|sass|scss)$/,
          },
          // use resource query to detect url mode or component mode, default component mode
          {
            type: 'asset/resource',
            resourceQuery: /assets/,
          },
          // ContextModule has no issuer
          {
            use: [
              {
                loader: 'babel-loader',
                options: {
                  cacheDirectory: true,
                },
              },
              {
                loader: '@svgr/webpack',
                options: {
                  babel: false,
                },
              },
            ],
          },
        ],
      },
    ],
  },
  optimization: {
    runtimeChunk: {
      name: 'runtime',
    },
    minimizer: [
      new TerserPlugin({
        extractComments: false,
        terserOptions: {
          sourceMap: false,
          format: {
            comments: false,
          },
        },
      }),
      new CssMinimizerPlugin({
        test: /\.css$/,
        minimizerOptions: {
          preset: [
            'default',
            {
              discardComments: { removeAll: true },
            },
          ],
        },
      }),
    ],
    splitChunks: {
      chunks: 'all', // all，async 和 initial
      minSize: 20000, // 生成 chunk 的最小体积
      minChunks: 1, // 拆分前必须共享模块的最小 chunks 数
      maxAsyncRequests: 5, // 按需加载时的最大并行请求数
      maxInitialRequests: Infinity, // 入口点的最大并行请求数
      automaticNameDelimiter: '~', // 指定用于生成名称的分隔符
      cacheGroups: {
        default: false,
        vendors: {
          name: 'chunk-vendor',
          chunks: 'all',
          minChunks: 2,
          test: /[\\/]node_modules[\\/]/,
          priority: -20,
        },
        commons: {
          name: `chunk-common`,
          chunks: 'all',
          minChunks: 2,
          // priority的值必须比vendors中的大，才能提取出来
          priority: -10,
        },
        ant_design: {
          chunks: 'all',
          name: `ant_design`,
          test: /antd/,
          priority: 0,
        },
        antv: {
          chunks: 'all',
          name: `antv`,
          test: /antv/,
          priority: 5,
        },
        ant_design_icons: {
          chunks: 'all',
          name: `ant_design_icon`,
          test: /[\\/]@ant-design[\\/]/,
          priority: 10,
        },
        moment: {
          chunks: 'all',
          name: `moment`,
          test: /moment/,
          priority: 30,
        },
        lodash: {
          chunks: 'all',
          name: `lodash`,
          test: /lodash/,
          priority: 40,
        },
        'nagrand-ui': {
          chunks: 'all',
          name: `nagrand-ui`,
          test: /nagrand-ui/,
          priority: 50,
        },
        'genesis-web-service': {
          chunks: 'all',
          name: `genesis-web-service`,
          test: /genesis-web-service/,
          priority: 60,
        },
      },
    },
  },
  plugins: [
    new AntdDayjsWebpackPlugin(),
    // new BundleAnalyzerPlugin(),
    new webpack.BannerPlugin('版权所有，翻版必究'),
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify('production'),
    }),
    new webpack.LoaderOptionsPlugin({
      minimize: true,
      debug: false,
    }),
    new HtmlWebpackPlugin({
      hash: false,
      title: 'Graphene',
      filename: 'index.html',
      template: path.resolve(__dirname, '../../../public/index.html'),
      favicon: path.resolve(__dirname, '../../../public/favicon.ico'),
      inject: 'body',
    }),
  ],
};

const configWithTimeMeasures = new SpeedMeasurePlugin({
  outputFormat: jsonBlob => {
    return generateSMPData(pkgName, jsonBlob);
  },
  outputTarget: output => {
    uploadData(output);
  },
}).wrap(prdConfig);

configWithTimeMeasures.optimization.minimizer.push(
  new MiniCssExtractPlugin({
    filename: 'main.[contenthash:8].css',
  }),
  new BuildBundlePlugin({
    outputTarget: output => {
      if (process.env.APP_ENV !== 'development') {
        uploadBundleData(require('../package.json').name, output);
      }
    },
  })
);

module.exports = configWithTimeMeasures;
