{"- -": "", "--": "", "+ Add": "+ 新增", "Accumulate Product SA to Policy SA": "", "Accumulated to Premium": "累加到保费", "Add": "新增", "Add a Condition": "", "Add Block": "新增区块", "Add Declaration Library": "新增声明库", "Add New": "新增", "Add New Voucher": "新增", "Add Object Component": "", "Add POS Items": "", "Add Product And Liability": "产品和责任", "Add Relationship Package": "添加关联产品组合", "Add Rider": "", "Add successfully": "", "Additional Grace Period": "", "Adjust to 1st if Effective Day Not in Expiry Month": "起期日不在止期月份时顺延至1号", "Adjusted Market Value Floating Ratio": "", "After": "", "After(Days)": "", "Age Calculation Basis": "年龄计算规则", "Age Validation": "", "Agency Company": "代理公司", "Agency Company or Sales Platform": "代理公司或销售平台", "Agent": "代理人", "Agent Category": "代理人类型", "Agreement Code": "协议代码", "ALL": "全部", "All Creations": "", "All questions have been answered.": "已回答所有问题。", "All relevant addresses will be deleted together, please confirm!": "所有级联的地址也将一并删除，请确认是否删除", "All relevant bankCode will be deleted together, please confirm!": "所有级联的银行代码也将一并删除，请确认是否删除", "Allow Manual Adjustment": "", "Allow Master Policy Issuance": "允许触发master维度保单", "Allow Renewal": "", "Allow Renewal is already selected and cannot be used together with Open End Policy. Do you want to deselect Allow Renewal and proceed with selecting Open End Policy instead?": "", "​Allowed backdating range compared to Policy Insure Date. E.g. Insure Date is 15th Dec, if backdating range is 3 days ~ 6 days, the allowed range of Effective Date is 9th Dec ~ 12th Dec.": "与投保日期相比​可回溯的投保天数范围。如：投保日期为12月15日，回溯生效阈值为3天~6天，则允许的投保日期范围为12月9日~12月12日", "​Allowed forward-dating range compared to Policy Insure Date. E.g. Insure Date is 15th Dec, if forward-dating range is 3 days ~ 6 days, the allowed range of Effective Date is 18th Dec ~ 21th Dec.": "与投保日期相比​允许未来的投保天数范围。如：投保日期为12月15日，远期生效阈值为3天~6天，则允许的投保日期范围为12月18日~12月21日", "Amount Limit": "责任限额", "Amount Limit Remark": "保障限额备注", "Amount Range": "", "An open-end policy does not have a fixed expiry date. Coverage continues unless terminated, and is typically reviewed periodically.": "", "AND": "", "and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula": "", "Annual Premium": "", "Annuity Configuration": "年金配置", "Answer": "回答", "Applicable Scenario": "适用场景", "Application": "投保", "Application Elements": "投保要素", "Application Elements Group": "投保要素组", "Application Elements Group Code": "投保要素组代码", "Application Elements Group Name": "投保要素组名称", "Application Information": "适用信息", "Applied Unit": "", "Are you sure to clear this section?": "确定要清空这个模块吗？", "Are you sure to delete this POS Item ?": "", "Are you sure to delete this record ?": "", "Are you sure you want to delete these sales channels": "您确认要删除这些销售渠道吗？", "Assignee Elements": "", "Associated Goods and Plans": "关联的商品和计划", "Attach to Product": "关联到产品", "Attached To": "", "Auto Deduction Date": "", "Auto Deduction Date Compare to Due Date": "", "Automatic Adjustment": "", "Available": "可用", "Back": "返回", "Back to Search": "返回查询", "Back to Validation": "返回去验证", "Backdating": "回溯生效阈值", "Backdating or Forward-dating period calculation basis": "回溯/预约生效日期的计算基准", "Bank": "银行", "Base Currency": "", "base_next": "下一步", "Basic Configuration": "基本配置", "Basic Info": "基本信息", "Basic Information": "基本信息", "Be Attached By": "", "Before(Days)": "", "Belonging Info": "归属信息", "Beneficial Owner Elements": "实益拥有人要素", "Beneficiary Elements": "受益人要素", "BENEFICIARY INFO": "受益人信息", "Benefit Configuration": "保额配置", "Benefit Illustration": "利益演示", "Benefit Option Mapping": "福利选项映射", "Benefit Option Selection": "福利选项选择", "Benefit Option Selection & Matching": "福利选项选择和匹配", "Broker Company": "保险经纪公司", "btn_upload": "上载", "Bulk Delete": "批量删除", "By Formula": "按公式", "Caculation Item": "计算项目", "Calculate": "计算", "Calculation Cycle": "计算周期", "Calculation Method": "计算方式", "Campaign Discount": "活动折扣", "Cancel": "取消", "Cancel pin": "", "Cancellation Type": "", "Car Owner": "车辆所有者", "Cash Bonus Configuration": "现金红利配置", "Category": "类别", "Change installment premium calculation method will clear the existing configuration, please confirm.": "", "Changes have not been saved.": "修改的内容还未保存。", "Changing beneficiary category will clear the existing beneficiary information.": "", "Changing insured category will clear the existing insured information.": "", "Changing policyholder category will clear the existing policyholder information.": "", "Changing this setting will clear all configured coverage details and related information. Are you sure to continue?": "", "Channel": "渠道", "Channel and channnel - sub channel could not co-exisit. Please change.": "渠道 和 渠道-子渠道 不能同时存在，请修改。", "Choose the type of charge period to be supported": "选择要支持的充电周期类型", "Choose the type of coverage period to be supported": "请选择可支持的承期间的种类", "Claim Experience": "理赔经验", "Claim Stack Correlation": "", "Claim Stack Execution Order": "", "Claim Stack Setting": "", "claim_upload_time": "上传时间", "Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.": "召回的计算方法在此处配置，而召回计算的触发点将在其他业务模块定义，如保全项约定模块等", "Clear All": "清空所有", "Click here to upload": "点击这里上传", "Click or drag the file here to upload": "点击或者拖动文件到这里进行上传", "Closure & Next": "下架附加险", "Closure Rider": "下架附加险", "Code": "代码", "Code & Question Declaration": "代码和问题声明", "Collapse all": "", "Commission Clawback": "", "Commission Ratio": "佣金率", "Company Name": "公司名称", "Component Code": "", "Component Description": "", "Component Name": "", "Configuration Validation": "配置校验", "Configure": "配置", "Configure Benefit Details": "配置福利选项详细信息", "Confirm": "确认", "Congratulations!": "恭喜！", "Consentee Elements": "监护人要素", "CONSENTEE INFO": "", "Contact Person Elements": "", "Content Category": "内容类别", "Copy from Insured": "从被保人复制", "Copy from Policyholder": "从投保人复制", "Copy Reminder": "", "Copy Successfully": "复制成功", "Copy to New Goods": "复制生成新营销商品", "Copy to New Version": "复制生成新营销商品版本", "Copying elements from the insured module will overwrite existing content, please confirm.": "将从被保人模块复制所有元素并覆盖现有内容，请确认。", "Copying elements from the policyholder module will overwrite existing content, please confirm.": "将从投保人模块复制所有元素并覆盖现有内容，请确认。", "Coverage Date Change Type": "", "Coverage Period": "", "Coverage Period Configuration": "", "Coverage Period Fields to Revise": "", "Coverage Period Range": "保障期间范围", "Coverage Period Type": "", "Coverage Period Value Type": "保障期限值类型", "Coverage Type": "", "Currency": "币种", "Current Rider Status on Package": "产品组合上的附加险状态", "Customer ID No.": "客户证件号码", "Customer Info": "客户信息", "Customer Name": "客户名称", "Customized Document": "自定义文件", "Cut off Date": "", "Cut off Time": "", "Daily Basis: The system calculates the daily premium and then calculates each installment premium based on the number of days in that installment period. (For a full-year policy, the premiums of each installment could be different)": "", "Data Address": "资料地址", "Date + Time": "日期+时间", "Date of Birth": "出生日期", "Day of Fixed Due Date": "", "Day(s)": "", "Day(s) After Due Date": "", "Day(s) Before Due Date": "", "Days": "天数", "Days Type": "日期类型", "Declarant": "声明人", "Declaration": "告知", "Declaration Category": "申报类别", "Declaration Configuration": "告知设定", "Declaration Content Category": "申报内容类别", "Declaration Detail": "申报明细", "Declaration Library": "申报库管理", "Declaration Question Code": "申报问题代码", "Declaration Type": "申报类型", "Deduction Date": "", "Deduction Date Compare to Due Date.e.g. if deduct on due date, enter 0 if deduct prior to due date, enter 1, 2, 3 etc.": "", "Deduction Date Compare to renewal Policy effective date.e.g. if deduct on effective date, enter 0 if deduct prior to effective date, enter 1, 2, 3 etc.": "", "Default Answer": "默认答案", "Default Time": "", "Default Time Editable": "", "Default Value-Matrix Table": "", "Defer Period": "延期期限", "Define auto submit time for renewal proposal by comparing with previous policy expired date. If not configured, a manual submit is needed.": "", "Define the day before policy expired date and days after expired date to extract renewal policy": "", "Defined Application Elements": "", "Delete": "删除", "Delete Failed": "", "Delete Success": "删除成功", "Delete Successfully": "删除成功", "Deleted successfully": "删除成功", "describe": "描述", "Description": "描述", "Designated Beneficiary": "指定受益人", "Display Name": "显示名称", "Display Name of Field": "姓名显示字符", "Display or Not": "是否展示", "Dispute Settlement": "争议处理", "Documents Display": "文件展示", "Download Successfully": "下载成功", "Download Template": "下载模板", "Driver": "驾驶员", "Due Date": "", "Dunning Rule": "", "Duplicate Bank. Please change.": "重复的银行信息，请修改。", "Duplicate Broker Company. Please change.": "重复的经纪公司信息，请修改。", "Duplicate Company Name. Please change.": "重复的公司名称，请修改。", "Duplicate formula configuration for same formula category and sub category. Please edit original one.": "同一公式类型和子类型下配置了重复的公式，请修改。", "Duplicate Front Line Channel. Please change.": "", "Duplicate Key Account Channel. Please change.": "", "Duplicate Leasing Channel. Please change.": "重复的租赁渠道，请修改。", "Duplicate Order. Please change.": "", "Duplicate Sales Platform. Please change.": "重复的销售平台，请修改。", "Duplicated data exists": "", "Duration to Display": "展示时间段", "dutyText": "责任", "EARLIEST OF": "", "Edit Claim Stack Definition": "编辑累加器定义", "Edit Declaration Library": "编辑声明库", "Edit Goods Label": "", "Edit Plan": "", "Edit Product And Liability": "编辑产品和责任", "Edit Reminder": "编辑提醒", "edit success": "编辑成功", "Edit Voucher": "编辑券", "Editing": "", "EDITING": "", "Editing benefit factors will clear the existing uploaded benefit details，please confirm.": "编辑福利选项因子将会清空已上传的福利选项详细信息，请确认。", "Effective": "生效", "EFFECTIVE": "", "Effective Date Month End to Expiry Date Month End": "", "Effective Date Month End to Expiry Date Month End: If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "", "Effective Date Rule": "保单生效日期规则设置", "Effective end date cannot be earlier than the start date.": "结束日需晚于生效日。", "Effective From": "生效规则", "Effective Period": "有效期", "Effective Period Extension for Reversed Voucher": "是否要延长已退回的券的有效期", "Element": "", "Element Value": "要素值", "Elements": "", "Elements Detail Configuration": "", "Elements Group": "", "Elements Matrix": "", "Elements Selection": "", "Elements Value": "", "Enable Multi-Main Benefits": "", "Enable Payment": "启用支付", "Enable Underwriting": "启用核保", "End date": "截止日期", "End Date": "结束日期", "End Time": "", "Enter an integer greater than or equal to 1": "请输入≥1的整数", "Enumerate Configuration": "枚举值设定", "Enumerated": "枚举值", "Enumeration": "枚举值", "Enumeration Configuration Method": "枚举配置方法", "Enumeration values cannot be empty.": "枚举值不能为空。", "Existing Stack in the Product": "", "Exists duplicate liability orders": "存在重复责任排序", "Exists duplicate value": "存在重复的值", "Expand all": "", "Expired": "失效", "Expiry Date Adjustment": "", "Expiry date calculation method": "", "Expiry Date Calculation Method": "", "Expiry Date CalMethod": "止期计算方法", "Expiry Date Rule": "", "Expiry Date should be after Effective Date": "止期需要晚于起期", "Expiry Time Agreement Type": "止期计算方式", "Extra Loading": "加费", "Extra Premium": "加费金额", "Extract Bill Date": "", "Extract day(s) must be greater than offset day(s)": "", "Extract premium from X days prior to due date": "", "Extract review policy and generate bill from X days prior to Effective Date + .total Review Recurrencies.": "", "Extraction Method": "", "Facilitator Code": "", "Facilitator Code & Name": "主持人代码和姓名", "Facilitator Name": "", "Fee Type": "费用类型", "Fellow Traveller": "同行人", "Field Name": "字段名称", "Field Validation Rule": "字段校验规则", "Field Value": "价值", "File": "文件", "File Declaration": "文件说明", "File Management": "文件管理", "File size should small than 10M": "文件大小应小于10M", "FILE TYPE": "文件类型", "file upload failed.": "文件上载失败", "file uploaded successfully": "文件上传成功", "File Version": "文件版本", "Filters": "过滤器", "First": "", "first.": "", "Fixed Amount": "固定金额", "Fixed due date rule：Fixed due date or period end date": "", "Fixed Time": "", "For Fixed Day of Month, please input number from 1 and 28": "", "For No Claim Bonus": "无理赔奖金", "for Policy": "保单", "For POS Premium Refund": "保全退款保费", "for Proposal": "投保单", "Formula": "", "Formula Category": "公式种类", "Formula Category (Clawback)": "公式种类（召回）", "Formula Code": "公式代码", "Formula Name": "公式名称", "Formula Name (Clawback)": "公式名称（召回）", "Formula Sub Category": "公式子类型", "Formula Sub Category (Clawback)": "", "Forward-dating": "远期生效阈值", "Free Charge": "是否免费", "Freelook Period": "", "Front Line Channel": "", "Function Label Code": "功能标签代码", "Function label code already exists!": "该功能标签代码已存在", "Function Label Name": "功能标签名称", "Fund": "基金", "Fund Allocation": "基金分配", "Fund allocation percent summation must be 100%.": "基金分配比例之和应为 100%", "Fund allocation percent summation must be 100%. ": "", "Gender": "性别", "Generate Matrix Table": "", "Generate Matrix Table Successfully": "", "Generate new policy number for renewal": "", "Generate New Policy Number For Renewal": "", "Generation Date": "生成日期", "Goods": "商品", "Goods Category": "营销商品分类", "Goods Code": "营销商品代码", "Goods File Management": "商品文件管理", "Goods id is required": "需要商品id", "Goods Introduction": "营销商品内容概要", "Goods Label": "商品标签", "Goods Label Configuration": "商品标签管理", "Goods Name": "商品名称", "Goods Type": "商品类型", "Goods Validation": "商品验证", "Goods Version": "营销商品版本", "Grace Period": "", "Group Policy Level": "团体保单层", "Guarantee Period": "保证期", "have_disabled": "已作废", "Here has undone plans, please complete first": "仍有未完成配置的计划，请完成相关配置", "Hotel": "酒店", "ID Type": "证件类型", "If Commission GST is configured based on NetCommission, then we could configure as": "", "If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "", "If multiple versions are defined for the benefit option at the product level, multi-version matching is not currently supported.": "", "If Open End Policy is enabled, \"Coverage Period\" will be ineffective.": "", "If sales platform is configured, all sub channels under this sales platform could sell this product.": "", "If the effective date falls on a day that does not exist in the expiry month, and the calculated expiry date is set to the last day of that month, this adjustment will shift the expiry date to the 1st of the following month. (e.g. 2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1)": "当起期是一个在止期月份中不存在的日期，系统会将止期定为当月最后一天。选择此选项后，止期将自动调整到下个月的 1 号。（例：2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1）", "Including Levy": "含征税", "Including Stamp Duty": "含印花税", "Individual": "个人", "Initial Premium Period": "", "Input Method": "", "Insert fields": "", "Installment Detail": "", "Installment Premium": "分期保费", "Installment Premium Calculation Basis": "", "Installment Premium Calculation Method": "", "Installment Premium Frequency": "", "Insurable Interest": "可保利益", "Insurable Interest SA": "可保利益保额", "Insurable Interest SA Setting": "可保利益保额设定", "Insurance Applicant": "投保人", "Insurance Case": "保险案例", "Insurance Charge Period": "保险收费期", "Insurance Coverage Period": "保险责任期", "Insurance Coverage Period Configuration": "保险期间类型配置", "insurance_goods_name": "商品名称", "Insured": "被保人", "INSURED INFO": "被保人信息", "Insured Notice": "投保须知", "Insuring Process Configuration": "保险流程配置", "Interval": "区间", "Investment Product": "投资类产品", "Is Insured Mandatory?": "", "Is Open End Policy": "", "Issuance Agreement": "", "Issuance Mode Check": "", "Issuing Model of Policy": "保单类型", "Key Account Channel": "", "language": "语言", "LANGUAGE": "语言", "Lapse Date": "", "Last Modifier": "最近修改人", "LATEST OF": "", "Launch": "", "Launch / Closure": "上架或下架附加险", "Launch & Next": "上架附加险", "Launch Date": "上架时间", "Launch Rider": "上架附加险", "Launch Successfully": "", "LAUNCHED": "", "Leasing Channel": "租赁渠道", "Legal Beneficiary": "法定受益人", "Levy": "征税", "Liability Category": "责任类型", "Liability Claim Stack": "", "Liability Coverage / Premium": "承保责任/保费", "Liability Coverage / Premium Remarks": "承保责任/保费备注", "Liability is required": "责任为必填", "Liability Limit": "责任限额", "Liability Limit Remark": "责任限额备注", "Liability Name": "责任名称", "Liability Order": "", "Liability Premium": "责任保费", "Liability Premium Setting": "责任保费设定", "Liability Remark": "责任备注", "Liability SA": "责任保额", "Liability SA Remark": "责任保额备注", "Liability SA Setting": "责任保额设定", "Liability Stack": "", "liabilityPremium": "责任保费", "LiabilityPremiumRemarks": "责任保费备注", "Limit Sales Volume": "限定销售商品数", "limited": "限量", "Limited": "限制", "Main": "主险", "Main Benefit": "", "Main Condition Type": "主要条件类型", "Main Product Code": "主险代码", "Main Product Code / Name": "", "Mandatory": "必选", "Mandatory Liability": "", "Mandatory or Not": "是否必选", "Mandatory Rider": "必选责任", "Mandatory Waiver": "必选豁免", "Manual Annual Premium": "", "Manual Annual Premium Input Method": "", "Marketing Center": "", "Marketing Center - Application Elements Group": "", "Marketing Center - Marketing Goods": "", "Marketing Center - Package": "", "Marketing Center - Quick Quotation": "", "Marketing Center - Service Maintenance": "", "Marketing Goods": "营销商品", "Matched Label Content": "匹配的标签内容", "Max": "", "Max Allowed Designated Beneficiary Number": "最大指定受益人数", "Max Allowed Insured Number": "最大被保险人数", "Max Allowed Number": "最大数", "Max Allowed Number Limit": "最大数限制", "Max Allowed Object Number Limit": "最大标的数", "Max Down-Regulation Ratio": "", "Max Guaranteed Renewable": "", "Max Number of Inputted Insured": "最大可被保人数", "Max Up-Regulation Ratio": "", "Maximum 30 days": "最长30天", "Maximum Amount": "最大金额", "Maximum Sales Units": "最大可销售商品份数", "Maximum Voucher Numbers": "最大可用券数量", "Maximum Voucher numbers can be used in one-time": "一次可以使用的券数量", "MENU": "", "Min": "", "Minimum Price to Show": "最低价格", "Modal Factor": "要素因子", "Modifier": "", "Month(s)": "", "Monthly Basis: The system calculates the monthly premium and then calculates each installment premium based on the number of months in that installment period. (For a full-year policy, the premiums of each installment are the same)": "", "More >>": "更多>>", "Multi Language": "", "Mutually Exclusive Main Benefits": "", "Mutually Exclusive With": "", "My Creations": "", "My Task": "", "NCB Issue Type": "无理赔奖金发放形式", "NCD": "NCD", "Net Premium Adjustment": "", "New Business": "新契约", "New Tag": "", "Next": "下一步", "no": "否", "No": "否", "No Claim Discount": "无赔优系数(NCD)", "No Data": "暂无数据", "No Results Found": "没有数据", "No-Recurring Single Top Up / Single Top Up's collection status does not impact policy effectiveness.": "", "No.": "序号", "Nominee Elements": "继任投保人要素", "NOMINEE INFO": "", "Not Relevant": "不相关", "Note: Fund allocation percent summation must be 100%.": "注意：基金分配比例之和应为 100%", "Note: Fund allocation percent summation must be 100%. ": "", "Notification Code": "通知代码", "Notification Configuration": "通知配置", "Notification Scenario": "通知场景", "Notification Trigger": "通知触发器", "number": "序号", "Number": "", "Number of Files/URLs": "文件/链接的数量", "Object": "", "Object Category": "标的类型", "Object Component": "", "Object Elements": "标的要素", "OBJECT INFO": "标的信息", "Object Sub-category": "标的子类型", "Offset Date": "", "Offset from X Days Compare to Expired Date": "", "Offset from X days prior to due date": "", "okText": "确定", "On Sale": "特价", "Once deleted, the configured notifications which related with these sales channels will also be deleted.": "一旦删除，与这些销售渠道相关的配置通知也将被删除。", "Only normal policy and group policy support goods validation.": "", "Only one record is allowed for each questionnaire purpose under the same trigger. No duplicated allowed. Please check.": "每种问卷用途在每个触发节点仅能配置一条记录，不可重复添加。请检查。", "Open End Policy": "", "Open End Policy cannot be used together with Renewal Agreement, Expiry Date Rule, Coverage Period, Pre-calculate Premium installment method or Single Premium (Payment Period & Frequency), please check these configurations before submit.": "", "Open End Policy is already selected and cannot be used together with Allow Renewal. Do you want to deselect Open End Policy and proceed with selecting Allow Renewal instead?": "", "Optional": "", "Optional / Required": "", "Optional Liability": "", "Optional/Mandatory": "", "Order": "排序编号", "Organization": "团体", "Other": "其他", "Other Setting": "其他设置", "Overdue Auto Deduction": "", "Overdue Handling": "", "Overdue Status": "", "Package": "产品组合管理", "Package Agreement": "产品组合协议", "Package Category": "产品组合分类", "Package Code": "产品组合代码", "Package code is required": "产品组合代码为必填项", "Package Configuration": "", "Package Management": "", "Package Name": "产品组合名称", "Package Net Premium Adjustment": "", "PackageId is required": "需要PackageId", "Parameter have been changed. The uploaded matrixtable will be cleared. Please confirm.": "参数已被修改，已上传的矩阵表将被清空，请确认。", "Parameters": "", "Parameters(Result)": "", "Participating Product": "分红产品", "Partner Fee Calculation": "合作伙伴费用计算", "pay_method": "支付方式", "Payer Elements": "付款人要素", "PAYER INFO": "付款人信息", "Payment Defer Period Type": "延迟给付期间类型", "Payment Frequency": "付款频率", "Payment Option": "给付选项", "Payment Period": "给付期间", "Payment Period Type": "给付期间类型", "payment_type": "支付类型", "Period Type": "期间类型", "Pin": "", "Plan": "计划", "Plan (Plan Code + Plan Name)": "", "Plan Code": "保障计划代码", "Plan code is required": "保障计划代码为必填项", "Plan Configuration": "", "Plan Display": "", "Plan Group": "", "plan group code must be unique": "", "Plan Group Name": "", "Plan is required": "保障计划为必填项", "Plan Name": "保障计划名称", "Plan name is required": "保障计划名称为必填项", "Plan Premium Model": "", "Plan Recommend": "", "Plan Recommendation-Matrix Table": "", "Planned Premium Configuration": "", "Planned premium is required": "计划保费必填", "Please": "", "Please add one “Beneficiary” at least.": "请至少添加一个受益人。", "Please add one “Insured” at least.": "请至少添加一个被保人。", "Please add one “Object” at least.": "请至少添加一个标的。", "Please add stack correlation": "", "Please associate goods and plan.": "请关联商品和计划。", "Please Calculate first!": "请先计算。", "Please choose at least one coverage period agreement": "请至少选择一个保险期间的约定种类。", "Please choose at least one installment premium frequency agreement": "", "Please choose at least one premium period agreement": "", "Please complete the configuration": "请完成相关设置", "Please configure intermediate SA calculation formula in Product Center for corresponding liability.": "请在产品中心对相关的责任定义中间保额公式。", "Please configure POS premium refund rule for each sales channel.": "请给每个渠道配置保全退保保费发放规则。", "Please configure the min or max value of the range.": "最大範囲または最小範囲を設定してください。", "Please confirm that all configurations have been saved before submission": "请在提交前确认所有配置已保存", "Please confirm that the information of the beneficiary is consistent with insured.": "请确认受益人的信息与被保人一致。", "Please confirm that the information of the beneficiary is consistent with policyholder.": "请确认受益人的信息与投保人一致。", "Please confirm that the information of the insured is consistent with policyholder.": "请确认被保人的信息与投保人一致。", "Please copy your File URL first.": "清闲复制你的文件链接。", "Please define numerical values in the table without including the percentage sign. For example, if you wish to define 10%, simply enter 10 in the table.": "请在表格中定义数字数值，不包括百分号。例如，如果您希望定义10%，请在表格中直接输入10。", "Please do not select the same fund for the same Premium Type": "请不要给一个保费类型设定相同的基金。", "Please don’t enter special characters": "请不要输入特殊字符", "Please don’t enter special characters, such as~`!@#$%^*()+.": "请不要输入特殊字符如：~`!#$%^*()+", "Please don’t enter special characters, such as~`!@#$%^&*()+.": "不可输入特殊字符，例~`!@#$%^&*()+.", "Please ensure sum of beneficiary ratio equals to 100%.": "请确保同一顺位下的受益人比例之和为 100%。", "Please enter English letters": "", "Please enter numeric value": "请输入数值", "Please enter premium of regular top up or single top up.": "", "Please enter the correct number": "请输入正确的号码", "Please fill in all quotation factors before clicking “Calculation”": "点击“计算”按钮前，请确保已填写所有询价要素。", "Please fill in the following information before selecting rider product": "请在选择附加险前，填写以下信息。", "Please fill in the following quotation factors before calculation": "请在计算前，填写以下询价要素。", "Please input": "请输入", "Please input at least one form item": "", "Please input at least one option of file, file address, and file declaration.": "请至少输入一个文件、文件地址和文件声明选项。", "Please input both customer ID type and number.": "请输入用户的证件类型和证件号。", "Please input in this format: 'HH:mm'. e.g. 12:00": "", "Please input number": "请输入数字。", "Please input or paste the URL here": "请在此输入或粘贴链接。", "Please input Premium Allocation": "请输入保费分配。", "Please input right format": "请输入正确的格式", "Please select": "请选择", "Please select a package or add a coverage plan.": "", "Please select a package or add a plan.": "", "Please select at least one element.": "请至少输入一个要素。", "Please select at least two default answer.": "请至少选择两个默认答案", "Please select Fund": "请选择基金。", "Please select goods first": "", "Please select language": "请选择语言。", "Please select lanuch date first": "请选择上架日期", "Please select related formula for Commission": "请为佣金选择对应的公式。", "Please select related formula for Commission Clawback": "请为佣金召回选择对应的公式。", "Please select Related Package": "请选择关联的产品组合。", "Please select the object component that you want to add": "", "Please select the sub category that you want to add": "", "Please upload a CSV or Excel file.": "请上传一个 CSV 或 Excel 格式的文件。", "Please upload file": "请上传文件", "Please upload file of .xls, .xlsx": "请上载.xls、.xlsx的文件", "pleaseInput": "请输入", "POLICY BASIC INFO": "保单基本信息", "Policy Change": "", "Policy Claim Stack": "", "Policy Effective Without Collection (NB)": "非见费出单（新保）", "Policy Effective Without Collection (Renewal)": "非见费出单（续保）", "Policy Elements": "保单字段", "Policy Issuance Order": "保单签发顺序", "Policy Manual": "保险手册", "Policy Net Premium Adjustment": "", "Policy No.": "保单号", "Policy SA": "", "Policy Stack": "", "Policy Type": "", "Policy will be auto reviewed on each Review Recurrency from the Effective Date.": "", "Policy Year": "保单年度", "Policyholder": "投保人", "POLICYHOLDER INFO": "投保人信息", "PolicyStatusEnum.POLICY_EFFECT": "生效", "POS Premium Refund Type": "保全退款保费发放形式", "Premium Adjustment": "", "Premium Agreement": "", "Premium Allocation": "保费分配", "Premium Allocation must equal to 100%.": "", "Premium allocation section: Premium type “All” should not combined with other premium types.": "保费分配：保费类型“全部”不应与其他保费类型同时存在。", "Premium Allocation(%)": "保费分配比例(%)", "Premium Calculation Method": "保费计算方式", "Premium Composition": "", "Premium Currency": "", "Premium Discount": "保费折扣", "Premium Due Date Rule": "", "Premium Due Date Rules": "", "Premium End Date Calculation Rule": "", "Premium Frequency": "", "Premium Funder Elements": "", "Premium Notice Date Compare with Due Date": "", "Premium Period": "缴费期间", "Premium Period & Installment": "", "Premium Period Configuration": "缴费期间配置", "Premium Period Type": "缴费期间约定类型", "Premium Period Value Type": "", "Premium Related": "保费相关", "Premium Type": "保费类型", "Pricing Currency": "", "Pricing Currency corresponding to the calculated result of the formula. If select \"Premium Currency\", the formula's calculated result will be output directly.If \"SA Currency\", the calculated result of the formula will be converted from the SA currency to the premium currency using the exchange rate.": "", "Process Related": "与过程相关", "Product": "产品", "Product / Liability": "产品 / 责任", "Product & Liability": "", "Product and liability are required": "产品和责任为必填项", "Product Category": "产品分类", "Product Center": "", "Product Claim Stack": "", "Product Code": "产品代码", "Product Code - Version": "", "Product Code/Name": "产品分类 / 名称", "Product Correlation Matrix": "产品关联关矩阵", "Product Info": "产品信息", "Product Name": "产品名称", "Product Name / Code": "产品名称 / 分类", "Product Overdue Status": "", "Product Premium": "产品保费", "Product Premium Setting": "产品保费设置", "Product SA": "产品保额", "Product SA Setting": "产品保额设置", "Product Stack": "", "Product Tax": "产品税", "Product Version": "产品版本", "Products Involved in Accumulation": "", "Public Display Period": "公示期间", "Public Task": "", "Publish": "发布", "Publish Success": "发布成功", "Publish successfully": "", "Question Code": "问号", "Question Declaration": "问题声明", "Question Description": "问题描述", "Questionnaire": "问卷", "QUESTIONNAIRE": "问卷", "Questionnaire Name": "问卷名", "Questionnaire Purpose": "问卷目的", "Quick Menu": "", "Quotation": "询价", "Quotation Code": "", "Quotation Details": "询价详细信息", "Random": "随机数", "Range": "范围", "Ratetable Category": "费率表类型", "Ratetable Code": "费率表代码", "Re-Define Effective Date Rule": "重定义保险起期规则", "Recommend": "推荐", "recommendOnlyOne": "请推荐至少一个方案", "Refund Formula": "退费公式", "Regular Top Up": "定期追加投资", "Regulation of Products": "产品管理", "Related Main": "", "Related Main Product": "关联的主险", "Related Package Info": "关联的产品组合信息", "Relational Policy No.": "", "Renewal": "续保", "Renewal Agreement": "", "Renewal Extraction Period": "", "Renewal Grace Period": "", "Renewal Policy Effective Date Rule": "", "Renewal Policy Effective Without Collection": "", "Renewal Proposal Submit Date": "", "Retrieve the policy to be reviewed and generate the bill X days prior to the Review Date.": "", "Review Extraction Date": "", "Review Recurrence": "", "Rider": "附加险", "Rider Code": "附加险代码", "Rider Liability": "附加险责任", "Rider Name": "附加险名称", "Rider Premium Payment Method": "附加险保费支付方式", "Rider Product Code": "附加险产品代码", "RSTU／STU must be collected for initial premium": "", "Rule/Rule Set": "", "Rule1：Corresponding to effective date or period end date": "", "Rule2：Corresponding to effective date or next date of period end date": "", "Rule3: Corresponding to effective date or period end date and fixed Feb 28 as period end date": "", "SA / Premium Details of Each Insured": "每个被保人的保额 / 保费详细信息", "SA Currency": "", "Sales Attributes": "销售属性", "Sales Channel": "销售渠道", "Sales Channel is empty or multiple sales channels exist.": "渠道信息为空或存在多个渠道。", "Sales Configuration": "销售设置", "Sales District": "销售地区", "Sales Platform": "销售平台", "Sales Time": "可销售有效期间", "salesType": "销售类型", "Same as Launch Period": "", "Same as SA Currency": "", "Same Premium Calculation for Each Insured": "每个被保人用相同的保费计算方式", "Save": "保存", "Save Sort": "保存排序", "Save success": "", "Save Successful.": "", "Save successfully": "保存成功", "Save Successfully": "保存成功", "save_suc": "保存成功", "Scenario Validation": "场景验证", "Search": "搜索", "Search Goods Name": "查询商品", "Search Voucher Name": "查询券", "Secondary Life Insured Elements": "第二被保人要素", "Select Benefit Factors": "", "Select from the coverage periods in the associated products, or click \"Add New\" to create a new one.": "", "Select from the premium periods in the associated products, or click \"Add New\" to create a new one.": "", "Select Parameter": "选择参数", "SELECT PLAN": "选择保障方案", "Select Product": "", "Select Questionnaire Language": "选择问卷语言", "Select Relationship Package": "选择关联产品组合", "Select Rider": "选择附加险", "Select Stack Code": "", "Selected sales channel has been configured with conflicted POS premium refund rule.": "所选销售渠道已配置相关的保全退保保费发放规则。", "Service Code": "", "Service Company": "", "Service Effective Time": "", "Service Fee Clawback": "", "Service Maintenance": "", "Service Name": "", "Service Type": "服务类型", "service_page_amount": "服务总额", "service_page_create_time": "创建时间", "service_page_creator": "创建人", "service_page_effective_time": "服务有效时间", "service_page_effective_time2": "有效时间", "service_page_fac_code": "服务商Code", "service_page_fac_name": "服务商名称", "service_page_file_name": "文件名", "service_page_modi_time": "修改时间", "service_page_modifier": "修改人", "service_page_service_code": "服务编码", "service_page_service_name": "服务名称", "service_page_service_status": "服务状态", "service_page_total": "合计", "service_page_used": "已使用", "Set Default Time": "", "Set Fixed Time": "", "Set Relationship with Package": "设置与产品组合的关系", "Settlement Rule": "结算规则", "Show Financial Declaration": "显示财务告知", "Show Health Declaration": "显示健康告知", "Show in Application Form": "投保时显示", "Single Top Up": "单次追加投资", "Sorry no goods were found": "没有商品数据", "Sort": "排序", "sort successfully": "排序成功", "Special Agreement": "特别约定", "Specified SA & Premium": "", "Stack Code": "累加器编号", "Stack code have been changed. The uploaded file will be cleared. Please confirm.": "", "Stack Component Name": "", "Stack Content": "", "Stack Description": "出险描述", "Stack Level": "", "Stack Name": "累加器名字", "Stack Template Code": "", "Stack Template Name": "", "Stack Type": "累加器类型", "Stack Unit": "单位", "Stack Unit Type": "", "Stack Value": "值", "Stack Value Matrix": "", "Stack Value Type": "值类型", "Stamp Duty": "印花税", "Standard Premium": "标准保费", "Start date": "开始日期", "Start Time": "", "status": "状态", "Sub Condition Type": "次要条件类型", "Submit": "提交", "Submit success": "提交成功", "Submit successfully": "提交成功", "Submit successfully!": "", "Sum Assured": "保险金额", "Summary": "", "Support maintaining the association between stack values, only support value types of fixed amount and enumeration.": "", "Supported Assignee Type": "", "Supported Beneficiary Type": "支持的受益人类型", "Survival Benefit Configuration": "生存金设置", "Switching the package will clear all definitions under the plan. Please confirm to proceed with the change.": "更换此产品组合将清空保障计划下的所有定义。请确认是否要继续更改。", "System error": "系统错误", "System will automatically review the policy on each Review Recurrence starting from the effective date.": "", "System will automatically review the policy on each Review Recurrence starting from the effective date. When review recurred on monthly basis, 'Premium Period Type' in Premium Period & Installment must be set up as 'Monthly' as well.": "", "Tag": "", "Target User of Voucher": "券发放的对象用户", "Targeted Customer": "目标客户", "Tax": "税", "Tax Amount": "税额", "Tax Setting": "税设定", "Tax Type": "税种", "Tax Value Type": "税值类型", "Template Code / Name": "", "Tenant Enumerated Sync": "", "text_delete_success": "刪除成功", "The coverage period agreement will be deleted": "", "The Display Time should be within the Launch Date": "显示时间应在发布日期内", "The effective date of this file can't intersect with the existed file": "此文件的生效日不能与现有文件有交集。", "The fixed due date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.": "", "The following package(s) have used this application elements group. Editing this record and the following package(s) will be affected. Please check before change:": "以下产品组合中已使用此投保要素组。编辑此记录，以下产品组合将受到影响。更改前请确认：", "The fund should be unique for universal product.": "", "The goods code could only contain letters, numbers, and underscores (_).": "商品code仅可以包含字母，数字，下划线", "The input number cannot be zero": "", "The insurance application failed. Please check the information and resubmit.": "", "The marketing goods contain policy types other than Normal Policy or Group Policy. These types are not subject to standard validation checks before launching. Do you want to proceed?": "", "The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59": "", "The package code could only contain letters, numbers, and underscores (_).": "产品组合代码只能包含字母、数字和下划线（_）。", "The packages associated with the coverage plan must be within the selected packages.": "", "The packages associated with the plan must be within the selected packages.": "", "The plan code could only contain letters, numbers, and underscores (_).": "计划code 仅可以包含字母，数字，下划线", "The product lacks liabilities, and please add the configuration in the product center": "产品下的责任未配置，请在产品中心完成配置", "The questionnaire does not exist.": "", "The questionnaire is filled out. Proceed to insurance application.": "", "The sales channels have related configured notifications, please update notification configurations if needed.": "销售渠道已配置相关通知，如有需要，请更新对应的通知配置。", "The Sales Time should be within the Launch Date": "商品未上架，销售时间必须在商品上架时间以内", "The second value should greater than the first value": "", "The sum of Maximum Sales Units of each plan is greater than Goods Wholesale Volume!": "", "The sum of Maximum Sales Units of each plan is greather than Goods Wholesale Volume!": "各计划的最大销量之和大于商品批发量!", "There is unfinished editing in <{{title}}>. Please complete before proceeding.": "", "There must be one or more rider benefits under the virtual main benefit.": "虚拟主险必须附加1个或1个以上的附加险。", "This application elements group is relied on": "", "This file was effective in the past, are you sure to delete it?": "此文件曾在过去时间生效，确定要删除吗？", "This record was configured based on old version. Currently, viewing and editing are not supported.": "此配置数据版本过旧，暂不支持查看跟编辑。", "Ticket": "", "Tied Agent": "内部代理人", "Time Zone": "时区", "Times Type": "", "Tips: For relationship, you can configure as “attachable”, “non-attachable” or “compulsory”.": "", "Total Premium": "总保费", "Transaction Amount": "交易金额", "Transaction Date": "交易日期", "Transaction Information": "交易信息", "Trigger": "触发", "Trigger Point by Event Policy": "事件保单触发点", "Trip": "旅程", "Trustee Elements": "受托人要素", "TRUSTEE INFO": "", "Type": "", "Unanswered question exists, please confirm.": "存在未回答的问题，请确认。", "Uncompleted questionnaire exists, please confirm.": "存在未完成的问卷，请确认。", "Underwriting Error Message": "核保错误信息", "Underwriting Order": "核保顺序", "Underwriting Related": "承保相关", "Underwriting Type": "核保类型", "Unified Underwriting Conclusion Flag": "", "Unlimited": "不限量", "Unnamed Insured": "", "Unset": "未设定", "Updatable or Not": "是否可更新", "Updatable When Manual UW": "是否人核可更新", "Updatable When POS": "是否保全可更新", "Upload": "上传", "Upload Batch No.": "上传批号", "Upload Fail!": "", "Upload Files": "文件上传", "Upload Successful.": "", "Upload Successfully": "上传成功", "URL": "链接", "usage_time": "使用时间", "Usage-based Goods": "基于使用的商品", "User Input": "请输入", "Validation Response": "验证响应", "value": "", "Value Assignment": "", "Value Code": "值", "Value Type": "值类型", "Variable Amount": "可变金额", "Vehicle Additional Equipment": "车辆附加设备", "Vehicle Database Name": "车型库名称", "Vehicle Loan": "车辆贷款", "Vehicle Market Value Formula": "", "Vehicle Related Configuration": "", "Version": "版本定义", "View Component": "", "View My Creations": "", "View Voucher": "查看券", "Voucher": "券", "Voucher Application Channel": "适用渠道", "Voucher Basic Info": "", "Voucher Calculation & Effective Rule": "计算+生效规则", "Voucher Channel": "适用渠道", "Voucher Code": "券代码", "Voucher Consumption Status": "", "Voucher Correlation": "券绑定", "Voucher Detail": "券信息", "Voucher Effective Period": "", "Voucher Face Amount": "券面值", "Voucher Name": "券名称", "Voucher Number Generation Rule": "券码规则", "Voucher Status": "券状态", "Voucher Type": "券类型", "Voucher Type - Face Amount": "", "Voucher Value Type": "券类型", "Waiver": "豁免", "Waiver Details": "豁免详细信息", "Waiver Product": "豁免产品", "Waiver Product Duplicated": "豁免产品重复", "Waiver Rider Liability": "豁免附加险责任", "Warning Threshold": "警告阈值", "Whether to keep the voucher balance": "是否找零", "Wholesale Volume": "批发量", "Wholesale Volume Warning": "最大销量预警", "With Guarantee Period": "带有保证期限", "Year": "", "Year(s)": "", "yes": "是", "Yes": "是", "Yes-Recurring Single Top Up / Single Top Up must be collected before policy is effective.": "是-周期性单次追加投资或单次追加投资需在保单生效前收讫", "You are applying": "申请中", "You can only upload PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG": "只允许上传 PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG 类型的文件。", "You can only upload xls, xlsx": "只允许上传 XLS/XLSX 类型的文件。", "You can only upload xls, xlsx, doc, docx, pdf, jpg or png.": "只允许上传xls, xlsx, doc, docx, pdf, jpg 或 png 格式的文件", "You can only upload XLSX/CSV": "", "Your policy has been issued.": "您的保单已签发。", "Your proposal has been issued.": "您的投保单已签发。"}