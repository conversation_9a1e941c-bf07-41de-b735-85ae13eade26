{"- -": "--", "--": "--", "+ Add": "+ 追加", "Accumulate Product SA to Policy SA": "", "Accumulated to Premium": "保険料に加算", "Add": "追加", "Add a Condition": "", "Add Block": "ブロック追加", "Add Declaration Library": "告知を追加", "Add New": "新規追加", "Add New Voucher": "クーポン券を新規追加", "Add Object Component": "", "Add POS Items": "", "Add Product And Liability": "商品と補償内容", "Add Related Formula": "", "Add Relationship Package": "関連組み合わせ商品を追加してください。", "Add Rider": "特約を追加してください。", "Add successfully": "", "Additional Grace Period": "", "Adjust to 1st if Effective Day Not in Expiry Month": "", "Adjusted Market Value Floating Ratio": "", "After": "", "After(Days)": "後（日）", "Age Calculation Basis": "年齢計算ルール", "Age Validation": "", "Agency Company": "代理店会社", "Agency Company or Sales Platform": "代理店会社あるいは販売チャネル", "Agent": "代理人", "Agent Category": "代理人カテゴリー", "Agreement Code": "約定コード", "ALL": "すべて", "All Creations": "", "All questions have been answered.": "すべての質問は解決しました。", "All relevant addresses will be deleted together, please confirm!": "関連する住所も同時に削除されますから、ご確認ください", "All relevant bankCode will be deleted together, please confirm!": "関連する銀行コードも同時に削除されますから、ご確認ください", "Allow Manual Adjustment": "", "Allow Master Policy Issuance": "マスタ契約による加入", "Allow Renewal": "", "Allow Renewal is already selected and cannot be used together with Open End Policy. Do you want to deselect Allow Renewal and proceed with selecting Open End Policy instead?": "", "​Allowed backdating range compared to Policy Insure Date. E.g. Insure Date is 15th Dec, if backdating range is 3 days ~ 6 days, the allowed range of Effective Date is 9th Dec ~ 12th Dec.": "保険契約日と比較して、遡及日付の範囲が許可されています。例えば、保険契約日が12月15日で、遡及日付範囲が3日6日の場合、有効日の範囲は12月9~12日です。", "​Allowed forward-dating range compared to Policy Insure Date. E.g. Insure Date is 15th Dec, if forward-dating range is 3 days ~ 6 days, the allowed range of Effective Date is 18th Dec ~ 21th Dec.": "保険契約日と比較して、将来日付の範囲が許可されています。例えば、保険契約日が12月15日で、将来日付範囲が3日6日の場合、有効日の範囲は12月18~21日です。", "Amount Limit": "補償限度額", "Amount Limit Remark": "補償限度額の説明", "Amount Range": "", "An open-end policy does not have a fixed expiry date. Coverage continues unless terminated, and is typically reviewed periodically.": "", "AND": "と", "and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula": "そして、<commGST>の式の中で、Order 1で計算されたネット手数料のスキーマファクターを使用して式を設定できます。", "Annual Premium": "", "Annuity Configuration": "年金の設定", "Answer": "回答", "Applicable Scenario": "適用パターン", "Application": "申込", "Application Elements": "加入要素", "Application Elements Group": "加入要素グループ", "Application Elements Group Code": "加入要素グループコード", "Application Elements Group Name": "加入要素グループ名", "Application Information": "適用の詳細", "Applied Unit": "", "Are you sure to clear this section?": "この部分の内容をクリアしてもよろしいでしょうか。", "Are you sure to delete this POS Item ?": "", "Are you sure to delete this record ?": "", "Are you sure you want to delete these sales channels": "これらの販売チャネルを削除しますか。", "Assignee Elements": "譲渡先の要素", "Associated Goods and Plans": "関連販売商品と商品組合せ", "Attach to Product": "商品に添付", "Attached To": "に付き", "Auto Deduction Date": "", "Auto Deduction Date Compare to Due Date": "", "Automatic Adjustment": "", "Available": "利用可能", "Back": "戻る", "Back to Search": "検索に戻る", "Back to Validation": "バリデーションに戻る", "Backdating": "遡及", "Backdating or Forward-dating period calculation basis": "遡及期間または先日付け期間の計算基準", "Bank": "銀行", "Base Currency": "", "base_next": "次へ", "Basic Configuration": "基本設定", "Basic Info": "基本情報", "Basic Information": "基本情報", "Be Attached By": "付加された", "Before(Days)": "前（日）", "Belonging Info": "所属情報", "Beneficial Owner Elements": "実益所有者要素", "Beneficiary Elements": "受取人要素", "BENEFICIARY INFO": "受取人情報", "Benefit Configuration": "保険金額設定", "Benefit Illustration": "給付金説明", "Benefit Option Mapping": "受益オプションのマッピング", "Benefit Option Selection": "受益オプションの選択", "Benefit Option Selection & Matching": "受益オプションの選択＆マッチング", "Broker Company": "保険仲介会社", "btn_upload": "アップロード", "Bulk Delete": "一括削除", "By Formula": "計算式別", "Caculation Item": "計算項目", "Calculate": "計算", "Calculation Cycle": "計算サイクル", "Calculation Level": "", "Calculation Method": "計算基礎", "Campaign Discount": "キャンペーン割引", "Cancel": "キャンセル", "Cancel pin": "", "Cancellation Type": "", "Car Owner": "車両所有者", "Cash Bonus Configuration": "現金ボーナスの設定", "Category": "カテゴリ", "Change installment premium calculation method will clear the existing configuration, please confirm.": "", "Changes have not been saved.": "編集内容が保存されていません", "Changing beneficiary category will clear the existing beneficiary information.": "受取人の種類を変更すると、既存の受取人情報が消去されます。", "Changing insured category will clear the existing insured information.": "被保険者の種類を変更すると、既存の保険情報が消去されます。", "Changing policyholder category will clear the existing policyholder information.": "契約者の種類を変更すると、既存の契約者情報が消去されます。", "Changing this setting will clear all configured coverage details and related information. Are you sure to continue?": "", "Channel": "チャネル", "Channel and channel - sub channel could not co-exist. Please change.": "", "Channel and channnel - sub channel could not co-exisit. Please change.": "チャネルとチャネルサブチャネルが共存できませんから、 変更してください。", "Choose the type of charge period to be supported": "払込期間種類を選択してください", "Choose the type of coverage period to be supported": "保険期間の種類を選択してください", "Claim Experience": "保険金請求経験", "Claim Stack Correlation": "", "Claim Stack Execution Order": "", "Claim Stack Setting": "クレームスタック設定", "claim_upload_time": "アップロード時間", "Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.": "クローバックの計算方法はここで設定します。一方、クローバックの計算をトリガーする場所は、POSアイテムなどのビジネスモジュール構成で定義します。", "Clear All": "全削除", "Click here to upload": "アップロードするにはここをクリックしてください。", "Click or drag the file here to upload": "クリックあるいはドラックしてアップロードする", "Closure & Next": "特約終了", "Closure Rider": "特約終了", "Code": "コード", "Code & Question Declaration": "コードと質問型の告知", "Collapse all": "", "Commission Clawback": "", "Commission Ratio": "手数料レート", "Company Name": "会社名", "Component Code": "", "Component Description": "", "Component Name": "", "Configuration Validation": "バリデーション設定", "Configure": "設定", "Configure Benefit Details": "利益情報の設定", "Confirm": "確認", "Congratulations!": "おめでとう", "Consentee Elements": "親権者要素", "CONSENTEE INFO": "親権者の情報", "Contact Person Elements": "", "Content Category": "内容種類", "Copy from Insured": "被保険者からのコピー", "Copy from Policyholder": "契約者からのコピー", "Copy Reminder": "", "Copy Successfully": "正常にコピーされました", "Copy to New Goods": "新しい販売商品をコーピー作成", "Copy to New Version": "新しい販売商品バージョンをコーピー作成", "Copying elements from the insured module will overwrite existing content, please confirm.": "被保険者モジュールから要素をコピーすると、既存の内容が上書きされます。確認してください。", "Copying elements from the policyholder module will overwrite existing content, please confirm.": "契約者モジュールから要素をコピーすると、既存の内容が上書きされます。確認してください。", "Coverage Date Change Type": "", "Coverage Period": "", "Coverage Period Configuration": "", "Coverage Period Fields to Revise": "", "Coverage Period Range": "保険期間の範囲", "Coverage Period Type": "", "Coverage Period Value Type": "補償期間タイプ", "Coverage Type": "", "Currency": "通貨", "Current Rider Status on Package": "現在パッケージでの特約状態", "Customer ID No.": "顧客ID番号", "Customer Info": "顧客情報", "Customer Name": "顧客名", "Customized Document": "カスタマイズドキュメント", "Cut off Date": "締切日", "Cut off Time": "締切時", "Daily Basis: The system calculates the daily premium and then calculates each installment premium based on the number of days in that installment period. (For a full-year policy, the premiums of each installment could be different)": "", "Data Address": "資料アドレス", "Date + Time": "日付+タイムスタンプ", "Date of Birth": "生年月日", "Day of Fixed Due Date": "", "Day(s)": "", "Day(s) After Due Date": "", "Day(s) Before Due Date": "", "Days": "日間", "Days Type": "日タイプ", "Declarant": "告知", "Declaration": "告知事項", "Declaration Category": "告知種類", "Declaration Configuration": "告知事項設定", "Declaration Content Category": "告知内容種類", "Declaration Detail": "告知詳細", "Declaration Library": "告知ライブラリー管理", "Declaration Question Code": "質問コード", "Declaration Type": "告知種類", "Deduction Date": "", "Deduction Date Compare to Due Date.e.g. if deduct on due date, enter 0 if deduct prior to due date, enter 1, 2, 3 etc.": "", "Deduction Date Compare to renewal Policy effective date.e.g. if deduct on effective date, enter 0 if deduct prior to effective date, enter 1, 2, 3 etc.": "", "Default Answer": "デフォルト回答", "Default Time": "", "Default Time Editable": "", "Default Value-Matrix Table": "", "Defer Period": "繰下げ期間", "Define auto submit time for renewal proposal by comparing with previous policy expired date. If not configured, a manual submit is needed.": "", "Define the day before policy expired date and days after expired date to extract renewal policy": "", "Defined Application Elements": "", "Delete": "削除", "Delete Failed": "", "Delete Success": "削除は成功しました。", "Delete Successfully": "削除は成功しました。", "Deleted successfully": "削除完了", "describe": "内容", "Description": "説明", "Designated Beneficiary": "指定された受取人", "Display Name": "名称展示", "Display Name of Field": "名称展示項目", "Display or Not": "表示要否", "Dispute Settlement": "紛争処理", "Documents Display": "ファイル表示", "Download Successfully": "ダウンロードしました", "Download Template": "テンプレートダウンロード", "Driver": "運転手", "Due Date": "", "Dunning Rule": "", "Duplicate Bank. Please change.": "銀行情報は重複しましたから、変更してください。", "Duplicate Broker Company. Please change.": "保険仲介会社情報は重複しましたから、変更してください。", "Duplicate Company Name. Please change.": "会社名称は重複しましたから、変更してください。", "Duplicate formula configuration for same formula category and sub category. Please edit original one.": "同じカテゴリとサブカテゴリにで重複したフォミューラが設定されました。変更してください。", "Duplicate Front Line Channel. Please change.": "", "Duplicate Key Account Channel. Please change.": "", "Duplicate Leasing Channel. Please change.": "リースチェネルは重複しましたから、変更してください。", "Duplicate Order. Please change.": "順番は重複しましたから、変更してください。", "Duplicate Sales Platform. Please change.": "販売チャネルは重複しましたから、変更してください。", "Duplicated data exists": "", "Duration to Display": "表示期間", "dutyText": "保障内容", "EARLIEST OF": "", "Edit Claim Stack Definition": "クレームスタック設定を編集", "Edit Declaration Library": "告知編集", "Edit Goods Label": "", "Edit Plan": "", "Edit Product And Liability": "商品と保障内容を編集", "Edit Related Formula": "", "Edit Reminder": "リマインダーの編集", "edit success": "編集しました", "Edit Voucher": "クーポン券編集", "Editing": "編集", "EDITING": "", "Editing benefit factors will clear the existing uploaded benefit details，please confirm.": "利益要素を編集すると、既存のアップロードされた利益の詳細が消去されます。ご確認してください。", "Effective": "有効", "EFFECTIVE": "", "Effective Date Month End to Expiry Date Month End": "", "Effective Date Month End to Expiry Date Month End: If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "", "Effective Date Rule": "有効日ルール", "Effective end date cannot be earlier than the start date.": "終了日は開始日より早くできません。", "Effective From": "有効ルール", "Effective Period": "有効期間", "Effective Period Extension for Reversed Voucher": "取り下げたクーポン券の有効期間を延長する要否", "Element": "", "Element Value": "要素値", "Elements": "", "Elements Detail Configuration": "要素情報の設定", "Elements Group": "", "Elements Matrix": "要素のマトリックス", "Elements Selection": "要素の選択", "Elements Value": "要素値", "Enable Multi-Main Benefits": "", "Enable Payment": "支払可能", "Enable Underwriting": "査定有効", "End date": "終了日", "End Date": "終了日", "End Time": "", "Enter an integer greater than or equal to 1": "1以上の整数を入力してください", "Enumerate Configuration": "列挙値設定", "Enumerated": "列挙値", "Enumeration": "列挙値", "Enumeration Configuration Method": "列挙構成方法", "Enumeration values cannot be empty.": "列挙値を設定してください。", "Existing Stack in the Product": "", "Exists duplicate liability orders": "補償内容の順番は重複しました。", "Exists duplicate value": "重複する値が存在します。", "Expand all": "", "Expired": "期限切れ", "Expiry Date Adjustment": "", "Expiry date calculation method": "", "Expiry Date Calculation Method": "", "Expiry Date CalMethod": "満期日呼び出すメソッド", "Expiry Date Rule": "", "Expiry Date should be after Effective Date": "有効期限は発効日以降でなければならない。", "Expiry Time Agreement Type": "満期計算方法", "Extra Loading": "追加補償費用", "Extra Premium": "割増保険料", "Extract Bill Date": "", "Extract day(s) must be greater than offset day(s)": "", "Extract premium from X days prior to due date": "", "Extract review policy and generate bill from X days prior to Effective Date + .total Review Recurrencies.": "", "Extraction Method": "", "Facilitator Code": "", "Facilitator Code & Name": "ファシリテーターコードと名称", "Facilitator Name": "", "Fee Type": "費用類型", "Fellow Traveller": "旅行同行者", "Field Name": "名称", "Field Validation Rule": "チェックルール", "Field Value": "フィールド値", "File": "ファイル", "File Declaration": "ファイル説明", "File Management": "ファイル管理", "File size should small than 10M": "10M以下のファイルをアップロードしてください", "FILE TYPE": "ファイルタイプ", "file upload failed.": "アップロードに失敗しました", "file uploaded successfully": "アップロードを完了しました", "File Version": "ファイルバージョン", "Filters": "フィルター", "First": "最初", "first.": "最初", "Fixed Amount": "固定金額", "Fixed due date rule：Fixed due date or period end date": "", "Fixed Time": "", "For Fixed Day of Month, please input number from 1 and 28": "", "For No Claim Bonus": "ノークレームボーナスについて", "for Policy": "保険契約書", "For POS Premium Refund": "異動の返還保険料について", "for Proposal": "保険申込書", "Formula": "", "Formula Category": "計算式項目", "Formula Category (Clawback)": "計算式項目（払い戻し）", "Formula Code": "計算式コード", "Formula Description": "", "Formula Name": "計算式名", "Formula Name (Clawback)": "計算式名称（払い戻し）", "Formula Sub Category": "計算式サブカテゴリ", "Formula Sub Category (Clawback)": "計算式サブカテゴリ（払い戻し）", "Forward-dating": "先日付け", "Free Charge": "無料フラグ", "Freelook Period": "", "Front Line Channel": "", "Function Label Code": "機能ラベルコード", "Function label code already exists!": "機能ラベルコードは既に存在しています。", "Function Label Name": "機能ラベル名称", "Fund": "ファンド", "Fund Allocation": "ファンド配分", "Fund allocation percent summation must be 100%.": "ファンド配分の割合の合計は100%でなければなりません。", "Fund allocation percent summation must be 100%. ": "ファンド配分の割合の合計は100%でなければなりません。", "Gender": "性别", "Generate Matrix Table": "", "Generate Matrix Table Successfully": "", "Generate new policy number for renewal": "", "Generate New Policy Number For Renewal": "", "Generation Date": "作成日", "Goods": "販売商品", "Goods Category": "販売商品カテゴリー", "Goods Code": "販売商品コード", "Goods File Management": "商品ファイル管理", "Goods id is required": "販売商品を入力してください", "Goods Introduction": "販売商品説明概要", "Goods Label": "商品ラベル", "Goods Label Configuration": "商品ラベル設定", "Goods Name": "販売商品名", "Goods Type": "販売商品種類", "Goods Validation": "販売商品のバリデーション", "Goods Version": "販売商品バージョン", "Grace Period": "", "Group Policy Level": "団体契約ラベル", "Guarantee Period": "保証期間", "have_disabled": "無効", "Here has undone plans, please complete first": "プランの配置を完成してください", "Hotel": "ホテル", "ID Type": "IDタイプ", "If Commission GST is configured based on NetCommission, then we could configure as": "ネットコミッションに基づいてコミッションの消費税を設定する場合、次のように設定できます。", "If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "", "If multiple versions are defined for the benefit option at the product level, multi-version matching is not currently supported.": "", "If Open End Policy is enabled, \"Coverage Period\" will be ineffective.": "", "If sales platform is configured, all sub channels under this sales platform could sell this product.": "販売チャネルが設定されている場合、この販売チャネルの元にあるすべてのサブチャネルがこの商品を販売できます。", "If the effective date falls on a day that does not exist in the expiry month, and the calculated expiry date is set to the last day of that month, this adjustment will shift the expiry date to the 1st of the following month. (e.g. 2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1)": "", "Including Levy": "税込み", "Including Stamp Duty": "印紙税を含む", "Individual": "個人", "Initial Premium Period": "", "Input Method": "", "Insert fields": "", "Installment Detail": "", "Installment Premium": "分割払保険料", "Installment Premium Calculation Basis": "", "Installment Premium Calculation Method": "", "Installment Premium Frequency": "", "Insurable Interest": "被保険利息", "Insurable Interest SA": "被保険利息保険金額", "Insurable Interest SA Setting": "被保険利息保険金額の設定", "Insurance Applicant": "契約者", "Insurance Case": "保険ケース", "Insurance Charge Period": "払込期間", "Insurance Coverage Period": "保険期間", "Insurance Coverage Period Configuration": "補償期間種類設定", "insurance_goods_name": "販売商品名", "Insured": "被保険者", "INSURED INFO": "被保険者情報", "Insured Notice": "重要事項説明書", "Insuring Process Configuration": "保険加入プロセス設定", "Interval": "間隔", "Investment Product": "投資型商品", "Is Insured Mandatory?": "", "Is Open End Policy": "", "Issuance Agreement": "", "Issuance Mode Check": "発行モードチェック", "Issuing Model of Policy": "保険契約種類", "Key Account Channel": "", "language": "言語", "LANGUAGE": "言語", "Lapse Date": "", "Last Modifier": "最終更新者", "LATEST OF": "", "Launch": "", "Launch / Closure": "特約開始/終了", "Launch & Next": "特約開始", "Launch Date": "オンライン時間", "Launch Rider": "特約開始", "Launch Successfully": "", "LAUNCHED": "", "Leasing Channel": "リースチェネル", "Legal Beneficiary": "法定受取人", "Levy": "賦課金", "Liability Category": "補償内容種類", "Liability Claim Stack": "", "Liability Coverage / Premium": "補償内容/保険料", "Liability Coverage / Premium Remarks": "補償内容/保険料説明", "Liability is required": "補償が必要です", "Liability Limit": "補償内容の制限", "Liability Limit Remark": "補償内容制限の説明", "Liability Name": "補償内容名称", "Liability Order": "補償内容の順番", "Liability Premium": "補償内容の保険料", "Liability Premium Setting": "補償内容の保険料設定", "Liability Remark": "補償内容備考", "Liability SA": "補償内容の保険金額", "Liability SA Remark": "補償内容の保険金額の説明", "Liability SA Setting": "補償内容の保険金額設定", "Liability Stack": "", "liabilityPremium": "補償内容の保険料", "LiabilityPremiumRemarks": "補償内容保険料の説明", "Limit Sales Volume": "限定販売商品数", "limited": "制限", "Limited": "制限", "Main": "主契約", "Main Benefit": "", "Main Condition Type": "主な条件の種類", "Main Product Code": "主プロダクトコード", "Main Product Code / Name": "主な商品のコード/名称", "Mandatory": "必須", "Mandatory Liability": "必須補償", "Mandatory or Not": "必須か否か", "Mandatory Rider": "必須特約", "Mandatory Waiver": "必須免除", "Manual Annual Premium": "", "Manual Annual Premium Input Method": "", "Marketing Center": "マーケティングセンター", "Marketing Center - Application Elements Group": "", "Marketing Center - Marketing Goods": "", "Marketing Center - Package": "", "Marketing Center - Quick Quotation": "", "Marketing Center - Service Maintenance": "", "Marketing Goods": "販売商品設定", "Matched Label Content": "マッチされたラベル内容", "Max": "", "Max Allowed Designated Beneficiary Number": "最大許容指定受取人数", "Max Allowed Insured Number": "最大被保険者人数", "Max Allowed Number": "最大数", "Max Allowed Number Limit": "最大数", "Max Allowed Object Number Limit": "最大対象数制限", "Max Down-Regulation Ratio": "", "Max Guaranteed Renewable": "", "Max Number of Inputted Insured": "最大被保険者人数", "Max Up-Regulation Ratio": "", "Maximum 30 days": "最大30日", "Maximum Amount": "最大金額", "Maximum Sales Units": "最大販売ユニット数", "Maximum Voucher Numbers": "最大利用可能なクーポン券数", "Maximum Voucher numbers can be used in one-time": "最大一括利用可能なクーポン券数", "MENU": "メニュー", "Min": "", "Minimum Price to Show": "最低価格", "Modal Factor": "係数", "Modifier": "", "Month(s)": "", "Monthly Basis: The system calculates the monthly premium and then calculates each installment premium based on the number of months in that installment period. (For a full-year policy, the premiums of each installment are the same)": "", "More >>": "もっと", "Multi Language": "", "Mutually Exclusive Main Benefits": "", "Mutually Exclusive With": "", "My Creations": "", "My Task": "", "NCB Issue Type": "ノークレームボーナスの支給方法", "NCD": "NCD", "Net Premium Adjustment": "", "New Business": "新規契約", "New Tag": "", "Next": "次へ", "no": "いいえ", "No": "いいえ", "No Claim Discount": "無請求割引", "No Data": "該当データはありません", "No Results Found": "データなし", "No-Recurring Single Top Up / Single Top Up's collection status does not impact policy effectiveness.": "非定期的な単回追加/単回追加の回収状況は、保険契約の有効性に影響を与えません。\n\n", "No.": "番号", "Nominee Elements": "指定代理人要素", "NOMINEE INFO": "受取名義人情報", "Not Relevant": "関係なし", "Note: Fund allocation percent summation must be 100%.": "注：ファンド配分の割合の合計は100%でなければなりません。", "Note: Fund allocation percent summation must be 100%. ": "注：ファンド配分の割合の合計は100%でなければなりません。", "Notification Code": "通知コード", "Notification Configuration": "通知設定", "Notification Scenario": "通知シナリオ", "Notification Trigger": "通知トリガ", "number": "番号", "Number": "", "Number of Files/URLs": "ファイル数/URL数", "Object": "", "Object Category": "保険対象カテゴリー", "Object Component": "", "Object Elements": "補償対象物要素", "OBJECT INFO": "対象情報", "Object Sub-category": "補償対象物のサブカテゴリー", "Offset Date": "", "Offset from X Days Compare to Expired Date": "", "Offset from X days prior to due date": "", "okText": "確定", "On Sale": "販売中", "Once deleted, the configured notifications which related with these sales channels will also be deleted.": "一旦削除されると、これらの販売チャネルに関する通知も削除されます。", "Only normal policy and group policy support goods validation.": "", "Only one record is allowed for each questionnaire purpose under the same trigger. No duplicated allowed. Please check.": "同じトリガーの下では、各アンケートの目的に対して1つのレコードのみが許可されます。 重複できません。 確認してください。", "Open End Policy": "", "Open End Policy cannot be used together with Renewal Agreement, Expiry Date Rule, Coverage Period, Pre-calculate Premium installment method or Single Premium (Payment Period & Frequency), please check these configurations before submit.": "", "Open End Policy is already selected and cannot be used together with Allow Renewal. Do you want to deselect Open End Policy and proceed with selecting Allow Renewal instead?": "", "Optional": "任意", "Optional / Required": "", "Optional Liability": "任意補償", "Optional/Mandatory": "任意/必須", "Order": "順番", "Organization": "団体", "Other": "その他", "Other Setting": "その他の設定", "Overdue Auto Deduction": "", "Overdue Handling": "", "Overdue Status": "", "Package": "保険商品組合せ管理", "Package Agreement": "保険商品組合特約", "Package Category": "商品組合せカテゴリー", "Package Code": "商品組合せコード", "Package code is required": "商品組み合わせコードは必須項目です", "Package Configuration": "", "Package Management": "", "Package Name": "保険商品組合せ名", "Package Net Premium Adjustment": "", "PackageId is required": "商品組合せを入力してください", "Parameter have been changed. The uploaded matrixtable will be cleared. Please confirm.": "パラメータが変更されました。アップロードしたマトリクステーブルはクリアされます。ご確認ください。", "Parameters": "", "Parameters(Result)": "", "Participating Product": "配当商品", "Partner Fee Calculation": "パートナー料金の計算", "pay_method": "支払方式", "Payer Elements": "支払者要素", "PAYER INFO": "支払者情報", "Payment Defer Period Type": "支払遅延期間タイプ", "Payment Frequency": "払込方法", "Payment Option": "支払の選択", "Payment Period": "支払期間", "Payment Period Type": "支払期間タイプ", "payment_type": "支払タイプ", "Period Type": "期間タイプ", "Pin": "", "Plan": "プラン", "Plan (Plan Code + Plan Name)": "", "Plan Code": "補償内容プランコード", "Plan code is required": "補償プランコードは必須項目です", "Plan Configuration": "", "Plan Display": "", "Plan Group": "", "plan group code must be unique": "", "Plan Group Name": "", "Plan is required": "補償プランは必須項目です", "Plan Name": "補償プラン名", "Plan name is required": "補償プラン名称は必須項目です", "Plan Premium Model": "", "Plan Recommend": "", "Plan Recommendation-Matrix Table": "カバレッジプランの推奨-マトリックス表", "Planned Premium Configuration": "計画保険料の配置", "Planned premium is required": "計画保険料が必須です", "Please": "お願い", "Please add one “Beneficiary” at least.": "一つの受取人を追加してください。", "Please add one “Insured” at least.": "一つ以上の被保険者を追加してください。", "Please add one “Object” at least.": "一つ以上の補償対象を追加してください。", "Please add stack correlation": "", "Please associate goods and plan.": "販売商品と商品組合せを関連付けてください。", "Please Calculate first!": "まず計算してください。", "Please choose at least one coverage period agreement": "少なくとも1つ保険期間の約定項目を選んでください", "Please choose at least one installment premium frequency agreement": "", "Please choose at least one premium period agreement": "", "Please complete the configuration": "設定項目を完成してください", "Please configure intermediate SA calculation formula in Product Center for corresponding liability.": "商品センターで、該当する賠償責任に対する中間保険金額の計算式を定義してください。", "Please configure POS premium refund rule for each sales channel.": "販売チャネルごとにPOS保険料返金ルールを設定してください", "Please configure the min or max value of the range.": "範囲の最小値または最大値を設定してください。", "Please confirm that all configurations have been saved before submission": "設定内容先に保存してください。", "Please confirm that the information of the beneficiary is consistent with insured.": "受取人の情報が被保険者と一致していることを確認してください。", "Please confirm that the information of the beneficiary is consistent with policyholder.": "受取人の情報が保険契約者と一致していることを確認してください。", "Please confirm that the information of the insured is consistent with policyholder.": "被保険者の情報が保険契約者と一致していることを確認してください。", "Please copy your File URL first.": "まずファイルのURLをコピーしてください。", "Please define numerical values in the table without including the percentage sign. For example, if you wish to define 10%, simply enter 10 in the table.": "", "Please do not select the same fund for the same Premium Type": "同じ保険料タイプで同じファンドを設定しないでください。", "Please don’t enter special characters": "特殊文字を入力しないでください", "Please don’t enter special characters, such as~`!@#$%^*()+.": "特殊記号が入力できません、例えば~`!@#$%^*()+など", "Please don’t enter special characters, such as~`!@#$%^&*()+.": "特殊文字を入力禁止！例~`!@#$%^&*()+.", "Please ensure sum of beneficiary ratio equals to 100%.": "受益者比率の合計が100%に等しいことを確認してください。", "Please enter English letters": "", "Please enter numeric value": "数字型を入力してください。", "Please enter premium of regular top up or single top up.": "定期追加保険または単一追加保険を入力してください。", "Please enter the correct number": "正しい番号を入力してください", "Please fill in all quotation factors before clicking “Calculation”": "計算ボタンをクリックする前に、すべての見積要素を入力してください。", "Please fill in the following information before selecting rider product": "特約を選択する前に、以下の情報を入力してください。", "Please fill in the following quotation factors before calculation": "計算の前に、見積書の以下の要素を入力してください。", "Please input": "入力してください", "Please input at least one form item": "", "Please input at least one option of file, file address, and file declaration.": "ファイル名、ファイルアドレス、またはファイル説明を何れか一つ入力してください", "Please input both customer ID type and number.": "お客様のIDタイプおよび番号を入力してください。", "Please input in this format: 'HH:mm'. e.g. 12:00": "", "Please input number": "番号を入力してください", "Please input or paste the URL here": "ここにURLを入力または貼り付けてください。", "Please input Premium Allocation": "保険料配分を入力してください。", "Please input right format": "正しいフォーマットを入力してください", "Please select": "選択してください", "Please select a package or add a coverage plan.": "", "Please select a package or add a plan.": "", "Please select at least one element.": "一つ以上の要素を入力してください。", "Please select at least two default answer.": "2つ以上のデフォルト値を選択してください。", "Please select Fund": "ファンドを選択してください。", "Please select goods first": "まず商品を選択してください。", "Please select language": "言語を選択してください。", "Please select lanuch date first": "販売期間を選択してください", "Please select related formula for Commission": "手数料の関連計算式を選択してください", "Please select related formula for Commission Clawback": "返還手数料の関連計算式を選択してください", "Please select Related Package": "関連商品組合せを選択してください。", "Please select the object component that you want to add": "", "Please select the sub category that you want to add": "", "Please upload a CSV or Excel file.": "CSVまたはExcelファイルをアップロードしてください。", "Please upload file": "ファイルをアップロードしてください", "Please upload file of .xls, .xlsx": "xls/xlsxファイルのみをアップロードしてください", "pleaseInput": "入力してください", "Policy Administration Level": "", "POLICY BASIC INFO": "契約基本情報", "Policy Change": "", "Policy Claim Stack": "", "Policy Effective Without Collection (NB)": "契約有効で保険料の後払い(NB)", "Policy Effective Without Collection (Renewal)": "契約有効で保険料の後払い(Renewal)", "Policy Elements": "契約要素", "Policy Issuance Order": "保険契約オーダー", "Policy Manual": "契約マニュアル", "Policy Net Premium Adjustment": "", "Policy No.": "加入者番号", "Policy SA": "", "Policy Stack": "", "Policy Type": "", "Policy will be auto reviewed on each Review Recurrency from the Effective Date.": "", "Policy Year": "契約年度", "Policyholder": "契約者", "POLICYHOLDER INFO": "契約者情報", "PolicyStatusEnum.POLICY_EFFECT": "有効", "POS additional/refund net premium = (product net premium after POS - product net premium before) * unearned period / coverage period": "", "POS Premium Refund Type": "異動返還保険料の支給方法", "Premium Adjustment": "", "Premium Agreement": "保険料設定", "Premium Allocation": "保険料配分", "Premium Allocation must equal to 100%.": "", "Premium allocation section: Premium type “All” should not combined with other premium types.": "保険料割り当てセクション:保険料タイプ「すべて」は、ほかの保険料タイプと組み合わないでください", "Premium Allocation(%)": "保険料配分率(%)", "Premium Calculation Method": "保険料計算方法", "Premium Composition": "", "Premium Currency": "保険料通貨", "Premium Discount": "保険料割引", "Premium Due Date Rule": "", "Premium Due Date Rules": "", "Premium End Date Calculation Rule": "", "Premium Frequency": "", "Premium Funder Elements": "保険料提供者要素", "Premium Notice Date Compare with Due Date": "", "Premium Period": "保険料払込期間", "Premium Period & Installment": "", "Premium Period Configuration": "保険料期間設定", "Premium Period Type": "保険料払込期間タイプ", "Premium Period Value Type": "", "Premium Related": "保険料関連", "Premium Type": "保険料タイプ", "Pricing Currency": "", "Pricing Currency corresponding to the calculated result of the formula. If select \"Premium Currency\", the formula's calculated result will be output directly.If \"SA Currency\", the calculated result of the formula will be converted from the SA currency to the premium currency using the exchange rate.": "", "Process Related": "プロセス関係", "Product": "商品", "Product / Liability": "商品/補償", "Product & Liability": "", "Product and liability are required": "商品と責任は必須項目です", "Product Category": "商品カテゴリ", "Product Center": "", "Product Claim Stack": "", "Product Code": "保険商品コード", "Product Code - Name": "", "Product Code - Version": "", "Product Code/Name": "商品コード/名", "Product Correlation Matrix": "商品相関行列", "Product Info": "商品情報", "Product Name": "商品名", "Product Name / Code": "商品名/分類", "Product Net Premium Adjustment": "", "Product Overdue Status": "", "Product Premium": "商品保険料", "Product Premium Setting": "商品保険料設定", "Product SA": "商品保険金額", "Product SA Setting": "商品保険金設定", "Product Stack": "", "Product Tax": "商品税", "Product Version": "保険商品バージョン", "Products Involved in Accumulation": "", "Public Display Period": "公開期間", "Public Task": "", "Publish": "発表", "Publish Success": "公開に成功しました", "Publish successfully": "", "Question Code": "質問コード", "Question Declaration": "質問型告知", "Question Description": "質問説明", "Questionnaire": "アンケート", "QUESTIONNAIRE": "アンケート", "Questionnaire Name": "アンケート名", "Questionnaire Purpose": "アンケート目的", "Quick Menu": "", "Quotation": "クォーテーション", "Quotation Code": "見積書コード", "Quotation Details": "クォーテーション詳細", "Random": "ランダム", "Range": "範囲", "Ratetable Category": "レートテーブル種類", "Ratetable Code": "レートテーブルコード", "Re-Define Effective Date Rule": "有効日ルールの再定義", "Recommend": "おすすめプラン", "recommendOnlyOne": "1つのプランのみをお勧めしてください", "Refund Formula": "返戻計算式", "Regular Top Up": "定期増額", "Regulation of Products": "商品管理", "Related Main": "", "Related Main Product": "関連の主契約", "Related Package Info": "関連商品組合せ情報", "Relational Policy No.": "リレーション加入者番号", "Renewal": "自動継続", "Renewal Agreement": "", "Renewal Extraction Period": "", "Renewal Grace Period": "", "Renewal Policy Effective Date Rule": "", "Renewal Policy Effective Without Collection": "", "Renewal Proposal Submit Date": "", "Retrieve the policy to be reviewed and generate the bill X days prior to the Review Date.": "", "Review Extraction Date": "", "Review Recurrence": "", "Rider": "特約", "Rider Code": "特約コード", "Rider Liability": "特約補償", "Rider Name": "特約名", "Rider Premium Payment Method": "特約保険料の支払方法", "Rider Product Code": "特約商品コード", "Rider Product Code / Name": "", "RSTU／STU must be collected for initial premium": "初期保険料としてRSTU／STUを収集する必要があります", "Rule/Rule Set": "", "Rule1：Corresponding to effective date or period end date": "", "Rule2：Corresponding to effective date or next date of period end date": "", "Rule3: Corresponding to effective date or period end date and fixed Feb 28 as period end date": "", "SA / Premium Details of Each Insured": "各被保険者の保険金額/保険料詳細", "SA Currency": "保険金の通貨", "Sales Attributes": "販売属性", "Sales Channel": "販売チャネル", "Sales Channel is empty or multiple sales channels exist.": "販売チャネルは空または複数の販売チャネルが存在します。", "Sales Configuration": "販売チャネル設定", "Sales District": "販売エリア", "Sales Platform": "販売プラットフォーム", "Sales Time": "販売期間", "salesType": "販売タイプ", "Same as Launch Period": "", "Same as SA Currency": "", "Same Premium Calculation for Each Insured": "各被保険者に同じ保険料計算方法を適用する", "Save": "保存", "Save Sort": "ソート順", "Save success": "", "Save Successful.": "保存しました。", "Save successfully": "保存完了", "Save Successfully": "保存成功", "save_suc": "保存しました", "Scenario Validation": "シナリオ検証", "Search": "検索", "Search Goods Name": "販売商品名を検索", "Search Voucher Name": "クーポン券名称検索", "Secondary Life Insured Elements": "第二被保険者要素", "Select Benefit Factors": "利益要素選択", "Select from the coverage periods in the associated products, or click \"Add New\" to create a new one.": "", "Select from the premium periods in the associated products, or click \"Add New\" to create a new one.": "", "Select Parameter": "パラメータを選択", "SELECT PLAN": "補償プランを選択してください", "Select Product": "", "Select Questionnaire Language": "アンケート言語の選択", "Select Relationship Package": "関係パッケージの選択", "Select Rider": "", "Select Stack Code": "スタックコードを選択", "Selected sales channel has been configured with conflicted POS premium refund rule.": "選択された販売チャネルには、異動返還保険料の支給ルールが設定されています。", "Service Code": "", "Service Company": "", "Service Effective Time": "", "Service Fee Clawback": "", "Service Maintenance": "", "Service Name": "", "Service Type": "サービス種類", "service_page_amount": "サービス金額", "service_page_create_time": "作成日時", "service_page_creator": "作成者", "service_page_effective_time": "サービス有効日時", "service_page_effective_time2": "有効日時", "service_page_fac_code": "推進者コード", "service_page_fac_name": "推進者名", "service_page_file_name": "ファイル名", "service_page_modi_time": "編集日時", "service_page_modifier": "編集者", "service_page_service_code": "サービスコード", "service_page_service_name": "サービス名", "service_page_service_status": "サービスステータス", "service_page_total": "合計", "service_page_used": "使用済み", "Set Default Time": "", "Set Fixed Time": "", "Set Relationship with Package": "パッケージとの関係を設定する", "Settlement Rule": "決済ルール", "Show Financial Declaration": "財務告知表示要否", "Show Health Declaration": "健康告知表示要否", "Show in Application Form": "申請書に表示する", "Single Top Up": "単一追加入金", "Sorry no goods were found": "商品データがありません", "Sort": "ソート順", "sort successfully": "ソートしました", "Special Agreement": "特別な契約", "Specified SA & Premium": "", "Stack Code": "スタックコード", "Stack code have been changed. The uploaded file will be cleared. Please confirm.": "スタックコードが変わりましたので、アップロードされたファイルもクリアされます。ご確認ください。", "Stack Component Name": "", "Stack Content": "", "Stack Description": "スタック説明", "Stack Level": "", "Stack Name": "スタック名称", "Stack Template Code": "", "Stack Template Name": "", "Stack Type": "スタックタイプ", "Stack Unit": "スタック単位", "Stack Unit Type": "", "Stack Value": "スタック値", "Stack Value Matrix": "スタック値のマトリックス", "Stack Value Type": "値タイプ", "Stamp Duty": "印紙税", "Standard Premium": "標準保険料", "Start date": "開始日", "Start Time": "", "status": "状況", "Sub Condition Type": "サブ条件", "Submit": "確定", "Submit success": "提出しました", "Submit successfully": "送信に成功しました", "Submit successfully!": "", "Sum Assured": "保険金合計", "Summary": "", "Support maintaining the association between stack values, only support value types of fixed amount and enumeration.": "", "Supported Assignee Type": "サポートされている譲渡先タイプ", "Supported Beneficiary Type": "サポートされている受益者タイプ", "Survival Benefit Configuration": "生存給付金構成", "Switching the package will clear all definitions under the plan. Please confirm to proceed with the change.": "商品組合を切り替えると、補償プラン内のすべての定義がクリアされます。この変更を進めてもよろしいですか？", "System error": "システムエラー", "System will automatically review the policy on each Review Recurrence starting from the effective date.": "", "System will automatically review the policy on each Review Recurrence starting from the effective date. When review recurred on monthly basis, 'Premium Period Type' in Premium Period & Installment must be set up as 'Monthly' as well.": "", "Tag": "", "Target User of Voucher": "クーポン券支給ターゲットユーザ", "Targeted Customer": "ターゲット顧客", "Tax": "税", "Tax Amount": "税額", "Tax Setting": "税金設定", "Tax Type": "税金タイプ", "Tax Value Type": "税金額タイプ", "Template Code / Name": "", "Tenant Enumerated Sync": "", "text_delete_success": "削除しました", "The coverage period agreement will be deleted": "", "The Display Time should be within the Launch Date": "表示時間は販売期間に設定してください", "The effective date of this file can't intersect with the existed file": "このファイルの有効開始日は既存ファイルのものと重複できません", "The fixed due date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.": "", "The following package(s) have used this application elements group. Editing this record and the following package(s) will be affected. Please check before change:": "下記の商品組合せ下記のに、この加入要素グループが使用されています。本レコードを編集すると下記の商品組合せに影響がありますので、編集前にご確認をください：", "The fund should be unique for universal product.": "このファンドはユニバーサル商品に対してユニークである必要があります。", "The goods code could only contain letters, numbers, and underscores (_).": "商品コードには、文字、数字、およびアンダースコア（_）のみを含めることができます。", "The input number cannot be zero": "", "The insurance application failed. Please check the information and resubmit.": "", "The marketing goods contain policy types other than Normal Policy or Group Policy. These types are not subject to standard validation checks before launching. Do you want to proceed?": "", "The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59": "", "The package code could only contain letters, numbers, and underscores (_).": "商品組合せコードには、文字、番号、およびアンダースコア（_）のみを含めることができます。", "The packages associated with the coverage plan must be within the selected packages.": "", "The packages associated with the plan must be within the selected packages.": "", "The plan code could only contain letters, numbers, and underscores (_).": "プランコードには、文字、数字、およびアンダースコア（_）のみを含めることができます。", "The product lacks liabilities, and please add the configuration in the product center": "商品の責任内容は設定されていません、プロダクトセンターで設定してください", "The questionnaire does not exist.": "", "The questionnaire is filled out. Proceed to insurance application.": "", "The sales channels have related configured notifications, please update notification configurations if needed.": "販売チャネルには関する通知があります。必要に応じて通知設定を更新してください。", "The Sales Time should be within the Launch Date": "販売時間は発売日の範囲内でなければなりません", "The second value should greater than the first value": "", "The sum of Maximum Sales Units of each plan is greater than Goods Wholesale Volume!": "", "The sum of Maximum Sales Units of each plan is greather than Goods Wholesale Volume!": "各プランの最大販売数の合計数は商品の卸売り量より大きい！", "There is unfinished editing in <{{title}}>. Please complete before proceeding.": "", "There must be one or more rider benefits under the virtual main benefit.": "バーチャル主契約に1つ以上の特約を付加しなければなりません。", "This application elements group is relied on": "", "This file was effective in the past, are you sure to delete it?": "該当のファイルは過去に有効でしたが、削除してもいいですか？", "This record was configured based on old version. Currently, viewing and editing are not supported.": "このレコードは古いバージョンに基づいて設定されていましたので、現時点では照会と編集はできません。", "Ticket": "チケット", "Tied Agent": "社内募集人", "Time Zone": "時間帯", "Times Type": "回数タイプ", "Tips: For relationship, you can configure as “attachable”, “non-attachable” or “compulsory”.": "", "Total Premium": "総保険料", "Transaction Amount": "トランザクション金額", "Transaction Date": "トランザクション日", "Transaction Information": "取引情報", "Trigger": "トリガー", "Trigger Point by Event Policy": "事件契約のトリガーポイント", "Trip": "旅行", "Trustee Elements": "受託者要素", "TRUSTEE INFO": "受託者情報", "Type": "", "Unanswered question exists, please confirm.": "未回答の質問があります。ご確認してください。", "Uncompleted questionnaire exists, please confirm.": "未記入のアンケートが存在しますので、ご確認ください。", "Underwriting Error Message": "査定エラーメッセージ", "Underwriting Order": "査定オーダー", "Underwriting Related": "査定関連", "Underwriting Type": "査定タイプ", "Unified Underwriting Conclusion Flag": "", "Unlimited": "無制限", "Unnamed Insured": "", "Unset": "未設定", "Updatable or Not": "更新可能かどうか", "Updatable When Manual UW": "マニュアル査定で更新可否", "Updatable When POS": "異動で更新可否", "Upload": "アップロード", "Upload Batch No.": "アップロードバッチ番号", "Upload Fail!": "アップロードに失敗しました!", "Upload Files": "アップロードファイル", "Upload Successful.": "アップロードに成功しました。", "Upload Successfully": "アップロードしました", "URL": "URL", "usage_time": "使用期間", "Usage-based Goods": "利用ベース商品", "Use or not": "", "User Input": "入力してください", "Validation Response": "チェックレスポンス", "value": "", "Value Assignment": "", "Value Code": "値", "Value Type": "値タイプ", "Variable Amount": "可変金額", "Vehicle Additional Equipment": "自動車付加設備", "Vehicle Database Name": "車両データベース名", "Vehicle Loan": "車両ローン", "Vehicle Market Value Formula": "", "Vehicle Related Configuration": "", "Version": "バージョン", "View Component": "", "View My Creations": "", "View Voucher": "クーポン券照会", "Voucher": "クーポン券", "Voucher Application Channel": "クーポン券適用チャネル", "Voucher Basic Info": "クーポン基本情報", "Voucher Calculation & Effective Rule": "クーポン券計算と発効規則", "Voucher Channel": "クーポン券適用チャネル", "Voucher Code": "クーポンコード", "Voucher Consumption Status": "クーポン券の消費状況", "Voucher Correlation": "クーポン券設定", "Voucher Detail": "クーポン詳細", "Voucher Effective Period": "クーポン有効期間", "Voucher Face Amount": "クーポン券額面金額", "Voucher Name": "クーポン券名", "Voucher Number Generation Rule": "クーポン券採番ルール", "Voucher Status": "クーポン券ステータス", "Voucher Type": "クーポン券タイプ", "Voucher Type - Face Amount": "クーポンタイプ‐額面金額", "Voucher Value Type": "クーポン券タイプ", "Waiver": "免除", "Waiver Details": "免除詳細", "Waiver Product": "免除商品", "Waiver Product Duplicated": "免除商品は重複しました。", "Waiver Rider Liability": "免除特約の補償", "Warning Threshold": "警告のしきい値", "Whether to keep the voucher balance": "クーポン券お釣り要否", "Wholesale Volume": "販売件数", "Wholesale Volume Warning": "販売上限ワー二ング", "With Guarantee Period": "保証期間付き", "Year": "", "Year(s)": "", "yes": "はい", "Yes": "はい", "Yes-Recurring Single Top Up / Single Top Up must be collected before policy is effective.": "はい、契約が有効になる前に定期追加保険料/単一追加保険料を収集する必要があります。", "You are applying": "申込中", "You can only upload PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG": "PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEGタイプのファイルのみを許可します。", "You can only upload xls, xlsx": "アップロードできるのはxls、xlsxのみです", "You can only upload xls, xlsx, doc, docx, pdf, jpg or png.": "アップロード可能なファイいるの拡張子はxls, xlsx, doc, docx, pdf, jpg, pngのみです。", "You can only upload XLSX/CSV": "", "Your policy has been issued.": "契約が発行されました", "Your proposal has been issued.": "ご提案が発行されました。"}