{"- -": "", "--": "", "+ Add": "", "Accumulate Product SA to Policy SA": "", "Accumulated to Premium": "", "Add": "Add", "Add a Condition": "", "Add Block": "Tambahkan Blok", "Add Declaration Library": "Add Declaration Library", "Add New": "Tambah baru", "Add New Voucher": "", "Add Object Component": "", "Add POS Items": "", "Add Product And Liability": "", "Add Relationship Package": "", "Add Rider": "", "Add successfully": "", "Additional Grace Period": "", "Adjust to 1st if Effective Day Not in Expiry Month": "", "Adjusted Market Value Floating Ratio": "", "After": "", "After(Days)": "", "Age Calculation Basis": "<PERSON><PERSON>", "Age Validation": "", "Agency Company": "", "Agency Company or Sales Platform": "", "Agent": "", "Agent Category": "", "Agreement Code": "Kode kesepakatan", "ALL": "<PERSON><PERSON><PERSON>", "All Creations": "", "All questions have been answered.": "", "All relevant addresses will be deleted together, please confirm!": "", "All relevant bankCode will be deleted together, please confirm!": "", "Allow Manual Adjustment": "", "Allow Master Policy Issuance": "", "Allow Renewal": "", "Allow Renewal is already selected and cannot be used together with Open End Policy. Do you want to deselect Allow Renewal and proceed with selecting Open End Policy instead?": "", "​Allowed backdating range compared to Policy Insure Date. E.g. Insure Date is 15th Dec, if backdating range is 3 days ~ 6 days, the allowed range of Effective Date is 9th Dec ~ 12th Dec.": "", "​Allowed forward-dating range compared to Policy Insure Date. E.g. Insure Date is 15th Dec, if forward-dating range is 3 days ~ 6 days, the allowed range of Effective Date is 18th Dec ~ 21th Dec.": "", "Amount Limit": "<PERSON><PERSON><PERSON>", "Amount Limit Remark": "<PERSON><PERSON><PERSON>", "Amount Range": "", "An open-end policy does not have a fixed expiry date. Coverage continues unless terminated, and is typically reviewed periodically.": "", "AND": "", "and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula": "", "Annual Premium": "", "Annuity Configuration": "", "Answer": "<PERSON><PERSON><PERSON>", "Applicable Scenario": "", "Application": "", "Application Elements": "aplikasi Elements", "Application Elements Group": "", "Application Elements Group Code": "", "Application Elements Group Name": "", "Application Information": "", "Applied Unit": "", "Are you sure to clear this section?": "", "Are you sure to delete this POS Item ?": "", "Are you sure to delete this record ?": "", "Are you sure you want to delete these sales channels": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus saluran penjualan ini", "Assignee Elements": "", "Associated Goods and Plans": "", "Attach to Product": "", "Attached To": "", "Auto Deduction Date": "", "Auto Deduction Date Compare to Due Date": "", "Automatic Adjustment": "", "Available": "Tersedia", "Back": "", "Back to Search": "Ke<PERSON>li ke Pencarian", "Back to Validation": "", "Backdating": "backdating", "Backdating or Forward-dating period calculation basis": "", "Bank": "", "Base Currency": "", "base_next": "<PERSON><PERSON><PERSON><PERSON>", "Basic Configuration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dasar", "Basic Info": "", "Basic Information": "", "Be Attached By": "", "Before(Days)": "", "Belonging Info": "", "Beneficial Owner Elements": "", "Beneficiary Elements": "", "BENEFICIARY INFO": "", "Benefit Configuration": "<PERSON><PERSON><PERSON>", "Benefit Illustration": "", "Benefit Option Mapping": "", "Benefit Option Selection": "", "Benefit Option Selection & Matching": "", "Broker Company": "", "btn_upload": "Upload", "Bulk Delete": "massal Delete", "By Formula": "", "Caculation Item": "", "Calculate": "", "Calculation Cycle": "Calculation Cycle", "Calculation Method": "", "Campaign Discount": "", "Cancel": "Membatalkan", "Cancel pin": "", "Cancellation Type": "", "Car Owner": "", "Cash Bonus Configuration": "", "Category": "", "Change installment premium calculation method will clear the existing configuration, please confirm.": "", "Changes have not been saved.": "", "Changing beneficiary category will clear the existing beneficiary information.": "", "Changing insured category will clear the existing insured information.": "", "Changing policyholder category will clear the existing policyholder information.": "", "Changing this setting will clear all configured coverage details and related information. Are you sure to continue?": "", "Channel": "", "Channel and channnel - sub channel could not co-exisit. Please change.": "", "Choose the type of charge period to be supported": "<PERSON><PERSON><PERSON> jenis periode biaya harus didukung", "Choose the type of coverage period to be supported": "<PERSON><PERSON><PERSON> jenis masa pertanggungan harus didukung", "Claim Experience": "", "Claim Stack Correlation": "", "Claim Stack Execution Order": "", "Claim Stack Setting": "", "claim_upload_time": "Upload <PERSON><PERSON><PERSON>", "Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.": "", "Clear All": "", "Click here to upload": "", "Click or drag the file here to upload": "", "Closure & Next": "", "Closure Rider": "", "Code": "", "Code & Question Declaration": "Kode & Pertanyaan <PERSON>", "Collapse all": "", "Commission Clawback": "", "Commission Ratio": "<PERSON><PERSON>", "Company Name": "<PERSON><PERSON>", "Component Code": "", "Component Description": "", "Component Name": "", "Configuration Validation": "", "Configure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Configure Benefit Details": "", "Confirm": "", "Congratulations!": "", "Consentee Elements": "", "CONSENTEE INFO": "", "Contact Person Elements": "", "Content Category": "", "Copy from Insured": "", "Copy from Policyholder": "", "Copy Reminder": "", "Copy Successfully": "", "Copy to New Goods": "<PERSON>in Untuk New Barang", "Copy to New Version": "<PERSON>in ke New Version", "Copying elements from the insured module will overwrite existing content, please confirm.": "", "Copying elements from the policyholder module will overwrite existing content, please confirm.": "", "Coverage Date Change Type": "", "Coverage Period": "", "Coverage Period Configuration": "", "Coverage Period Fields to Revise": "", "Coverage Period Range": "Cakupan Periode Rentang", "Coverage Period Type": "", "Coverage Period Value Type": "", "Coverage Type": "", "Currency": "<PERSON>", "Current Rider Status on Package": "", "Customer ID No.": "", "Customer Info": "", "Customer Name": "", "Customized Document": "disesuaikan <PERSON>", "Cut off Date": "Cut off Date", "Cut off Time": "Cut off Time", "Daily Basis: The system calculates the daily premium and then calculates each installment premium based on the number of days in that installment period. (For a full-year policy, the premiums of each installment could be different)": "", "Data Address": "Data Alamat", "Date + Time": "", "Date of Birth": "", "Day of Fixed Due Date": "", "Day(s)": "", "Day(s) After Due Date": "", "Day(s) Before Due Date": "", "Days": "hari-hari", "Days Type": "", "Declarant": "pember<PERSON><PERSON>", "Declaration": "<PERSON><PERSON><PERSON><PERSON>", "Declaration Category": "<PERSON><PERSON><PERSON><PERSON>", "Declaration Configuration": "<PERSON><PERSON><PERSON><PERSON>", "Declaration Content Category": "<PERSON><PERSON><PERSON><PERSON>", "Declaration Detail": "<PERSON><PERSON><PERSON><PERSON> Detil", "Declaration Library": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Declaration Question Code": "<PERSON><PERSON><PERSON><PERSON>", "Declaration Type": "Deklarasi Type", "Deduction Date": "", "Deduction Date Compare to Due Date.e.g. if deduct on due date, enter 0 if deduct prior to due date, enter 1, 2, 3 etc.": "", "Deduction Date Compare to renewal Policy effective date.e.g. if deduct on effective date, enter 0 if deduct prior to effective date, enter 1, 2, 3 etc.": "", "Default Answer": "<PERSON><PERSON><PERSON> standar", "Default Time": "", "Default Time Editable": "", "Default Value-Matrix Table": "", "Defer Period": "", "Define auto submit time for renewal proposal by comparing with previous policy expired date. If not configured, a manual submit is needed.": "", "Define the day before policy expired date and days after expired date to extract renewal policy": "", "Defined Application Elements": "", "Delete": "", "Delete Failed": "", "Delete Success": "", "Delete Successfully": "", "Deleted successfully": "keberhasilan delete", "describe": "<PERSON><PERSON><PERSON><PERSON>", "Description": "", "Designated Beneficiary": "", "Display Name": "<PERSON><PERSON> ta<PERSON>", "Display Name of Field": "Tam<PERSON><PERSON> Nam<PERSON>", "Display or Not": "Tampilan atau Tidak", "Dispute Settlement": "<PERSON><PERSON><PERSON><PERSON><PERSON> seng<PERSON>a", "Documents Display": "<PERSON><PERSON><PERSON>", "Download Successfully": "", "Download Template": "Template Download", "Driver": "", "Due Date": "", "Dunning Rule": "", "Duplicate Bank. Please change.": "", "Duplicate Broker Company. Please change.": "", "Duplicate Company Name. Please change.": "", "Duplicate formula configuration for same formula category and sub category. Please edit original one.": "", "Duplicate Front Line Channel. Please change.": "", "Duplicate Key Account Channel. Please change.": "", "Duplicate Leasing Channel. Please change.": "", "Duplicate Order. Please change.": "", "Duplicate Sales Platform. Please change.": "", "Duplicated data exists": "", "Duration to Display": "Du<PERSON><PERSON> ke Tampilan", "dutyText": "Kewajiban", "EARLIEST OF": "", "Edit Claim Stack Definition": "", "Edit Declaration Library": "Edit Declaration Library", "Edit Goods Label": "", "Edit Plan": "", "Edit Product And Liability": "", "Edit Reminder": "", "edit success": "mengedit sukses", "Edit Voucher": "", "Editing": "", "EDITING": "", "Editing benefit factors will clear the existing uploaded benefit details，please confirm.": "", "Effective": "", "EFFECTIVE": "", "Effective Date Month End to Expiry Date Month End": "", "Effective Date Month End to Expiry Date Month End: If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. ******** ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" ******** 00:00:00 ~ 20240228 23:59:59)": "", "Effective Date Rule": "Tanggal Aturan Efektif", "Effective end date cannot be earlier than the start date.": "", "Effective From": "", "Effective Period": "Masa efektif", "Effective Period Extension for Reversed Voucher": "", "Element": "", "Element Value": "elemen <PERSON>", "Elements": "", "Elements Detail Configuration": "", "Elements Group": "", "Elements Matrix": "", "Elements Selection": "", "Elements Value": "", "Enable Multi-Main Benefits": "", "Enable Payment": "", "Enable Underwriting": "", "End date": "", "End Date": "<PERSON><PERSON> akhir", "End Time": "", "Enter an integer greater than or equal to 1": "", "Enumerate Configuration": "Menghitung Konfigurasi", "Enumerated": "enumerasi", "Enumeration": "", "Enumeration Configuration Method": "", "Enumeration values cannot be empty.": "", "Existing Stack in the Product": "", "Exists duplicate liability orders": "", "Exists duplicate value": "", "Expand all": "", "Expired": "", "Expiry Date Adjustment": "", "Expiry date calculation method": "", "Expiry Date Calculation Method": "", "Expiry Date CalMethod": "", "Expiry Date Rule": "", "Expiry Date should be after Effective Date": "", "Expiry Time Agreement Type": "", "Extra Loading": "", "Extra Premium": "", "Extract Bill Date": "", "Extract day(s) must be greater than offset day(s)": "", "Extract premium from X days prior to due date": "", "Extract review policy and generate bill from X days prior to Effective Date + .total Review Recurrencies.": "", "Extraction Method": "", "Facilitator Code": "", "Facilitator Code & Name": "Kode fasilitator & Nama", "Facilitator Name": "", "Fee Type": "<PERSON><PERSON><PERSON>", "Fellow Traveller": "", "Field Name": "Nama Field", "Field Validation Rule": "", "Field Value": "", "File": "Mengajukan", "File Declaration": "Deklarasi File", "File Management": "", "File size should small than 10M": "Ukuran file harus kecil dari 10M", "FILE TYPE": "", "file upload failed.": "upload file gagal.", "file uploaded successfully": "berkas ber<PERSON><PERSON>h", "File Version": "", "Filters": "", "First": "", "first.": "", "Fixed Amount": "", "Fixed due date rule：Fixed due date or period end date": "", "Fixed Time": "", "For Fixed Day of Month, please input number from 1 and 28": "", "For No Claim Bonus": "", "for Policy": "", "For POS Premium Refund": "", "for Proposal": "", "Formula": "", "Formula Category": "", "Formula Category (Clawback)": "", "Formula Code": "", "Formula Name": "Formula Nama", "Formula Name (Clawback)": "", "Formula Sub Category": "", "Formula Sub Category (Clawback)": "", "Forward-dating": "<PERSON><PERSON><PERSON>ken<PERSON>", "Free Charge": "", "Freelook Period": "", "Front Line Channel": "", "Function Label Code": "Fungsi Label Kode", "Function label code already exists!": "Fungsi kode label sudah ada!", "Function Label Name": "Fungsi Label Nama", "Fund": "", "Fund Allocation": "", "Fund allocation percent summation must be 100%.": "", "Fund allocation percent summation must be 100%. ": "", "Gender": "", "Generate Matrix Table": "", "Generate Matrix Table Successfully": "", "Generate new policy number for renewal": "", "Generate New Policy Number For Renewal": "", "Generation Date": "", "Goods": "", "Goods Category": "barang <PERSON>", "Goods Code": "<PERSON><PERSON> bar<PERSON>", "Goods File Management": "", "Goods id is required": "Barang id diperlukan", "Goods Introduction": "barang <PERSON>dahuluan", "Goods Label": "", "Goods Label Configuration": "", "Goods Name": "", "Goods Type": "", "Goods Validation": "", "Goods Version": "<PERSON><PERSON><PERSON> barang", "Grace Period": "", "Group Policy Level": "", "Guarantee Period": "", "have_disabled": "Dengan disabilitas", "Here has undone plans, please complete first": "<PERSON><PERSON><PERSON> memiliki re<PERSON>, silakan leng<PERSON> pertama", "Hotel": "", "ID Type": "", "If Commission GST is configured based on NetCommission, then we could configure as": "", "If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. ******** ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" ******** 00:00:00 ~ 20240228 23:59:59)": "", "If multiple versions are defined for the benefit option at the product level, multi-version matching is not currently supported.": "", "If Open End Policy is enabled, \"Coverage Period\" will be ineffective.": "", "If sales platform is configured, all sub channels under this sales platform could sell this product.": "", "If the effective date falls on a day that does not exist in the expiry month, and the calculated expiry date is set to the last day of that month, this adjustment will shift the expiry date to the 1st of the following month. (e.g. 2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1)": "", "Including Levy": "", "Including Stamp Duty": "", "Individual": "", "Initial Premium Period": "", "Input Method": "", "Insert fields": "", "Installment Detail": "", "Installment Premium": "", "Installment Premium Calculation Basis": "", "Installment Premium Calculation Method": "", "Installment Premium Frequency": "", "Insurable Interest": "", "Insurable Interest SA": "", "Insurable Interest SA Setting": "", "Insurance Applicant": "<PERSON><PERSON><PERSON><PERSON>", "Insurance Case": "<PERSON><PERSON><PERSON><PERSON>", "Insurance Charge Period": "<PERSON><PERSON><PERSON><PERSON>", "Insurance Coverage Period": "Asura<PERSON><PERSON>", "Insurance Coverage Period Configuration": "Asuransi Cakupan Periode Konfigurasi", "insurance_goods_name": "<PERSON><PERSON> barang", "Insured": "", "INSURED INFO": "", "Insured Notice": "Pemberitahuan Tertanggung", "Insuring Process Configuration": "Mengasuransikan Konfigurasi Proses", "Interval": "Selang", "Investment Product": "", "Is Insured Mandatory?": "", "Is Open End Policy": "", "Issuance Agreement": "", "Issuance Mode Check": "", "Issuing Model of Policy": "Menerbitkan Model Polis", "Key Account Channel": "", "language": "Bahasa", "LANGUAGE": "", "Lapse Date": "", "Last Modifier": "", "LATEST OF": "", "Launch": "", "Launch / Closure": "", "Launch & Next": "", "Launch Date": "<PERSON><PERSON> peluncuran", "Launch Rider": "", "Launch Successfully": "", "LAUNCHED": "", "Leasing Channel": "", "Legal Beneficiary": "", "Levy": "", "Liability Category": "", "Liability Claim Stack": "", "Liability Coverage / Premium": "Kewajiban Cakupan / Premium", "Liability Coverage / Premium Remarks": "Kewajiban Cakupan / Premium Keterangan", "Liability is required": "", "Liability Limit": "", "Liability Limit Remark": "", "Liability Name": "", "Liability Order": "", "Liability Premium": "", "Liability Premium Setting": "", "Liability Remark": "kewajiban Remark", "Liability SA": "", "Liability SA Remark": "", "Liability SA Setting": "", "Liability Stack": "", "liabilityPremium": "", "LiabilityPremiumRemarks": "", "Limit Sales Volume": "Batas Volume Penjualan", "limited": "terbatas", "Limited": "Terbatas", "Main": "<PERSON><PERSON><PERSON>", "Main Benefit": "", "Main Condition Type": "", "Main Product Code": "", "Main Product Code / Name": "", "Mandatory": "", "Mandatory Liability": "", "Mandatory or Not": "", "Mandatory Rider": "", "Mandatory Waiver": "", "Manual Annual Premium": "", "Manual Annual Premium Input Method": "", "Marketing Center": "", "Marketing Center - Application Elements Group": "", "Marketing Center - Marketing Goods": "", "Marketing Center - Package": "", "Marketing Center - Quick Quotation": "", "Marketing Center - Service Maintenance": "", "Marketing Goods": "<PERSON><PERSON><PERSON><PERSON>", "Matched Label Content": "", "Max": "", "Max Allowed Designated Beneficiary Number": "", "Max Allowed Insured Number": "", "Max Allowed Number": "", "Max Allowed Number Limit": "", "Max Allowed Object Number Limit": "", "Max Down-Regulation Ratio": "", "Max Guaranteed Renewable": "", "Max Number of Inputted Insured": "<PERSON> di<PERSON>", "Max Up-Regulation Ratio": "", "Maximum 30 days": "Maximum 30 days", "Maximum Amount": "", "Maximum Sales Units": "Maksimum Penjualan Unit", "Maximum Voucher Numbers": "", "Maximum Voucher numbers can be used in one-time": "", "MENU": "", "Min": "", "Minimum Price to Show": "Harga Minimum untuk <PERSON>", "Modal Factor": "modal Factor", "Modifier": "", "Month(s)": "", "Monthly Basis: The system calculates the monthly premium and then calculates each installment premium based on the number of months in that installment period. (For a full-year policy, the premiums of each installment are the same)": "", "More >>": "", "Multi Language": "", "Mutually Exclusive Main Benefits": "", "Mutually Exclusive With": "", "My Creations": "", "My Task": "", "NCB Issue Type": "", "NCD": "", "Net Premium Adjustment": "", "New Business": "", "New Tag": "", "Next": "", "no": "Tidak", "No": "", "No Claim Discount": "", "No Data": "Tidak ada data", "No Results Found": "", "No-Recurring Single Top Up / Single Top Up's collection status does not impact policy effectiveness.": "", "No.": "", "Nominee Elements": "", "NOMINEE INFO": "", "Not Relevant": "", "Note: Fund allocation percent summation must be 100%.": "", "Note: Fund allocation percent summation must be 100%. ": "", "Notification Code": "Pemberitahuan Kode", "Notification Configuration": "Kon<PERSON><PERSON><PERSON><PERSON> notif<PERSON>", "Notification Scenario": "", "Notification Trigger": "Pemberitahuan Pemicu", "number": "Tidak.", "Number": "", "Number of Files/URLs": "", "Object": "", "Object Category": "", "Object Component": "", "Object Elements": "", "OBJECT INFO": "", "Object Sub-category": "", "Offset Date": "", "Offset from X Days Compare to Expired Date": "", "Offset from X days prior to due date": "", "okText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "On Sale": "<PERSON><PERSON><PERSON>", "Once deleted, the configured notifications which related with these sales channels will also be deleted.": "<PERSON><PERSON><PERSON>, pemberitahuan dikonfigurasi yang terkait dengan saluran penjualan tersebut juga akan dihapus.", "Only normal policy and group policy support goods validation.": "", "Only one record is allowed for each questionnaire purpose under the same trigger. No duplicated allowed. Please check.": "", "Open End Policy": "", "Open End Policy cannot be used together with Renewal Agreement, Expiry Date Rule, Coverage Period, Pre-calculate Premium installment method or Single Premium (Payment Period & Frequency), please check these configurations before submit.": "", "Open End Policy is already selected and cannot be used together with Allow Renewal. Do you want to deselect Open End Policy and proceed with selecting Allow Renewal instead?": "", "Optional": "", "Optional / Required": "", "Optional Liability": "", "Optional/Mandatory": "", "Order": "", "Organization": "", "Other": "<PERSON>n", "Other Setting": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "Overdue Auto Deduction": "", "Overdue Handling": "", "Overdue Status": "", "Package": "<PERSON><PERSON>", "Package Agreement": "<PERSON><PERSON><PERSON><PERSON> paket", "Package Category": "paket <PERSON>", "Package Code": "", "Package code is required": "kode paket diperlukan", "Package Configuration": "", "Package Management": "", "Package Name": "<PERSON><PERSON> paket", "Package Net Premium Adjustment": "", "PackageId is required": "PackageId diperlukan", "Parameter have been changed. The uploaded matrixtable will be cleared. Please confirm.": "", "Parameters": "", "Parameters(Result)": "", "Participating Product": "", "Partner Fee Calculation": "", "pay_method": "<PERSON>", "Payer Elements": "", "PAYER INFO": "", "Payment Defer Period Type": "", "Payment Frequency": "", "Payment Option": "", "Payment Period": "", "Payment Period Type": "", "payment_type": "<PERSON><PERSON><PERSON>", "Period Type": "", "Pin": "", "Plan": "", "Plan (Plan Code + Plan Name)": "", "Plan Code": "<PERSON><PERSON><PERSON><PERSON>", "Plan code is required": "<PERSON><PERSON> rencana cakupan diperlukan", "Plan Configuration": "", "Plan Display": "", "Plan Group": "", "plan group code must be unique": "", "Plan Group Name": "", "Plan is required": "<PERSON><PERSON><PERSON> cakupan diperlukan", "Plan Name": "<PERSON><PERSON><PERSON><PERSON>", "Plan name is required": "<PERSON><PERSON><PERSON><PERSON> nama rencana <PERSON>an", "Plan Premium Model": "", "Plan Recommend": "", "Plan Recommendation-Matrix Table": "", "Planned Premium Configuration": "", "Planned premium is required": "", "Please": "", "Please add one “Beneficiary” at least.": "", "Please add one “Insured” at least.": "", "Please add one “Object” at least.": "", "Please add stack correlation": "", "Please associate goods and plan.": "", "Please Calculate first!": "", "Please choose at least one coverage period agreement": "<PERSON><PERSON><PERSON> pilih minimal satu perjanjian masa pertanggungan", "Please choose at least one installment premium frequency agreement": "", "Please choose at least one premium period agreement": "", "Please complete the configuration": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Please configure intermediate SA calculation formula in Product Center for corresponding liability.": "", "Please configure POS premium refund rule for each sales channel.": "", "Please configure the min or max value of the range.": "", "Please confirm that all configurations have been saved before submission": "", "Please confirm that the information of the beneficiary is consistent with insured.": "", "Please confirm that the information of the beneficiary is consistent with policyholder.": "", "Please confirm that the information of the insured is consistent with policyholder.": "", "Please copy your File URL first.": "", "Please define numerical values in the table without including the percentage sign. For example, if you wish to define 10%, simply enter 10 in the table.": "", "Please do not select the same fund for the same Premium Type": "", "Please don’t enter special characters": "<PERSON><PERSON> me<PERSON>kan karakter khusus", "Please don’t enter special characters, such as~`!@#$%^*()+.": "<PERSON><PERSON> me<PERSON> ka<PERSON> k<PERSON>, se<PERSON>i ~ `! @ # $% ^ * () +.", "Please don’t enter special characters, such as~`!@#$%^&*()+.": "<PERSON><PERSON> me<PERSON> ka<PERSON> k<PERSON>, se<PERSON>i ~ `! @ # $% ^ & * () +.", "Please ensure sum of beneficiary ratio equals to 100%.": "", "Please enter English letters": "", "Please enter numeric value": "", "Please enter premium of regular top up or single top up.": "", "Please enter the correct number": "<PERSON><PERSON><PERSON> masukkan angka yang benar", "Please fill in all quotation factors before clicking “Calculation”": "", "Please fill in the following information before selecting rider product": "", "Please fill in the following quotation factors before calculation": "", "Please input": "", "Please input at least one form item": "", "Please input at least one option of file, file address, and file declaration.": "<PERSON><PERSON><PERSON> masukan pada salah satu pilihan yang paling file, al<PERSON><PERSON> berka<PERSON>, dan berkas de<PERSON>.", "Please input both customer ID type and number.": "", "Please input in this format: 'HH:mm'. e.g. 12:00": "", "Please input number": "", "Please input or paste the URL here": "", "Please input Premium Allocation": "", "Please input right format": "Silakan format yang tepat masukan", "Please select": "", "Please select a package or add a coverage plan.": "", "Please select a package or add a plan.": "", "Please select at least one element.": "", "Please select at least two default answer.": "<PERSON><PERSON><PERSON> setidak<PERSON> dua jawaban default.", "Please select Fund": "", "Please select goods first": "", "Please select language": "", "Please select lanuch date first": "<PERSON><PERSON><PERSON> pilih tanggal Lanuch pertama", "Please select related formula for Commission": "", "Please select related formula for Commission Clawback": "", "Please select Related Package": "", "Please select the object component that you want to add": "", "Please select the sub category that you want to add": "", "Please upload a CSV or Excel file.": "", "Please upload file": "Silakan file upload", "Please upload file of .xls, .xlsx": "Silahkan upload file .xls, .xlsx", "pleaseInput": "", "POLICY BASIC INFO": "", "Policy Change": "", "Policy Claim Stack": "", "Policy Effective Without Collection (NB)": "", "Policy Effective Without Collection (Renewal)": "", "Policy Elements": "", "Policy Issuance Order": "", "Policy Manual": "<PERSON>ed<PERSON>", "Policy Net Premium Adjustment": "", "Policy No.": "", "Policy SA": "", "Policy Stack": "", "Policy Type": "", "Policy will be auto reviewed on each Review Recurrency from the Effective Date.": "", "Policy Year": "", "Policyholder": "", "POLICYHOLDER INFO": "", "PolicyStatusEnum.POLICY_EFFECT": "Efektif", "POS Premium Refund Type": "", "Premium Adjustment": "", "Premium Agreement": "", "Premium Allocation": "", "Premium Allocation must equal to 100%.": "", "Premium allocation section: Premium type “All” should not combined with other premium types.": "", "Premium Allocation(%)": "", "Premium Calculation Method": "", "Premium Composition": "", "Premium Currency": "", "Premium Discount": "", "Premium Due Date Rule": "", "Premium Due Date Rules": "", "Premium End Date Calculation Rule": "", "Premium Frequency": "", "Premium Funder Elements": "", "Premium Notice Date Compare with Due Date": "", "Premium Period": "", "Premium Period & Installment": "", "Premium Period Configuration": "", "Premium Period Type": "", "Premium Period Value Type": "", "Premium Related": "Premium terkait", "Premium Type": "", "Pricing Currency": "", "Pricing Currency corresponding to the calculated result of the formula. If select \"Premium Currency\", the formula's calculated result will be output directly.If \"SA Currency\", the calculated result of the formula will be converted from the SA currency to the premium currency using the exchange rate.": "", "Process Related": "proses terkait", "Product": "", "Product / Liability": "", "Product & Liability": "", "Product and liability are required": "Produk dan kewaj<PERSON>n yang dip<PERSON>an", "Product Category": "", "Product Center": "", "Product Claim Stack": "", "Product Code": "Kode Produk", "Product Code - Version": "", "Product Code/Name": "", "Product Correlation Matrix": "", "Product Info": "", "Product Name": "", "Product Name / Code": "", "Product Overdue Status": "", "Product Premium": "", "Product Premium Setting": "", "Product SA": "", "Product SA Setting": "", "Product Stack": "", "Product Tax": "", "Product Version": "<PERSON><PERSON>i produk", "Products Involved in Accumulation": "", "Public Display Period": "Public Display Period", "Public Task": "", "Publish": "", "Publish Success": "", "Publish successfully": "", "Question Code": "<PERSON><PERSON><PERSON>", "Question Declaration": "<PERSON><PERSON><PERSON>", "Question Description": "<PERSON><PERSON><PERSON>", "Questionnaire": "", "QUESTIONNAIRE": "", "Questionnaire Name": "", "Questionnaire Purpose": "", "Quick Menu": "", "Quotation": "", "Quotation Code": "", "Quotation Details": "", "Random": "", "Range": "", "Ratetable Category": "", "Ratetable Code": "", "Re-Define Effective Date Rule": "", "Recommend": "", "recommendOnlyOne": "", "Refund Formula": "", "Regular Top Up": "", "Regulation of Products": "Peraturan Produk", "Related Main": "", "Related Main Product": "", "Related Package Info": "", "Relational Policy No.": "", "Renewal": "", "Renewal Agreement": "", "Renewal Extraction Period": "", "Renewal Grace Period": "", "Renewal Policy Effective Date Rule": "", "Renewal Policy Effective Without Collection": "", "Renewal Proposal Submit Date": "", "Retrieve the policy to be reviewed and generate the bill X days prior to the Review Date.": "", "Review Extraction Date": "", "Review Recurrence": "", "Rider": "Pengendara", "Rider Code": "", "Rider Liability": "", "Rider Name": "", "Rider Premium Payment Method": "", "Rider Product Code": "", "RSTU／STU must be collected for initial premium": "", "Rule/Rule Set": "", "Rule1：Corresponding to effective date or period end date": "", "Rule2：Corresponding to effective date or next date of period end date": "", "Rule3: Corresponding to effective date or period end date and fixed Feb 28 as period end date": "", "SA / Premium Details of Each Insured": "", "SA Currency": "", "Sales Attributes": "Atribut penjualan", "Sales Channel": "<PERSON><PERSON><PERSON>", "Sales Channel is empty or multiple sales channels exist.": "Sales Channel is empty or multiple sales channels exist.", "Sales Configuration": "", "Sales District": "", "Sales Platform": "", "Sales Time": "penjualan Waktu", "salesType": "", "Same as Launch Period": "", "Same as SA Currency": "", "Same Premium Calculation for Each Insured": "", "Save": "", "Save Sort": "<PERSON><PERSON><PERSON>", "Save success": "", "Save Successful.": "", "Save successfully": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> save", "Save Successfully": "", "save_suc": "", "Scenario Validation": "", "Search": "", "Search Goods Name": "", "Search Voucher Name": "", "Secondary Life Insured Elements": "", "Select Benefit Factors": "", "Select from the coverage periods in the associated products, or click \"Add New\" to create a new one.": "", "Select from the premium periods in the associated products, or click \"Add New\" to create a new one.": "", "Select Parameter": "", "SELECT PLAN": "", "Select Product": "", "Select Questionnaire Language": "", "Select Relationship Package": "", "Select Rider": "", "Select Stack Code": "", "Selected sales channel has been configured with conflicted POS premium refund rule.": "", "Service Code": "", "Service Company": "", "Service Effective Time": "", "Service Fee Clawback": "", "Service Maintenance": "", "Service Name": "", "Service Type": "", "service_page_amount": "<PERSON><PERSON><PERSON>", "service_page_create_time": "Buat Waktu", "service_page_creator": "<PERSON><PERSON><PERSON>", "service_page_effective_time": "Layanan Waktu Efektif", "service_page_effective_time2": "Waktu efektif", "service_page_fac_code": "Kode fasilitator", "service_page_fac_name": "<PERSON><PERSON> fasilitator", "service_page_file_name": "Nama file", "service_page_modi_time": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "service_page_modifier": "Modifier", "service_page_service_code": "<PERSON><PERSON>", "service_page_service_name": "<PERSON><PERSON>n", "service_page_service_status": "Status pelayanan", "service_page_total": "Total", "service_page_used": "<PERSON><PERSON>", "Set Default Time": "", "Set Fixed Time": "", "Set Relationship with Package": "", "Settlement Rule": "", "Show Financial Declaration": "<PERSON><PERSON><PERSON><PERSON>", "Show Health Declaration": "<PERSON><PERSON><PERSON><PERSON>", "Show in Application Form": "", "Single Top Up": "", "Sorry no goods were found": "", "Sort": "Menyortir", "sort successfully": "keb<PERSON><PERSON><PERSON><PERSON> semacam", "Special Agreement": "<PERSON><PERSON><PERSON><PERSON>", "Specified SA & Premium": "", "Stack Code": "", "Stack code have been changed. The uploaded file will be cleared. Please confirm.": "", "Stack Component Name": "", "Stack Content": "", "Stack Description": "", "Stack Level": "", "Stack Name": "", "Stack Template Code": "", "Stack Template Name": "", "Stack Type": "", "Stack Unit": "", "Stack Unit Type": "", "Stack Value": "", "Stack Value Matrix": "", "Stack Value Type": "", "Stamp Duty": "", "Standard Premium": "", "Start date": "", "Start Time": "", "status": "Status", "Sub Condition Type": "", "Submit": "", "Submit success": "", "Submit successfully": "", "Submit successfully!": "", "Sum Assured": "", "Summary": "", "Support maintaining the association between stack values, only support value types of fixed amount and enumeration.": "", "Supported Assignee Type": "", "Supported Beneficiary Type": "", "Survival Benefit Configuration": "", "Switching the package will clear all definitions under the plan. Please confirm to proceed with the change.": "", "System error": "", "System will automatically review the policy on each Review Recurrence starting from the effective date.": "", "System will automatically review the policy on each Review Recurrence starting from the effective date. When review recurred on monthly basis, 'Premium Period Type' in Premium Period & Installment must be set up as 'Monthly' as well.": "", "Tag": "", "Target User of Voucher": "", "Targeted Customer": "target Pelanggan", "Tax": "", "Tax Amount": "", "Tax Setting": "", "Tax Type": "", "Tax Value Type": "", "Template Code / Name": "", "Tenant Enumerated Sync": "", "text_delete_success": "Ha<PERSON> berhasil!", "The coverage period agreement will be deleted": "", "The Display Time should be within the Launch Date": "<PERSON><PERSON><PERSON> harus dalam <PERSON> Peluncuran", "The effective date of this file can't intersect with the existed file": "Tanggal efektif dari file ini tidak dapat berpotongan dengan file ada", "The fixed due date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.": "", "The following package(s) have used this application elements group. Editing this record and the following package(s) will be affected. Please check before change:": "", "The fund should be unique for universal product.": "", "The goods code could only contain letters, numbers, and underscores (_).": "", "The input number cannot be zero": "", "The insurance application failed. Please check the information and resubmit.": "", "The marketing goods contain policy types other than Normal Policy or Group Policy. These types are not subject to standard validation checks before launching. Do you want to proceed?": "", "The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59": "", "The package code could only contain letters, numbers, and underscores (_).": "", "The packages associated with the coverage plan must be within the selected packages.": "", "The packages associated with the plan must be within the selected packages.": "", "The plan code could only contain letters, numbers, and underscores (_).": "", "The product lacks liabilities, and please add the configuration in the product center": "produk tidak memiliki k<PERSON>, dan silahkan tambahkan konfigurasi di pusat produk", "The questionnaire does not exist.": "", "The questionnaire is filled out. Proceed to insurance application.": "", "The sales channels have related configured notifications, please update notification configurations if needed.": "<PERSON><PERSON>n penjualan telah terkait pemberitahuan dikonfigu<PERSON>i, silakan konfigurasi notifikasi update jika dip<PERSON>.", "The Sales Time should be within the Launch Date": "Penjualan Waktu harus dalam <PERSON>gal Peluncuran", "The second value should greater than the first value": "", "The sum of Maximum Sales Units of each plan is greater than Goods Wholesale Volume!": "", "The sum of Maximum Sales Units of each plan is greather than Goods Wholesale Volume!": "<PERSON><PERSON><PERSON>um Penjualan Unit setiap rencana adalah greather daripada Barang Grosir Volume!", "There is unfinished editing in <{{title}}>. Please complete before proceeding.": "", "There must be one or more rider benefits under the virtual main benefit.": "", "This application elements group is relied on": "", "This file was effective in the past, are you sure to delete it?": "", "This record was configured based on old version. Currently, viewing and editing are not supported.": "", "Ticket": "", "Tied Agent": "", "Time Zone": "Zona waktu", "Times Type": "", "Tips: For relationship, you can configure as “attachable”, “non-attachable” or “compulsory”.": "", "Total Premium": "", "Transaction Amount": "", "Transaction Date": "", "Transaction Information": "", "Trigger": "", "Trigger Point by Event Policy": "Trigger Point oleh Polis Acara", "Trip": "", "Trustee Elements": "", "TRUSTEE INFO": "", "Type": "", "Unanswered question exists, please confirm.": "", "Uncompleted questionnaire exists, please confirm.": "", "Underwriting Error Message": "", "Underwriting Order": "", "Underwriting Related": "underwriting terkait", "Underwriting Type": "", "Unified Underwriting Conclusion Flag": "", "Unlimited": "Tak terbatas", "Unnamed Insured": "", "Unset": "", "Updatable or Not": "", "Updatable When Manual UW": "", "Updatable When POS": "", "Upload": "", "Upload Batch No.": "Upload Batch No.", "Upload Fail!": "", "Upload Files": "<PERSON><PERSON><PERSON> berkas", "Upload Successful.": "", "Upload Successfully": "", "URL": "", "usage_time": "<PERSON><PERSON>tu yang terpakai", "Usage-based Goods": "", "User Input": "", "Validation Response": "", "value": "", "Value Assignment": "", "Value Code": "<PERSON><PERSON>", "Value Type": "", "Variable Amount": "", "Vehicle Additional Equipment": "", "Vehicle Database Name": "", "Vehicle Loan": "", "Vehicle Market Value Formula": "", "Vehicle Related Configuration": "", "Version": "Versi: kapan", "View Component": "", "View My Creations": "", "View Voucher": "", "Voucher": "", "Voucher Application Channel": "", "Voucher Basic Info": "", "Voucher Calculation & Effective Rule": "", "Voucher Channel": "", "Voucher Code": "", "Voucher Consumption Status": "", "Voucher Correlation": "", "Voucher Detail": "", "Voucher Effective Period": "", "Voucher Face Amount": "", "Voucher Name": "", "Voucher Number Generation Rule": "", "Voucher Status": "", "Voucher Type": "", "Voucher Type - Face Amount": "", "Voucher Value Type": "", "Waiver": "", "Waiver Details": "", "Waiver Product": "", "Waiver Product Duplicated": "", "Waiver Rider Liability": "", "Warning Threshold": "waring <PERSON><PERSON><PERSON><PERSON>", "Whether to keep the voucher balance": "", "Wholesale Volume": "grosir Volume", "Wholesale Volume Warning": "Grosir Volume Peringatan", "With Guarantee Period": "", "Year": "", "Year(s)": "", "yes": "<PERSON><PERSON>", "Yes": "", "Yes-Recurring Single Top Up / Single Top Up must be collected before policy is effective.": "", "You are applying": "", "You can only upload PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG": "", "You can only upload xls, xlsx": "", "You can only upload xls, xlsx, doc, docx, pdf, jpg or png.": "You can only upload xls, xlsx, doc, docx, pdf, jpg or png.", "You can only upload XLSX/CSV": "", "Your policy has been issued.": "", "Your proposal has been issued.": ""}