{"- -": "--", "--": "--", "+ Add": "+ Add", "Accumulate Product SA to Policy SA": "Accumulate {{product,product}} SA to Policy SA", "Accumulated to Premium": "Accumulated to Premium", "Add": "Add", "Add a Condition": "Add a Condition", "Add Block": "Add Block", "Add Declaration Library": "Add Declaration Library", "Add New": "Add New", "Add New Voucher": "Add New Voucher", "Add Object Component": "Add Object Component", "Add POS Items": "Add POS Items", "Add Product And Liability": "{{product,product}} And Liability", "Add Relationship Package": "Add Relationship {{package,package}}", "Add Rider": "Add Rider", "Add successfully": "Add successfully", "Additional Grace Period": "Additional Grace Period", "Adjust to 1st if Effective Day Not in Expiry Month": "Adjust to 1st if Effective Day Not in Expiry Month", "Adjusted Market Value Floating Ratio": "Adjusted Market Value Floating Ratio", "After": "After", "After(Days)": "After(Days)", "Age Calculation Basis": "Age Calculation Basis", "Age Validation": "Age Validation", "Agency Company": "Agency Company", "Agency Company or Sales Platform": "Agency Company or Sales Platform", "Agent": "Agent", "Agent Category": "Agent Category", "Agreement Code": "Agreement Code", "ALL": "ALL", "All Creations": "All Creations", "All questions have been answered.": "All questions have been answered.", "All relevant addresses will be deleted together, please confirm!": "All relevant addresses will be deleted together, please confirm!", "All relevant bankCode will be deleted together, please confirm!": "All relevant bankCode will be deleted together, please confirm!", "Allow Manual Adjustment": "Allow Manual Adjustment", "Allow Master Policy Issuance": "Allow Master Policy Issuance", "Allow Renewal": "Allow <PERSON>", "Allow Renewal is already selected and cannot be used together with Open End Policy. Do you want to deselect Allow Renewal and proceed with selecting Open End Policy instead?": "Allow Renewal is already selected and cannot be used together with Open End Policy. Do you want to deselect Allow Renewal and proceed with selecting Open End Policy instead?", "​Allowed backdating range compared to Policy Insure Date. E.g. Insure Date is 15th Dec, if backdating range is 3 days ~ 6 days, the allowed range of Effective Date is 9th Dec ~ 12th Dec.": "​Allowed backdating range compared to Policy Insure Date. E.g. Insure Date is 15th Dec, if backdating range is 3 days ~ 6 days, the allowed range of Effective Date is 9th Dec ~ 12th Dec.", "​Allowed forward-dating range compared to Policy Insure Date. E.g. Insure Date is 15th Dec, if forward-dating range is 3 days ~ 6 days, the allowed range of Effective Date is 18th Dec ~ 21th Dec.": "​Allowed forward-dating range compared to Policy Insure Date. E.g. Insure Date is 15th Dec, if forward-dating range is 3 days ~ 6 days, the allowed range of Effective Date is 18th Dec ~ 21th Dec.", "Amount Limit": "Amount Limit", "Amount Limit Remark": "Amount Limit Remark", "Amount Range": "Amount Range", "An open-end policy does not have a fixed expiry date. Coverage continues unless terminated, and is typically reviewed periodically.": "An open-end policy does not have a fixed expiry date. Coverage continues unless terminated, and is typically reviewed periodically.", "AND": "AND", "and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula": "and in formula <commGST> we could use schema factor netCommission calculated in order 1 to configure formula", "Annual Premium": "Annual Premium", "Annuity Configuration": "Annuity Configuration", "Answer": "Answer", "Applicable Scenario": "Applicable Scenario", "Application": "Application", "Application Elements": "Application Elements", "Application Elements Group": "Application Elements Group", "Application Elements Group Code": "Application Elements Group Code", "Application Elements Group Name": "Application Elements Group Name", "Application Information": "Application Information", "Applied Unit": "Applied Unit", "Are you sure to clear this section?": "Are you sure to clear this section?", "Are you sure to delete this POS Item ?": "Are you sure to delete this POS Item ?", "Are you sure to delete this record ?": "Are you sure to delete this record ?", "Are you sure you want to delete these sales channels": "Are you sure you want to delete these sales channels", "Assignee Elements": "Assignee <PERSON>", "Associated Goods and Plans": "Associated Goods and Plans", "Attach to Product": "Attach to {{product,product}}", "Attached To": "Attached To", "Auto Deduction Date": "Auto Deduction Date", "Auto Deduction Date Compare to Due Date": "Auto Deduction Date Compare to Due Date", "Automatic Adjustment": "Automatic Adjustment", "Available": "Available", "Back": "Back", "Back to Search": "Back to Search", "Back to Validation": "Back to Validation", "Backdating": "Backdating", "Backdating or Forward-dating period calculation basis": "Backdating or Forward-dating period calculation basis", "Bank": "Bank", "Base Currency": "Base Currency", "base_next": "Next", "Basic Configuration": "Basic Configuration", "Basic Info": "Basic Info", "Basic Information": "Basic Information", "Be Attached By": "Be Attached By", "Before(Days)": "Before(Days)", "Belonging Info": "Belonging Info", "Beneficial Owner Elements": "Beneficial Owner Elements", "Beneficiary Elements": "Beneficiary Elements", "BENEFICIARY INFO": "BENEFICIARY INFO", "Benefit Configuration": "Benefit Configuration", "Benefit Illustration": "Benefit Illustration", "Benefit Option Mapping": "Benefit Option Mapping", "Benefit Option Selection": "Benefit Option Selection", "Benefit Option Selection & Matching": "Benefit Option Selection & Matching", "Broker Company": "Broker Company", "btn_upload": "Upload", "Bulk Delete": "Bulk Delete", "By Formula": "By Formula", "Caculation Item": "Caculation Item", "Calculate": "Calculate", "Calculation Cycle": "Calculation Cycle", "Calculation Method": "Calculation Method", "Campaign Discount": "Campaign Discount", "Cancel": "Cancel", "Cancel pin": "Cancel pin", "Cancellation Type": "Cancellation Type", "Car Owner": "Car Owner", "Cash Bonus Configuration": "Cash Bonus Configuration", "Category": "Category", "Change installment premium calculation method will clear the existing configuration, please confirm.": "Change installment premium calculation method will clear the existing configuration, please confirm.", "Changes have not been saved.": "Changes have not been saved.", "Changing beneficiary category will clear the existing beneficiary information.": "Changing beneficiary category will clear the existing beneficiary information.", "Changing insured category will clear the existing insured information.": "Changing insured category will clear the existing insured information.", "Changing policyholder category will clear the existing policyholder information.": "Changing policyholder category will clear the existing policyholder information.", "Changing this setting will clear all configured coverage details and related information. Are you sure to continue?": "Changing this setting will clear all configured coverage details and related information. Are you sure to continue?", "Channel": "Channel", "Channel and channnel - sub channel could not co-exisit. Please change.": "Channel and channel - sub channel could not co-exisit. Please change.", "Choose the type of charge period to be supported": "Choose the type of charge period to be supported", "Choose the type of coverage period to be supported": "Choose the type of coverage period to be supported", "Claim Experience": "Claim Experience", "Claim Stack Correlation": "<PERSON><PERSON><PERSON> Correlation", "Claim Stack Execution Order": "C<PERSON>m Stack Execution Order", "Claim Stack Setting": "<PERSON><PERSON><PERSON>", "claim_upload_time": "Upload Time", "Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.": "Clawback calculation method is configured here while where to trigger clawback calculation is defined in business module configuration such as POS item agreement.", "Clear All": "Clear All", "Click here to upload": "Click here to upload", "Click or drag the file here to upload": "Click or drag the file here to upload", "Closure & Next": "Closure Rider", "Closure Rider": "Closure Rider", "Code": "Code", "Code & Question Declaration": "Code & Question Declaration", "Collapse all": "Collapse all", "Commission Clawback": "Commission Clawback", "Commission Ratio": "Commission Ratio", "Company Name": "Company Name", "Component Code": "Component Code", "Component Description": "Component Description", "Component Name": "Component Name", "Configuration Validation": "Configuration Validation", "Configure": "Configure", "Configure Benefit Details": "Configure Benefit Details", "Confirm": "Confirm", "Congratulations!": "Congratulations!", "Consentee Elements": "Consentee Elements", "CONSENTEE INFO": "CONSENTEE INFO", "Contact Person Elements": "Contact Person Elements", "Content Category": "Content Category", "Copy from Insured": "Copy from Insured", "Copy from Policyholder": "Copy from Policyholder", "Copy Reminder": "<PERSON><PERSON>", "Copy Successfully": "<PERSON><PERSON> Successfully", "Copy to New Goods": "Copy to New Goods", "Copy to New Version": "Copy to New Version", "Copying elements from the insured module will overwrite existing content, please confirm.": "Copying elements from the insured module will overwrite existing content, please confirm.", "Copying elements from the policyholder module will overwrite existing content, please confirm.": "Copying elements from the policyholder module will overwrite existing content, please confirm.", "Coverage Date Change Type": "Coverage Date Change Type", "Coverage Period": "Coverage Period", "Coverage Period Configuration": "Coverage Period Configuration", "Coverage Period Fields to Revise": "Coverage Period Fields to Revise", "Coverage Period Range": "Coverage Period Range", "Coverage Period Type": "Coverage Period Type", "Coverage Period Value Type": "Coverage Period Value Type", "Coverage Type": "Coverage Type", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Current Rider Status on Package": "Current Rider Status on {{package,package}}", "Customer ID No.": "Customer ID No.", "Customer Info": "Customer Info", "Customer Name": "Customer Name", "Customized Document": "Customized Document", "Cut off Date": "Cut off Date", "Cut off Time": "Cut off Time", "Daily Basis: The system calculates the daily premium and then calculates each installment premium based on the number of days in that installment period. (For a full-year policy, the premiums of each installment could be different)": "Daily Basis: The system calculates the daily premium and then calculates each installment premium based on the number of days in that installment period. (For a full-year policy, the premiums of each installment could be different)", "Data Address": "Data Address", "Date + Time": "Date + Time", "Date of Birth": "Date of Birth", "Day of Fixed Due Date": "Day of Fixed Due Date", "Day(s)": "Day(s)", "Day(s) After Due Date": "Day(s) After Due Date", "Day(s) Before Due Date": "Day(s) Before Due Date", "Days": "Days", "Days Type": "Days Type", "Declarant": "Declarant", "Declaration": "Declaration", "Declaration Category": "Declaration Category", "Declaration Configuration": "Declaration Configuration", "Declaration Content Category": "Declaration Content Category", "Declaration Detail": "Declaration Detail", "Declaration Library": "Declaration Library", "Declaration Question Code": "Declaration Question Code", "Declaration Type": "Declaration Type", "Deduction Date": "Deduction Date", "Deduction Date Compare to Due Date.e.g. if deduct on due date, enter 0 if deduct prior to due date, enter 1, 2, 3 etc.": "Deduction Date Compare to Due Date.e.g. if deduct on due date, enter 0 if deduct prior to due date, enter 1, 2, 3 etc.", "Deduction Date Compare to renewal Policy effective date.e.g. if deduct on effective date, enter 0 if deduct prior to effective date, enter 1, 2, 3 etc.": "Deduction Date Compare to renewal Policy effective date.e.g. if deduct on effective date, enter 0 if deduct prior to effective date, enter 1, 2, 3 etc.", "Default Answer": "Default Answer", "Default Time": "Default Time", "Default Time Editable": "Default Time Editable", "Default Value-Matrix Table": "Default Value-Matrix Table", "Defer Period": "Defer Period", "Define auto submit time for renewal proposal by comparing with previous policy expired date. If not configured, a manual submit is needed.": "Define auto submit time for renewal proposal by comparing with previous policy expired date. If not configured, a manual submit is needed.", "Define the day before policy expired date and days after expired date to extract renewal policy": "Define the day before policy expired date and days after expired date to extract renewal policy", "Defined Application Elements": "Defined Application Elements", "Delete": "Delete", "Delete Failed": "Delete Failed", "Delete Success": "Delete Successfully", "Delete Successfully": "Delete Successfully", "Deleted successfully": "Deleted successfully", "describe": "Description", "Description": "Description", "Designated Beneficiary": "Designated Beneficiary", "Display Name": "Display Name", "Display Name of Field": "Display Name of Field", "Display or Not": "Display or Not", "Dispute Settlement": "Dispute Settlement", "Documents Display": "Documents Display", "Download Successfully": "Download Successfully", "Download Template": "Download Template", "Driver": "Driver", "Due Date": "Due Date", "Dunning Rule": "Dunning Rule", "Duplicate Bank. Please change.": "Duplicate Bank. Please change.", "Duplicate Broker Company. Please change.": "Duplicate Broker Company. Please change.", "Duplicate Company Name. Please change.": "Duplicate Company Name. Please change.", "Duplicate formula configuration for same formula category and sub category. Please edit original one.": "Duplicate formula configuration for same formula category and sub category. Please edit original one.", "Duplicate Front Line Channel. Please change.": "Duplicate Front Line Channel. Please change.", "Duplicate Key Account Channel. Please change.": "Duplicate Key Account Channel. Please change.", "Duplicate Leasing Channel. Please change.": "Duplicate Leasing Channel. Please change.", "Duplicate Order. Please change.": "Duplicate Order. Please change.", "Duplicate Sales Platform. Please change.": "Duplicate Sales Platform. Please change.", "Duplicated data exists": "Duplicated data exists", "Duration to Display": "Duration to Di<PERSON>lay", "dutyText": "Liability", "EARLIEST OF": "EARLIEST OF", "Edit Claim Stack Definition": "Edit Claim <PERSON>ack Definition", "Edit Declaration Library": "Edit Declaration Library", "Edit Goods Label": "Edit Goods Label", "Edit Plan": "Edit Plan", "Edit Product And Liability": "Edit {{product,product}} And Liability", "Edit Reminder": "<PERSON>minder", "edit success": "edit success", "Edit Voucher": "<PERSON>", "Editing": "Editing", "EDITING": "EDITING", "Editing benefit factors will clear the existing uploaded benefit details，please confirm.": "Editing benefit factors will clear the existing uploaded benefit details，please confirm.", "Effective": "Effective", "EFFECTIVE": "EFFECTIVE", "Effective Date Month End to Expiry Date Month End": "Effective Date Month End to Expiry Date Month End", "Effective Date Month End to Expiry Date Month End: If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "Effective Date Month End to Expiry Date Month End: If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)", "Effective Date Rule": "Effective Date Rule", "Effective end date cannot be earlier than the start date.": "Effective end date cannot be earlier than the start date.", "Effective From": "Effective From", "Effective Period": "Effective Period", "Effective Period Extension for Reversed Voucher": "Effective Period Extension for Reversed Voucher", "Element": "Element", "Element Value": "Element Value", "Elements": "Elements", "Elements Detail Configuration": "Elements Detail Configuration", "Elements Group": "Elements Group", "Elements Matrix": "Elements Matrix", "Elements Selection": "Elements Selection", "Elements Value": "Elements Value", "Enable Multi-Main Benefits": "Enable Multi-Main Benefits", "Enable Payment": "Enable Payment", "Enable Underwriting": "Enable Underwriting", "End date": "End date", "End Date": "End Date", "End Time": "End Time", "Enter an integer greater than or equal to 1": "Enter an integer greater than or equal to 1", "Enumerate Configuration": "Enumerate Configuration", "Enumerated": "Enumerated", "Enumeration": "Enumeration", "Enumeration Configuration Method": "Enumeration Configuration Method", "Enumeration values cannot be empty.": "Enumeration values cannot be empty.", "Existing Stack in the Product": "Existing Stack in the {{product,product}}", "Exists duplicate liability orders": "Exists duplicate liability orders", "Exists duplicate value": "Exists duplicate value", "Expand all": "Expand all", "Expired": "Expired", "Expiry Date Adjustment": "Expiry Date Adjustment", "Expiry date calculation method": "Expiry date calculation method", "Expiry Date Calculation Method": "Expiry Date Calculation Method", "Expiry Date CalMethod": "Expiry Date CalMethod", "Expiry Date Rule": "Expiry Date Rule", "Expiry Date should be after Effective Date": "Expiry Date should be after Effective Date", "Expiry Time Agreement Type": "Expiry Time Agreement Type", "Extra Loading": "Extra Loading", "Extra Premium": "Extra Premium", "Extract Bill Date": "Extract Bill Date", "Extract day(s) must be greater than offset day(s)": "Extract day(s) must be greater than offset day(s)", "Extract premium from X days prior to due date": "Extract premium from X days prior to due date", "Extract review policy and generate bill from X days prior to Effective Date + .total Review Recurrencies.": "Extract review policy and generate bill from X days prior to Effective Date + .total Review Recurrencies.", "Extraction Method": "Extraction Method", "Facilitator Code": "Facilitator Code", "Facilitator Code & Name": "Facilitator Code & Name", "Facilitator Name": "Facilitator Name", "Fee Type": "Fee Type", "Fellow Traveller": "Fellow Traveller", "Field Name": "Field Name", "Field Validation Rule": "Field Validation Rule", "Field Value": "Field Value", "File": "File", "File Declaration": "File Declaration", "File Management": "File Management", "File size should small than 10M": "File size should small than 10M", "FILE TYPE": "File Type", "file upload failed.": "file upload failed.", "file uploaded successfully": "file uploaded successfully", "File Version": "File Version", "Filters": "Filters", "First": "First", "first.": "first.", "Fixed Amount": "Fixed Amount", "Fixed due date rule：Fixed due date or period end date": "Fixed due date rule：Fixed due date or period end date", "Fixed Time": "Fixed Time", "For Fixed Day of Month, please input number from 1 and 28": "For Fixed Day of Month, please input number from 1 and 28", "For No Claim Bonus": "For No Claim Bonus", "for Policy": "for Policy", "For POS Premium Refund": "For POS Premium Refund", "for Proposal": "for Proposal", "Formula": "Formula", "Formula Category": "Formula Category", "Formula Category (Clawback)": "Formula Category (Clawback)", "Formula Code": "Formula Code", "Formula Name": "Formula Name", "Formula Name (Clawback)": "Formula Name (Clawback)", "Formula Sub Category": "Formula Sub Category", "Formula Sub Category (Clawback)": "Formula Sub Category (Clawback)", "Forward-dating": "Forward-dating", "Free Charge": "Free Charge", "Freelook Period": "Freelook Period", "Front Line Channel": "Front Line Channel", "Function Label Code": "Function Label Code", "Function label code already exists!": "Function label code already exists!", "Function Label Name": "Function Label Name", "Fund": "Fund", "Fund Allocation": "Fund Allocation", "Fund allocation percent summation must be 100%.": "Fund allocation percent summation must be 100%.", "Fund allocation percent summation must be 100%. ": "Fund allocation percent summation must be 100%. ", "Gender": "Gender", "Generate Matrix Table": "Generate Matrix Table", "Generate Matrix Table Successfully": "Generate Matrix Table Successfully", "Generate new policy number for renewal": "Generate new policy number for renewal", "Generate New Policy Number For Renewal": "Generate New Policy Number For Renewal", "Generation Date": "Generation Date", "Goods": "Goods", "Goods Category": "Goods Category", "Goods Code": "Goods Code", "Goods File Management": "Goods File Management", "Goods id is required": "Goods id is required", "Goods Introduction": "Goods Introduction", "Goods Label": "Goods Label", "Goods Label Configuration": "Goods Label Configuration", "Goods Name": "Goods Name", "Goods Type": "Goods Type", "Goods Validation": "Goods Validation", "Goods Version": "Goods Version", "Grace Period": "<PERSON> Period", "Group Policy Level": "Group Policy Level", "Guarantee Period": "Guarantee Period", "have_disabled": "Disabled", "Here has undone plans, please complete first": "Here has undone plans, please complete first", "Hotel": "Hotel", "ID Type": "ID Type", "If Commission GST is configured based on NetCommission, then we could configure as": "If Commission GST is configured based on NetCommission, then we could configure as", "If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)": "If effective date is the end day of month and effective date + coverage period is not the end day of month, adjust the expiry date as the end day of the month. (e.g. 20230228 ~ 20240229 -> If \"Effective Date + Coverage Period - 1s\" 20230228 00:00:00 ~ 20240228 23:59:59)", "If multiple versions are defined for the benefit option at the product level, multi-version matching is not currently supported.": "If multiple versions are defined for the benefit option at the product level, multi-version matching is not currently supported.", "If Open End Policy is enabled, \"Coverage Period\" will be ineffective.": "If Open End Policy is enabled, \"Coverage Period\" will be ineffective.", "If sales platform is configured, all sub channels under this sales platform could sell this product.": "If sales platform is configured, all sub channels under this sales platform could sell this {{product,product,lowerCase}}.", "If the effective date falls on a day that does not exist in the expiry month, and the calculated expiry date is set to the last day of that month, this adjustment will shift the expiry date to the 1st of the following month. (e.g. 2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1)": "If the effective date falls on a day that does not exist in the expiry month, and the calculated expiry date is set to the last day of that month, this adjustment will shift the expiry date to the 1st of the following month. (e.g. 2024/2/29 +1 year = 2025/3/1; 2024/5/31 + 1 month = 2024/7/1)", "Including Levy": "Including <PERSON>", "Including Stamp Duty": "Including Stamp Duty", "Individual": "Individual", "Initial Premium Period": "Initial Premium Period", "Input Method": "Input Method", "Insert fields": "Insert fields", "Installment Detail": "Installment Detail", "Installment Premium": "Installment Premium", "Installment Premium Calculation Basis": "Installment Premium Calculation Basis", "Installment Premium Calculation Method": "Installment Premium Calculation Method", "Installment Premium Frequency": "Installment Premium Frequency", "Insurable Interest": "Insurable Interest", "Insurable Interest SA": "Insurable Interest SA", "Insurable Interest SA Setting": "Insurable Interest SA Setting", "Insurance Applicant": "Insurance Applicant", "Insurance Case": "Insurance Case", "Insurance Charge Period": "Insurance Charge Period", "Insurance Coverage Period": "Insurance Coverage Period", "Insurance Coverage Period Configuration": "Insurance Coverage Period Configuration", "insurance_goods_name": "Goods Name", "Insured": "Insured", "INSURED INFO": "INSURED INFO", "Insured Notice": "Insured Notice", "Insuring Process Configuration": "Insuring Process Configuration", "Interval": "Interval", "Investment Product": "Investment {{product,product}}", "Is Insured Mandatory?": "Is Insured Mandatory?", "Is Open End Policy": "Is Open End Policy", "Issuance Agreement": "Issuance Agreement", "Issuance Mode Check": "Issuance Mode Check", "Issuing Model of Policy": "Issuing Model of Policy", "Key Account Channel": "Key Account Channel", "language": "Language", "LANGUAGE": "Language", "Lapse Date": "Lapse Date", "Last Modifier": "Last Modifier", "LATEST OF": "LATEST OF", "Launch": "Launch", "Launch / Closure": "Launch / Closure Rider", "Launch & Next": "Launch Rider", "Launch Date": "Launch Date", "Launch Rider": "Launch Rider", "Launch Successfully": "Launch Successfully", "LAUNCHED": "LAUNCHED", "Leasing Channel": "Leasing Channel", "Legal Beneficiary": "Legal Beneficiary", "Levy": "<PERSON>", "Liability Category": "Liability Category", "Liability Claim Stack": "Liability Clai<PERSON>", "Liability Coverage / Premium": "Liability Coverage / Premium", "Liability Coverage / Premium Remarks": "Liability Coverage / Premium Remarks", "Liability is required": "Liability is required.", "Liability Limit": "Liability Limit", "Liability Limit Remark": "Liability Limit Remark", "Liability Name": "Liability Name", "Liability Order": "Liability Order", "Liability Premium": "Liability Premium", "Liability Premium Setting": "Liability Premium Setting", "Liability Remark": "Liability Remark", "Liability SA": "Liability SA", "Liability SA Remark": "Liability SA Remark", "Liability SA Setting": "Liability SA Setting", "Liability Stack": "Liability Stack", "liabilityPremium": "Liability Premium", "LiabilityPremiumRemarks": "Liability Premium Remarks", "Limit Sales Volume": "Limit Sales Volume", "limited": "limited", "Limited": "Limited", "Main": "Main", "Main Benefit": "Main Benefit", "Main Condition Type": "Main Condition Type", "Main Product Code": "Main {{product,product}} Code", "Main Product Code / Name": "Main {{product,product}} Code / Name", "Mandatory": "Mandatory", "Mandatory Liability": "Mandatory Liability", "Mandatory or Not": "Mandatory or Not", "Mandatory Rider": "Mandatory Rider", "Mandatory Waiver": "Mandatory Waiver", "Manual Annual Premium": "Manual Annual Premium", "Manual Annual Premium Input Method": "Manual Annual Premium Input Method", "Marketing Center": "Marketing Center", "Marketing Center - Application Elements Group": "Marketing Center - Application Elements Group", "Marketing Center - Marketing Goods": "Marketing Center - Marketing Goods", "Marketing Center - Package": "Marketing Center - {{package,package}}", "Marketing Center - Quick Quotation": "Marketing Center - Quick Quotation", "Marketing Center - Service Maintenance": "Marketing Center - Service Maintenance", "Marketing Goods": "Marketing Goods", "Matched Label Content": "Matched Label Content", "Max": "Max", "Max Allowed Designated Beneficiary Number": "<PERSON> Allowed Designated Beneficiary Number", "Max Allowed Insured Number": "<PERSON> Allowed Insured Number", "Max Allowed Number": "<PERSON> Allowed Number", "Max Allowed Number Limit": "Max Allowed Number Limit", "Max Allowed Object Number Limit": "Max Allowed Object Number Limit", "Max Down-Regulation Ratio": "Max Down-Regulation Ratio", "Max Guaranteed Renewable": "Max Gua<PERSON>eed Renewable", "Max Number of Inputted Insured": "Max Number of Inputted Insured", "Max Up-Regulation Ratio": "Max Up-Regulation Ratio", "Maximum 30 days": "Maximum 30 days", "Maximum Amount": "Maximum Amount", "Maximum Sales Units": "Maximum Sales Units", "Maximum Voucher Numbers": "Maximum Voucher Numbers", "Maximum Voucher numbers can be used in one-time": "Maximum Voucher numbers can be used in one-time", "MENU": "MENU", "Min": "Min", "Minimum Price to Show": "Minimum Price to Show", "Modal Factor": "Modal Factor", "Modifier": "Modifier", "Month(s)": "Month(s)", "Monthly Basis: The system calculates the monthly premium and then calculates each installment premium based on the number of months in that installment period. (For a full-year policy, the premiums of each installment are the same)": "Monthly Basis: The system calculates the monthly premium and then calculates each installment premium based on the number of months in that installment period. (For a full-year policy, the premiums of each installment are the same)", "More >>": "More >>", "Multi Language": "Multi Language", "Mutually Exclusive Main Benefits": "Mutually Exclusive Main Benefits", "Mutually Exclusive With": "Mutually Exclusive With", "My Creations": "My Creations", "My Task": "My Task", "NCB Issue Type": "NCB Issue Type", "NCD": "NCD", "Net Premium Adjustment": "Net Premium Adjustment", "New Business": "New Business", "New Tag": "New Tag", "Next": "Next", "no": "No", "No": "No", "No Claim Discount": "No Claim Discount", "No Data": "No Data", "No Results Found": "No Results Found", "No-Recurring Single Top Up / Single Top Up's collection status does not impact policy effectiveness.": "No-Recurring Single Top Up / Single Top Up's collection status does not impact policy effectiveness.", "No.": "No.", "Nominee Elements": "Nominee Elements", "NOMINEE INFO": "NOMINEE INFO", "Not Relevant": "Not Relevant", "Note: Fund allocation percent summation must be 100%.": "Note: Fund allocation percent summation must be 100%.", "Note: Fund allocation percent summation must be 100%. ": "Note: Fund allocation percent summation must be 100%. ", "Notification Code": "Notification Code", "Notification Configuration": "Notification Configuration", "Notification Scenario": "Notification Scenario", "Notification Trigger": "Notification Trigger", "number": "No.", "Number": "Number", "Number of Files/URLs": "Number of Files/URLs", "Object": "Object", "Object Category": "Object Category", "Object Component": "Object Component", "Object Elements": "Object Elements", "OBJECT INFO": "OBJECT INFO", "Object Sub-category": "Object Sub-category", "Offset Date": "Offset Date", "Offset from X Days Compare to Expired Date": "Offset from X Days Compare to Expired Date", "Offset from X days prior to due date": "Offset from X days prior to due date", "okText": "Confirm", "On Sale": "On Sale", "Once deleted, the configured notifications which related with these sales channels will also be deleted.": "Once deleted, the configured notifications which related with these sales channels will also be deleted.", "Only normal policy and group policy support goods validation.": "Only normal policy and group policy support goods validation.", "Only one record is allowed for each questionnaire purpose under the same trigger. No duplicated allowed. Please check.": "Only one record is allowed for each questionnaire purpose under the same trigger. No duplicated allowed. Please check.", "Open End Policy": "Open End Policy", "Open End Policy cannot be used together with Renewal Agreement, Expiry Date Rule, Coverage Period, Pre-calculate Premium installment method or Single Premium (Payment Period & Frequency), please check these configurations before submit.": "Open End Policy cannot be used together with Renewal Agreement, Expiry Date Rule, Coverage Period, Pre-calculate Premium installment method or Single Premium (Payment Period & Frequency), please check these configurations before submit.", "Open End Policy is already selected and cannot be used together with Allow Renewal. Do you want to deselect Open End Policy and proceed with selecting Allow Renewal instead?": "Open End Policy is already selected and cannot be used together with Allow Renewal. Do you want to deselect Open End Policy and proceed with selecting Allow Renewal instead?", "Optional": "Optional", "Optional / Required": "Optional / Required", "Optional Liability": "Optional Liability", "Optional/Mandatory": "Optional/Mandatory", "Order": "Order", "Organization": "Organization", "Other": "Other", "Other Setting": "Other Setting", "Overdue Auto Deduction": "Overdue Auto Deduction", "Overdue Handling": "Overdue Handling", "Overdue Status": "Overdue Status", "Package": "{{package,package}}", "Package Agreement": "{{package,package}} Agreement", "Package Category": "{{package,package}} Category", "Package Code": "{{package,package}} Code", "Package code is required": "{{package,package}} code is required", "Package Configuration": "{{package,package}} Configuration", "Package Management": "{{package,package}} Management", "Package Name": "{{package,package}} Name", "Package Net Premium Adjustment": "{{package,package}} Net Premium Adjustment", "PackageId is required": "PackageId is required", "Parameter have been changed. The uploaded matrixtable will be cleared. Please confirm.": "Parameter have been changed. The uploaded matrixtable will be cleared. Please confirm.", "Parameters": "Parameters", "Parameters(Result)": "Parameters(Result)", "Participating Product": "Participating {{product,product}}", "Partner Fee Calculation": "Partner Fee Calculation", "pay_method": "Payment Method", "Payer Elements": "Payer Elements", "PAYER INFO": "PAYER INFO", "Payment Defer Period Type": "Payment Defer Period Type", "Payment Frequency": "Payment Frequency", "Payment Option": "Payment Option", "Payment Period": "Payment Period", "Payment Period Type": "Payment Period Type", "payment_type": "Payment Type", "Period Type": "Period Type", "Pin": "<PERSON>n", "Plan": "Plan", "Plan (Plan Code + Plan Name)": "Plan (Plan Code + Plan Name)", "Plan Code": "Plan Code", "Plan code is required": "Plan code is required", "Plan Configuration": "Plan Configuration", "Plan Display": "Plan Display", "Plan Group": "Plan Group", "plan group code must be unique": "plan group code must be unique", "Plan Group Name": "Plan Group Name", "Plan is required": "Plan is required", "Plan Name": "Plan Name", "Plan name is required": "Plan name is required", "Plan Premium Model": "Plan Premium Model", "Plan Recommend": "Plan Recommend", "Plan Recommendation-Matrix Table": "Plan Recommendation-Matrix Table", "Planned Premium Configuration": "Planned Premium Configuration", "Planned premium is required": "Planned premium is required", "Please": "Please", "Please add one “Beneficiary” at least.": "Please add one “Beneficiary” at least.", "Please add one “Insured” at least.": "Please add one “Insured” at least.", "Please add one “Object” at least.": "Please add one “Object” at least.", "Please add stack correlation": "Please add stack correlation", "Please associate goods and plan.": "Please associate goods and plan.", "Please Calculate first!": "Please Calculate first!", "Please choose at least one coverage period agreement": "Please choose at least one coverage period agreement", "Please choose at least one installment premium frequency agreement": "Please choose at least one installment premium frequency agreement", "Please choose at least one premium period agreement": "Please choose at least one premium period agreement", "Please complete the configuration": "Please complete the configuration", "Please configure intermediate SA calculation formula in Product Center for corresponding liability.": "Please configure intermediate SA calculation formula in Product Center for corresponding liability.", "Please configure POS premium refund rule for each sales channel.": "Please configure POS premium refund rule for each sales channel.", "Please configure the min or max value of the range.": "Please configure the min or max value of the range.", "Please confirm that all configurations have been saved before submission": "Please confirm that all configurations have been saved before submission", "Please confirm that the information of the beneficiary is consistent with insured.": "Please confirm that the information of the beneficiary is consistent with insured.", "Please confirm that the information of the beneficiary is consistent with policyholder.": "Please confirm that the information of the beneficiary is consistent with policyholder.", "Please confirm that the information of the insured is consistent with policyholder.": "Please confirm that the information of the insured is consistent with policyholder.", "Please copy your File URL first.": "Please copy your File URL first.", "Please define numerical values in the table without including the percentage sign. For example, if you wish to define 10%, simply enter 10 in the table.": "Please define numerical values in the table without including the percentage sign. For example, if you wish to define 10%, simply enter 10 in the table.", "Please do not select the same fund for the same Premium Type": "Please do not select the same fund for the same Premium Type", "Please don’t enter special characters": "Please don’t enter special characters", "Please don’t enter special characters, such as~`!@#$%^*()+.": "Please don’t enter special characters, such as~`!@#$%^*()+.", "Please don’t enter special characters, such as~`!@#$%^&*()+.": "Please don’t enter special characters, such as~`!@#$%^&*()+.", "Please ensure sum of beneficiary ratio equals to 100%.": "Please ensure sum of beneficiary ratio equals to 100%.", "Please enter English letters": "Please enter English letters", "Please enter numeric value": "Please enter numeric value", "Please enter premium of regular top up or single top up.": "Please enter premium of regular top up or single top up.", "Please enter the correct number": "Please enter the correct number", "Please fill in all quotation factors before clicking “Calculation”": "Please fill in all quotation factors before clicking “Calculation”", "Please fill in the following information before selecting rider product": "Please fill in the following information before selecting rider {{product,product,lowerCase}}", "Please fill in the following quotation factors before calculation": "Please fill in the following quotation factors before calculation", "Please input": "Please input", "Please input at least one form item": "Please input at least one form item", "Please input at least one option of file, file address, and file declaration.": "Please input at least one option of file, file address, and file declaration.", "Please input both customer ID type and number.": "Please input both customer ID type and number.", "Please input in this format: 'HH:mm'. e.g. 12:00": "Please input in this format: 'HH:mm'. e.g. 12:00", "Please input number": "Please input number", "Please input or paste the URL here": "Please input or paste the URL here", "Please input Premium Allocation": "Please input Premium Allocation", "Please input right format": "Please input right format", "Please select": "Please select", "Please select a package or add a coverage plan.": "Please select a {{package,package,lowerCase}} or add a coverage plan.", "Please select a package or add a plan.": "Please select a {{package,package,lowerCase}} or add a plan.", "Please select at least one element.": "Please select at least one element.", "Please select at least two default answer.": "Please select at least two default answer.", "Please select Fund": "Please select Fund", "Please select goods first": "Please select goods first", "Please select language": "Please select language", "Please select lanuch date first": "Please select lanuch date first", "Please select related formula for Commission": "Please select related formula for Commission", "Please select related formula for Commission Clawback": "Please select related formula for Commission Clawback", "Please select Related Package": "Please select Related {{package,package}}", "Please select the object component that you want to add": "Please select the object component that you want to add", "Please select the sub category that you want to add": "Please select the sub category that you want to add", "Please upload a CSV or Excel file.": "Please upload a CSV or Excel file.", "Please upload file": "Please upload file", "Please upload file of .xls, .xlsx": "Please upload file of .xls, .xlsx", "pleaseInput": "Please input", "POLICY BASIC INFO": "POLICY BASIC INFO", "Policy Change": "Policy Change", "Policy Claim Stack": "Policy Claim <PERSON>", "Policy Effective Without Collection (NB)": "Policy Effective Without Collection (NB)", "Policy Effective Without Collection (Renewal)": "Policy Effective Without Collection (Renewal)", "Policy Elements": "Policy Elements", "Policy Issuance Order": "Policy Issuance Order", "Policy Manual": "Policy Manual", "Policy Net Premium Adjustment": "Policy Net Premium Adjustment", "Policy No.": "Policy No.", "Policy SA": "Policy SA", "Policy Stack": "Policy Stack", "Policy Type": "Policy Type", "Policy will be auto reviewed on each Review Recurrency from the Effective Date.": "Policy will be auto reviewed on each Review Recurrency from the Effective Date.", "Policy Year": "Policy Year", "Policyholder": "Policyholder", "POLICYHOLDER INFO": "POLICYHOLDER INFO", "PolicyStatusEnum.POLICY_EFFECT": "Effective", "POS Premium Refund Type": "POS Premium Refund Type", "Premium Adjustment": "Premium Adjustment", "Premium Agreement": "Premium Agreement", "Premium Allocation": "Premium Allocation", "Premium Allocation must equal to 100%.": "Premium Allocation must equal to 100%.", "Premium allocation section: Premium type “All” should not combined with other premium types.": "Premium allocation section: Premium type “All” should not combined with other premium types.", "Premium Allocation(%)": "Premium Allocation(%)", "Premium Calculation Method": "Premium Calculation Method", "Premium Composition": "Premium Composition", "Premium Currency": "Premium Currency", "Premium Discount": "Premium Discount", "Premium Due Date Rule": "Premium Due Date Rule", "Premium Due Date Rules": "Premium Due Date Rules", "Premium End Date Calculation Rule": "Premium End Date Calculation Rule", "Premium Frequency": "Premium Frequency", "Premium Funder Elements": "Premium Funder Elements", "Premium Notice Date Compare with Due Date": "Premium Notice Date Compare with Due Date", "Premium Period": "Premium Period", "Premium Period & Installment": "Premium Period & Installment", "Premium Period Configuration": "Premium Period Configuration", "Premium Period Type": "Premium Period Type", "Premium Period Value Type": "Premium Period Value Type", "Premium Related": "Premium Related", "Premium Type": "Premium Type", "Pricing Currency": "Pricing Currency", "Pricing Currency corresponding to the calculated result of the formula. If select \"Premium Currency\", the formula's calculated result will be output directly.If \"SA Currency\", the calculated result of the formula will be converted from the SA currency to the premium currency using the exchange rate.": "Pricing Currency corresponding to the calculated result of the formula. If select \"Premium Currency\", the formula's calculated result will be output directly.If \"SA Currency\", the calculated result of the formula will be converted from the SA currency to the premium currency using the exchange rate.", "Process Related": "Process Related", "Product": "{{product,product}}", "Product / Liability": "{{product,product}} / Liability", "Product & Liability": "{{product,product}} & Liability", "Product and liability are required": "{{product,product}} and liability are required", "Product Category": "{{product,product}} Category", "Product Center": "Product Center", "Product Claim Stack": "{{product,product}} <PERSON><PERSON><PERSON>", "Product Code": "{{product,product}} Code", "Product Code - Version": "{{product,product}} Code - Version", "Product Code/Name": "{{product,product}} Code/Name", "Product Correlation Matrix": "{{product,product}} Correlation Matrix", "Product Info": "{{product,product}} Info", "Product Name": "{{product,product}} Name", "Product Name / Code": "{{product,product}} Name / Code", "Product Overdue Status": "{{product,product}} Overdue Status", "Product Premium": "{{product,product}} Premium", "Product Premium Setting": "{{product,product}} Premium Setting", "Product SA": "{{product,product}} SA", "Product SA Setting": "{{product,product}} SA Setting", "Product Stack": "Product Stack", "Product Tax": "{{product,product}} Tax", "Product Version": "{{product,product}} Version", "Products Involved in Accumulation": "{{product,product}}s Involved in Accumulation", "Public Display Period": "Public Display Period", "Public Task": "Public Task", "Publish": "Publish", "Publish Success": "Publish Success", "Publish successfully": "Publish successfully", "Question Code": "Question Code", "Question Declaration": "Question Declaration", "Question Description": "Question Description", "Questionnaire": "Questionnaire", "QUESTIONNAIRE": "QUESTIONNAIRE", "Questionnaire Name": "Questionnaire Name", "Questionnaire Purpose": "Questionnaire Purpose", "Quick Menu": "Quick Menu", "Quotation": "Quotation", "Quotation Code": "Quotation Code", "Quotation Details": "Quotation Details", "Random": "Random", "Range": "Range", "Ratetable Category": "Ratetable Category", "Ratetable Code": "Ratetable Code", "Re-Define Effective Date Rule": "Re-Define Effective Date Rule", "Recommend": "Recommend", "recommendOnlyOne": "Please recommend only one plan", "Refund Formula": "Refund Formula", "Regular Top Up": "Regular Top Up", "Regulation of Products": "Regulation of Products", "Related Main": "Related Main", "Related Main Product": "Related Main {{product,product}}", "Related Package Info": "Related {{package,package}} Info", "Relational Policy No.": "Relational Policy No.", "Renewal": "Renewal", "Renewal Agreement": "Renewal Agreement", "Renewal Extraction Period": "Renewal Extraction Period", "Renewal Grace Period": "<PERSON><PERSON>", "Renewal Policy Effective Date Rule": "Renewal Policy Effective Date Rule", "Renewal Policy Effective Without Collection": "Renewal Policy Effective Without Collection", "Renewal Proposal Submit Date": "Renewal Proposal Submit Date", "Retrieve the policy to be reviewed and generate the bill X days prior to the Review Date.": "Retrieve the policy to be reviewed and generate the bill X days prior to the Review Date.", "Review Extraction Date": "Review Extraction Date", "Review Recurrence": "Review Recurrence", "Rider": "Rider", "Rider Code": "Rider Code", "Rider Liability": "Rider Liability", "Rider Name": "Rider Name", "Rider Premium Payment Method": "Rider Premium Payment Method", "Rider Product Code": "Rider {{product,product}} Code", "RSTU／STU must be collected for initial premium": "RSTU／STU must be collected for initial premium", "Rule/Rule Set": "Rule/Rule Set", "Rule1：Corresponding to effective date or period end date": "Rule1：Corresponding to effective date or period end date", "Rule2：Corresponding to effective date or next date of period end date": "Rule2：Corresponding to effective date or next date of period end date", "Rule3: Corresponding to effective date or period end date and fixed Feb 28 as period end date": "Rule3: Corresponding to effective date or period end date and fixed Feb 28 as period end date", "SA / Premium Details of Each Insured": "SA / Premium Details of Each Insured", "SA Currency": "SA Currency", "Sales Attributes": "Sales Attributes", "Sales Channel": "Sales Channel", "Sales Channel is empty or multiple sales channels exist.": "Sales Channel is empty or multiple sales channels exist.", "Sales Configuration": "Sales Configuration", "Sales District": "Sales District", "Sales Platform": "Sales Platform", "Sales Time": "Sales Time", "salesType": "Sales Type", "Same as Launch Period": "Same as Launch Period", "Same as SA Currency": "Same as SA Currency", "Same Premium Calculation for Each Insured": "Same Premium Calculation for Each Insured", "Save": "Save", "Save Sort": "Save Sort", "Save success": "Save success", "Save Successful.": "Save Successful.", "Save successfully": "Save successfully", "Save Successfully": "Save Successfully", "save_suc": "Save successfully!", "Scenario Validation": "Scenario Validation", "Search": "Search", "Search Goods Name": "Search Goods Name", "Search Voucher Name": "Search Voucher Name", "Secondary Life Insured Elements": "Secondary Life Insured Elements", "Select Benefit Factors": "Select Benefit Factors", "Select from the coverage periods in the associated products, or click \"Add New\" to create a new one.": "Select from the coverage periods in the associated products, or click \"Add New\" to create a new one.", "Select from the premium periods in the associated products, or click \"Add New\" to create a new one.": "Select from the premium periods in the associated products, or click \"Add New\" to create a new one.", "Select Parameter": "Select Parameter", "SELECT PLAN": "SELECT PLAN", "Select Product": "Select {{product,product}}", "Select Questionnaire Language": "Select Questionnaire Language", "Select Relationship Package": "Select Relationship {{package,package}}", "Select Rider": "Select Rider", "Select Stack Code": "Select Stack Code", "Selected sales channel has been configured with conflicted POS premium refund rule.": "Selected sales channel has been configured with conflicted POS premium refund rule.", "Service Code": "Service Code", "Service Company": "Service Company", "Service Effective Time": "Service Effective Time", "Service Fee Clawback": "Service Fee Clawback", "Service Maintenance": "Service Maintenance", "Service Name": "Service Name", "Service Type": "Service Type", "service_page_amount": "Service Amount", "service_page_create_time": "Create Time", "service_page_creator": "Creator", "service_page_effective_time": "Service Effective Time", "service_page_effective_time2": "Effective Time", "service_page_fac_code": "Facilitator Code", "service_page_fac_name": "Facilitator Name", "service_page_file_name": "File Name", "service_page_modi_time": "Modified Time", "service_page_modifier": "Modifier", "service_page_service_code": "Service Code", "service_page_service_name": "Service Name", "service_page_service_status": "Service Status", "service_page_total": "Total", "service_page_used": "Used", "Set Default Time": "Set Default Time", "Set Fixed Time": "Set Fixed Time", "Set Relationship with Package": "Set Relationship with {{package,package}}", "Settlement Rule": "Settlement Rule", "Show Financial Declaration": "Show Financial Declaration", "Show Health Declaration": "Show Health Declaration", "Show in Application Form": "Show in Application Form", "Single Top Up": "Single Top Up", "Sorry no goods were found": "Sorry no goods were found", "Sort": "Sort", "sort successfully": "sort successfully", "Special Agreement": "Special Agreement", "Specified SA & Premium": "Specified SA & Premium", "Stack Code": "Stack Code", "Stack code have been changed. The uploaded file will be cleared. Please confirm.": "Stack code have been changed. The uploaded file will be cleared. Please confirm.", "Stack Component Name": "Stack Component Name", "Stack Content": "Stack Content", "Stack Description": "Stack Description", "Stack Level": "Stack Level", "Stack Name": "Stack Name", "Stack Template Code": "Stack Template Code", "Stack Template Name": "Stack Template Name", "Stack Type": "Stack Type", "Stack Unit": "Stack Unit", "Stack Unit Type": "Stack Unit Type", "Stack Value": "Stack Value", "Stack Value Matrix": "Stack Value Matrix", "Stack Value Type": "Stack Value Type", "Stamp Duty": "Stamp Duty", "Standard Premium": "Standard Premium", "Start date": "Start date", "Start Time": "Start Time", "status": "Status", "Sub Condition Type": "Sub Condition Type", "Submit": "Submit", "Submit success": "Submit successfully", "Submit successfully": "Submit successfully", "Submit successfully!": "Submit successfully!", "Sum Assured": "Sum Assured", "Summary": "Summary", "Support maintaining the association between stack values, only support value types of fixed amount and enumeration.": "Support maintaining the association between stack values, only support value types of fixed amount and enumeration.", "Supported Assignee Type": "Supported Assignee Type", "Supported Beneficiary Type": "Supported Beneficiary Type", "Survival Benefit Configuration": "Survival Benefit Configuration", "Switching the package will clear all definitions under the plan. Please confirm to proceed with the change.": "Switching the {{package,package,lowerCase}} will clear all definitions under the plan. Please confirm to proceed with the change.", "System error": "System error", "System will automatically review the policy on each Review Recurrence starting from the effective date.": "System will automatically review the policy on each Review Recurrence starting from the effective date.", "System will automatically review the policy on each Review Recurrence starting from the effective date. When review recurred on monthly basis, 'Premium Period Type' in Premium Period & Installment must be set up as 'Monthly' as well.": "System will automatically review the policy on each Review Recurrence starting from the effective date. When review recurred on monthly basis, 'Premium Period Type' in Premium Period & Installment must be set up as 'Monthly' as well.", "Tag": "Tag", "Target User of Voucher": "Target User of Voucher", "Targeted Customer": "Targeted Customer", "Tax": "Tax", "Tax Amount": "Tax Amount", "Tax Setting": "Tax Setting", "Tax Type": "Tax Type", "Tax Value Type": "Tax Value Type", "Template Code / Name": "Template Code / Name", "Tenant Enumerated Sync": "Tenant Enumerated Sync", "text_delete_success": "Delete successfully!", "The coverage period agreement will be deleted": "The coverage period agreement will be deleted", "The Display Time should be within the Launch Date": "The Display Time should be within the Launch Date", "The effective date of this file can't intersect with the existed file": "The effective date of this file can't intersect with the existed file", "The fixed due date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.": "The fixed due date should be Xth of month. If certain month does not have that date, system should move the due date to the last day of that month.", "The following package(s) have used this application elements group. Editing this record and the following package(s) will be affected. Please check before change:": "The following {{package,package,lowerCase}}(s) have used this application elements group. Editing this record and the following {{package,package,lowerCase}}(s) will be affected. Please check before change:", "The fund should be unique for universal product.": "The fund should be unique for universal {{product,product,lowerCase}}.", "The goods code could only contain letters, numbers, and underscores (_).": "The goods code could only contain letters, numbers, and underscores (_).", "The input number cannot be zero": "The input number cannot be zero", "The insurance application failed. Please check the information and resubmit.": "The insurance application failed. Please check the information and resubmit.", "The marketing goods contain policy types other than Normal Policy or Group Policy. These types are not subject to standard validation checks before launching. Do you want to proceed?": "The marketing goods contain policy types other than Normal Policy or Group Policy. These types are not subject to standard validation checks before launching. Do you want to proceed?", "The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59": "The Month should be natural month.E.g. 11/16 00:00:00~12/15 23:59:59 12/16 00:00:00~1/15 23:59:59", "The package code could only contain letters, numbers, and underscores (_).": "The {{package,package,lowerCase}} code could only contain letters, numbers, and underscores (_).", "The packages associated with the coverage plan must be within the selected packages.": "The {{package,package,lowerCase}}s associated with the coverage plan must be within the selected {{package,package,lowerCase}}s.", "The packages associated with the plan must be within the selected packages.": "The {{package,package,lowerCase}}s associated with the plan must be within the selected {{package,package,lowerCase}}s.", "The plan code could only contain letters, numbers, and underscores (_).": "The plan code could only contain letters, numbers, and underscores (_).", "The product lacks liabilities, and please add the configuration in the product center": "The {{product,product,lowerCase}} lacks liabilities, and please add the configuration in the {{product,product,lowerCase}} center", "The questionnaire does not exist.": "The questionnaire does not exist.", "The questionnaire is filled out. Proceed to insurance application.": "The questionnaire is filled out. Proceed to insurance application.", "The sales channels have related configured notifications, please update notification configurations if needed.": "The sales channels have related configured notifications, please update notification configurations if needed.", "The Sales Time should be within the Launch Date": "The Sales Time should be within the Launch Date", "The second value should greater than the first value": "The second value should greater than the first value", "The sum of Maximum Sales Units of each plan is greater than Goods Wholesale Volume!": "The sum of Maximum Sales Units of each plan is greater than Goods Wholesale Volume!", "The sum of Maximum Sales Units of each plan is greather than Goods Wholesale Volume!": "The sum of Maximum Sales Units of each plan is greather than Goods Wholesale Volume!", "There is unfinished editing in <{{title}}>. Please complete before proceeding.": "There is unfinished editing in <{{title}}>. Please complete before proceeding.", "There must be one or more rider benefits under the virtual main benefit.": "There must be one or more rider benefits under the virtual main benefit.", "This application elements group is relied on": "This application elements group is relied on", "This file was effective in the past, are you sure to delete it?": "This file was effective in the past, are you sure to delete it?", "This record was configured based on old version. Currently, viewing and editing are not supported.": "This record was configured based on old version. Currently, viewing and editing are not supported.", "Ticket": "Ticket", "Tied Agent": "Tied Agent", "Time Zone": "Time Zone", "Times Type": "Times Type", "Tips: For relationship, you can configure as “attachable”, “non-attachable” or “compulsory”.": "Tips: For relationship, you can configure as “attachable”, “non-attachable” or “compulsory”.", "Total Premium": "Total Premium", "Transaction Amount": "Transaction Amount", "Transaction Date": "Transaction Date", "Transaction Information": "Transaction Information", "Trigger": "<PERSON><PERSON>", "Trigger Point by Event Policy": "Trigger Point by Event Policy", "Trip": "Trip", "Trustee Elements": "Trustee Elements", "TRUSTEE INFO": "TRUSTEE INFO", "Type": "Type", "Unanswered question exists, please confirm.": "Unanswered question exists, please confirm.", "Uncompleted questionnaire exists, please confirm.": "Uncompleted questionnaire exists, please confirm.", "Underwriting Error Message": "Underwriting Error Message", "Underwriting Order": "Underwriting Order", "Underwriting Related": "Underwriting Related", "Underwriting Type": "Underwriting Type", "Unified Underwriting Conclusion Flag": "Unified Underwriting Conclusion Flag", "Unlimited": "Unlimited", "Unnamed Insured": "Unnamed Insured", "Unset": "Unset", "Updatable or Not": "Updatable or Not", "Updatable When Manual UW": "Updatable When Manual UW", "Updatable When POS": "Updatable When POS", "Upload": "Upload", "Upload Batch No.": "Upload Batch No.", "Upload Fail!": "Upload Fail!", "Upload Files": "Upload Files", "Upload Successful.": "Upload Successful.", "Upload Successfully": "Upload Successfully", "URL": "URL", "usage_time": "Usage Time", "Usage-based Goods": "Usage-based Goods", "User Input": "User Input", "Validation Response": "Validation Response", "value": "value", "Value Assignment": "Value Assignment", "Value Code": "Value Code", "Value Type": "Value Type", "Variable Amount": "Variable Amount", "Vehicle Additional Equipment": "Vehicle Additional Equipment", "Vehicle Database Name": "Vehicle Database Name", "Vehicle Loan": "Vehicle Loan", "Vehicle Market Value Formula": "Vehicle Market Value Formula", "Vehicle Related Configuration": "Vehicle Related Configuration", "Version": "Version", "View Component": "View Component", "View My Creations": "View My Creations", "View Voucher": "View Voucher", "Voucher": "Voucher", "Voucher Application Channel": "Voucher Application Channel", "Voucher Basic Info": "Voucher Basic Info", "Voucher Calculation & Effective Rule": "Voucher Calculation & Effective Rule", "Voucher Channel": "Voucher Channel", "Voucher Code": "Voucher Code", "Voucher Consumption Status": "Voucher Consumption Status", "Voucher Correlation": "Voucher Correlation", "Voucher Detail": "Voucher Detail", "Voucher Effective Period": "Voucher Effective Period", "Voucher Face Amount": "Voucher Face Amount", "Voucher Name": "Voucher Name", "Voucher Number Generation Rule": "Voucher Number Generation Rule", "Voucher Status": "Voucher Status", "Voucher Type": "Voucher Type", "Voucher Type - Face Amount": "Voucher Type - Face Amount", "Voucher Value Type": "Voucher Value Type", "Waiver": "Waiver", "Waiver Details": "Waiver Details", "Waiver Product": "Waiver {{product,product}}", "Waiver Product Duplicated": "Waiver {{product,product}} Duplicated", "Waiver Rider Liability": "Waiver Rider Liability", "Warning Threshold": "Warning Threshold", "Whether to keep the voucher balance": "Whether to keep the voucher balance", "Wholesale Volume": "Wholesale Volume", "Wholesale Volume Warning": "Wholesale Volume Warning", "With Guarantee Period": "With Guarantee Period", "Year": "Year", "Year(s)": "Year(s)", "yes": "Yes", "Yes": "Yes", "Yes-Recurring Single Top Up / Single Top Up must be collected before policy is effective.": "Yes-Recurring Single Top Up / Single Top Up must be collected before policy is effective.", "You are applying": "You are applying", "You can only upload PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG": "You can only upload PDF/DOC/DOCX/XLS/XLSX/PNG/JPG/JPEG", "You can only upload xls, xlsx": "You can only upload xls, xlsx", "You can only upload xls, xlsx, doc, docx, pdf, jpg or png.": "You can only upload xls, xlsx, doc, docx, pdf, jpg or png.", "You can only upload XLSX/CSV": "You can only upload XLSX/CSV", "Your policy has been issued.": "Your policy has been issued.", "Your proposal has been issued.": "Your proposal has been issued."}