/** @type {import('ts-jest/dist/types').InitialOptionsTsJest} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jest-environment-jsdom-sixteen',
  transform: {
    // 将.js后缀的文件使用babel-jest处理
    '^.+\\.js$': 'babel-jest',
    '^.+\\.(ts|tsx)$': 'ts-jest',
    '^.+\\.(css|less|scss|svg|png)$': '<rootDir>/jest/styleMock.js',
  },
  transformIgnorePatterns: ['/node_modules/(?!(lodash|antd))'],
  moduleNameMapper: {
    // 设置webpack的alias
    '^@market(.*)$': '<rootDir>/src/$1',
  },
  globals: {
    'ts-jest': {
      // 忽略ts错误
      diagnostics: false,
    },
  },
};
