const path = require('path');
const { merge } = require('webpack-merge');
const common = require('./rspack.common');
const rspack = require('@rspack/core');

const pkgName = require('../package.json').name;
const { uploadBundleData } = require('../../../scripts/bundle');
const BuildBundlePlugin = require('../../../scripts/build-bundle-plugin');
const BuildTimerPlugin = require('../../../scripts/build-timer-rspack-plugin');

const { generateSMPData, uploadData } = require('../../../scripts/buildProfiling');

const prdConfig = merge(common, {
  mode: 'production',
  devtool: false,
  node: false,
  output: {
    path: path.resolve(__dirname, '../../../dist/children', pkgName),
    library: pkgName,
    publicPath: `/children/${pkgName}/`,
    filename: '[name].bundle.[contenthash:8].js',
    chunkFilename: '[name].[contenthash:8].js',
    assetModuleFilename: '[name].[contenthash:8][ext]',
    libraryTarget: 'umd',
  },
  cache: false,
  optimization: {
    runtimeChunk: {
      name: 'runtime',
    },
    minimizer: [
      new rspack.SwcJsMinimizerRspackPlugin({
        comments: false,
        dropConsole: true,
      }),
      new rspack.SwcCssMinimizerRspackPlugin(),
    ],
    splitChunks: {
      chunks: 'all', // all，async 和 initial
      minSize: 20000, // 生成 chunk 的最小体积
      minChunks: 1, // 拆分前必须共享模块的最小 chunks 数
      maxAsyncRequests: 5, // 按需加载时的最大并行请求数
      maxInitialRequests: Infinity, // 入口点的最大并行请求数
      automaticNameDelimiter: '~', // 指定用于生成名称的分隔符
      cacheGroups: {
        default: false,
        vendors: {
          name: 'chunk-vendor',
          chunks: 'all',
          minChunks: 2,
          test: /[\\/]node_modules[\\/]/,
          priority: -20,
        },
        commons: {
          name: `chunk-common`,
          chunks: 'all',
          minChunks: 2,
          // priority的值必须比vendors中的大，才能提取出来
          priority: -10,
        },
        ant_design: {
          chunks: 'all',
          name: `ant_design`,
          test: /antd/,
          priority: 0,
        },
        antv: {
          chunks: 'all',
          name: `antv`,
          test: /antv/,
          priority: 5,
        },
        ant_design_icons: {
          chunks: 'all',
          name: `ant_design_icon`,
          test: /[\\/]@ant-design[\\/]/,
          priority: 10,
        },
        moment: {
          chunks: 'all',
          name: `moment`,
          test: /moment/,
          priority: 30,
        },
        lodash: {
          chunks: 'all',
          name: `lodash`,
          test: /lodash/,
          priority: 40,
        },
        'nagrand-ui': {
          chunks: 'all',
          name: `nagrand-ui`,
          test: /nagrand-ui/,
          priority: 50,
        },
        'genesis-web-service': {
          chunks: 'all',
          name: `genesis-web-service`,
          test: /genesis-web-service/,
          priority: 60,
        },
      },
    },
  },
  plugins: [
    new rspack.HtmlRspackPlugin({
      title: 'Peak3 | Graphene',
      filename: 'index.html',
      template: path.resolve(__dirname, '../../../public/index.html'),
      favicon: path.resolve(__dirname, '../../../public/favicon.ico'),
    }),
    new rspack.BannerPlugin({
      banner: '版权所有，翻版必究',
      footer: true,
    }),
    new BuildTimerPlugin({
      outputFormat: jsonBlob => {
        return generateSMPData(pkgName, jsonBlob);
      },
      outputTarget: output => {
        uploadData(output);
      },
    }),
    new BuildBundlePlugin({
      outputTarget: output => {
        if (process.env.NODE_ENV === 'production') {
          uploadBundleData(require('../package.json').name, output);
        }
      },
    }),
  ],
});

module.exports = prdConfig;
