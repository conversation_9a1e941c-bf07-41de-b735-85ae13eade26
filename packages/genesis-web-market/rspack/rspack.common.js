const path = require('path');
const { prependD<PERSON><PERSON>agrand, lessVars } = require('@zhongan/nagrand-ui');
const AntdMomentWebpackPlugin = require('@ant-design/moment-webpack-plugin');
const rspack = require('@rspack/core');

const marketLessVars = { ...lessVars };
delete marketLessVars['@ant-prefix'];

module.exports = {
  entry: path.resolve(__dirname, '../src/index.js'),
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      '@market': path.resolve(__dirname, '../src'),
    },
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx|mjs|mjsx|ts|tsx)$/,
        include: [
          path.resolve(__dirname, './src'),
          /genesis-web-/,
          /product-types/, // 处理后端接口类型文件
          /market-types/,
          /calculator-types/,
          /metadata-types/,
        ],
        use: [
          {
            loader: 'builtin:swc-loader',
            options: {
              sourceMap: true,
              jsc: {
                parser: { syntax: 'typescript', tsx: true },
                transform: {
                  react: {
                    runtime: 'automatic',
                    development: process.env.NODE_ENV === 'development',
                    refresh: process.env.NODE_ENV === 'development',
                  },
                },
              },
            },
          },
        ],
      },
      {
        test: /\.css$/i,
        use: [{ loader: 'postcss-loader' }],
        type: 'css',
      },
      {
        test: /\.less$/,
        type: 'css',
        use: [
          {
            loader: 'less-loader',
            options: {
              additionalData: `${prependDataNagrand.replace(/\$/g, '@')};@ant-prefix: market-ant4;`,
              lessOptions: {
                modifyVars: {
                  ...marketLessVars,
                  'ant-prefix': 'market-ant4',
                },
                javascriptEnabled: true,
                math: 'always',
                sourceMap: false,
              },
            },
          },
        ],
      },
      {
        test: /\.s[ac]ss$/i,
        include: [path.resolve(__dirname, '../src'), /genesis-web-/, /node_modules\/@zhongan/],
        use: [
          {
            loader: 'style-loader',
            options: {
              insert: 'body', // 从 body 底部插入
              injectType: 'singletonStyleTag',
            },
          },
          {
            loader: 'css-loader',
            options: {
              import: false,
              modules: {
                exportLocalsConvention: 'camelCase',
                auto: true,
                localIdentName: '[path][name]__[local]--[hash:base64:5]',
              },
            },
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: {
                  tailwindcss: {},
                  autoprefixer: {},
                },
              },
            },
          },
          {
            loader: 'sass-loader',
            options: {
              additionalData: `${prependDataNagrand};$market-prefix: market-ant4;$ant-prefix: market-ant4;`,
            },
          },
        ],
      },
      // webpack asset module, use resource instead of url for convenience
      {
        test: /\.(woff|woff2|eot|ttf|otf|png|jpe?g|gif|ico)$/i,
        type: 'asset/resource',
      },
      {
        test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
        oneOf: [
          // use svg as component within js, jsx, ts, tsx, as url within css, scss, less
          {
            type: 'asset/resource',
            issuer: /\.(css|pcss|less|sass|scss)$/,
          },
          // use resource query to detect url mode or component mode, default component mode
          {
            type: 'asset/resource',
            resourceQuery: /assets/,
          },
          // ContextModule has no issuer
          {
            use: [
              {
                loader: '@svgr/webpack',
              },
            ],
          },
        ],
      },
    ],
  },
  plugins: [
    new AntdMomentWebpackPlugin({
      disableDayjsAlias: true,
    }),
    new rspack.ProvidePlugin({
      React: [require.resolve('react')],
      ReactDOM: [require.resolve('react-dom')],
    }),
  ],
};
