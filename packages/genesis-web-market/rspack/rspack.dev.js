const path = require('path');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const ReactRefreshPlugin = require('@rspack/plugin-react-refresh');
const { merge } = require('webpack-merge');
const rspack = require('@rspack/core');

const common = require('./rspack.common');

const { DevelopHelperPlugin } = require('../../../scripts/develop');
const { developHelperConfig } = require('../../../config');

module.exports = merge(common, {
  mode: 'development',
  devtool: 'eval-cheap-source-map',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].bundle.js',
    chunkFilename: '[id].chunk.js',
    // multiple asset name conflict
    assetModuleFilename: '[name].[contenthash:8][ext]',
  },
  devServer: {
    client: {
      overlay: false,
    },
    historyApiFallback: true,
    host: '0.0.0.0',
    port: process.env.DEV_PORT || 8080,
  },
  plugins: [
    new ReactRefreshPlugin(),
    new CleanWebpackPlugin(),
    new rspack.HtmlRspackPlugin({
      title: 'Peak3 | Graphene',
      filename: 'index.html',
      template: path.resolve(__dirname, '../../../public/index.html'),
      favicon: path.resolve(__dirname, '../../../public/favicon.ico'),
    }),
    new DevelopHelperPlugin(developHelperConfig),
  ],
  optimization: {
    runtimeChunk: {
      name: 'runtime',
    },
  },
});
