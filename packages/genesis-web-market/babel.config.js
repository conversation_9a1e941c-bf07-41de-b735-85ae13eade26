/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
module.exports = {
  presets: [
    [
      '@babel/preset-env',
      {
        modules: false,
        loose: true,
      },
    ],
    [
      '@babel/preset-react',
      {
        runtime: 'automatic',
      },
    ],
    '@babel/preset-typescript',
  ],
  plugins: [
    '@babel/plugin-proposal-class-properties',
    '@babel/plugin-proposal-optional-chaining',
    '@babel/plugin-proposal-nullish-coalescing-operator',
    '@babel/plugin-proposal-logical-assignment-operators',
    ['@babel/plugin-proposal-private-property-in-object', { loose: false }],
    ['@babel/plugin-proposal-private-methods', { loose: false }],
    [
      'babel-plugin-import',
      {
        libraryName: 'lodash-es',
        libraryDirectory: '.',
        style: false,
        camel2DashComponentName: false,
      },
      'lodash-es',
    ],
    [
      '@babel/plugin-transform-runtime',
      {
        helpers: true,
        corejs: 3,
      },
    ],
  ],
};
