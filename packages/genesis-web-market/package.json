{"name": "genesis-web-market", "version": "0.0.1", "private": true, "scripts": {"start": "cross-env NODE_ENV=development DEV_PORT=auto pnpm rspack serve --config rspack/rspack.dev.js", "start:dev": "cross-env NODE_ENV=development PROXY_CHANNEL=dev pnpm rspack serve --config rspack/rspack.dev.js", "start:test1": "cross-env NODE_ENV=development PROXY_CHANNEL=test1 pnpm rspack serve --config rspack/rspack.dev.js", "start:sit": "cross-env NODE_ENV=development PROXY_CHANNEL=sit pnpm rspack serve --config rspack/rspack.dev.js", "start:stg": "cross-env NODE_ENV=development PROXY_CHANNEL=stg pnpm rspack serve --config rspack/rspack.dev.js", "start:iac": "cross-env NODE_ENV=development PROXY_CHANNEL=iac pnpm rspack serve --config rspack/rspack.dev.js", "serve": "cross-env NODE_ENV=development pnpm webpack serve --hot --config webpack/webpack.dev.js --port=8080", "integrate": "cross-env NODE_ENV=production rspack --config rspack/rspack.prd.js --watch", "build:webpack": "pnpm webpack --config webpack/webpack.prd.js", "build": "pnpm rspack build --config rspack/rspack.prd.js", "lint-staged": "pnpm exec lint-staged", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"src/**/*\" --end-of-line auto", "clean:cache": "rm -rf webpack/.appcache", "prettier": "prettier -c --write \"**/*\"", "test": "cross-env NODE_ENV=test jest", "clear_jest": "jest --clear<PERSON>ache", "ack": "ts-node -T ../../scripts/auto-collect-i18n-keys/index packages/genesis-web-market/src"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@antv/g2plot": "^2.4.31", "@codemirror/autocomplete": "6.18.4", "@formily/antd-v5": "^1.1.9", "@formily/core": "2.3.1", "@formily/react": "2.3.1", "@uiw/react-codemirror": "4.21.20", "@zhongan/nagrand-ui": "^3.1.4", "@reduxjs/toolkit": "^2.5.0", "ahooks": "^3.8.4", "antd": "^5.22.3", "array-move": "^4.0.0", "calculator-types": "2.68.0", "classnames": "^2.5.1", "genesis-web-component": "workspace:*", "genesis-web-service": "workspace:*", "genesis-web-shared": "workspace:*", "jest": "26.3.0", "jest-environment-jsdom-sixteen": "^2.0.0", "lodash-es": "^4.17.21", "market-types": "2.84.2", "metadata-types": "2.68.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "product-types": "2.68.0", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-highlight-words": "^0.20.0", "react-i18next": "^15.4.0", "react-quill": "^1.3.5", "react-redux": "^9.2.0", "react-router-dom": "^6.27.0", "react-sortable-hoc": "^2.0.0", "redbox-react": "~1.6.0", "swr": "^2.2.5", "ts-jest": "26.5.5", "uuid": "^11.0.5", "xss": "^1.0.15", "qiankun": "^2.10.16"}, "devDependencies": {"@ant-design/moment-webpack-plugin": "^0.0.4", "@babel/cli": "^7.0.0", "@babel/core": "^7.13.10", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-logical-assignment-operators": "^7.13.8", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.13.8", "@babel/plugin-proposal-optional-chaining": "^7.13.8", "@babel/plugin-proposal-private-methods": "^7.0.0", "@babel/plugin-proposal-private-property-in-object": "^7.0.0", "@babel/plugin-proposal-throw-expressions": "^7.0.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.13.10", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.13.0", "@babel/runtime-corejs3": "^7.23.8", "@pmmmwh/react-refresh-webpack-plugin": "^0.4.3", "@rspack/cli": "0.7.5", "@rspack/core": "0.7.5", "@rspack/plugin-react-refresh": "0.7.5", "@svgr/webpack": "^5.5.0", "@types/jest": "^26.0.24", "@types/lodash-es": "^4.17.12", "@types/node": "^14.18.63", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/react-highlight-words": "^0.20.0", "@types/react-redux": "^7.1.33", "@types/react-scroll": "^1.8.10", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "autoprefixer": "^10.4.16", "babel-eslint": "^10.1.0", "babel-jest": "26.3.0", "babel-loader": "^8.3.0", "babel-plugin-import": "^1.13.8", "clean-webpack-plugin": "^4.0.0", "cross-env": "^7.0.3", "css-loader": "^5.2.7", "css-minimizer-webpack-plugin": "^3.4.1", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.6.0", "http-proxy-middleware": "^1.3.1", "less": "^4.2.0", "less-loader": "^8.1.1", "less-plugin-scope-modifyvar": "^1.0.2", "mini-css-extract-plugin": "^1.6.2", "opn": "^6.0.0", "postcss": "^8.4.33", "postcss-loader": "^5.3.0", "prettier-eslint": "^12.0.0", "react-refresh": "^0.9.0", "sass": "^1.69.7", "sass-loader": "^10.5.2", "speed-measure-webpack-plugin": "^1.5.0", "style-loader": "^2.0.0", "stylelint": "^13.13.1", "tailwindcss": "^3.4.17", "terser-webpack-plugin": "^5.3.10", "webpack-bundle-analyzer": "^4.10.1", "webpack-dev-server": "^3.11.3", "webpack-merge": "^5.10.0"}, "dependenciesMeta": {"antd": {"injected": true}, "genesis-web-component": {"injected": true}, "genesis-web-shared": {"injected": true}, "genesis-web-service": {"injected": true}}}