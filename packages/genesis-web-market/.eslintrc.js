/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

module.exports = {
  root: true,
  extends: ['airbnb', 'prettier'],
  parser: 'babel-eslint',
  parserOptions: {
    requireConfigFile: false,
    tsconfigRootDir: __dirname,
    ecmaFeature: {
      jsx: true,
    },
  },
  env: {
    browser: true,
    node: true,
  },
  settings: {
    react: {
      fragment: 'Fragment',
    },
    'import/resolver': {
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
      },
    },
  },
  ignorePatterns: ['**/asset/', 'node_modules/', 'static/', 'cli/'],
  overrides: [
    {
      files: ['**/*.ts', '**/*.tsx'],
      parser: '@typescript-eslint/parser',
      parserOptions: {
        project: './tsconfig.json',
        ecmaVersion: 10,
        sourceType: 'module',
        requireConfigFile: false,
        ecmaFeature: {
          jsx: true,
        },
      },
      extends: [
        'plugin:@typescript-eslint/recommended',
        'plugin:@typescript-eslint/eslint-recommended',
        'plugin:@typescript-eslint/recommended-requiring-type-checking',
      ],
      plugins: ['@typescript-eslint'],
      rules: {
        'import/extensions': 'off',
        'no-restricted-globals': 'off',
        'arrow-body-style': ['warn', 'as-needed'],
        curly: ['warn', 'multi-line'],
        'dot-notation': 'warn',
        eqeqeq: 'warn',
        'no-useless-constructor': 'off',
        'no-use-before-define': 'off',
        'new-parens': 'warn',
        'no-console': 'off',
        'no-caller': 'warn',
        'no-eval': 'warn',
        'no-new-wrappers': 'warn',
        'no-throw-literal': 'warn',
        'no-trailing-spaces': 'warn',
        'no-var': 'warn',
        'no-undef-init': 'warn',
        'no-unused-expressions': 'warn',
        'object-shorthand': 'warn',
        '@typescript-eslint/no-unsafe-return': 'warn',
        quotes: [
          'warn',
          'single',
          {
            avoidEscape: true,
            allowTemplateLiterals: true,
          },
        ],
        radix: 'warn',
        'spaced-comment': ['warn', 'always'],
        'space-infix-ops': 'warn',
        'import/newline-after-import': 'warn',
        'import/order': [
          'warn',
          {
            groups: [
              ['internal', 'external', 'builtin'],
              ['index', 'sibling', 'parent'],
            ],
            'newlines-between': 'always-and-inside-groups',
          },
        ],
        '@typescript-eslint/array-type': 'off',
        '@typescript-eslint/ban-ts-ignore': 'off',
        '@typescript-eslint/consistent-type-definitions': ['warn', 'interface'],
        '@typescript-eslint/no-floating-promises': 'off',
        '@typescript-eslint/consistent-type-assertions': [
          'warn',
          { assertionStyle: 'as', objectLiteralTypeAssertions: 'allow-as-parameter' },
        ],
        '@typescript-eslint/explicit-function-return-type': 'off',
        '@typescript-eslint/explicit-member-accessibility': [
          'warn',
          {
            accessibility: 'no-public',
          },
        ],
        '@typescript-eslint/member-delimiter-style': 'off',
        '@typescript-eslint/member-ordering': [
          'warn',
          {
            classes: [
              'private-static-field',
              'protected-static-field',
              'public-static-method',
              'constructor',
              'protected-static-method',
              'private-static-method',
              'protected-static-method',
              'public-static-method',
              'private-instance-method',
              'protected-instance-method',
              'public-instance-method',
            ],
          },
        ],
        '@typescript-eslint/no-empty-function': 'off',
        '@typescript-eslint/no-empty-interface': [
          'warn',
          {
            allowSingleExtends: true,
          },
        ],
        '@typescript-eslint/no-unsafe-assignment': 'warn',
        '@typescript-eslint/no-explicit-any': 'off',
        '@typescript-eslint/no-for-in-array': 'off',
        '@typescript-eslint/no-non-null-assertion': 'off',
        '@typescript-eslint/no-unused-vars': 'off',
        '@typescript-eslint/no-use-before-define': ['error'],
        '@typescript-eslint/prefer-function-type': 'warn',
        '@typescript-eslint/prefer-for-of': 'warn',
        '@typescript-eslint/prefer-string-starts-ends-with': 'warn',
        '@typescript-eslint/space-before-function-paren': [
          'warn',
          {
            anonymous: 'never',
            named: 'never',
            asyncArrow: 'always',
          },
        ],
        '@typescript-eslint/unified-signatures': 'warn',
        '@typescript-eslint/no-shadow': ['error'],
        '@typescript-eslint/explicit-module-boundary-types': 'off',
        '@typescript-eslint/no-misused-promises': 'off',
      },
    },
  ],
  plugins: ['react', 'react-hooks'],
  rules: {
    'import/extensions': 'off',
    'max-len': 0, // 单行最大长度 0 不校验
    indent: ['warn', 2, { SwitchCase: 1 }], // TODO: set to error later
    'quote-props': 0,
    'import/prefer-default-export': 0,
    'no-async-promise-executor': 0,
    'class-methods-use-this': 0,
    'no-useless-escape': 0,
    'no-confusing-arrow': 1,
    'no-unused-expressions': 0,
    'no-implicit-coercion': 0,
    'func-names': 0,
    'arrow-body-style': 0,
    'react/prefer-stateless-function': 0,
    'react/prop-types': [0], // propTypes校验警告
    'react/jsx-filename-extension': [2, { extensions: ['.js', '.jsx', '.tsx'] }], // 允许在js中使用jsx内容
    'react/jsx-props-no-spreading': 0,
    'operator-linebreak': 0, // 换行位置检查
    'react/sort-comp': 0, // 不强制eact成员方法顺序
    'object-curly-newline': 0,
    'no-underscore-dangle': 0, // 允许下划线命名
    'prefer-destructuring': 0, // 不强制解构赋值
    'react/react-in-jsx-scope': 0, // 取消使用jsx是必须引入react的限制
    'react/destructuring-assignment': 0, // 关闭强制使用结构解析
    'jsx-a11y/no-static-element-interactions': 0, // 关闭非button标签上使用click事件的校验
    'jsx-a11y/click-events-have-key-events': 0, // 关闭非button标签上使用click事件的校验
    'jsx-a11y/no-noninteractive-element-interactions': 0, // 关闭非button标签上使用click事件的校验
    'react-hooks/exhaustive-deps': [1],
    'implicit-arrow-linebreak': 0,
    'function-paren-newline': 0,

    // Victor modified/Users/<USER>/Workspace/gui/genesis-web/base/client/router.js
    camelcase: 0,
    'arrow-parens': [1, 'as-needed'], // TODO: set to error later
    'no-plusplus': [2, { allowForLoopAfterthoughts: true }],
    'react/no-deprecated': ['warn'], // TODO: remove it 37个
    'jsx-a11y/alt-text': 1, // TODO: remove it 80个 优先级降低
    'no-param-reassign': 0,
    'react/no-string-refs': 1, // TODO: remove it
    'no-restricted-globals': 1,
    'no-console': ['warn'], // TODO: set to error later
    'no-unused-vars': ['warn'], // TODO: set to error later
    'no-prototype-builtins': 1, // TODO: remove it
    'react/no-unused-state': 1, // TODO: remove it
    'import/order': 1,
    'react/no-array-index-key': 1, // TODO: remove it
    'react/require-default-props': 1, // TODO: remove it
    'import/no-webpack-loader-syntax': 1, // TODO: remove it
    'consistent-return': 1, // TODO: remove it
    'no-nested-ternary': 1, // TODO: remove it
    'react/no-access-state-in-setstate': 1, // TODO: remove it
    'react/state-in-constructor': 1, // TODO: remove it
    'import/no-cycle': 1, // TODO: remove it
    'no-restricted-syntax': 1, // TODO: remove it
    'react/static-property-placement': 1, // TODO: remove it
    'react/forbid-prop-types': 1, // TODO: remove it
    'prefer-const': 1, // TODO: remove it
    'array-callback-return': 1, // TODO: remove it
    'no-use-before-define': 1, // TODO: remove it
    'no-return-assign': 1, // TODO: remove it
    'react/default-props-match-prop-types': 1, // TODO: remove it
    'react/button-has-type': 1, // TODO: remove it
    'guard-for-in': 1, // TODO: remove it
    'no-loop-func': 1, // TODO: remove it
    'no-lonely-if': 1, // TODO: remove it
    'import/no-dynamic-require': 1, // TODO: remove it
    'global-require': 1, // TODO: remove it
    'no-await-in-loop': 1, // TODO: remove it
    'react/no-did-update-set-state': 1, // TODO: remove it
    'react/no-unused-prop-types': 1, // TODO: remove it
    'react/no-will-update-set-state': 1, // TODO: remove it
    'no-script-url': 1, // TODO: remove it
    'new-cap': 1, // TODO: remove it
    'no-mixed-operators': 1, // TODO: remove it
    'react/jsx-fragments': [2, 'element'], // TODO: remove it after upgrade of react
    'import/no-unresolved': [
      2,
      {
        ignore: ['^@client/', '^@server/', '^@market/'],
      },
    ],
    // https://github.com/benmosher/eslint-plugin-import/issues/422
    'import/no-extraneous-dependencies': ['error', { devDependencies: true }],
    'react/jsx-pascal-case': 0,
    'jsx-a11y/anchor-is-valid': 0,
    'jsx-a11y/label-has-associated-control': 1,
    'jsx-a11y/control-has-associated-label': 1,
    'default-case': 0,
    radix: 0,
    'max-classes-per-file': 0,
    'no-restricted-properties': 1,
    'jsx-a11y/iframe-has-title': 1,
    'no-continue': 0,
    'no-multi-assign': 0,
    'no-bitwise': 0,
    'react/jsx-curly-newline': 0,

    // zhangyang modified
    'react/jsx-wrap-multilines': 0, // 与prettier冲突
    'react/jsx-one-expression-per-line': 'off', // TODO: remove it 481个
    'newline-per-chained-call': 1, // TODO: remove it 8个 手动修复完之后，--fix会再改坏，后面再研究
    'nonblock-statement-body-position': 1, // TODO: remove it 42个
    curly: 1, // TODO: remove it 42个
    'react/jsx-indent': 1, // TODO: remove it 手动修复完之后，--fix会再改坏，后面再研究
    'no-empty-function': ['error', { allow: ['functions', 'arrowFunctions'] }],
    'no-useless-constructor': 'error',
    'react/no-children-prop': 'error',
    'react/no-typos': 'error',
    'react/jsx-no-target-blank': 2,
    'no-shadow': 0,
    'linebreak-style': ['off', 'windows'],
  },
  globals: {
    window: true,
  },
};
