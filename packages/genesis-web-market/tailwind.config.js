/** @type {import('tailwindcss').Config} */
const { lessWithCssVars, presetTwConfig } = require('@zhongan/nagrand-ui');

module.exports = {
  presets: [presetTwConfig],
  content: ['./src/**/*.tsx', './src/**/*.ts', './src/**/*.js', './src/**/*.jsx'],
  corePlugins: {
    preflight: false, // 去掉 tailwindcss 的基础样式设置
  },
  darkMode: 'class',
  theme: {
    extend: {
      colors: lessWithCssVars,
    },
    fontFamily: {
      helvetica: ['Helvetica'],
    },
  },
  variants: {},
  plugins: [
    function ({ addUtilities }) {
      const newUtilities = {
        '.divide-x-half': {
          transform: 'scale(1, 0.5)',
          height: '1px',
          width: '100%',
          borderBottomWidth: '1px',
          borderBottomStyle: 'solid',
        },
      };
      addUtilities(newUtilities, ['responsive', 'hover']);
    },
  ],
  prependData: `
    @import '@zhongan/nagrand-ui/dist/styles/variables.less';
    @import '@zhongan/nagrand-ui/dist/styles/mixins.less';
  `,
};
